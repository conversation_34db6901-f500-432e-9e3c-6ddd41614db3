"""
统一异常处理基类
解决异常处理设计不一致问题
"""
from fastapi import HTTPException, status
from typing import Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)


class BaseAPIException(HTTPException):
    """统一API异常基类"""
    
    def __init__(
        self, 
        status_code: int, 
        detail: str, 
        error_code: Optional[str] = None,
        headers: Optional[Dict[str, Any]] = None
    ):
        super().__init__(status_code=status_code, detail=detail, headers=headers)
        self.error_code = error_code
        
        # 记录异常日志
        logger.error(f"API异常: {error_code} - {detail}")


class ValidationError(BaseAPIException):
    """数据验证异常"""
    
    def __init__(self, detail: str, field: Optional[str] = None):
        error_code = "VALIDATION_ERROR"
        if field:
            detail = f"字段 '{field}': {detail}"
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=detail,
            error_code=error_code
        )


class NotFoundError(BaseAPIException):
    """资源不存在异常"""
    
    def __init__(self, resource: str, identifier: Optional[str] = None):
        if identifier:
            detail = f"{resource} (ID: {identifier}) 不存在"
        else:
            detail = f"{resource} 不存在"
        
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=detail,
            error_code="RESOURCE_NOT_FOUND"
        )


class PermissionDeniedError(BaseAPIException):
    """权限不足异常"""
    
    def __init__(self, action: Optional[str] = None):
        if action:
            detail = f"无权限执行操作: {action}"
        else:
            detail = "权限不足"
        
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=detail,
            error_code="PERMISSION_DENIED"
        )


class AuthenticationError(BaseAPIException):
    """认证失败异常"""
    
    def __init__(self, detail: str = "认证失败"):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=detail,
            error_code="AUTHENTICATION_FAILED",
            headers={"WWW-Authenticate": "Bearer"}
        )


class ConflictError(BaseAPIException):
    """资源冲突异常"""
    
    def __init__(self, detail: str):
        super().__init__(
            status_code=status.HTTP_409_CONFLICT,
            detail=detail,
            error_code="RESOURCE_CONFLICT"
        )


class ServiceUnavailableError(BaseAPIException):
    """服务不可用异常"""
    
    def __init__(self, service: str, detail: Optional[str] = None):
        if detail:
            message = f"服务 {service} 不可用: {detail}"
        else:
            message = f"服务 {service} 不可用"
        
        super().__init__(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=message,
            error_code="SERVICE_UNAVAILABLE"
        )


class RateLimitError(BaseAPIException):
    """请求频率限制异常"""
    
    def __init__(self, detail: str = "请求过于频繁，请稍后再试"):
        super().__init__(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=detail,
            error_code="RATE_LIMIT_EXCEEDED"
        )


class InternalServerError(BaseAPIException):
    """内部服务器错误"""
    
    def __init__(self, detail: str = "内部服务器错误"):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=detail,
            error_code="INTERNAL_SERVER_ERROR"
        )


# 业务特定异常

class UserNotFoundError(NotFoundError):
    """用户不存在异常"""
    
    def __init__(self, user_id: Optional[int] = None):
        if user_id:
            super().__init__("用户", str(user_id))
        else:
            super().__init__("用户")


class NewsNotFoundError(NotFoundError):
    """新闻不存在异常"""
    
    def __init__(self, news_id: Optional[int] = None):
        if news_id:
            super().__init__("新闻", str(news_id))
        else:
            super().__init__("新闻")


class SubscriptionNotFoundError(NotFoundError):
    """订阅不存在异常"""
    
    def __init__(self, subscription_id: Optional[int] = None):
        if subscription_id:
            super().__init__("订阅", str(subscription_id))
        else:
            super().__init__("订阅")


class DuplicateUserError(ConflictError):
    """用户重复异常"""
    
    def __init__(self, field: str, value: str):
        super().__init__(f"用户{field} '{value}' 已存在")


class InvalidCredentialsError(AuthenticationError):
    """无效凭据异常"""
    
    def __init__(self):
        super().__init__("用户名或密码错误")


class TokenExpiredError(AuthenticationError):
    """令牌过期异常"""
    
    def __init__(self):
        super().__init__("访问令牌已过期")


class InvalidTokenError(AuthenticationError):
    """无效令牌异常"""
    
    def __init__(self):
        super().__init__("无效的访问令牌")


# 异常处理工具函数

def handle_database_error(e: Exception) -> BaseAPIException:
    """处理数据库异常"""
    error_msg = str(e).lower()
    
    if "duplicate" in error_msg or "unique constraint" in error_msg:
        return ConflictError("数据重复，违反唯一性约束")
    elif "foreign key" in error_msg:
        return ValidationError("外键约束违反，相关资源不存在")
    elif "not null" in error_msg:
        return ValidationError("必填字段不能为空")
    else:
        logger.error(f"数据库异常: {str(e)}")
        return InternalServerError("数据库操作失败")


def handle_external_service_error(service: str, e: Exception) -> BaseAPIException:
    """处理外部服务异常"""
    logger.error(f"外部服务 {service} 异常: {str(e)}")
    return ServiceUnavailableError(service, str(e))
