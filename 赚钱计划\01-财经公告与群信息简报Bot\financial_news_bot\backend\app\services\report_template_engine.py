"""
简报模板引擎
提供简报模板的渲染、配置和管理功能
"""
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from jinja2 import Environment, BaseLoader, Template, select_autoescape
from sqlalchemy.orm import Session

from ..models.report import ReportTemplate, ReportType
from ..models.news import News
from ..utils.text_processing import extract_keywords, calculate_importance_score

logger = logging.getLogger(__name__)


class ReportTemplateEngine:
    """简报模板引擎类"""
    
    def __init__(self, db: Session):
        self.db = db
        self.jinja_env = Environment(
            loader=BaseLoader(),
            autoescape=select_autoescape(['html', 'xml']),
            trim_blocks=True,
            lstrip_blocks=True
        )
        
        # 注册自定义过滤器
        self._register_custom_filters()
        
        # 预定义模板配置
        self.default_templates = self._get_default_templates()
    
    def _register_custom_filters(self):
        """注册自定义Jinja2过滤器"""
        
        @self.jinja_env.filter('format_datetime')
        def format_datetime(value: datetime, format_str: str = '%Y-%m-%d %H:%M') -> str:
            """格式化日期时间"""
            if not value:
                return ""
            return value.strftime(format_str)
        
        @self.jinja_env.filter('truncate_words')
        def truncate_words(text: str, word_count: int = 50) -> str:
            """按词数截断文本"""
            if not text:
                return ""
            words = text.split()
            if len(words) <= word_count:
                return text
            return ' '.join(words[:word_count]) + '...'
        
        @self.jinja_env.filter('extract_summary')
        def extract_summary(text: str, max_length: int = 200) -> str:
            """提取文本摘要"""
            if not text:
                return ""
            if len(text) <= max_length:
                return text
            # 找到最后一个句号位置
            last_period = text.rfind('。', 0, max_length)
            if last_period > max_length * 0.7:  # 如果句号位置合理
                return text[:last_period + 1]
            return text[:max_length] + '...'
        
        @self.jinja_env.filter('format_number')
        def format_number(value: float, precision: int = 2) -> str:
            """格式化数字"""
            if value is None:
                return "0"
            if value >= 1e8:
                return f"{value/1e8:.{precision}f}亿"
            elif value >= 1e4:
                return f"{value/1e4:.{precision}f}万"
            else:
                return f"{value:.{precision}f}"
        
        @self.jinja_env.filter('highlight_keywords')
        def highlight_keywords(text: str, keywords: List[str]) -> str:
            """高亮关键词"""
            if not text or not keywords:
                return text
            
            result = text
            for keyword in keywords:
                result = result.replace(
                    keyword, 
                    f'<span class="highlight">{keyword}</span>'
                )
            return result
    
    def _get_default_templates(self) -> Dict[ReportType, Dict[str, Any]]:
        """获取默认模板配置"""
        return {
            ReportType.MORNING: {
                "name": "标准晨报模板",
                "description": "包含市场要闻、公司公告等标准章节的晨报模板",
                "template_config": {
                    "max_news_per_section": 5,
                    "total_word_limit": 2000,
                    "include_images": True,
                    "time_range_hours": 16
                },
                "sections": [
                    {
                        "name": "market_overview",
                        "title": "市场概览",
                        "content_type": "market_summary",
                        "max_items": 1,
                        "priority": "high",
                        "template": """
                        <div class="section market-overview">
                            <h2>{{ section.title }}</h2>
                            <div class="market-summary">
                                <p>{{ market_summary }}</p>
                            </div>
                        </div>
                        """
                    },
                    {
                        "name": "important_news",
                        "title": "重要资讯",
                        "content_type": "important_news",
                        "max_items": 5,
                        "priority": "high",
                        "template": """
                        <div class="section important-news">
                            <h2>{{ section.title }}</h2>
                            <ul class="news-list">
                            {% for news in section.news %}
                                <li class="news-item">
                                    <h3>{{ news.title }}</h3>
                                    <p class="news-summary">{{ news.summary | extract_summary(150) }}</p>
                                    <div class="news-meta">
                                        <span class="source">{{ news.source }}</span>
                                        <span class="time">{{ news.published_at | format_datetime }}</span>
                                        <span class="importance">重要性: {{ news.importance_score }}/100</span>
                                    </div>
                                </li>
                            {% endfor %}
                            </ul>
                        </div>
                        """
                    },
                    {
                        "name": "company_announcements",
                        "title": "公司公告",
                        "content_type": "company_news",
                        "max_items": 3,
                        "priority": "medium",
                        "template": """
                        <div class="section company-announcements">
                            <h2>{{ section.title }}</h2>
                            <ul class="announcement-list">
                            {% for news in section.news %}
                                <li class="announcement-item">
                                    <h3>{{ news.title }}</h3>
                                    <p>{{ news.summary | extract_summary(100) }}</p>
                                    <div class="announcement-meta">
                                        <span class="company">{{ news.company_name or news.source }}</span>
                                        <span class="time">{{ news.published_at | format_datetime }}</span>
                                    </div>
                                </li>
                            {% endfor %}
                            </ul>
                        </div>
                        """
                    },
                    {
                        "name": "regulatory_updates",
                        "title": "监管动态",
                        "content_type": "regulatory_news",
                        "max_items": 2,
                        "priority": "medium",
                        "template": """
                        <div class="section regulatory-updates">
                            <h2>{{ section.title }}</h2>
                            <ul class="regulatory-list">
                            {% for news in section.news %}
                                <li class="regulatory-item">
                                    <h3>{{ news.title }}</h3>
                                    <p>{{ news.summary | extract_summary(120) }}</p>
                                    <div class="regulatory-meta">
                                        <span class="authority">{{ news.source }}</span>
                                        <span class="time">{{ news.published_at | format_datetime }}</span>
                                    </div>
                                </li>
                            {% endfor %}
                            </ul>
                        </div>
                        """
                    },
                    {
                        "name": "upcoming_events",
                        "title": "今日关注",
                        "content_type": "upcoming_events",
                        "max_items": 3,
                        "priority": "low",
                        "template": """
                        <div class="section upcoming-events">
                            <h2>{{ section.title }}</h2>
                            <ul class="events-list">
                            {% for event in section.events %}
                                <li class="event-item">
                                    <span class="event-time">{{ event.time | format_datetime('%H:%M') }}</span>
                                    <span class="event-title">{{ event.title }}</span>
                                    <span class="event-importance">{{ event.importance }}</span>
                                </li>
                            {% endfor %}
                            </ul>
                        </div>
                        """
                    }
                ],
                "main_template": """
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="utf-8">
                    <title>{{ report.title }}</title>
                    <style>
                        .report { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; }
                        .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 10px; }
                        .section { margin: 20px 0; }
                        .section h2 { color: #333; border-left: 4px solid #007acc; padding-left: 10px; }
                        .news-list, .announcement-list, .regulatory-list { list-style: none; padding: 0; }
                        .news-item, .announcement-item, .regulatory-item { margin: 15px 0; padding: 10px; border-left: 3px solid #ddd; }
                        .news-meta, .announcement-meta, .regulatory-meta { font-size: 0.9em; color: #666; margin-top: 5px; }
                        .highlight { background-color: #ffeb3b; padding: 2px 4px; }
                        .events-list { list-style: none; padding: 0; }
                        .event-item { display: flex; justify-content: space-between; padding: 5px 0; border-bottom: 1px solid #eee; }
                    </style>
                </head>
                <body>
                    <div class="report">
                        <div class="header">
                            <h1>{{ report.title }}</h1>
                            <p>{{ report.report_date | format_datetime('%Y年%m月%d日') }}</p>
                        </div>
                        
                        {% for section in sections %}
                            {{ section.rendered_content | safe }}
                        {% endfor %}
                        
                        <div class="footer">
                            <p><small>本简报由财经新闻Bot自动生成，仅供参考，不构成投资建议。</small></p>
                            <p><small>生成时间: {{ report.created_at | format_datetime }}</small></p>
                        </div>
                    </div>
                </body>
                </html>
                """
            },
            
            ReportType.EVENING: {
                "name": "标准晚报模板",
                "description": "包含市场收盘、盘后公告等标准章节的晚报模板",
                "template_config": {
                    "max_news_per_section": 4,
                    "total_word_limit": 1500,
                    "include_images": False,
                    "time_range_hours": 8
                },
                "sections": [
                    {
                        "name": "market_close",
                        "title": "市场收盘",
                        "content_type": "market_close",
                        "max_items": 1,
                        "priority": "high"
                    },
                    {
                        "name": "after_hours_news",
                        "title": "盘后要闻",
                        "content_type": "after_hours_news",
                        "max_items": 4,
                        "priority": "high"
                    },
                    {
                        "name": "earnings_preview",
                        "title": "业绩预告",
                        "content_type": "earnings_news",
                        "max_items": 3,
                        "priority": "medium"
                    },
                    {
                        "name": "tomorrow_preview",
                        "title": "明日关注",
                        "content_type": "tomorrow_events",
                        "max_items": 3,
                        "priority": "low"
                    }
                ]
            }
        }
    
    async def create_template(
        self,
        template_data: Dict[str, Any],
        user_id: int
    ) -> ReportTemplate:
        """创建新的简报模板"""
        try:
            template = ReportTemplate(
                name=template_data["name"],
                type=template_data["type"],
                description=template_data.get("description"),
                template_config=template_data["template_config"],
                sections=template_data["sections"],
                style_config=template_data.get("style_config"),
                generation_rules=template_data.get("generation_rules"),
                content_filters=template_data.get("content_filters"),
                priority_rules=template_data.get("priority_rules"),
                is_default=template_data.get("is_default", False),
                created_by=user_id
            )
            
            self.db.add(template)
            self.db.commit()
            self.db.refresh(template)
            
            logger.info(f"Created report template: {template.name} (ID: {template.id})")
            return template
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to create report template: {e}")
            raise
    
    async def get_template(self, template_id: int) -> Optional[ReportTemplate]:
        """获取模板"""
        return self.db.query(ReportTemplate).filter(
            ReportTemplate.id == template_id,
            ReportTemplate.is_active == True
        ).first()
    
    async def get_default_template(self, report_type: ReportType) -> Optional[ReportTemplate]:
        """获取默认模板"""
        return self.db.query(ReportTemplate).filter(
            ReportTemplate.type == report_type,
            ReportTemplate.is_default == True,
            ReportTemplate.is_active == True
        ).first()
    
    async def render_section(
        self,
        section_config: Dict[str, Any],
        section_data: Dict[str, Any],
        context: Dict[str, Any]
    ) -> str:
        """渲染单个章节"""
        try:
            # 获取章节模板
            section_template = section_config.get("template", "")
            if not section_template:
                # 使用默认模板
                section_template = self._get_default_section_template(
                    section_config.get("content_type", "default")
                )
            
            # 创建Jinja2模板
            template = self.jinja_env.from_string(section_template)
            
            # 准备渲染上下文
            render_context = {
                "section": section_config,
                "data": section_data,
                **context
            }
            
            # 渲染模板
            rendered_content = template.render(render_context)
            
            return rendered_content
            
        except Exception as e:
            logger.error(f"Failed to render section {section_config.get('name', 'unknown')}: {e}")
            return f"<div class='error'>章节渲染失败: {str(e)}</div>"
    
    async def render_report(
        self,
        template: ReportTemplate,
        sections_data: List[Dict[str, Any]],
        report_context: Dict[str, Any]
    ) -> str:
        """渲染完整简报"""
        try:
            # 渲染各个章节
            rendered_sections = []
            
            for i, section_config in enumerate(template.sections):
                section_data = sections_data[i] if i < len(sections_data) else {}
                
                rendered_content = await self.render_section(
                    section_config,
                    section_data,
                    report_context
                )
                
                rendered_sections.append({
                    "name": section_config["name"],
                    "title": section_config["title"],
                    "rendered_content": rendered_content
                })
            
            # 获取主模板
            main_template_str = template.template_config.get(
                "main_template",
                self.default_templates[template.type]["main_template"]
            )
            
            # 创建主模板
            main_template = self.jinja_env.from_string(main_template_str)
            
            # 渲染完整简报
            full_context = {
                "sections": rendered_sections,
                **report_context
            }
            
            rendered_report = main_template.render(full_context)
            
            return rendered_report
            
        except Exception as e:
            logger.error(f"Failed to render report: {e}")
            raise
    
    def _get_default_section_template(self, content_type: str) -> str:
        """获取默认章节模板"""
        default_templates = {
            "default": """
            <div class="section">
                <h2>{{ section.title }}</h2>
                <div class="content">{{ data.content or '暂无内容' }}</div>
            </div>
            """,
            "news_list": """
            <div class="section">
                <h2>{{ section.title }}</h2>
                <ul class="news-list">
                {% for news in data.news %}
                    <li class="news-item">
                        <h3>{{ news.title }}</h3>
                        <p>{{ news.summary | extract_summary(100) }}</p>
                        <div class="news-meta">
                            <span class="source">{{ news.source }}</span>
                            <span class="time">{{ news.published_at | format_datetime }}</span>
                        </div>
                    </li>
                {% endfor %}
                </ul>
            </div>
            """
        }
        
        return default_templates.get(content_type, default_templates["default"])
    
    async def preview_template(
        self,
        template: ReportTemplate,
        sample_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """预览模板效果"""
        try:
            # 生成预览数据
            if not sample_data:
                sample_data = await self._generate_sample_data(template)
            
            # 渲染预览
            preview_context = {
                "report": {
                    "title": f"示例{template.type.value}简报",
                    "report_date": datetime.now(),
                    "created_at": datetime.now()
                }
            }
            
            rendered_content = await self.render_report(
                template,
                sample_data["sections"],
                preview_context
            )
            
            return {
                "preview_content": rendered_content,
                "sections_preview": sample_data["sections_preview"],
                "estimated_stats": sample_data["estimated_stats"],
                "generation_notes": sample_data["generation_notes"]
            }
            
        except Exception as e:
            logger.error(f"Failed to preview template: {e}")
            raise
    
    async def _generate_sample_data(self, template: ReportTemplate) -> Dict[str, Any]:
        """生成基于真实数据的预览样本"""
        sections_data = []
        sections_preview = []
        total_words = 0
        
        # 获取真实新闻数据作为预览样本
        try:
            from sqlalchemy.orm import Session
            from ..database import SessionLocal
            from ..models.news import News

            db = SessionLocal()
            try:
                # 获取最近的真实新闻数据
                recent_hours = template.template_config.get('time_range_hours', 16)
                cutoff_time = datetime.now() - timedelta(hours=recent_hours)

                recent_news = db.query(News).filter(
                    News.created_at >= cutoff_time
                ).order_by(News.importance_score.desc()).limit(20).all()

                if not recent_news:
                    recent_news = db.query(News).order_by(
                        News.created_at.desc()
                    ).limit(10).all()
            finally:
                db.close()
        except Exception as e:
            logger.error(f"获取真实新闻数据失败: {e}")
            recent_news = []

        for section_config in template.sections:
            # 使用真实新闻数据生成预览
            if recent_news:
                section_data = {
                    "news": [
                        {
                            "title": news.title,
                            "summary": news.summary or news.content[:200] + "...",
                            "source": news.source,
                            "published_at": news.publish_time or news.created_at,
                            "importance_score": news.importance_score or 50
                        }
                        for news in recent_news[:min(section_config.get("max_items", 3), 3)]
                    ]
                }
            else:
                # 如果没有真实数据，返回空数据而不是模拟数据
                section_data = {
                    "news": [],
                    "note": "暂无相关新闻数据"
                }
            
            sections_data.append(section_data)
            
            # 计算预览信息
            section_words = sum(len(news["summary"]) for news in section_data["news"])
            total_words += section_words
            
            sections_preview.append({
                "name": section_config["name"],
                "title": section_config["title"],
                "item_count": len(section_data["news"]),
                "estimated_words": section_words
            })
        
        return {
            "sections": sections_data,
            "sections_preview": sections_preview,
            "estimated_stats": {
                "total_words": total_words,
                "read_time": max(1, total_words // 200),  # 假设每分钟200字
                "sections": len(template.sections)
            },
            "generation_notes": [
                f"预览基于最近{template.template_config.get('time_range_hours', 16)}小时的真实新闻数据",
                f"共使用{len(recent_news)}条新闻数据" if recent_news else "暂无可用的新闻数据",
                "实际简报将包含更完整的数据分析和格式化"
            ]
        }
