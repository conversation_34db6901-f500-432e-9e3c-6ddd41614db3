"""
规则管理API路由
提供推送规则、过滤规则等管理功能
"""
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
from datetime import datetime

from ..database import get_db
from ..dependencies.auth import get_current_active_user
from ..dependencies.permissions import require_admin, require_advanced_subscription
from ..models.user import User

router = APIRouter(prefix="/rules", tags=["规则管理"])


@router.get("/", response_model=List[Dict[str, Any]])
async def get_rules(
    rule_type: Optional[str] = Query(None, description="规则类型过滤"),
    active_only: bool = Query(True, description="只显示启用的规则"),
    current_user: User = Depends(require_admin()),
    db: Session = Depends(get_db)
):
    """
    获取规则列表（管理员权限）
    """
    try:
        # 从数据库查询真实的规则数据
        from ..services.rule_engine import rule_engine

        # 获取所有规则
        rules_data = await rule_engine.get_all_rules(db)

        # 转换为API响应格式
        rules = []
        for rule in rules_data:
            rules.append({
                "id": rule.id,
                "name": rule.name,
                "type": rule.rule_type,
                "description": rule.description,
                "conditions": rule.conditions,
                "is_active": rule.is_active,
                "created_at": rule.created_at.isoformat() if rule.created_at else None,
                "updated_at": rule.updated_at.isoformat() if rule.updated_at else None,
                "execution_count": rule.execution_count or 0,
                "last_executed": rule.last_executed.isoformat() if rule.last_executed else None
            })
        
        # 应用过滤
        if rule_type:
            rules = [r for r in rules if r["type"] == rule_type]
        
        if active_only:
            rules = [r for r in rules if r["is_active"]]
        
        return rules
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取规则列表失败: {str(e)}"
        )


@router.post("/", response_model=Dict[str, Any])
async def create_rule(
    rule_data: Dict[str, Any],
    current_user: User = Depends(require_admin()),
    db: Session = Depends(get_db)
):
    """
    创建新规则（管理员权限）
    """
    try:
        # 使用真实的规则引擎创建规则
        from ..services.rule_engine import rule_engine
        from ..schemas.rule import RuleCreate

        # 验证规则数据
        if not rule_data.get("name"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="规则名称不能为空"
            )

        if not rule_data.get("conditions"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="规则条件不能为空"
            )

        # 创建规则对象
        rule_create = RuleCreate(
            name=rule_data["name"],
            rule_type=rule_data.get("type", "custom"),
            description=rule_data.get("description", ""),
            conditions=rule_data["conditions"],
            is_active=rule_data.get("is_active", True)
        )

        # 在数据库中创建规则
        created_rule = await rule_engine.create_rule(db, rule_create, current_user.id)

        return {
            "success": True,
            "message": "规则创建成功",
            "rule": {
                "id": created_rule.id,
                "name": created_rule.name,
                "type": created_rule.rule_type,
                "description": created_rule.description,
                "conditions": created_rule.conditions,
                "is_active": created_rule.is_active,
                "created_at": created_rule.created_at.isoformat(),
                "created_by": created_rule.created_by
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建规则失败: {str(e)}"
        )


@router.get("/{rule_id}", response_model=Dict[str, Any])
async def get_rule(
    rule_id: int,
    current_user: User = Depends(require_admin()),
    db: Session = Depends(get_db)
):
    """
    获取规则详情（管理员权限）
    """
    try:
        # 从数据库查询真实的规则详情
        from ..services.rule_engine import rule_engine

        rule = await rule_engine.get_rule_by_id(db, rule_id)

        if not rule:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"规则 {rule_id} 不存在"
            )

        # 获取规则执行统计
        execution_stats = await rule_engine.get_rule_execution_stats(db, rule_id)

        return {
            "id": rule.id,
            "name": rule.name,
            "type": rule.rule_type,
            "description": rule.description,
            "conditions": rule.conditions,
            "is_active": rule.is_active,
            "execution_count": rule.execution_count or 0,
            "last_executed": rule.last_executed.isoformat() if rule.last_executed else None,
            "success_count": execution_stats.get("success_count", 0),
            "failure_count": execution_stats.get("failure_count", 0),
            "avg_execution_time": execution_stats.get("avg_execution_time", 0.0),
            "created_at": rule.created_at.isoformat() if rule.created_at else None,
            "updated_at": rule.updated_at.isoformat() if rule.updated_at else None,
            "created_by": rule.created_by
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取规则详情失败: {str(e)}"
        )


@router.put("/{rule_id}", response_model=Dict[str, Any])
async def update_rule(
    rule_id: int,
    rule_data: Dict[str, Any],
    current_user: User = Depends(require_admin()),
    db: Session = Depends(get_db)
):
    """
    更新规则（管理员权限）
    """
    try:
        # 使用真实的规则引擎更新规则
        from ..services.rule_engine import rule_engine
        from ..schemas.rule import RuleUpdate

        # 检查规则是否存在
        existing_rule = await rule_engine.get_rule_by_id(db, rule_id)
        if not existing_rule:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"规则 {rule_id} 不存在"
            )

        # 创建更新对象
        rule_update = RuleUpdate(
            name=rule_data.get("name"),
            rule_type=rule_data.get("type"),
            description=rule_data.get("description"),
            conditions=rule_data.get("conditions"),
            is_active=rule_data.get("is_active")
        )

        # 在数据库中更新规则
        updated_rule = await rule_engine.update_rule(db, rule_id, rule_update)

        return {
            "success": True,
            "message": "规则更新成功",
            "rule": {
                "id": updated_rule.id,
                "name": updated_rule.name,
                "type": updated_rule.rule_type,
                "description": updated_rule.description,
                "conditions": updated_rule.conditions,
                "is_active": updated_rule.is_active,
                "updated_at": updated_rule.updated_at.isoformat(),
                "created_at": updated_rule.created_at.isoformat() if updated_rule.created_at else None
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新规则失败: {str(e)}"
        )


@router.delete("/{rule_id}", response_model=Dict[str, str])
async def delete_rule(
    rule_id: int,
    current_user: User = Depends(require_admin()),
    db: Session = Depends(get_db)
):
    """
    删除规则（管理员权限）
    """
    try:
        # 使用真实的规则引擎删除规则
        from ..services.rule_engine import rule_engine

        # 检查规则是否存在
        rule = await rule_engine.get_rule_by_id(db, rule_id)
        if not rule:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"规则 {rule_id} 不存在"
            )

        # 删除规则
        success = await rule_engine.delete_rule(db, rule_id)

        if success:
            return {
                "success": True,
                "message": f"规则 {rule_id} ({rule.name}) 已删除"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="删除规则失败"
            )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除规则失败: {str(e)}"
        )


@router.post("/{rule_id}/test", response_model=Dict[str, Any])
async def test_rule(
    rule_id: int,
    test_data: Optional[Dict[str, Any]] = None,
    current_user: User = Depends(require_admin()),
    db: Session = Depends(get_db)
):
    """
    测试规则（管理员权限）
    """
    try:
        # 使用真实的规则引擎测试规则
        from ..services.rule_engine import rule_engine
        import time

        # 检查规则是否存在
        rule = await rule_engine.get_rule_by_id(db, rule_id)
        if not rule:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"规则 {rule_id} 不存在"
            )

        # 记录测试开始时间
        start_time = time.time()

        # 执行真实的规则测试
        test_results = await rule_engine.test_rule(db, rule_id, test_data)

        # 计算执行时间
        execution_time = time.time() - start_time

        return {
            "success": True,
            "message": "规则测试完成",
            "test_result": {
                "rule_id": rule_id,
                "rule_name": rule.name,
                "test_passed": test_results["success"],
                "match_count": test_results["match_count"],
                "execution_time": round(execution_time, 3),
                "test_data": test_data or {},
                "results": test_results["results"],
                "errors": test_results.get("errors", []),
                "performance_metrics": test_results.get("performance_metrics", {})
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"规则测试失败: {str(e)}"
        )


@router.get("/{rule_id}/statistics", response_model=Dict[str, Any])
async def get_rule_statistics(
    rule_id: int,
    days: int = Query(7, ge=1, le=90, description="统计天数"),
    current_user: User = Depends(require_admin()),
    db: Session = Depends(get_db)
):
    """
    获取规则统计信息（管理员权限）
    """
    try:
        # 使用真实的规则引擎获取统计信息
        from ..services.rule_engine import rule_engine
        from datetime import datetime, timedelta

        # 检查规则是否存在
        rule = await rule_engine.get_rule_by_id(db, rule_id)
        if not rule:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"规则 {rule_id} 不存在"
            )

        # 获取指定时间段的统计数据
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        statistics = await rule_engine.get_rule_statistics(
            db, rule_id, start_date, end_date
        )

        return {
            "rule_id": rule_id,
            "rule_name": rule.name,
            "period_days": days,
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "total_triggers": statistics["total_triggers"],
            "successful_triggers": statistics["successful_triggers"],
            "failed_triggers": statistics["failed_triggers"],
            "success_rate": statistics["success_rate"],
            "avg_execution_time": statistics["avg_execution_time"],
            "daily_stats": statistics["daily_stats"],
            "performance_trends": statistics.get("performance_trends", []),
            "error_analysis": statistics.get("error_analysis", {})
        }
        
        return statistics
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取规则统计失败: {str(e)}"
        )
