#!/usr/bin/env python3
"""
第6周集成测试
验证第6周开发的内容与第1-5周的所有内容完全融合
"""
import pytest
import asyncio
import requests
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any

class Week6IntegrationTester:
    """第6周集成测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.test_results = []
        self.auth_token = None
        
    async def run_all_integration_tests(self) -> Dict[str, Any]:
        """运行所有集成测试"""
        print("🚀 开始第6周集成测试...")
        
        results = {
            "timestamp": datetime.now().isoformat(),
            "test_suite": "第6周集成测试",
            "tests": []
        }
        
        # 1. 测试基础服务可用性
        results["tests"].append(await self.test_basic_services())
        
        # 2. 测试用户认证与第6周功能的集成
        results["tests"].append(await self.test_auth_integration())
        
        # 3. 测试新闻系统与安全功能的集成
        results["tests"].append(await self.test_news_security_integration())
        
        # 4. 测试推送系统与分析功能的集成
        results["tests"].append(await self.test_push_analytics_integration())
        
        # 5. 测试性能监控与现有系统的集成
        results["tests"].append(await self.test_performance_monitoring_integration())
        
        # 6. 测试合规系统与业务逻辑的集成
        results["tests"].append(await self.test_compliance_integration())
        
        # 7. 测试数据流完整性
        results["tests"].append(await self.test_data_flow_integrity())
        
        # 计算总体结果
        total_tests = sum(len(test_group.get("tests", [])) for test_group in results["tests"])
        passed_tests = sum(
            len([t for t in test_group.get("tests", []) if t.get("status") == "PASS"])
            for test_group in results["tests"]
        )
        
        results["summary"] = {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "success_rate": (passed_tests / total_tests * 100) if total_tests > 0 else 0
        }
        
        return results
    
    async def test_basic_services(self) -> Dict[str, Any]:
        """测试基础服务可用性"""
        print("📋 测试基础服务可用性...")
        
        results = {
            "test_name": "基础服务可用性测试",
            "timestamp": datetime.now().isoformat(),
            "tests": []
        }
        
        # 测试核心API端点
        core_endpoints = [
            "/health",
            "/api/v1/auth/health",
            "/api/v1/news/health",
            "/api/v1/push/health",
            "/api/v1/analytics/health"
        ]
        
        for endpoint in core_endpoints:
            try:
                response = requests.get(f"{self.base_url}{endpoint}", timeout=10)
                
                results["tests"].append({
                    "endpoint": endpoint,
                    "status": "PASS" if response.status_code == 200 else "FAIL",
                    "status_code": response.status_code,
                    "response_time": response.elapsed.total_seconds()
                })
                
            except Exception as e:
                results["tests"].append({
                    "endpoint": endpoint,
                    "status": "ERROR",
                    "error": str(e)
                })
        
        return results
    
    async def test_auth_integration(self) -> Dict[str, Any]:
        """测试用户认证与第6周功能的集成"""
        print("🔐 测试认证系统集成...")
        
        results = {
            "test_name": "认证系统集成测试",
            "timestamp": datetime.now().isoformat(),
            "tests": []
        }
        
        # 测试用户注册
        test_user = {
            "email": "<EMAIL>",
            "password": "TestPassword123!",
            "username": "integration_tester"
        }
        
        try:
            # 注册用户
            register_response = requests.post(
                f"{self.base_url}/api/v1/auth/register",
                json=test_user,
                timeout=10
            )
            
            results["tests"].append({
                "test": "用户注册",
                "status": "PASS" if register_response.status_code in [200, 201, 409] else "FAIL",
                "status_code": register_response.status_code
            })
            
            # 登录获取token
            login_response = requests.post(
                f"{self.base_url}/api/v1/auth/login",
                json={
                    "email": test_user["email"],
                    "password": test_user["password"]
                },
                timeout=10
            )
            
            if login_response.status_code == 200:
                token_data = login_response.json()
                self.auth_token = token_data.get("access_token")
                
                results["tests"].append({
                    "test": "用户登录",
                    "status": "PASS",
                    "has_token": bool(self.auth_token)
                })
            else:
                results["tests"].append({
                    "test": "用户登录",
                    "status": "FAIL",
                    "status_code": login_response.status_code
                })
            
            # 测试认证保护的端点
            if self.auth_token:
                protected_endpoints = [
                    "/api/v1/auth/me",
                    "/api/v1/analytics/push/user-preferences",
                    "/api/v1/news/subscriptions"
                ]
                
                headers = {"Authorization": f"Bearer {self.auth_token}"}
                
                for endpoint in protected_endpoints:
                    try:
                        response = requests.get(
                            f"{self.base_url}{endpoint}",
                            headers=headers,
                            timeout=10
                        )
                        
                        results["tests"].append({
                            "test": f"认证访问 {endpoint}",
                            "status": "PASS" if response.status_code in [200, 404] else "FAIL",
                            "status_code": response.status_code
                        })
                        
                    except Exception as e:
                        results["tests"].append({
                            "test": f"认证访问 {endpoint}",
                            "status": "ERROR",
                            "error": str(e)
                        })
            
        except Exception as e:
            results["tests"].append({
                "test": "认证流程",
                "status": "ERROR",
                "error": str(e)
            })
        
        return results
    
    async def test_news_security_integration(self) -> Dict[str, Any]:
        """测试新闻系统与安全功能的集成"""
        print("📰 测试新闻安全集成...")
        
        results = {
            "test_name": "新闻安全集成测试",
            "timestamp": datetime.now().isoformat(),
            "tests": []
        }
        
        # 测试内容过滤
        test_contents = [
            "正常的财经新闻内容",
            "<script>alert('xss')</script>恶意内容",
            "包含敏感词的内容"
        ]
        
        for content in test_contents:
            try:
                # 模拟内容提交
                response = requests.post(
                    f"{self.base_url}/api/v1/news/content/validate",
                    json={"content": content},
                    headers={"Authorization": f"Bearer {self.auth_token}"} if self.auth_token else {},
                    timeout=10
                )
                
                results["tests"].append({
                    "test": f"内容过滤 - {content[:20]}...",
                    "status": "PASS" if response.status_code in [200, 400] else "FAIL",
                    "status_code": response.status_code
                })
                
            except Exception as e:
                results["tests"].append({
                    "test": f"内容过滤 - {content[:20]}...",
                    "status": "ERROR",
                    "error": str(e)
                })
        
        # 测试新闻访问权限
        try:
            response = requests.get(
                f"{self.base_url}/api/v1/news/",
                timeout=10
            )
            
            results["tests"].append({
                "test": "新闻列表访问",
                "status": "PASS" if response.status_code == 200 else "FAIL",
                "status_code": response.status_code
            })
            
        except Exception as e:
            results["tests"].append({
                "test": "新闻列表访问",
                "status": "ERROR",
                "error": str(e)
            })
        
        return results
    
    async def test_push_analytics_integration(self) -> Dict[str, Any]:
        """测试推送系统与分析功能的集成"""
        print("📱 测试推送分析集成...")
        
        results = {
            "test_name": "推送分析集成测试",
            "timestamp": datetime.now().isoformat(),
            "tests": []
        }
        
        if not self.auth_token:
            results["tests"].append({
                "test": "推送分析集成",
                "status": "SKIP",
                "reason": "需要认证token"
            })
            return results
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        
        # 测试推送事件记录
        test_event = {
            "session_id": "test_session_123",
            "device_id": "test_device_456",
            "event_type": "notification_sent",
            "event_data": {
                "notification_id": "test_notif_789",
                "title": "测试通知"
            },
            "device_type": "desktop"
        }
        
        try:
            response = requests.post(
                f"{self.base_url}/api/v1/analytics/push/events",
                json=test_event,
                headers=headers,
                timeout=10
            )
            
            results["tests"].append({
                "test": "推送事件记录",
                "status": "PASS" if response.status_code in [200, 201] else "FAIL",
                "status_code": response.status_code
            })
            
        except Exception as e:
            results["tests"].append({
                "test": "推送事件记录",
                "status": "ERROR",
                "error": str(e)
            })
        
        # 测试推送分析报告
        try:
            response = requests.get(
                f"{self.base_url}/api/v1/analytics/push/report",
                headers=headers,
                timeout=10
            )
            
            results["tests"].append({
                "test": "推送分析报告",
                "status": "PASS" if response.status_code == 200 else "FAIL",
                "status_code": response.status_code
            })
            
        except Exception as e:
            results["tests"].append({
                "test": "推送分析报告",
                "status": "ERROR",
                "error": str(e)
            })
        
        # 测试A/B测试配置
        try:
            response = requests.get(
                f"{self.base_url}/api/v1/analytics/push/ab-test-config",
                headers=headers,
                timeout=10
            )
            
            results["tests"].append({
                "test": "A/B测试配置",
                "status": "PASS" if response.status_code == 200 else "FAIL",
                "status_code": response.status_code
            })
            
        except Exception as e:
            results["tests"].append({
                "test": "A/B测试配置",
                "status": "ERROR",
                "error": str(e)
            })
        
        return results
    
    async def test_performance_monitoring_integration(self) -> Dict[str, Any]:
        """测试性能监控与现有系统的集成"""
        print("⚡ 测试性能监控集成...")
        
        results = {
            "test_name": "性能监控集成测试",
            "timestamp": datetime.now().isoformat(),
            "tests": []
        }
        
        # 测试性能指标收集
        try:
            response = requests.get(
                f"{self.base_url}/api/v1/monitoring/metrics",
                timeout=10
            )
            
            results["tests"].append({
                "test": "性能指标收集",
                "status": "PASS" if response.status_code in [200, 404] else "FAIL",
                "status_code": response.status_code
            })
            
        except Exception as e:
            results["tests"].append({
                "test": "性能指标收集",
                "status": "ERROR",
                "error": str(e)
            })
        
        # 测试系统健康检查
        try:
            response = requests.get(
                f"{self.base_url}/health",
                timeout=10
            )
            
            if response.status_code == 200:
                health_data = response.json()
                
                results["tests"].append({
                    "test": "系统健康检查",
                    "status": "PASS",
                    "health_status": health_data.get("status", "unknown")
                })
            else:
                results["tests"].append({
                    "test": "系统健康检查",
                    "status": "FAIL",
                    "status_code": response.status_code
                })
                
        except Exception as e:
            results["tests"].append({
                "test": "系统健康检查",
                "status": "ERROR",
                "error": str(e)
            })
        
        return results
    
    async def test_compliance_integration(self) -> Dict[str, Any]:
        """测试合规系统与业务逻辑的集成"""
        print("⚖️ 测试合规系统集成...")
        
        results = {
            "test_name": "合规系统集成测试",
            "timestamp": datetime.now().isoformat(),
            "tests": []
        }
        
        # 测试数据保护API
        try:
            response = requests.get(
                f"{self.base_url}/api/v1/compliance/data-protection/status",
                timeout=10
            )
            
            results["tests"].append({
                "test": "数据保护状态",
                "status": "PASS" if response.status_code in [200, 404] else "FAIL",
                "status_code": response.status_code
            })
            
        except Exception as e:
            results["tests"].append({
                "test": "数据保护状态",
                "status": "ERROR",
                "error": str(e)
            })
        
        # 测试合规监控
        try:
            response = requests.get(
                f"{self.base_url}/api/v1/compliance/monitoring/status",
                timeout=10
            )
            
            results["tests"].append({
                "test": "合规监控状态",
                "status": "PASS" if response.status_code in [200, 404] else "FAIL",
                "status_code": response.status_code
            })
            
        except Exception as e:
            results["tests"].append({
                "test": "合规监控状态",
                "status": "ERROR",
                "error": str(e)
            })
        
        return results
    
    async def test_data_flow_integrity(self) -> Dict[str, Any]:
        """测试数据流完整性"""
        print("🔄 测试数据流完整性...")
        
        results = {
            "test_name": "数据流完整性测试",
            "timestamp": datetime.now().isoformat(),
            "tests": []
        }
        
        # 测试完整的用户操作流程
        if self.auth_token:
            headers = {"Authorization": f"Bearer {self.auth_token}"}
            
            # 1. 获取新闻列表
            try:
                news_response = requests.get(
                    f"{self.base_url}/api/v1/news/",
                    headers=headers,
                    timeout=10
                )
                
                results["tests"].append({
                    "test": "新闻数据获取",
                    "status": "PASS" if news_response.status_code == 200 else "FAIL",
                    "status_code": news_response.status_code
                })
                
                # 2. 如果有新闻，测试详情获取
                if news_response.status_code == 200:
                    news_data = news_response.json()
                    if news_data.get("items"):
                        first_news = news_data["items"][0]
                        news_id = first_news.get("id")
                        
                        if news_id:
                            detail_response = requests.get(
                                f"{self.base_url}/api/v1/news/{news_id}",
                                headers=headers,
                                timeout=10
                            )
                            
                            results["tests"].append({
                                "test": "新闻详情获取",
                                "status": "PASS" if detail_response.status_code == 200 else "FAIL",
                                "status_code": detail_response.status_code
                            })
                
            except Exception as e:
                results["tests"].append({
                    "test": "新闻数据流",
                    "status": "ERROR",
                    "error": str(e)
                })
        
        # 测试数据库连接
        try:
            response = requests.get(
                f"{self.base_url}/api/v1/health/database",
                timeout=10
            )
            
            results["tests"].append({
                "test": "数据库连接",
                "status": "PASS" if response.status_code in [200, 404] else "FAIL",
                "status_code": response.status_code
            })
            
        except Exception as e:
            results["tests"].append({
                "test": "数据库连接",
                "status": "ERROR",
                "error": str(e)
            })
        
        return results
    
    def save_results(self, results: Dict[str, Any], output_file: str):
        """保存测试结果"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            print(f"✅ 集成测试结果已保存: {output_file}")
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")


async def main():
    """主函数"""
    tester = Week6IntegrationTester()
    
    try:
        results = await tester.run_all_integration_tests()
        tester.save_results(results, "week6_integration_test_results.json")
        
        # 输出摘要
        summary = results["summary"]
        print(f"\n🎯 第6周集成测试摘要")
        print(f"总测试数: {summary['total_tests']}")
        print(f"通过测试: {summary['passed_tests']}")
        print(f"成功率: {summary['success_rate']:.1f}%")
        
        # 显示失败的测试
        failed_tests = []
        for test_group in results["tests"]:
            for test in test_group.get("tests", []):
                if test.get("status") == "FAIL":
                    test_name = f"{test_group['test_name']} - {test.get('test', test.get('endpoint', 'Unknown'))}"
                    failed_tests.append(test_name)
        
        if failed_tests:
            print(f"\n❌ 失败的测试:")
            for i, test in enumerate(failed_tests, 1):
                print(f"{i}. {test}")
        else:
            print(f"\n✅ 所有测试通过!")
        
        return summary['success_rate'] >= 80  # 80%以上通过率视为成功
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
