// 可用性测试报告生成器
import { performanceMonitor } from './performanceMonitor';

interface UsabilityTestData {
  testId: string;
  testName: string;
  testDate: Date;
  participants: ParticipantData[];
  tasks: TaskData[];
  metrics: UsabilityMetrics;
  feedback: FeedbackData[];
  recommendations: Recommendation[];
}

interface ParticipantData {
  id: string;
  demographics: {
    age: number;
    gender: string;
    experience: 'beginner' | 'intermediate' | 'expert';
    device: string;
  };
  taskResults: TaskResult[];
  overallSatisfaction: number;
  comments: string[];
}

interface TaskData {
  id: string;
  name: string;
  description: string;
  expectedTime: number;
  successCriteria: string[];
  difficulty: 'easy' | 'medium' | 'hard';
}

interface TaskResult {
  taskId: string;
  participantId: string;
  completed: boolean;
  timeSpent: number;
  errors: number;
  assistanceNeeded: boolean;
  satisfactionScore: number;
  comments: string;
  clickPath: string[];
}

interface UsabilityMetrics {
  taskCompletionRate: number;
  averageTaskTime: number;
  errorRate: number;
  userSatisfactionScore: number;
  systemUsabilityScale: number;
  netPromoterScore: number;
  learnabilityScore: number;
  efficiencyScore: number;
  memorabilityScore: number;
}

interface FeedbackData {
  participantId: string;
  type: 'positive' | 'negative' | 'suggestion';
  category: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  frequency: number;
}

interface Recommendation {
  id: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  category: string;
  issue: string;
  recommendation: string;
  impact: string;
  effort: 'low' | 'medium' | 'high';
  affectedTasks: string[];
}

class UsabilityReportGenerator {
  private testData: UsabilityTestData;

  constructor(testData: UsabilityTestData) {
    this.testData = testData;
  }

  // 生成完整的可用性测试报告
  generateReport(): string {
    const sections = [
      this.generateExecutiveSummary(),
      this.generateMethodology(),
      this.generateParticipantProfile(),
      this.generateTaskAnalysis(),
      this.generateMetricsAnalysis(),
      this.generateFeedbackAnalysis(),
      this.generateRecommendations(),
      this.generateAppendix(),
    ];

    return sections.join('\n\n');
  }

  private generateExecutiveSummary(): string {
    const metrics = this.testData.metrics;
    const criticalIssues = this.testData.recommendations.filter(r => r.priority === 'critical').length;
    const highIssues = this.testData.recommendations.filter(r => r.priority === 'high').length;

    return `
# 可用性测试报告

## 执行摘要

### 测试概览
- **测试名称**: ${this.testData.testName}
- **测试日期**: ${this.testData.testDate.toLocaleDateString()}
- **参与者数量**: ${this.testData.participants.length}
- **测试任务数**: ${this.testData.tasks.length}

### 关键发现
- **任务完成率**: ${metrics.taskCompletionRate.toFixed(1)}%
- **平均任务时间**: ${(metrics.averageTaskTime / 1000).toFixed(1)}秒
- **用户满意度**: ${metrics.userSatisfactionScore.toFixed(1)}/10
- **系统可用性量表(SUS)**: ${metrics.systemUsabilityScale.toFixed(1)}/100

### 问题严重程度
- **严重问题**: ${criticalIssues}个
- **高优先级问题**: ${highIssues}个
- **总建议数**: ${this.testData.recommendations.length}个

### 总体评价
${this.generateOverallAssessment()}
    `;
  }

  private generateOverallAssessment(): string {
    const metrics = this.testData.metrics;
    
    if (metrics.systemUsabilityScale >= 80) {
      return '系统整体可用性表现优秀，用户能够高效完成大部分任务。';
    } else if (metrics.systemUsabilityScale >= 68) {
      return '系统可用性处于可接受水平，但仍有改进空间。';
    } else {
      return '系统可用性存在显著问题，需要重点改进。';
    }
  }

  private generateMethodology(): string {
    return `
## 测试方法

### 测试设计
- **测试类型**: 调节性可用性测试
- **测试环境**: 远程/实验室环境
- **测试设备**: 桌面端、移动端
- **数据收集**: 任务完成情况、时间记录、错误统计、满意度评分

### 参与者招募
- **招募标准**: 目标用户群体
- **样本大小**: ${this.testData.participants.length}名参与者
- **激励机制**: 测试奖励

### 测试流程
1. 前期问卷调查
2. 任务执行观察
3. 后期访谈
4. 满意度评估
    `;
  }

  private generateParticipantProfile(): string {
    const participants = this.testData.participants;
    const avgAge = participants.reduce((sum, p) => sum + p.demographics.age, 0) / participants.length;
    const experienceDistribution = this.getDistribution(participants, p => p.demographics.experience);
    const deviceDistribution = this.getDistribution(participants, p => p.demographics.device);

    return `
## 参与者概况

### 基本信息
- **总人数**: ${participants.length}
- **平均年龄**: ${avgAge.toFixed(1)}岁
- **年龄范围**: ${Math.min(...participants.map(p => p.demographics.age))} - ${Math.max(...participants.map(p => p.demographics.age))}岁

### 经验水平分布
${Object.entries(experienceDistribution).map(([level, count]) => 
  `- ${level}: ${count}人 (${((count / participants.length) * 100).toFixed(1)}%)`
).join('\n')}

### 设备使用分布
${Object.entries(deviceDistribution).map(([device, count]) => 
  `- ${device}: ${count}人 (${((count / participants.length) * 100).toFixed(1)}%)`
).join('\n')}
    `;
  }

  private generateTaskAnalysis(): string {
    const taskAnalysis = this.testData.tasks.map(task => {
      const results = this.getTaskResults(task.id);
      const completionRate = (results.filter(r => r.completed).length / results.length) * 100;
      const avgTime = results.reduce((sum, r) => sum + r.timeSpent, 0) / results.length;
      const avgErrors = results.reduce((sum, r) => sum + r.errors, 0) / results.length;
      const avgSatisfaction = results.reduce((sum, r) => sum + r.satisfactionScore, 0) / results.length;

      return `
### ${task.name}
- **描述**: ${task.description}
- **完成率**: ${completionRate.toFixed(1)}%
- **平均时间**: ${(avgTime / 1000).toFixed(1)}秒 (预期: ${(task.expectedTime / 1000).toFixed(1)}秒)
- **平均错误数**: ${avgErrors.toFixed(1)}
- **满意度**: ${avgSatisfaction.toFixed(1)}/10
- **难度**: ${task.difficulty}

#### 主要问题
${this.getTaskIssues(task.id)}
      `;
    });

    return `
## 任务分析

${taskAnalysis.join('\n')}
    `;
  }

  private generateMetricsAnalysis(): string {
    const metrics = this.testData.metrics;

    return `
## 指标分析

### 核心可用性指标
| 指标 | 数值 | 基准 | 评价 |
|------|------|------|------|
| 任务完成率 | ${metrics.taskCompletionRate.toFixed(1)}% | >90% | ${metrics.taskCompletionRate >= 90 ? '✅ 优秀' : metrics.taskCompletionRate >= 80 ? '⚠️ 良好' : '❌ 需改进'} |
| 平均任务时间 | ${(metrics.averageTaskTime / 1000).toFixed(1)}秒 | <30秒 | ${metrics.averageTaskTime < 30000 ? '✅ 优秀' : metrics.averageTaskTime < 60000 ? '⚠️ 良好' : '❌ 需改进'} |
| 错误率 | ${metrics.errorRate.toFixed(1)}% | <5% | ${metrics.errorRate < 5 ? '✅ 优秀' : metrics.errorRate < 10 ? '⚠️ 良好' : '❌ 需改进'} |
| 用户满意度 | ${metrics.userSatisfactionScore.toFixed(1)}/10 | >8.0 | ${metrics.userSatisfactionScore >= 8 ? '✅ 优秀' : metrics.userSatisfactionScore >= 7 ? '⚠️ 良好' : '❌ 需改进'} |

### 系统可用性量表 (SUS)
- **得分**: ${metrics.systemUsabilityScale.toFixed(1)}/100
- **等级**: ${this.getSUSGrade(metrics.systemUsabilityScale)}
- **百分位**: ${this.getSUSPercentile(metrics.systemUsabilityScale)}

### 净推荐值 (NPS)
- **得分**: ${metrics.netPromoterScore.toFixed(1)}
- **分类**: ${this.getNPSCategory(metrics.netPromoterScore)}
    `;
  }

  private generateFeedbackAnalysis(): string {
    const feedback = this.testData.feedback;
    const categoryDistribution = this.getDistribution(feedback, f => f.category);
    const severityDistribution = this.getDistribution(feedback, f => f.severity);

    return `
## 用户反馈分析

### 反馈分类分布
${Object.entries(categoryDistribution).map(([category, count]) => 
  `- ${category}: ${count}条`
).join('\n')}

### 问题严重程度分布
${Object.entries(severityDistribution).map(([severity, count]) => 
  `- ${severity}: ${count}条`
).join('\n')}

### 高频问题
${this.getTopIssues()}
    `;
  }

  private generateRecommendations(): string {
    const recommendations = this.testData.recommendations.sort((a, b) => {
      const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });

    return `
## 改进建议

${recommendations.map((rec, index) => `
### ${index + 1}. ${rec.issue}
- **优先级**: ${rec.priority}
- **类别**: ${rec.category}
- **建议**: ${rec.recommendation}
- **预期影响**: ${rec.impact}
- **实施难度**: ${rec.effort}
- **影响任务**: ${rec.affectedTasks.join(', ')}
`).join('\n')}
    `;
  }

  private generateAppendix(): string {
    return `
## 附录

### A. 测试任务详情
${this.testData.tasks.map(task => `
#### ${task.name}
- **描述**: ${task.description}
- **成功标准**: ${task.successCriteria.join(', ')}
- **预期时间**: ${(task.expectedTime / 1000).toFixed(1)}秒
`).join('\n')}

### B. 原始数据统计
- **总测试时长**: ${this.calculateTotalTestTime()}
- **数据收集时间**: ${this.testData.testDate.toISOString()}
- **测试环境**: Web浏览器
- **记录方式**: 自动化 + 人工观察

### C. 统计显著性
${this.generateStatisticalAnalysis()}
    `;
  }

  // 辅助方法
  private getDistribution<T>(items: T[], keyFn: (item: T) => string): Record<string, number> {
    return items.reduce((acc, item) => {
      const key = keyFn(item);
      acc[key] = (acc[key] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }

  private getTaskResults(taskId: string): TaskResult[] {
    return this.testData.participants.flatMap(p => 
      p.taskResults.filter(r => r.taskId === taskId)
    );
  }

  private getTaskIssues(taskId: string): string {
    const issues = this.testData.recommendations.filter(r => 
      r.affectedTasks.includes(taskId)
    );
    
    if (issues.length === 0) {
      return '无重大问题';
    }
    
    return issues.map(issue => `- ${issue.issue}`).join('\n');
  }

  private getSUSGrade(score: number): string {
    if (score >= 80) return 'A (优秀)';
    if (score >= 68) return 'B (良好)';
    if (score >= 51) return 'C (可接受)';
    if (score >= 39) return 'D (较差)';
    return 'F (失败)';
  }

  private getSUSPercentile(score: number): string {
    // 基于SUS基准数据的百分位估算
    if (score >= 85) return '前10%';
    if (score >= 80) return '前25%';
    if (score >= 68) return '前50%';
    if (score >= 51) return '前75%';
    return '后25%';
  }

  private getNPSCategory(score: number): string {
    if (score >= 50) return '优秀';
    if (score >= 0) return '良好';
    return '需改进';
  }

  private getTopIssues(): string {
    const issueFrequency = this.testData.feedback.reduce((acc, feedback) => {
      acc[feedback.description] = (acc[feedback.description] || 0) + feedback.frequency;
      return acc;
    }, {} as Record<string, number>);

    const topIssues = Object.entries(issueFrequency)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5);

    return topIssues.map(([issue, frequency]) => 
      `- ${issue} (${frequency}次提及)`
    ).join('\n');
  }

  private calculateTotalTestTime(): string {
    const totalMinutes = this.testData.participants.reduce((sum, p) => {
      return sum + p.taskResults.reduce((taskSum, r) => taskSum + r.timeSpent, 0);
    }, 0) / (1000 * 60);

    return `${totalMinutes.toFixed(1)}分钟`;
  }

  private generateStatisticalAnalysis(): string {
    // 简化的统计分析
    const sampleSize = this.testData.participants.length;
    const confidenceLevel = sampleSize >= 30 ? '95%' : '90%';
    
    return `
- **样本大小**: ${sampleSize}
- **置信水平**: ${confidenceLevel}
- **统计方法**: 描述性统计
- **显著性检验**: ${sampleSize >= 30 ? '适用' : '样本较小，结果仅供参考'}
    `;
  }
}

// 导出报告生成器
export default UsabilityReportGenerator;

// 便捷方法
export const generateUsabilityReport = (testData: UsabilityTestData): string => {
  const generator = new UsabilityReportGenerator(testData);
  return generator.generateReport();
};
