"""
pytest配置文件
提供测试夹具和配置
"""
import pytest
import asyncio
from typing import Generator, AsyncGenerator
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from app.main import app
from app.database import get_db, Base
from app.core.config import settings
from app.models.user import User
from app.models.subscription import Subscription
from app.models.news import News
from app.core.security import get_password_hash

# 测试数据库配置
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"

engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="function")
def db_session():
    """创建测试数据库会话"""
    Base.metadata.create_all(bind=engine)
    session = TestingSessionLocal()
    try:
        yield session
    finally:
        session.close()
        Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def client(db_session):
    """创建测试客户端"""
    def override_get_db():
        try:
            yield db_session
        finally:
            pass

    app.dependency_overrides[get_db] = override_get_db
    with TestClient(app) as test_client:
        yield test_client
    app.dependency_overrides.clear()


@pytest.fixture
def test_user(db_session):
    """创建测试用户"""
    user = User(
        username="testuser",
        email="<EMAIL>",
        password_hash=get_password_hash("testpassword"),
        is_active=True
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user


@pytest.fixture
def test_admin_user(db_session):
    """创建测试管理员用户"""
    admin = User(
        username="admin",
        email="<EMAIL>",
        password_hash=get_password_hash("adminpassword"),
        is_active=True,
        role="ADMIN"
    )
    db_session.add(admin)
    db_session.commit()
    db_session.refresh(admin)
    return admin


@pytest.fixture
def test_subscription(db_session, test_user):
    """创建测试订阅"""
    subscription = Subscription(
        name="测试订阅",
        keywords=["测试", "财经"],
        categories=["finance"],
        is_active=True,
        user_id=test_user.id
    )
    db_session.add(subscription)
    db_session.commit()
    db_session.refresh(subscription)
    return subscription


@pytest.fixture
def test_news(db_session):
    """创建测试新闻"""
    news = News(
        title="测试新闻标题",
        content="这是测试新闻内容",
        summary="测试新闻摘要",
        source="sse",
        source_url="https://example.com/news/1",
        category="finance"
    )
    db_session.add(news)
    db_session.commit()
    db_session.refresh(news)
    return news


@pytest.fixture
def auth_headers(client, test_user):
    """获取认证头"""
    login_data = {
        "username": test_user.username,
        "password": "testpassword"
    }
    response = client.post("/api/v1/auth/login", data=login_data)
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
def admin_auth_headers(client, test_admin_user):
    """获取管理员认证头"""
    login_data = {
        "username": test_admin_user.username,
        "password": "adminpassword"
    }
    response = client.post("/api/v1/auth/login", data=login_data)
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}


# 测试数据
@pytest.fixture
def sample_news_data():
    """示例新闻数据"""
    return {
        "title": "央行发布最新货币政策报告",
        "content": "中国人民银行今日发布了最新的货币政策执行报告...",
        "summary": "央行发布货币政策报告，强调稳健货币政策",
        "source": "sse",
        "source_url": "https://www.pbc.gov.cn/news/123456",
        "category": "finance",
        "published_at": "2024-01-15T10:00:00Z"
    }


@pytest.fixture
def sample_subscription_data():
    """示例订阅数据"""
    return {
        "name": "央行政策订阅",
        "keywords": ["央行", "货币政策", "利率"],
        "categories": ["finance"],
        "channels": ["email", "wechat"],
        "schedule": {
            "frequency": "daily",
            "time": "09:00"
        }
    }


@pytest.fixture
def sample_user_data():
    """示例用户数据"""
    return {
        "username": "newuser",
        "email": "<EMAIL>",
        "password": "newpassword123"
    }


# Mock数据
@pytest.fixture
def mock_external_api_response():
    """模拟外部API响应"""
    return {
        "status": "success",
        "data": [
            {
                "title": "Mock News Title",
                "content": "Mock news content",
                "url": "https://mock.com/news/1",
                "published_at": "2024-01-15T10:00:00Z"
            }
        ]
    }


# 异步测试支持
@pytest.fixture
async def async_client():
    """异步测试客户端"""
    from httpx import AsyncClient
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac


# 数据库事务回滚
@pytest.fixture(autouse=True)
def enable_db_access_for_all_tests(db_session):
    """为所有测试启用数据库访问"""
    pass


# 清理缓存
@pytest.fixture(autouse=True)
def clear_cache():
    """清理Redis缓存"""
    # 这里可以添加清理Redis缓存的逻辑
    yield
    # 测试后清理
