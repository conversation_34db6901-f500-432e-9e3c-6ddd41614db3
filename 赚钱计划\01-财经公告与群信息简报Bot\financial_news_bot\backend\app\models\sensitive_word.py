from sqlalchemy import Column, Integer, String, Enum, TIMESTAMP, func
from sqlalchemy.dialects.mysql import ENUM as MySQLEnum
from ..database import Base
import enum

class SensitiveWordType(str, enum.Enum):
    FORBIDDEN = "forbidden"  # 禁用词，直接过滤
    WARNING = "warning"      # 警示词，标注提醒

class SensitiveWord(Base):
    __tablename__ = "sensitive_words"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    word = Column(String(100), nullable=False, unique=True, index=True, comment="敏感词内容")
    type = Column(MySQLEnum(SensitiveWordType), nullable=False, comment="敏感词类型")
    category = Column(String(50), nullable=True, index=True, comment="敏感词分类")
    created_at = Column(TIMESTAMP, server_default=func.now(), nullable=False)
    
    def __repr__(self):
        return f"<SensitiveWord(id={self.id}, word='{self.word}', type='{self.type}', category='{self.category}')>"
