import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ThemeConfig, NotificationItem, BreadcrumbItem } from '@/types';

interface UIState {
  theme: ThemeConfig;
  sidebarCollapsed: boolean;
  loading: boolean;
  notifications: NotificationItem[];
  breadcrumbs: BreadcrumbItem[];
  pageTitle: string;
  isMobile: boolean;
  modalVisible: boolean;
  drawerVisible: boolean;
}

const initialState: UIState = {
  theme: {
    primaryColor: '#1890ff',
    darkMode: false,
    compactMode: false,
    borderRadius: 6,
  },
  sidebarCollapsed: false,
  loading: false,
  notifications: [],
  breadcrumbs: [],
  pageTitle: '财经新闻Bot',
  isMobile: false,
  modalVisible: false,
  drawerVisible: false,
};

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    setTheme: (state, action: PayloadAction<Partial<ThemeConfig>>) => {
      state.theme = { ...state.theme, ...action.payload };
      localStorage.setItem('theme', JSON.stringify(state.theme));
    },
    toggleSidebar: (state) => {
      state.sidebarCollapsed = !state.sidebarCollapsed;
      localStorage.setItem('sidebarCollapsed', JSON.stringify(state.sidebarCollapsed));
    },
    setSidebarCollapsed: (state, action: PayloadAction<boolean>) => {
      state.sidebarCollapsed = action.payload;
      localStorage.setItem('sidebarCollapsed', JSON.stringify(state.sidebarCollapsed));
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    addNotification: (state, action: PayloadAction<Omit<NotificationItem, 'id' | 'timestamp'>>) => {
      const notification: NotificationItem = {
        ...action.payload,
        id: Date.now().toString(),
        timestamp: new Date().toISOString(),
        read: false,
      };
      state.notifications.unshift(notification);
      // 限制通知数量
      if (state.notifications.length > 50) {
        state.notifications = state.notifications.slice(0, 50);
      }
    },
    markNotificationAsRead: (state, action: PayloadAction<string>) => {
      const notification = state.notifications.find(n => n.id === action.payload);
      if (notification) {
        notification.read = true;
      }
    },
    markAllNotificationsAsRead: (state) => {
      state.notifications.forEach(notification => {
        notification.read = true;
      });
    },
    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter(n => n.id !== action.payload);
    },
    clearNotifications: (state) => {
      state.notifications = [];
    },
    setBreadcrumbs: (state, action: PayloadAction<BreadcrumbItem[]>) => {
      state.breadcrumbs = action.payload;
    },
    setPageTitle: (state, action: PayloadAction<string>) => {
      state.pageTitle = action.payload;
      document.title = `${action.payload} - 财经新闻Bot`;
    },
    setIsMobile: (state, action: PayloadAction<boolean>) => {
      state.isMobile = action.payload;
      // 移动端自动收起侧边栏
      if (action.payload) {
        state.sidebarCollapsed = true;
      }
    },
    setModalVisible: (state, action: PayloadAction<boolean>) => {
      state.modalVisible = action.payload;
    },
    setDrawerVisible: (state, action: PayloadAction<boolean>) => {
      state.drawerVisible = action.payload;
    },
    initializeTheme: (state) => {
      const savedTheme = localStorage.getItem('theme');
      if (savedTheme) {
        try {
          state.theme = { ...state.theme, ...JSON.parse(savedTheme) };
        } catch (error) {
          console.warn('Failed to parse saved theme:', error);
        }
      }
      
      const savedSidebarState = localStorage.getItem('sidebarCollapsed');
      if (savedSidebarState) {
        try {
          state.sidebarCollapsed = JSON.parse(savedSidebarState);
        } catch (error) {
          console.warn('Failed to parse saved sidebar state:', error);
        }
      }
    },
    toggleDarkMode: (state) => {
      state.theme.darkMode = !state.theme.darkMode;
      localStorage.setItem('theme', JSON.stringify(state.theme));
    },
    toggleCompactMode: (state) => {
      state.theme.compactMode = !state.theme.compactMode;
      localStorage.setItem('theme', JSON.stringify(state.theme));
    },
    setPrimaryColor: (state, action: PayloadAction<string>) => {
      state.theme.primaryColor = action.payload;
      localStorage.setItem('theme', JSON.stringify(state.theme));
    },
    setBorderRadius: (state, action: PayloadAction<number>) => {
      state.theme.borderRadius = action.payload;
      localStorage.setItem('theme', JSON.stringify(state.theme));
    },
  },
});

export const {
  setTheme,
  toggleSidebar,
  setSidebarCollapsed,
  setLoading,
  addNotification,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  removeNotification,
  clearNotifications,
  setBreadcrumbs,
  setPageTitle,
  setIsMobile,
  setModalVisible,
  setDrawerVisible,
  initializeTheme,
  toggleDarkMode,
  toggleCompactMode,
  setPrimaryColor,
  setBorderRadius,
} = uiSlice.actions;

export default uiSlice.reducer;
