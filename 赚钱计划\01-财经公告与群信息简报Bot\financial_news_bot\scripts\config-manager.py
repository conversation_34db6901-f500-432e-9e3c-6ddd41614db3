#!/usr/bin/env python3
"""
统一配置管理工具
整合配置生成、验证、安全检查等功能
"""
import os
import sys
import secrets
import argparse
from pathlib import Path
from typing import Dict, List, Any
import re

class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.env_file = self.project_root / ".env"
        self.env_example = self.project_root / ".env.example"
    
    def generate_secure_config(self):
        """生成安全配置"""
        print("🔐 生成安全配置...")
        
        # 生成安全密钥
        secure_values = {
            "SECRET_KEY": secrets.token_urlsafe(64),
            "JWT_SECRET_KEY": secrets.token_urlsafe(64),
            "MYSQL_ROOT_PASSWORD": self._generate_password(16),
            "REDIS_PASSWORD": self._generate_password(12),
        }
        
        # 读取示例配置
        if not self.env_example.exists():
            print("❌ .env.example 文件不存在")
            return False
        
        with open(self.env_example, 'r', encoding='utf-8') as f:
            example_content = f.read()
        
        # 替换安全值
        config_content = example_content
        for key, value in secure_values.items():
            pattern = f"{key}=.*"
            replacement = f"{key}={value}"
            config_content = re.sub(pattern, replacement, config_content)
        
        # 写入.env文件
        with open(self.env_file, 'w', encoding='utf-8') as f:
            f.write(config_content)
        
        print("✅ 安全配置生成完成")
        print("🔑 生成的安全密钥:")
        for key, value in secure_values.items():
            print(f"  {key}: {value[:20]}...")
        
        return True
    
    def _generate_password(self, length: int) -> str:
        """生成安全密码"""
        import string
        alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
        return ''.join(secrets.choice(alphabet) for _ in range(length))
    
    def validate_config(self) -> Dict[str, Any]:
        """验证配置"""
        print("✅ 验证配置...")
        
        if not self.env_file.exists():
            return {
                "valid": False,
                "errors": [".env 文件不存在"],
                "warnings": [],
                "suggestions": ["运行 'python config-manager.py generate' 生成配置文件"]
            }
        
        # 读取配置
        config = self._load_env_file()
        
        # 验证规则
        validation_results = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "suggestions": []
        }
        
        # 必需的配置项
        required_keys = [
            "DATABASE_URL", "SECRET_KEY", "GLM_API_KEY",
            "MYSQL_ROOT_PASSWORD", "MYSQL_DATABASE"
        ]
        
        for key in required_keys:
            if key not in config or not config[key]:
                validation_results["errors"].append(f"缺少必需配置: {key}")
                validation_results["valid"] = False
        
        # 安全检查
        security_checks = [
            ("SECRET_KEY", 32, "密钥长度应至少32字符"),
            ("JWT_SECRET_KEY", 32, "JWT密钥长度应至少32字符"),
            ("MYSQL_ROOT_PASSWORD", 8, "数据库密码应至少8字符")
        ]
        
        for key, min_length, message in security_checks:
            if key in config and len(config[key]) < min_length:
                validation_results["warnings"].append(f"{key}: {message}")
        
        # 检查默认值
        default_checks = [
            ("SECRET_KEY", "your-secret-key", "使用默认密钥不安全"),
            ("MYSQL_ROOT_PASSWORD", "password", "使用默认数据库密码不安全"),
            ("GLM_API_KEY", "your-glm-api-key", "需要配置真实的GLM API密钥")
        ]
        
        for key, default_value, message in default_checks:
            if key in config and config[key] == default_value:
                validation_results["errors"].append(f"{key}: {message}")
                validation_results["valid"] = False
        
        # URL格式检查
        if "DATABASE_URL" in config:
            if not config["DATABASE_URL"].startswith(("mysql://", "mysql+pymysql://")):
                validation_results["warnings"].append("DATABASE_URL 格式可能不正确")
        
        # 生成建议
        if not validation_results["valid"]:
            validation_results["suggestions"].append("运行 'python config-manager.py generate' 重新生成安全配置")
        
        if validation_results["warnings"]:
            validation_results["suggestions"].append("建议修复所有警告以提高安全性")
        
        return validation_results
    
    def _load_env_file(self) -> Dict[str, str]:
        """加载环境变量文件"""
        config = {}
        
        with open(self.env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    config[key.strip()] = value.strip()
        
        return config
    
    def show_config_template(self):
        """显示配置模板"""
        print("📋 配置模板:")
        
        template = """
# 数据库配置
DATABASE_URL=mysql+pymysql://root:your-password@localhost:3306/financial_news_bot
MYSQL_ROOT_PASSWORD=your-secure-password
MYSQL_DATABASE=financial_news_bot

# Redis配置
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=your-redis-password

# 安全配置
SECRET_KEY=your-very-long-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30

# AI服务配置
GLM_API_KEY=your-glm-api-key-from-zhipu-ai

# 推送服务配置
WECHAT_WORK_WEBHOOK=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=your-key
FEISHU_WEBHOOK=https://open.feishu.cn/open-apis/bot/v2/hook/your-hook-id
EMAIL_SMTP_HOST=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your-app-password

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=structured
ENABLE_FILE_LOGGING=true
ENABLE_CONSOLE_LOGGING=true

# 应用配置
ENVIRONMENT=production
DEBUG=false
"""
        print(template.strip())
    
    def backup_config(self):
        """备份配置"""
        print("💾 备份配置...")
        
        if not self.env_file.exists():
            print("❌ .env 文件不存在，无法备份")
            return False
        
        import time
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        backup_file = self.project_root / f".env.backup.{timestamp}"
        
        import shutil
        shutil.copy2(self.env_file, backup_file)
        
        print(f"✅ 配置已备份到: {backup_file}")
        return True
    
    def restore_config(self, backup_file: str):
        """恢复配置"""
        print(f"🔄 恢复配置: {backup_file}")
        
        backup_path = Path(backup_file)
        if not backup_path.exists():
            print(f"❌ 备份文件不存在: {backup_file}")
            return False
        
        import shutil
        shutil.copy2(backup_path, self.env_file)
        
        print("✅ 配置恢复完成")
        return True
    
    def list_backups(self):
        """列出备份文件"""
        print("📁 配置备份文件:")
        
        backup_files = list(self.project_root.glob(".env.backup.*"))
        
        if not backup_files:
            print("  无备份文件")
            return
        
        backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        
        for backup_file in backup_files:
            import time
            mtime = backup_file.stat().st_mtime
            mtime_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(mtime))
            print(f"  {backup_file.name} ({mtime_str})")
    
    def check_permissions(self):
        """检查文件权限"""
        print("🔒 检查文件权限...")
        
        if not self.env_file.exists():
            print("❌ .env 文件不存在")
            return False
        
        # 检查文件权限（Unix系统）
        if os.name != 'nt':  # 非Windows系统
            import stat
            file_stat = self.env_file.stat()
            file_mode = stat.filemode(file_stat.st_mode)
            
            print(f"📄 .env 文件权限: {file_mode}")
            
            # 检查是否对其他用户可读
            if file_stat.st_mode & stat.S_IROTH:
                print("⚠️ 警告: .env 文件对其他用户可读，存在安全风险")
                print("建议运行: chmod 600 .env")
                return False
            else:
                print("✅ 文件权限安全")
        else:
            print("ℹ️ Windows系统，跳过权限检查")
        
        return True

def main():
    parser = argparse.ArgumentParser(description="财经新闻Bot配置管理工具")
    parser.add_argument("command", choices=[
        "generate", "validate", "template", "backup", "restore", "list-backups", "check-permissions"
    ], help="要执行的命令")
    parser.add_argument("--backup-file", help="要恢复的备份文件名")
    
    args = parser.parse_args()
    manager = ConfigManager()
    
    if args.command == "generate":
        success = manager.generate_secure_config()
    elif args.command == "validate":
        result = manager.validate_config()
        
        if result["valid"]:
            print("✅ 配置验证通过")
        else:
            print("❌ 配置验证失败")
            for error in result["errors"]:
                print(f"  错误: {error}")
        
        for warning in result["warnings"]:
            print(f"  警告: {warning}")
        
        for suggestion in result["suggestions"]:
            print(f"  建议: {suggestion}")
        
        success = result["valid"]
    elif args.command == "template":
        manager.show_config_template()
        success = True
    elif args.command == "backup":
        success = manager.backup_config()
    elif args.command == "restore":
        if not args.backup_file:
            print("❌ 请指定备份文件: --backup-file filename")
            success = False
        else:
            success = manager.restore_config(args.backup_file)
    elif args.command == "list-backups":
        manager.list_backups()
        success = True
    elif args.command == "check-permissions":
        success = manager.check_permissions()
    else:
        parser.print_help()
        success = False
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
