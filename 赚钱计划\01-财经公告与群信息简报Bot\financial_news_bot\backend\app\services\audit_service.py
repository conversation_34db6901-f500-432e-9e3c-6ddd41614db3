"""
审计日志服务 - 微服务适配器版本
记录系统关键操作和安全事件
现在通过微服务处理审计功能
"""
import logging
import json
from datetime import datetime
from typing import Optional, Dict, Any, List
from enum import Enum
from dataclasses import dataclass, asdict
from sqlalchemy.orm import Session
from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

from app.database import get_db
from datetime import timedelta

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

Base = declarative_base()


class AuditEventType(Enum):
    """审计事件类型"""
    # 认证相关
    LOGIN_SUCCESS = "login_success"
    LOGIN_FAILED = "login_failed"
    LOGOUT = "logout"
    PASSWORD_CHANGE = "password_change"
    TOKEN_REFRESH = "token_refresh"
    
    # 用户管理
    USER_CREATE = "user_create"
    USER_UPDATE = "user_update"
    USER_DELETE = "user_delete"
    USER_ROLE_CHANGE = "user_role_change"
    
    # 数据操作
    DATA_CREATE = "data_create"
    DATA_READ = "data_read"
    DATA_UPDATE = "data_update"
    DATA_DELETE = "data_delete"
    DATA_EXPORT = "data_export"
    
    # 系统操作
    SYSTEM_CONFIG_CHANGE = "system_config_change"
    SYSTEM_BACKUP = "system_backup"
    SYSTEM_RESTORE = "system_restore"
    
    # 安全事件
    SECURITY_BREACH_ATTEMPT = "security_breach_attempt"
    RATE_LIMIT_EXCEEDED = "rate_limit_exceeded"
    SUSPICIOUS_ACTIVITY = "suspicious_activity"
    IP_BLOCKED = "ip_blocked"
    
    # 业务操作
    SUBSCRIPTION_CREATE = "subscription_create"
    SUBSCRIPTION_UPDATE = "subscription_update"
    SUBSCRIPTION_DELETE = "subscription_delete"
    NEWS_PUBLISH = "news_publish"
    PUSH_SEND = "push_send"


class AuditSeverity(Enum):
    """审计严重级别"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class AuditEvent:
    """审计事件数据类"""
    event_type: AuditEventType
    user_id: Optional[str]
    ip_address: str
    user_agent: Optional[str]
    resource: Optional[str]
    action: str
    result: str  # success, failed, error
    severity: AuditSeverity
    details: Dict[str, Any]
    timestamp: datetime
    session_id: Optional[str] = None
    request_id: Optional[str] = None


class AuditLog(Base):
    """审计日志数据模型"""
    __tablename__ = "audit_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    event_type = Column(String(50), nullable=False, index=True)
    user_id = Column(String(50), index=True)
    ip_address = Column(String(45), nullable=False, index=True)
    user_agent = Column(Text)
    resource = Column(String(200), index=True)
    action = Column(String(100), nullable=False)
    result = Column(String(20), nullable=False, index=True)
    severity = Column(String(20), nullable=False, index=True)
    details = Column(Text)  # JSON格式
    timestamp = Column(DateTime, nullable=False, index=True)
    session_id = Column(String(100))
    request_id = Column(String(100))


class AuditService:
    """审计服务"""
    
    def __init__(self):
        # 导入微服务适配器
        from app.adapters.microservice_adapter import microservice_manager
        self.microservice_manager = microservice_manager

        self.sensitive_fields = {
            "password", "token", "secret", "key", "credential",
            "auth", "session", "cookie", "private"
        }

        logger.info("审计服务初始化完成 (微服务模式)")
    
    def _sanitize_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """清理敏感数据"""
        if not isinstance(data, dict):
            return data
        
        sanitized = {}
        for key, value in data.items():
            key_lower = key.lower()
            
            # 检查是否为敏感字段
            is_sensitive = any(sensitive in key_lower for sensitive in self.sensitive_fields)
            
            if is_sensitive:
                sanitized[key] = "[REDACTED]"
            elif isinstance(value, dict):
                sanitized[key] = self._sanitize_data(value)
            elif isinstance(value, list):
                sanitized[key] = [
                    self._sanitize_data(item) if isinstance(item, dict) else item
                    for item in value
                ]
            else:
                sanitized[key] = value
        
        return sanitized
    
    def log_event(
        self,
        event_type: AuditEventType,
        user_id: Optional[str],
        ip_address: str,
        action: str,
        result: str = "success",
        severity: AuditSeverity = AuditSeverity.LOW,
        resource: Optional[str] = None,
        user_agent: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        session_id: Optional[str] = None,
        request_id: Optional[str] = None
    ):
        """记录审计事件 - 通过微服务处理"""
        try:
            # 清理敏感数据
            clean_details = self._sanitize_data(details or {})

            # 通过微服务记录审计事件
            import asyncio
            asyncio.create_task(
                self.microservice_manager.security_adapter.log_audit_event(
                    event_type=event_type.value,
                    user_id=user_id,
                    ip_address=ip_address,
                    action=action,
                    result=result,
                    severity=severity.value,
                    resource=resource,
                    user_agent=user_agent,
                    details=clean_details,
                    session_id=session_id,
                    request_id=request_id
                )
            )

            # 保留本地备份记录
            self._log_to_local_file(event_type, user_id, ip_address, action, result, severity, clean_details)

        except Exception as e:
            logger.error(f"Failed to log audit event: {str(e)}")
            # 降级到本地记录
            self._log_to_local_file(event_type, user_id, ip_address, action, result, severity, details or {})

    def _log_to_local_file(self, event_type, user_id, ip_address, action, result, severity, details):
        """本地文件备份记录"""
        try:
            log_entry = {
                "timestamp": datetime.now().isoformat(),
                "event_type": event_type.value if hasattr(event_type, 'value') else str(event_type),
                "user_id": user_id,
                "ip_address": ip_address,
                "action": action,
                "result": result,
                "severity": severity.value if hasattr(severity, 'value') else str(severity),
                "details": details
            }

            # 根据严重级别选择日志级别
            severity_str = severity.value if hasattr(severity, 'value') else str(severity)
            if severity_str == "CRITICAL":
                logger.critical(f"AUDIT_BACKUP: {json.dumps(log_entry, ensure_ascii=False)}")
            elif severity_str == "HIGH":
                logger.error(f"AUDIT_BACKUP: {json.dumps(log_entry, ensure_ascii=False)}")
            elif severity_str == "MEDIUM":
                logger.warning(f"AUDIT_BACKUP: {json.dumps(log_entry, ensure_ascii=False)}")
            else:
                logger.info(f"AUDIT_BACKUP: {json.dumps(log_entry, ensure_ascii=False)}")

        except Exception as e:
            logger.error(f"本地备份记录失败: {str(e)}")
    
    def _save_to_database(self, event: AuditEvent):
        """保存审计事件到数据库"""
        try:
            db = next(get_db())
            
            audit_log = AuditLog(
                event_type=event.event_type.value,
                user_id=event.user_id,
                ip_address=event.ip_address,
                user_agent=event.user_agent,
                resource=event.resource,
                action=event.action,
                result=event.result,
                severity=event.severity.value,
                details=json.dumps(event.details, ensure_ascii=False),
                timestamp=event.timestamp,
                session_id=event.session_id,
                request_id=event.request_id
            )
            
            db.add(audit_log)
            db.commit()
            
        except Exception as e:
            logger.error(f"Failed to save audit log to database: {str(e)}")
            if 'db' in locals():
                db.rollback()
        finally:
            if 'db' in locals():
                db.close()
    
    def _log_to_file(self, event: AuditEvent):
        """记录审计事件到日志文件"""
        try:
            log_entry = {
                "timestamp": event.timestamp.isoformat(),
                "event_type": event.event_type.value,
                "user_id": event.user_id,
                "ip_address": event.ip_address,
                "action": event.action,
                "result": event.result,
                "severity": event.severity.value,
                "resource": event.resource,
                "details": event.details
            }
            
            # 根据严重级别选择日志级别
            if event.severity == AuditSeverity.CRITICAL:
                logger.critical(f"AUDIT: {json.dumps(log_entry, ensure_ascii=False)}")
            elif event.severity == AuditSeverity.HIGH:
                logger.error(f"AUDIT: {json.dumps(log_entry, ensure_ascii=False)}")
            elif event.severity == AuditSeverity.MEDIUM:
                logger.warning(f"AUDIT: {json.dumps(log_entry, ensure_ascii=False)}")
            else:
                logger.info(f"AUDIT: {json.dumps(log_entry, ensure_ascii=False)}")
                
        except Exception as e:
            logger.error(f"Failed to log audit event to file: {str(e)}")
    
    def _send_alert(self, event: AuditEvent):
        """发送高严重级别事件告警"""
        try:
            alert_message = f"""
            安全告警 - {event.severity.value.upper()}
            
            事件类型: {event.event_type.value}
            用户ID: {event.user_id or 'N/A'}
            IP地址: {event.ip_address}
            操作: {event.action}
            结果: {event.result}
            时间: {event.timestamp.isoformat()}
            资源: {event.resource or 'N/A'}
            详情: {json.dumps(event.details, ensure_ascii=False, indent=2)}
            """
            
            # TODO: 集成实际的告警系统（邮件、钉钉、企业微信等）
            logger.critical(f"SECURITY ALERT: {alert_message}")
            
        except Exception as e:
            logger.error(f"Failed to send security alert: {str(e)}")
    
    def query_logs(
        self,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        user_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        event_type: Optional[AuditEventType] = None,
        severity: Optional[AuditSeverity] = None,
        result: Optional[str] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """查询审计日志"""
        try:
            db = next(get_db())
            
            query = db.query(AuditLog)
            
            # 添加过滤条件
            if start_time:
                query = query.filter(AuditLog.timestamp >= start_time)
            if end_time:
                query = query.filter(AuditLog.timestamp <= end_time)
            if user_id:
                query = query.filter(AuditLog.user_id == user_id)
            if ip_address:
                query = query.filter(AuditLog.ip_address == ip_address)
            if event_type:
                query = query.filter(AuditLog.event_type == event_type.value)
            if severity:
                query = query.filter(AuditLog.severity == severity.value)
            if result:
                query = query.filter(AuditLog.result == result)
            
            # 排序和分页
            logs = query.order_by(AuditLog.timestamp.desc()).offset(offset).limit(limit).all()
            
            # 转换为字典格式
            result_logs = []
            for log in logs:
                log_dict = {
                    "id": log.id,
                    "event_type": log.event_type,
                    "user_id": log.user_id,
                    "ip_address": log.ip_address,
                    "user_agent": log.user_agent,
                    "resource": log.resource,
                    "action": log.action,
                    "result": log.result,
                    "severity": log.severity,
                    "timestamp": log.timestamp.isoformat(),
                    "session_id": log.session_id,
                    "request_id": log.request_id
                }
                
                # 解析详情JSON
                try:
                    log_dict["details"] = json.loads(log.details) if log.details else {}
                except json.JSONDecodeError:
                    log_dict["details"] = {}
                
                result_logs.append(log_dict)
            
            return result_logs
            
        except Exception as e:
            logger.error(f"Failed to query audit logs: {str(e)}")
            return []
        finally:
            if 'db' in locals():
                db.close()
    
    def get_statistics(self, days: int = 7) -> Dict[str, Any]:
        """获取审计统计信息"""
        try:
            db = next(get_db())
            
            # 计算时间范围
            end_time = datetime.utcnow()
            start_time = end_time - timedelta(days=days)
            
            # 基础统计
            total_events = db.query(AuditLog).filter(
                AuditLog.timestamp >= start_time
            ).count()
            
            # 按事件类型统计
            event_type_stats = db.query(
                AuditLog.event_type,
                db.func.count(AuditLog.id).label('count')
            ).filter(
                AuditLog.timestamp >= start_time
            ).group_by(AuditLog.event_type).all()
            
            # 按严重级别统计
            severity_stats = db.query(
                AuditLog.severity,
                db.func.count(AuditLog.id).label('count')
            ).filter(
                AuditLog.timestamp >= start_time
            ).group_by(AuditLog.severity).all()
            
            # 按结果统计
            result_stats = db.query(
                AuditLog.result,
                db.func.count(AuditLog.id).label('count')
            ).filter(
                AuditLog.timestamp >= start_time
            ).group_by(AuditLog.result).all()
            
            # 活跃用户统计
            active_users = db.query(AuditLog.user_id).filter(
                AuditLog.timestamp >= start_time,
                AuditLog.user_id.isnot(None)
            ).distinct().count()
            
            # 活跃IP统计
            active_ips = db.query(AuditLog.ip_address).filter(
                AuditLog.timestamp >= start_time
            ).distinct().count()
            
            return {
                "period_days": days,
                "total_events": total_events,
                "active_users": active_users,
                "active_ips": active_ips,
                "event_types": {row.event_type: row.count for row in event_type_stats},
                "severities": {row.severity: row.count for row in severity_stats},
                "results": {row.result: row.count for row in result_stats},
                "generated_at": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get audit statistics: {str(e)}")
            return {}
        finally:
            if 'db' in locals():
                db.close()


# 全局审计服务实例
audit_service = AuditService()
