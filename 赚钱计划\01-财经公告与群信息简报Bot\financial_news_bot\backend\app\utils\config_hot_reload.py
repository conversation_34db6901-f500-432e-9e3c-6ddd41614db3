"""
配置热重载机制
支持运行时动态更新配置，无需重启服务
"""
import os
import json
import logging
import threading
import time
from typing import Dict, Any, Callable, Optional
from datetime import datetime
from pathlib import Path
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

from ..constants import EnvironmentConstants

logger = logging.getLogger(__name__)


class ConfigChangeHandler(FileSystemEventHandler):
    """配置文件变更处理器"""
    
    def __init__(self, config_manager: 'ConfigHotReloadManager'):
        self.config_manager = config_manager
    
    def on_modified(self, event):
        if not event.is_directory and event.src_path.endswith(('.env', '.json', '.yaml', '.yml')):
            logger.info(f"检测到配置文件变更: {event.src_path}")
            self.config_manager.reload_config(event.src_path)


class ConfigHotReloadManager:
    """配置热重载管理器"""
    
    def __init__(self):
        self.config_cache: Dict[str, Any] = {}
        self.file_timestamps: Dict[str, float] = {}
        self.reload_callbacks: Dict[str, list] = {}
        self.observer: Optional[Observer] = None
        self.lock = threading.RLock()
        self.enabled = EnvironmentConstants.is_development() or os.getenv('CONFIG_HOT_RELOAD', 'false').lower() == 'true'
        
        if self.enabled:
            self._setup_file_watcher()
    
    def _setup_file_watcher(self):
        """设置文件监控"""
        try:
            self.observer = Observer()
            event_handler = ConfigChangeHandler(self)
            
            # 监控项目根目录的配置文件
            watch_paths = [
                '.',  # 项目根目录
                'app/config',  # 配置目录
            ]
            
            for path in watch_paths:
                if os.path.exists(path):
                    self.observer.schedule(event_handler, path, recursive=False)
            
            self.observer.start()
            logger.info("配置文件热重载监控已启动")
            
        except Exception as e:
            logger.error(f"配置文件监控启动失败: {str(e)}")
            self.enabled = False
    
    def register_callback(self, config_key: str, callback: Callable[[Any], None]):
        """注册配置变更回调"""
        with self.lock:
            if config_key not in self.reload_callbacks:
                self.reload_callbacks[config_key] = []
            self.reload_callbacks[config_key].append(callback)
    
    def load_config_file(self, file_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            path = Path(file_path)
            if not path.exists():
                return {}
            
            # 检查文件时间戳
            current_timestamp = path.stat().st_mtime
            cached_timestamp = self.file_timestamps.get(file_path, 0)
            
            if current_timestamp <= cached_timestamp and file_path in self.config_cache:
                return self.config_cache[file_path]
            
            # 读取配置文件
            config_data = {}
            if path.suffix == '.json':
                with open(path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
            elif path.suffix in ['.yaml', '.yml']:
                try:
                    import yaml
                    with open(path, 'r', encoding='utf-8') as f:
                        config_data = yaml.safe_load(f)
                except ImportError:
                    logger.warning("PyYAML未安装，无法解析YAML配置文件")
            elif path.suffix == '.env':
                config_data = self._parse_env_file(path)
            
            # 更新缓存
            with self.lock:
                self.config_cache[file_path] = config_data
                self.file_timestamps[file_path] = current_timestamp
            
            logger.info(f"配置文件已加载: {file_path}")
            return config_data
            
        except Exception as e:
            logger.error(f"加载配置文件失败 {file_path}: {str(e)}")
            return {}
    
    def _parse_env_file(self, file_path: Path) -> Dict[str, str]:
        """解析.env文件"""
        config = {}
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        config[key.strip()] = value.strip().strip('"\'')
        except Exception as e:
            logger.error(f"解析.env文件失败: {str(e)}")
        return config
    
    def reload_config(self, file_path: str):
        """重新加载配置"""
        if not self.enabled:
            return
        
        try:
            # 强制重新加载
            if file_path in self.file_timestamps:
                del self.file_timestamps[file_path]
            
            new_config = self.load_config_file(file_path)
            
            # 触发回调
            self._trigger_callbacks(file_path, new_config)
            
            logger.info(f"配置已热重载: {file_path}")
            
        except Exception as e:
            logger.error(f"配置热重载失败 {file_path}: {str(e)}")
    
    def _trigger_callbacks(self, file_path: str, new_config: Dict[str, Any]):
        """触发配置变更回调"""
        with self.lock:
            # 触发文件级回调
            if file_path in self.reload_callbacks:
                for callback in self.reload_callbacks[file_path]:
                    try:
                        callback(new_config)
                    except Exception as e:
                        logger.error(f"配置回调执行失败: {str(e)}")
            
            # 触发配置键级回调
            for key, value in new_config.items():
                if key in self.reload_callbacks:
                    for callback in self.reload_callbacks[key]:
                        try:
                            callback(value)
                        except Exception as e:
                            logger.error(f"配置键回调执行失败 {key}: {str(e)}")
    
    def get_config_value(self, file_path: str, key: str, default: Any = None) -> Any:
        """获取配置值"""
        config = self.load_config_file(file_path)
        return config.get(key, default)
    
    def update_config_value(self, file_path: str, key: str, value: Any) -> bool:
        """更新配置值（仅支持JSON文件）"""
        try:
            if not file_path.endswith('.json'):
                logger.warning("仅支持更新JSON配置文件")
                return False
            
            config = self.load_config_file(file_path)
            config[key] = value
            
            # 写入文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            # 更新缓存
            with self.lock:
                self.config_cache[file_path] = config
                self.file_timestamps[file_path] = time.time()
            
            logger.info(f"配置已更新: {file_path}[{key}] = {value}")
            return True
            
        except Exception as e:
            logger.error(f"更新配置失败: {str(e)}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """获取热重载状态"""
        return {
            'enabled': self.enabled,
            'watched_files': len(self.file_timestamps),
            'cached_configs': len(self.config_cache),
            'registered_callbacks': sum(len(callbacks) for callbacks in self.reload_callbacks.values()),
            'observer_running': self.observer.is_alive() if self.observer else False,
            'last_reload': max(self.file_timestamps.values()) if self.file_timestamps else None
        }
    
    def stop(self):
        """停止热重载监控"""
        if self.observer:
            self.observer.stop()
            self.observer.join()
            logger.info("配置热重载监控已停止")


# 全局实例
config_hot_reload = ConfigHotReloadManager()


def get_config_hot_reload() -> ConfigHotReloadManager:
    """获取配置热重载管理器实例"""
    return config_hot_reload


# 装饰器：自动重载配置
def auto_reload_config(config_file: str):
    """装饰器：自动重载配置"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            # 确保配置是最新的
            config_hot_reload.load_config_file(config_file)
            return func(*args, **kwargs)
        return wrapper
    return decorator
