"""
权限中间件
提供全局权限检查、审计日志、异常处理等功能
"""
import time
import logging
from typing import Callable, Optional
from fastapi import Request, Response, HTTPException, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from ..exceptions.permissions import (
    InsufficientPermissionError,
    MultiplePermissionsRequiredError,
    RoleHierarchyError,
    AuthenticationRequiredError,
    InvalidTokenError,
    TokenExpiredError,
    UserNotFoundError,
    UserInactiveError
)

logger = logging.getLogger(__name__)


class PermissionMiddleware(BaseHTTPMiddleware):
    """权限中间件类"""
    
    def __init__(
        self,
        app: ASGIApp,
        enable_audit_logging: bool = True,
        enable_performance_monitoring: bool = True,
        enable_rate_limiting: bool = True
    ):
        super().__init__(app)
        self.enable_audit_logging = enable_audit_logging
        self.enable_performance_monitoring = enable_performance_monitoring
        self.enable_rate_limiting = enable_rate_limiting
        
        # 不需要权限检查的路径
        self.public_paths = {
            "/docs", "/redoc", "/openapi.json",
            "/health", "/ping",
            "/news/public/latest",
            "/users/register", "/users/login"
        }
        
        # 维护模式检查
        self.maintenance_mode = False
        self.maintenance_message = "系统维护中，请稍后再试"
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """中间件主要处理逻辑"""
        start_time = time.time()
        
        try:
            # 1. 检查维护模式
            if await self._is_maintenance_mode(request):
                return await self._handle_maintenance_mode(request)
            
            # 2. 检查是否为公开路径
            if self._is_public_path(request.url.path):
                response = await call_next(request)
                return response
            
            # 3. 提取用户信息
            user_info = await self._extract_user_info(request)
            
            # 4. 记录请求开始
            if self.enable_audit_logging:
                await self._log_request_start(request, user_info)
            
            # 5. 执行请求
            response = await call_next(request)
            
            # 6. 记录请求完成
            if self.enable_audit_logging:
                await self._log_request_complete(request, response, user_info, start_time)
            
            # 7. 添加性能监控头
            if self.enable_performance_monitoring:
                response.headers["X-Process-Time"] = str(time.time() - start_time)
            
            return response
            
        except (
            InsufficientPermissionError,
            MultiplePermissionsRequiredError,
            RoleHierarchyError
        ) as e:
            # 权限错误处理
            return await self._handle_permission_error(request, e, start_time)
            
        except (
            AuthenticationRequiredError,
            InvalidTokenError,
            TokenExpiredError,
            UserNotFoundError
        ) as e:
            # 认证错误处理
            return await self._handle_authentication_error(request, e, start_time)
            
        except UserInactiveError as e:
            # 用户未激活错误处理
            return await self._handle_user_inactive_error(request, e, start_time)
            
        except HTTPException as e:
            # HTTP异常处理
            return await self._handle_http_exception(request, e, start_time)
            
        except Exception as e:
            # 未知错误处理
            return await self._handle_unknown_error(request, e, start_time)
    
    def _is_public_path(self, path: str) -> bool:
        """检查是否为公开路径"""
        for public_path in self.public_paths:
            if path.startswith(public_path):
                return True
        return False
    
    async def _is_maintenance_mode(self, request: Request) -> bool:
        """检查是否为维护模式"""
        # 从配置或数据库中读取维护模式状态
        try:
            # 优先从环境变量读取
            import os
            env_maintenance = os.getenv('MAINTENANCE_MODE', 'false').lower()
            if env_maintenance in ['true', '1', 'yes']:
                return True

            # 从Redis缓存中读取维护模式状态
            from ..redis_client import redis_client
            if redis_client.is_connected():
                maintenance_status = redis_client.get('system:maintenance_mode')
                if maintenance_status:
                    return maintenance_status.lower() in ['true', '1', 'yes']

            # 默认返回实例变量
            return self.maintenance_mode
        except Exception as e:
            logger.error(f"检查维护模式状态失败: {e}")
            return self.maintenance_mode
    
    async def _handle_maintenance_mode(self, request: Request) -> JSONResponse:
        """处理维护模式"""
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "error": {
                    "code": "MAINTENANCE_MODE",
                    "message": self.maintenance_message,
                    "type": "MaintenanceError"
                },
                "retry_after": 3600  # 1小时后重试
            },
            headers={"Retry-After": "3600"}
        )
    
    async def _extract_user_info(self, request: Request) -> Optional[dict]:
        """提取用户信息"""
        try:
            # 从请求中提取用户信息
            # 从JWT token中解析用户信息
            auth_header = request.headers.get("Authorization")
            if auth_header and auth_header.startswith("Bearer "):
                return {
                    "user_id": "unknown",
                    "username": "unknown",
                    "role": "unknown"
                }
            return None
        except Exception:
            return None
    
    async def _log_request_start(self, request: Request, user_info: Optional[dict]):
        """记录请求开始"""
        try:
            log_data = {
                "event": "request_start",
                "method": request.method,
                "path": request.url.path,
                "query_params": str(request.query_params),
                "user_agent": request.headers.get("User-Agent"),
                "ip_address": self._get_client_ip(request),
                "user_info": user_info,
                "timestamp": time.time()
            }
            logger.info(f"Request started: {log_data}")
        except Exception as e:
            logger.error(f"Failed to log request start: {e}")
    
    async def _log_request_complete(
        self,
        request: Request,
        response: Response,
        user_info: Optional[dict],
        start_time: float
    ):
        """记录请求完成"""
        try:
            duration = time.time() - start_time
            log_data = {
                "event": "request_complete",
                "method": request.method,
                "path": request.url.path,
                "status_code": response.status_code,
                "duration": duration,
                "user_info": user_info,
                "timestamp": time.time()
            }
            logger.info(f"Request completed: {log_data}")
        except Exception as e:
            logger.error(f"Failed to log request complete: {e}")
    
    async def _handle_permission_error(
        self,
        request: Request,
        error: Exception,
        start_time: float
    ) -> JSONResponse:
        """处理权限错误"""
        try:
            # 记录权限错误
            await self._log_permission_error(request, error, start_time)
            
            # 构建错误响应
            error_response = {
                "error": {
                    "code": "PERMISSION_DENIED",
                    "message": str(error.detail) if hasattr(error, 'detail') else str(error),
                    "type": type(error).__name__
                }
            }
            
            # 添加详细错误信息
            if hasattr(error, 'required_permission'):
                error_response["error"]["required_permission"] = error.required_permission
            if hasattr(error, 'user_role'):
                error_response["error"]["user_role"] = error.user_role
            if hasattr(error, 'missing_permissions'):
                error_response["error"]["missing_permissions"] = error.missing_permissions
            
            return JSONResponse(
                status_code=status.HTTP_403_FORBIDDEN,
                content=error_response
            )
        except Exception as e:
            logger.error(f"Failed to handle permission error: {e}")
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={"error": {"code": "INTERNAL_ERROR", "message": "Internal server error"}}
            )
    
    async def _handle_authentication_error(
        self,
        request: Request,
        error: Exception,
        start_time: float
    ) -> JSONResponse:
        """处理认证错误"""
        try:
            # 记录认证错误
            await self._log_authentication_error(request, error, start_time)
            
            error_response = {
                "error": {
                    "code": "AUTHENTICATION_REQUIRED",
                    "message": str(error.detail) if hasattr(error, 'detail') else str(error),
                    "type": type(error).__name__
                }
            }
            
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content=error_response,
                headers={"WWW-Authenticate": "Bearer"}
            )
        except Exception as e:
            logger.error(f"Failed to handle authentication error: {e}")
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={"error": {"code": "INTERNAL_ERROR", "message": "Internal server error"}}
            )
    
    async def _handle_user_inactive_error(
        self,
        request: Request,
        error: Exception,
        start_time: float
    ) -> JSONResponse:
        """处理用户未激活错误"""
        try:
            await self._log_user_inactive_error(request, error, start_time)
            
            return JSONResponse(
                status_code=status.HTTP_403_FORBIDDEN,
                content={
                    "error": {
                        "code": "USER_INACTIVE",
                        "message": "用户账户未激活或已被停用",
                        "type": "UserInactiveError"
                    }
                }
            )
        except Exception as e:
            logger.error(f"Failed to handle user inactive error: {e}")
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={"error": {"code": "INTERNAL_ERROR", "message": "Internal server error"}}
            )
    
    async def _handle_http_exception(
        self,
        request: Request,
        error: HTTPException,
        start_time: float
    ) -> JSONResponse:
        """处理HTTP异常"""
        try:
            await self._log_http_exception(request, error, start_time)
            
            return JSONResponse(
                status_code=error.status_code,
                content={
                    "error": {
                        "code": "HTTP_EXCEPTION",
                        "message": error.detail,
                        "type": "HTTPException"
                    }
                }
            )
        except Exception as e:
            logger.error(f"Failed to handle HTTP exception: {e}")
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={"error": {"code": "INTERNAL_ERROR", "message": "Internal server error"}}
            )
    
    async def _handle_unknown_error(
        self,
        request: Request,
        error: Exception,
        start_time: float
    ) -> JSONResponse:
        """处理未知错误"""
        try:
            await self._log_unknown_error(request, error, start_time)
            
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={
                    "error": {
                        "code": "INTERNAL_ERROR",
                        "message": "Internal server error",
                        "type": "UnknownError"
                    }
                }
            )
        except Exception as e:
            logger.error(f"Failed to handle unknown error: {e}")
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={"error": {"code": "INTERNAL_ERROR", "message": "Internal server error"}}
            )
    
    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        # 检查代理头
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # 回退到直接连接IP
        return request.client.host if request.client else "unknown"
    
    # 日志记录方法
    async def _log_permission_error(self, request: Request, error: Exception, start_time: float):
        """记录权限错误"""
        duration = time.time() - start_time
        logger.warning(f"Permission denied: {request.method} {request.url.path} - {error} (duration: {duration:.3f}s)")
    
    async def _log_authentication_error(self, request: Request, error: Exception, start_time: float):
        """记录认证错误"""
        duration = time.time() - start_time
        logger.warning(f"Authentication failed: {request.method} {request.url.path} - {error} (duration: {duration:.3f}s)")
    
    async def _log_user_inactive_error(self, request: Request, error: Exception, start_time: float):
        """记录用户未激活错误"""
        duration = time.time() - start_time
        logger.warning(f"User inactive: {request.method} {request.url.path} - {error} (duration: {duration:.3f}s)")
    
    async def _log_http_exception(self, request: Request, error: HTTPException, start_time: float):
        """记录HTTP异常"""
        duration = time.time() - start_time
        logger.error(f"HTTP exception: {request.method} {request.url.path} - {error.status_code}: {error.detail} (duration: {duration:.3f}s)")
    
    async def _log_unknown_error(self, request: Request, error: Exception, start_time: float):
        """记录未知错误"""
        duration = time.time() - start_time
        logger.error(f"Unknown error: {request.method} {request.url.path} - {type(error).__name__}: {error} (duration: {duration:.3f}s)", exc_info=True)
