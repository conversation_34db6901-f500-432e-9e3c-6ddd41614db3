import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAppSelector } from '@/store';
import LoadingSpinner from './LoadingSpinner';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermission?: string;
  requiredSubscription?: 'basic' | 'advanced' | 'premium';
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredPermission,
  requiredSubscription,
}) => {
  const location = useLocation();
  const { isAuthenticated, user, loading } = useAppSelector(state => state.auth);

  // 如果正在加载，显示加载状态
  if (loading) {
    return <LoadingSpinner size="large" tip="验证用户身份中..." />;
  }

  // 如果未认证，重定向到登录页
  if (!isAuthenticated || !user) {
    return <Navigate to="/auth/login" state={{ from: location }} replace />;
  }

  // 检查用户是否激活
  if (!user.is_active) {
    return <Navigate to="/auth/activate" replace />;
  }

  // 检查权限（如果需要）
  if (requiredPermission) {
    // 这里可以根据实际的权限系统来实现
    // 暂时简化处理
    console.log('Checking permission:', requiredPermission);
  }

  // 检查订阅级别（如果需要）
  if (requiredSubscription) {
    const subscriptionLevels = {
      free: 0,
      basic: 1,
      advanced: 2,
      premium: 3,
    };

    const userLevel = subscriptionLevels[user.subscription_type] || 0;
    const requiredLevel = subscriptionLevels[requiredSubscription];

    if (userLevel < requiredLevel) {
      return <Navigate to="/subscription/upgrade" replace />;
    }
  }

  return <>{children}</>;
};

export default ProtectedRoute;
