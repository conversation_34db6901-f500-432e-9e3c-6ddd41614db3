"""
推送规则引擎服务
实现智能推送规则和条件过滤
"""
import logging
import re
import json
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

from app.models.push_rule import PushRule, RuleCondition, RuleExecution, RuleType, RuleOperator, RuleStatus
from app.models.user import User
from app.models.news import News
from app.models.subscription import Subscription

logger = logging.getLogger(__name__)

class RuleEngine:
    """规则引擎类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_rule(self, rule_data: Dict[str, Any], user_id: int) -> PushRule:
        """
        创建新规则
        
        Args:
            rule_data: 规则数据
            user_id: 用户ID
        
        Returns:
            创建的规则对象
        """
        try:
            # 验证规则条件
            self._validate_conditions(rule_data.get('conditions', []))
            
            rule = PushRule(
                name=rule_data['name'],
                description=rule_data.get('description'),
                rule_type=rule_data.get('rule_type', RuleType.FILTER),
                priority=rule_data.get('priority', 0),
                conditions=rule_data['conditions'],
                actions=rule_data.get('actions', {}),
                user_id=user_id,
                subscription_id=rule_data.get('subscription_id'),
                status=rule_data.get('status', RuleStatus.ACTIVE),
                is_global=rule_data.get('is_global', False)
            )
            
            self.db.add(rule)
            self.db.commit()
            self.db.refresh(rule)
            
            logger.info(f"创建规则成功: {rule.name} (ID: {rule.id})")
            return rule
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"创建规则失败: {str(e)}")
            raise
    
    def update_rule(self, rule_id: int, rule_data: Dict[str, Any], user_id: int) -> PushRule:
        """
        更新规则
        
        Args:
            rule_id: 规则ID
            rule_data: 更新数据
            user_id: 用户ID
        
        Returns:
            更新后的规则对象
        """
        try:
            rule = self.get_rule_by_id(rule_id)
            
            if not rule:
                raise ValueError(f"规则不存在: {rule_id}")
            
            # 权限检查
            if rule.user_id != user_id and not rule.is_global:
                raise PermissionError("没有权限编辑此规则")
            
            # 验证新的规则条件
            if 'conditions' in rule_data:
                self._validate_conditions(rule_data['conditions'])
            
            # 更新字段
            for field, value in rule_data.items():
                if hasattr(rule, field) and field not in ['id', 'user_id', 'created_at']:
                    setattr(rule, field, value)
            
            rule.updated_at = datetime.now()
            
            self.db.commit()
            self.db.refresh(rule)
            
            logger.info(f"更新规则成功: {rule.name} (ID: {rule.id})")
            return rule
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新规则失败: {str(e)}")
            raise
    
    def get_rule_by_id(self, rule_id: int) -> Optional[PushRule]:
        """根据ID获取规则"""
        return self.db.query(PushRule).filter(PushRule.id == rule_id).first()
    
    def get_rules_by_user(self, user_id: int, rule_type: Optional[RuleType] = None) -> List[PushRule]:
        """
        获取用户的规则列表
        
        Args:
            user_id: 用户ID
            rule_type: 规则类型过滤
        
        Returns:
            规则列表
        """
        query = self.db.query(PushRule).filter(
            (PushRule.user_id == user_id) | (PushRule.is_global == True)
        )
        
        if rule_type:
            query = query.filter(PushRule.rule_type == rule_type)
        
        return query.order_by(PushRule.priority.desc(), PushRule.created_at.desc()).all()
    
    def evaluate_rules(self, context: Dict[str, Any], user_id: int, 
                      rule_type: Optional[RuleType] = None) -> List[Dict[str, Any]]:
        """
        评估规则
        
        Args:
            context: 评估上下文
            user_id: 用户ID
            rule_type: 规则类型过滤
        
        Returns:
            匹配的规则和动作列表
        """
        try:
            # 获取适用的规则
            rules = self.get_rules_by_user(user_id, rule_type)
            active_rules = [r for r in rules if r.is_active]
            
            matched_rules = []
            
            for rule in active_rules:
                start_time = datetime.now()
                
                try:
                    # 评估规则条件
                    if self._evaluate_conditions(rule.conditions, context):
                        matched_rules.append({
                            "rule": rule,
                            "actions": rule.actions or {}
                        })
                        
                        # 记录成功执行
                        rule.increment_execution(True)
                        self._log_execution(rule, context, {"matched": True}, True, start_time)
                    else:
                        # 记录执行但不匹配
                        rule.increment_execution(True)
                        self._log_execution(rule, context, {"matched": False}, True, start_time)
                
                except Exception as e:
                    logger.error(f"规则 {rule.id} 评估失败: {str(e)}")
                    rule.increment_execution(False)
                    self._log_execution(rule, context, {}, False, start_time, str(e))
            
            self.db.commit()
            
            return matched_rules
            
        except Exception as e:
            logger.error(f"规则评估失败: {str(e)}")
            return []
    
    def filter_news_by_rules(self, news_list: List[News], user_id: int, 
                           subscription: Optional[Subscription] = None) -> List[News]:
        """
        根据规则过滤新闻
        
        Args:
            news_list: 新闻列表
            user_id: 用户ID
            subscription: 订阅对象
        
        Returns:
            过滤后的新闻列表
        """
        if not news_list:
            return []
        
        # 获取过滤规则
        filter_rules = self.get_rules_by_user(user_id, RuleType.FILTER)
        active_filter_rules = [r for r in filter_rules if r.is_active]
        
        if not active_filter_rules:
            return news_list
        
        filtered_news = []
        
        for news in news_list:
            should_include = True
            
            # 构建新闻上下文
            news_context = self._build_news_context(news, subscription)
            
            # 应用过滤规则
            for rule in active_filter_rules:
                try:
                    if self._evaluate_conditions(rule.conditions, news_context):
                        # 检查规则动作
                        actions = rule.actions or {}
                        if actions.get('action') == 'exclude':
                            should_include = False
                            break
                        elif actions.get('action') == 'include':
                            should_include = True
                            # 包含规则优先级更高，直接跳出
                            break
                
                except Exception as e:
                    logger.error(f"新闻过滤规则 {rule.id} 执行失败: {str(e)}")
                    continue
            
            if should_include:
                filtered_news.append(news)
        
        logger.info(f"规则过滤: {len(news_list)} -> {len(filtered_news)} 条新闻")
        return filtered_news
    
    def should_trigger_push(self, context: Dict[str, Any], user_id: int) -> bool:
        """
        检查是否应该触发推送
        
        Args:
            context: 触发上下文
            user_id: 用户ID
        
        Returns:
            是否应该触发推送
        """
        trigger_rules = self.evaluate_rules(context, user_id, RuleType.TRIGGER)
        
        for rule_result in trigger_rules:
            actions = rule_result.get('actions', {})
            if actions.get('action') == 'trigger_push':
                return True
            elif actions.get('action') == 'block_push':
                return False
        
        # 默认允许推送
        return True
    
    def _evaluate_conditions(self, conditions: List[Dict[str, Any]], context: Dict[str, Any]) -> bool:
        """
        评估规则条件
        
        Args:
            conditions: 条件列表
            context: 评估上下文
        
        Returns:
            条件是否满足
        """
        if not conditions:
            return True
        
        # 按条件组分组
        condition_groups = {}
        for condition in conditions:
            group = condition.get('condition_group', 0)
            if group not in condition_groups:
                condition_groups[group] = []
            condition_groups[group].append(condition)
        
        # 评估每个条件组（组间OR，组内AND）
        group_results = []
        
        for group_conditions in condition_groups.values():
            group_result = True
            
            for condition in group_conditions:
                condition_result = self._evaluate_single_condition(condition, context)
                
                logical_op = condition.get('logical_operator', 'AND')
                if logical_op == 'AND':
                    group_result = group_result and condition_result
                elif logical_op == 'OR':
                    group_result = group_result or condition_result
                
                # 如果是AND操作且结果为False，可以提前退出
                if logical_op == 'AND' and not condition_result:
                    group_result = False
                    break
            
            group_results.append(group_result)
        
        # 条件组之间是OR关系
        return any(group_results)
    
    def _evaluate_single_condition(self, condition: Dict[str, Any], context: Dict[str, Any]) -> bool:
        """
        评估单个条件
        
        Args:
            condition: 条件定义
            context: 评估上下文
        
        Returns:
            条件是否满足
        """
        field_name = condition.get('field_name')
        operator = condition.get('operator')
        expected_value = condition.get('field_value')
        
        if not field_name or not operator:
            return False
        
        # 获取实际值
        actual_value = self._get_field_value(field_name, context)
        
        # 执行比较
        return self._compare_values(actual_value, operator, expected_value)
    
    def _get_field_value(self, field_path: str, context: Dict[str, Any]) -> Any:
        """
        从上下文中获取字段值
        
        Args:
            field_path: 字段路径（支持点号分隔）
            context: 上下文数据
        
        Returns:
            字段值
        """
        try:
            value = context
            for part in field_path.split('.'):
                if isinstance(value, dict):
                    value = value.get(part)
                elif isinstance(value, list) and part.isdigit():
                    index = int(part)
                    value = value[index] if 0 <= index < len(value) else None
                else:
                    return None
            return value
        except (KeyError, IndexError, TypeError):
            return None
    
    def _compare_values(self, actual: Any, operator: str, expected: Any) -> bool:
        """
        比较两个值
        
        Args:
            actual: 实际值
            operator: 比较操作符
            expected: 期望值
        
        Returns:
            比较结果
        """
        try:
            if operator == RuleOperator.EQUALS:
                return actual == expected
            elif operator == RuleOperator.NOT_EQUALS:
                return actual != expected
            elif operator == RuleOperator.CONTAINS:
                return str(expected).lower() in str(actual).lower() if actual else False
            elif operator == RuleOperator.NOT_CONTAINS:
                return str(expected).lower() not in str(actual).lower() if actual else True
            elif operator == RuleOperator.GREATER_THAN:
                return float(actual) > float(expected) if actual is not None else False
            elif operator == RuleOperator.LESS_THAN:
                return float(actual) < float(expected) if actual is not None else False
            elif operator == RuleOperator.GREATER_EQUAL:
                return float(actual) >= float(expected) if actual is not None else False
            elif operator == RuleOperator.LESS_EQUAL:
                return float(actual) <= float(expected) if actual is not None else False
            elif operator == RuleOperator.IN:
                expected_list = expected if isinstance(expected, list) else [expected]
                return actual in expected_list
            elif operator == RuleOperator.NOT_IN:
                expected_list = expected if isinstance(expected, list) else [expected]
                return actual not in expected_list
            elif operator == RuleOperator.REGEX:
                return bool(re.search(str(expected), str(actual), re.IGNORECASE)) if actual else False
            else:
                logger.warning(f"未知的操作符: {operator}")
                return False
        except (ValueError, TypeError) as e:
            logger.error(f"值比较失败: {actual} {operator} {expected}, 错误: {str(e)}")
            return False
    
    def _build_news_context(self, news: News, subscription: Optional[Subscription] = None) -> Dict[str, Any]:
        """
        构建新闻评估上下文
        
        Args:
            news: 新闻对象
            subscription: 订阅对象
        
        Returns:
            新闻上下文
        """
        context = {
            "news": {
                "id": news.id,
                "title": news.title,
                "content": news.content,
                "summary": news.summary,
                "source": news.source,
                "category": news.category,
                "importance_score": news.importance_score,
                "published_at": news.published_at,
                "companies": news.companies or [],
                "keywords": news.keywords or []
            },
            "current_time": datetime.now(),
            "current_hour": datetime.now().hour,
            "current_weekday": datetime.now().weekday()
        }
        
        if subscription:
            context["subscription"] = {
                "id": subscription.id,
                "name": subscription.name,
                "keywords": subscription.keywords or [],
                "companies": subscription.companies or [],
                "categories": subscription.categories or []
            }
        
        return context
    
    def _validate_conditions(self, conditions: List[Dict[str, Any]]):
        """验证规则条件格式"""
        if not isinstance(conditions, list):
            raise ValueError("条件必须是列表格式")
        
        for condition in conditions:
            if not isinstance(condition, dict):
                raise ValueError("每个条件必须是字典格式")
            
            required_fields = ['field_name', 'operator']
            for field in required_fields:
                if field not in condition:
                    raise ValueError(f"条件缺少必需字段: {field}")
    
    def _log_execution(self, rule: PushRule, context: Dict[str, Any], result: Dict[str, Any], 
                      success: bool, start_time: datetime, error_msg: str = None):
        """记录规则执行日志"""
        try:
            execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
            
            execution = RuleExecution(
                rule_id=rule.id,
                execution_context=context,
                execution_result=result,
                is_success=success,
                error_message=error_msg,
                execution_time_ms=execution_time
            )
            
            self.db.add(execution)
            
        except Exception as e:
            logger.error(f"记录规则执行日志失败: {str(e)}")

# 预定义规则模板
RULE_TEMPLATES = {
    "high_importance_filter": {
        "name": "高重要性新闻过滤",
        "description": "只推送重要性评分大于80的新闻",
        "rule_type": RuleType.FILTER,
        "conditions": [
            {
                "field_name": "news.importance_score",
                "operator": RuleOperator.GREATER_THAN,
                "field_value": 80,
                "logical_operator": "AND",
                "condition_group": 0
            }
        ],
        "actions": {
            "action": "include"
        }
    },
    "working_hours_trigger": {
        "name": "工作时间推送触发",
        "description": "只在工作时间(9-18点)触发推送",
        "rule_type": RuleType.TRIGGER,
        "conditions": [
            {
                "field_name": "current_hour",
                "operator": RuleOperator.GREATER_EQUAL,
                "field_value": 9,
                "logical_operator": "AND",
                "condition_group": 0
            },
            {
                "field_name": "current_hour",
                "operator": RuleOperator.LESS_EQUAL,
                "field_value": 18,
                "logical_operator": "AND",
                "condition_group": 0
            }
        ],
        "actions": {
            "action": "trigger_push"
        }
    }
}
