"""
智能推荐系统服务
基于用户行为和内容特征的个性化推荐

TODO: 此服务已完整实现但未在路由中使用，为个性化推荐功能预留
      可考虑在新闻推荐、个性化订阅等功能中集成此服务
      依赖ai_service，需要GLM API支持
"""
import asyncio
import logging
import json
import math
from typing import Dict, List, Optional, Any, Set, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
from collections import defaultdict, Counter

from app.config import settings
from app.services.ai_service import get_glm_service

logger = logging.getLogger(__name__)


@dataclass
class UserBehavior:
    """用户行为数据"""
    user_id: str
    read_articles: List[str]        # 阅读的文章ID列表
    search_queries: List[str]       # 搜索查询列表
    clicked_companies: List[str]    # 点击的公司列表
    preferred_categories: List[str] # 偏好的分类
    reading_time: Dict[str, float] # 文章阅读时长
    interaction_scores: Dict[str, float]  # 交互评分
    last_active: datetime = None

    def __post_init__(self):
        if self.last_active is None:
            self.last_active = datetime.now()


@dataclass
class ContentFeature:
    """内容特征"""
    content_id: str
    title: str
    category: str
    companies: List[str]
    keywords: List[str]
    entities: List[str]
    sentiment_score: float
    importance_score: float
    publish_time: datetime
    
    def __post_init__(self):
        if not hasattr(self, 'publish_time') or self.publish_time is None:
            self.publish_time = datetime.now()


@dataclass
class RecommendationResult:
    """推荐结果"""
    content_id: str
    title: str
    score: float
    reasons: List[str]
    category: str
    companies: List[str]
    keywords: List[str]


class RecommendationService:
    """智能推荐服务"""
    
    def __init__(self):
        """初始化推荐服务"""
        self.glm_service = get_glm_service()
        self.user_behaviors: Dict[str, UserBehavior] = {}
        self.content_features: Dict[str, ContentFeature] = {}
        self.similarity_cache: Dict[str, Dict[str, float]] = {}
        
        # 推荐算法权重配置
        self.weights = {
            "content_based": 0.4,      # 基于内容的推荐
            "collaborative": 0.3,      # 协同过滤推荐
            "popularity": 0.2,         # 热度推荐
            "freshness": 0.1          # 时效性推荐
        }
        
        logger.info("智能推荐服务初始化完成")
    
    async def update_user_behavior(self, user_id: str, action: str, 
                                  content_id: str, metadata: Dict[str, Any] = None):
        """
        更新用户行为数据
        
        Args:
            user_id: 用户ID
            action: 行为类型 (read, search, click, like, share)
            content_id: 内容ID
            metadata: 额外元数据
        """
        try:
            if user_id not in self.user_behaviors:
                self.user_behaviors[user_id] = UserBehavior(
                    user_id=user_id,
                    read_articles=[],
                    search_queries=[],
                    clicked_companies=[],
                    preferred_categories=[],
                    reading_time={},
                    interaction_scores={}
                )
            
            behavior = self.user_behaviors[user_id]
            behavior.last_active = datetime.now()
            
            if action == "read":
                if content_id not in behavior.read_articles:
                    behavior.read_articles.append(content_id)
                
                # 记录阅读时长
                if metadata and "reading_time" in metadata:
                    behavior.reading_time[content_id] = metadata["reading_time"]
                
                # 更新交互评分
                base_score = 1.0
                if metadata and "reading_time" in metadata:
                    # 阅读时长越长，评分越高
                    time_bonus = min(metadata["reading_time"] / 60.0, 2.0)  # 最多2分钟奖励
                    base_score += time_bonus
                
                behavior.interaction_scores[content_id] = base_score
                
            elif action == "search":
                query = metadata.get("query", "") if metadata else ""
                if query and query not in behavior.search_queries:
                    behavior.search_queries.append(query)
                    
            elif action == "click_company":
                company = metadata.get("company", "") if metadata else ""
                if company and company not in behavior.clicked_companies:
                    behavior.clicked_companies.append(company)
            
            elif action in ["like", "share", "bookmark"]:
                # 高价值交互
                current_score = behavior.interaction_scores.get(content_id, 0)
                bonus = {"like": 2.0, "share": 3.0, "bookmark": 1.5}.get(action, 1.0)
                behavior.interaction_scores[content_id] = current_score + bonus
            
            logger.debug(f"更新用户 {user_id} 行为: {action} -> {content_id}")
            
        except Exception as e:
            logger.error(f"更新用户行为失败: {str(e)}")
    
    async def add_content_feature(self, content_id: str, title: str, content: str,
                                 category: str = None, entities: List[Dict[str, Any]] = None):
        """
        添加内容特征
        
        Args:
            content_id: 内容ID
            title: 标题
            content: 内容
            category: 分类
            entities: 实体列表
        """
        try:
            # 提取关键词
            keywords = await self._extract_keywords(title, content)
            
            # 提取公司名称
            companies = []
            entity_names = []
            if entities:
                for entity in entities:
                    if entity.get("type") == "company":
                        companies.append(entity.get("text", ""))
                    entity_names.append(entity.get("text", ""))
            
            # 计算重要性评分
            importance_score = await self._calculate_importance_score(title, content, entities)
            
            # 情感分析评分
            sentiment_score = await self._get_sentiment_score(title, content)
            
            feature = ContentFeature(
                content_id=content_id,
                title=title,
                category=category or "未分类",
                companies=companies,
                keywords=keywords,
                entities=entity_names,
                sentiment_score=sentiment_score,
                importance_score=importance_score,
                publish_time=datetime.now()
            )
            
            self.content_features[content_id] = feature
            logger.debug(f"添加内容特征: {content_id}")
            
        except Exception as e:
            logger.error(f"添加内容特征失败: {str(e)}")
    
    async def get_recommendations(self, user_id: str, limit: int = 10,
                                 exclude_read: bool = True) -> List[RecommendationResult]:
        """
        获取个性化推荐
        
        Args:
            user_id: 用户ID
            limit: 推荐数量限制
            exclude_read: 是否排除已读内容
            
        Returns:
            推荐结果列表
        """
        try:
            if user_id not in self.user_behaviors:
                # 新用户，返回热门推荐
                return await self._get_popular_recommendations(limit)
            
            user_behavior = self.user_behaviors[user_id]
            
            # 获取候选内容
            candidates = list(self.content_features.keys())
            
            if exclude_read:
                candidates = [
                    cid for cid in candidates 
                    if cid not in user_behavior.read_articles
                ]
            
            if not candidates:
                return []
            
            # 计算推荐分数
            recommendations = []
            
            for content_id in candidates:
                content_feature = self.content_features[content_id]
                
                # 基于内容的推荐分数
                content_score = await self._calculate_content_based_score(
                    user_behavior, content_feature
                )
                
                # 协同过滤分数
                collaborative_score = await self._calculate_collaborative_score(
                    user_id, content_id
                )
                
                # 热度分数
                popularity_score = await self._calculate_popularity_score(content_id)
                
                # 时效性分数
                freshness_score = self._calculate_freshness_score(content_feature)
                
                # 综合分数
                total_score = (
                    content_score * self.weights["content_based"] +
                    collaborative_score * self.weights["collaborative"] +
                    popularity_score * self.weights["popularity"] +
                    freshness_score * self.weights["freshness"]
                )
                
                # 生成推荐理由
                reasons = self._generate_recommendation_reasons(
                    user_behavior, content_feature, content_score, collaborative_score
                )
                
                recommendations.append(RecommendationResult(
                    content_id=content_id,
                    title=content_feature.title,
                    score=total_score,
                    reasons=reasons,
                    category=content_feature.category,
                    companies=content_feature.companies,
                    keywords=content_feature.keywords
                ))
            
            # 按分数排序并返回前N个
            recommendations.sort(key=lambda x: x.score, reverse=True)
            return recommendations[:limit]
            
        except Exception as e:
            logger.error(f"获取推荐失败: {str(e)}")
            return []
    
    async def _extract_keywords(self, title: str, content: str) -> List[str]:
        """提取关键词"""
        try:
            # 使用AI提取关键词
            glm_service = get_glm_service()
            keywords_result = await glm_service.extract_keywords(title, content)
            
            if keywords_result:
                return [kw.get("keyword", "") for kw in keywords_result[:10]]
            
            # 简单的关键词提取作为备选
            import jieba
            words = jieba.lcut(f"{title} {content}")
            # 过滤停用词和短词
            stop_words = {'的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这'}
            keywords = [word for word in words if len(word) > 1 and word not in stop_words]
            
            # 统计词频并返回前10个
            from collections import Counter
            word_counts = Counter(keywords)
            return [word for word, count in word_counts.most_common(10)]
            
        except Exception as e:
            logger.error(f"提取关键词失败: {str(e)}")
            return []
    
    async def _calculate_importance_score(self, title: str, content: str, 
                                        entities: List[Dict[str, Any]] = None) -> float:
        """计算重要性评分"""
        score = 0.0
        
        # 基于标题长度
        title_length_score = min(len(title) / 50.0, 1.0)
        score += title_length_score * 0.2
        
        # 基于内容长度
        content_length_score = min(len(content) / 1000.0, 1.0)
        score += content_length_score * 0.3
        
        # 基于实体数量
        if entities:
            entity_score = min(len(entities) / 10.0, 1.0)
            score += entity_score * 0.3
        
        # 基于关键词密度
        important_keywords = ["重大", "公告", "业绩", "增长", "下降", "合作", "收购", "重组"]
        text = f"{title} {content}"
        keyword_matches = sum(1 for keyword in important_keywords if keyword in text)
        keyword_score = min(keyword_matches / len(important_keywords), 1.0)
        score += keyword_score * 0.2
        
        return min(score, 1.0)
    
    async def _get_sentiment_score(self, title: str, content: str) -> float:
        """获取情感评分"""
        try:
            glm_service = get_glm_service()
            sentiment_result = await glm_service.analyze_sentiment(title, content)
            
            if sentiment_result:
                return sentiment_result.get("sentiment_score", 0.0)
            
            return 0.0
            
        except Exception as e:
            logger.error(f"获取情感评分失败: {str(e)}")
            return 0.0
    
    async def _calculate_content_based_score(self, user_behavior: UserBehavior, 
                                           content_feature: ContentFeature) -> float:
        """计算基于内容的推荐分数"""
        score = 0.0
        
        # 基于用户偏好的公司
        if user_behavior.clicked_companies:
            company_match = len(set(content_feature.companies) & set(user_behavior.clicked_companies))
            if company_match > 0:
                score += 0.4 * (company_match / len(user_behavior.clicked_companies))
        
        # 基于用户搜索历史
        if user_behavior.search_queries:
            text = f"{content_feature.title} {' '.join(content_feature.keywords)}"
            query_matches = sum(1 for query in user_behavior.search_queries if query.lower() in text.lower())
            if query_matches > 0:
                score += 0.3 * (query_matches / len(user_behavior.search_queries))
        
        # 基于用户偏好分类
        if user_behavior.preferred_categories:
            if content_feature.category in user_behavior.preferred_categories:
                score += 0.3
        
        return min(score, 1.0)
    
    async def _calculate_collaborative_score(self, user_id: str, content_id: str) -> float:
        """计算协同过滤分数"""
        # 基于用户行为的协同过滤实现
        # 找到相似用户
        similar_users = await self._find_similar_users(user_id)
        
        if not similar_users:
            return 0.0
        
        # 计算相似用户对该内容的平均评分
        total_score = 0.0
        count = 0
        
        for similar_user_id, similarity in similar_users:
            if similar_user_id in self.user_behaviors:
                similar_behavior = self.user_behaviors[similar_user_id]
                if content_id in similar_behavior.interaction_scores:
                    total_score += similar_behavior.interaction_scores[content_id] * similarity
                    count += 1
        
        return (total_score / count) if count > 0 else 0.0
    
    async def _find_similar_users(self, user_id: str, limit: int = 10) -> List[Tuple[str, float]]:
        """找到相似用户"""
        if user_id not in self.user_behaviors:
            return []
        
        target_behavior = self.user_behaviors[user_id]
        similarities = []
        
        for other_user_id, other_behavior in self.user_behaviors.items():
            if other_user_id == user_id:
                continue
            
            # 计算用户相似度
            similarity = self._calculate_user_similarity(target_behavior, other_behavior)
            if similarity > 0.1:  # 相似度阈值
                similarities.append((other_user_id, similarity))
        
        # 按相似度排序
        similarities.sort(key=lambda x: x[1], reverse=True)
        return similarities[:limit]
    
    def _calculate_user_similarity(self, behavior1: UserBehavior, behavior2: UserBehavior) -> float:
        """计算用户相似度"""
        # 基于共同阅读文章的Jaccard相似度
        read1 = set(behavior1.read_articles)
        read2 = set(behavior2.read_articles)
        
        if not read1 or not read2:
            return 0.0
        
        intersection = len(read1 & read2)
        union = len(read1 | read2)
        
        return intersection / union if union > 0 else 0.0
    
    async def _calculate_popularity_score(self, content_id: str) -> float:
        """计算热度分数"""
        # 统计有多少用户阅读了这个内容
        read_count = sum(
            1 for behavior in self.user_behaviors.values()
            if content_id in behavior.read_articles
        )
        
        # 统计总交互分数
        total_interaction = sum(
            behavior.interaction_scores.get(content_id, 0)
            for behavior in self.user_behaviors.values()
        )
        
        # 归一化分数
        max_users = len(self.user_behaviors) if self.user_behaviors else 1
        popularity = (read_count / max_users) * 0.5 + min(total_interaction / 10.0, 1.0) * 0.5
        
        return min(popularity, 1.0)
    
    def _calculate_freshness_score(self, content_feature: ContentFeature) -> float:
        """计算时效性分数"""
        now = datetime.now()
        time_diff = now - content_feature.publish_time
        hours_old = time_diff.total_seconds() / 3600
        
        # 24小时内的内容得分最高，之后逐渐衰减
        if hours_old <= 24:
            return 1.0
        elif hours_old <= 72:  # 3天内
            return 0.7
        elif hours_old <= 168:  # 1周内
            return 0.4
        else:
            return 0.1
    
    def _generate_recommendation_reasons(self, user_behavior: UserBehavior, 
                                       content_feature: ContentFeature,
                                       content_score: float, collaborative_score: float) -> List[str]:
        """生成推荐理由"""
        reasons = []
        
        # 基于公司关注
        common_companies = set(content_feature.companies) & set(user_behavior.clicked_companies)
        if common_companies:
            reasons.append(f"您关注的公司: {', '.join(list(common_companies)[:2])}")
        
        # 基于搜索历史
        for query in user_behavior.search_queries[-3:]:  # 最近3个搜索
            if query.lower() in content_feature.title.lower():
                reasons.append(f"与您的搜索'{query}'相关")
                break
        
        # 基于分类偏好
        if content_feature.category in user_behavior.preferred_categories:
            reasons.append(f"您感兴趣的{content_feature.category}类新闻")
        
        # 基于协同过滤
        if collaborative_score > 0.5:
            reasons.append("与您兴趣相似的用户也在关注")
        
        # 基于时效性
        time_diff = datetime.now() - content_feature.publish_time
        if time_diff.total_seconds() < 3600:  # 1小时内
            reasons.append("最新发布")
        
        return reasons[:3]  # 最多返回3个理由
    
    async def _get_popular_recommendations(self, limit: int) -> List[RecommendationResult]:
        """获取热门推荐（新用户）"""
        recommendations = []
        
        # 按重要性评分排序
        sorted_contents = sorted(
            self.content_features.items(),
            key=lambda x: x[1].importance_score,
            reverse=True
        )
        
        for content_id, feature in sorted_contents[:limit]:
            recommendations.append(RecommendationResult(
                content_id=content_id,
                title=feature.title,
                score=feature.importance_score,
                reasons=["热门内容", "重要新闻"],
                category=feature.category,
                companies=feature.companies,
                keywords=feature.keywords
            ))
        
        return recommendations


# 全局推荐服务实例
recommendation_service = None

def get_recommendation_service() -> RecommendationService:
    """获取推荐服务实例"""
    global recommendation_service
    if recommendation_service is None:
        recommendation_service = RecommendationService()
    return recommendation_service
