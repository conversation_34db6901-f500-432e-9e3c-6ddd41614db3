import React, { useEffect, useRef, useState } from 'react';
import { message } from 'antd';

interface TouchOptimizedProps {
  children: React.ReactNode;
  onPullRefresh?: () => Promise<void>;
  onLoadMore?: () => Promise<void>;
  enablePullRefresh?: boolean;
  enableLoadMore?: boolean;
  refreshThreshold?: number;
  loadMoreThreshold?: number;
  className?: string;
  style?: React.CSSProperties;
}

const TouchOptimized: React.FC<TouchOptimizedProps> = ({
  children,
  onPullRefresh,
  onLoadMore,
  enablePullRefresh = false,
  enableLoadMore = false,
  refreshThreshold = 80,
  loadMoreThreshold = 100,
  className,
  style,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [pullDistance, setPullDistance] = useState(0);
  const [touchStart, setTouchStart] = useState({ x: 0, y: 0 });
  const [touchMove, setTouchMove] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    let startY = 0;
    let currentY = 0;
    let isDragging = false;

    const handleTouchStart = (e: TouchEvent) => {
      startY = e.touches[0].clientY;
      setTouchStart({ x: e.touches[0].clientX, y: e.touches[0].clientY });
      isDragging = true;
    };

    const handleTouchMove = (e: TouchEvent) => {
      if (!isDragging) return;

      currentY = e.touches[0].clientY;
      const deltaY = currentY - startY;
      setTouchMove({ x: e.touches[0].clientX, y: e.touches[0].clientY });

      // 下拉刷新
      if (enablePullRefresh && container.scrollTop === 0 && deltaY > 0) {
        e.preventDefault();
        const distance = Math.min(deltaY * 0.5, refreshThreshold * 1.5);
        setPullDistance(distance);
        
        // 添加触觉反馈
        if (distance >= refreshThreshold && 'vibrate' in navigator) {
          navigator.vibrate(10);
        }
      }

      // 上拉加载更多
      if (enableLoadMore && !isLoadingMore) {
        const scrollTop = container.scrollTop;
        const scrollHeight = container.scrollHeight;
        const clientHeight = container.clientHeight;
        
        if (scrollTop + clientHeight >= scrollHeight - loadMoreThreshold) {
          handleLoadMore();
        }
      }
    };

    const handleTouchEnd = async () => {
      isDragging = false;
      
      if (enablePullRefresh && pullDistance >= refreshThreshold && !isRefreshing) {
        await handlePullRefresh();
      }
      
      setPullDistance(0);
      setTouchStart({ x: 0, y: 0 });
      setTouchMove({ x: 0, y: 0 });
    };

    const handlePullRefresh = async () => {
      if (!onPullRefresh || isRefreshing) return;
      
      setIsRefreshing(true);
      try {
        await onPullRefresh();
        message.success('刷新成功');
      } catch (error) {
        message.error('刷新失败');
      } finally {
        setIsRefreshing(false);
      }
    };

    const handleLoadMore = async () => {
      if (!onLoadMore || isLoadingMore) return;
      
      setIsLoadingMore(true);
      try {
        await onLoadMore();
      } catch (error) {
        message.error('加载失败');
      } finally {
        setIsLoadingMore(false);
      }
    };

    // 添加事件监听器
    container.addEventListener('touchstart', handleTouchStart, { passive: false });
    container.addEventListener('touchmove', handleTouchMove, { passive: false });
    container.addEventListener('touchend', handleTouchEnd);

    // 优化滚动性能
    container.style.webkitOverflowScrolling = 'touch';
    container.style.overflowScrolling = 'touch';

    return () => {
      container.removeEventListener('touchstart', handleTouchStart);
      container.removeEventListener('touchmove', handleTouchMove);
      container.removeEventListener('touchend', handleTouchEnd);
    };
  }, [enablePullRefresh, enableLoadMore, onPullRefresh, onLoadMore, refreshThreshold, loadMoreThreshold, isRefreshing, isLoadingMore, pullDistance]);

  const renderPullRefreshIndicator = () => {
    if (!enablePullRefresh) return null;

    const progress = Math.min(pullDistance / refreshThreshold, 1);
    const rotation = progress * 360;

    return (
      <div
        style={{
          position: 'absolute',
          top: -60,
          left: '50%',
          transform: 'translateX(-50%)',
          width: 40,
          height: 40,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#fff',
          borderRadius: '50%',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          opacity: pullDistance > 0 ? 1 : 0,
          transition: 'opacity 0.2s ease',
          zIndex: 1000,
        }}
      >
        {isRefreshing ? (
          <div
            style={{
              width: 20,
              height: 20,
              border: '2px solid #f0f0f0',
              borderTop: '2px solid #1890ff',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite',
            }}
          />
        ) : (
          <div
            style={{
              width: 20,
              height: 20,
              transform: `rotate(${rotation}deg)`,
              transition: 'transform 0.1s ease',
            }}
          >
            ↓
          </div>
        )}
      </div>
    );
  };

  const renderLoadMoreIndicator = () => {
    if (!enableLoadMore || !isLoadingMore) return null;

    return (
      <div
        style={{
          padding: '16px',
          textAlign: 'center',
          color: '#666',
        }}
      >
        <div
          style={{
            display: 'inline-block',
            width: 20,
            height: 20,
            border: '2px solid #f0f0f0',
            borderTop: '2px solid #1890ff',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            marginRight: '8px',
          }}
        />
        正在加载更多...
      </div>
    );
  };

  return (
    <>
      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
          
          .touch-optimized {
            /* 确保触摸目标至少44px */
            min-height: 44px;
          }
          
          .touch-optimized button,
          .touch-optimized .ant-btn {
            min-height: 44px;
            min-width: 44px;
          }
          
          .touch-optimized .ant-list-item {
            min-height: 44px;
            padding: 12px 16px;
          }
          
          .touch-optimized .ant-menu-item {
            min-height: 44px;
            line-height: 44px;
          }
          
          /* 优化滚动性能 */
          .touch-optimized {
            -webkit-overflow-scrolling: touch;
            overflow-scrolling: touch;
            transform: translateZ(0);
            will-change: scroll-position;
          }
          
          /* 触摸反馈 */
          .touch-optimized button:active,
          .touch-optimized .ant-btn:active {
            transform: scale(0.98);
            transition: transform 0.1s ease;
          }
          
          /* 禁用文本选择在触摸设备上 */
          @media (hover: none) and (pointer: coarse) {
            .touch-optimized {
              -webkit-user-select: none;
              -moz-user-select: none;
              -ms-user-select: none;
              user-select: none;
            }
            
            .touch-optimized input,
            .touch-optimized textarea {
              -webkit-user-select: text;
              -moz-user-select: text;
              -ms-user-select: text;
              user-select: text;
            }
          }
        `}
      </style>
      <div
        ref={containerRef}
        className={`touch-optimized ${className || ''}`}
        style={{
          position: 'relative',
          height: '100%',
          overflow: 'auto',
          transform: `translateY(${pullDistance}px)`,
          transition: pullDistance === 0 ? 'transform 0.3s ease' : 'none',
          ...style,
        }}
      >
        {renderPullRefreshIndicator()}
        {children}
        {renderLoadMoreIndicator()}
      </div>
    </>
  );
};

export default TouchOptimized;
