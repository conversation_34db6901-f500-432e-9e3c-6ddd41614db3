"""
智能内容聚合器
负责根据不同章节类型和用户偏好聚合相关新闻内容

TODO: 此服务已实现但未在路由中使用，为高级内容聚合功能预留
      可考虑在个性化推荐、智能简报生成等功能中使用
"""
import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, func, text

from ..models.news import News
from ..models.user import User
from ..models.subscription import Subscription
from ..services.ai_service import AIService

logger = logging.getLogger(__name__)


class ContentAggregator:
    """内容聚合器类"""
    
    def __init__(self, db: Session):
        self.db = db
        self.ai_service = AIService()
        
        # 内容类型映射
        self.content_type_mapping = {
            "market_news": self._get_market_news,
            "portfolio_news": self._get_portfolio_news,
            "regulatory_news": self._get_regulatory_news,
            "industry_news": self._get_industry_news,
            "upcoming_events": self._get_upcoming_events,
            "market_summary": self._get_market_summary,
            "company_earnings": self._get_company_earnings,
            "policy_news": self._get_policy_news,
            "tomorrow_preview": self._get_tomorrow_preview
        }
        
        # 关键词映射
        self.keyword_mapping = {
            "market_news": ["市场", "股市", "指数", "涨跌", "交易", "成交量"],
            "regulatory_news": ["监管", "政策", "法规", "央行", "证监会", "银保监会"],
            "industry_news": ["行业", "板块", "概念", "龙头", "产业链"],
            "company_earnings": ["业绩", "财报", "营收", "利润", "季报", "年报"],
            "policy_news": ["政策", "国务院", "发改委", "财政部", "税收"],
            "upcoming_events": ["会议", "发布", "上市", "除权", "分红"]
        }
    
    async def get_section_news(
        self,
        content_type: str,
        time_range: Tuple[datetime, datetime],
        max_items: int = 5,
        user: Optional[User] = None,
        personalization_config: Optional[Dict[str, Any]] = None
    ) -> List[News]:
        """
        获取指定章节类型的新闻
        
        Args:
            content_type: 内容类型
            time_range: 时间范围 (start_time, end_time)
            max_items: 最大条目数
            user: 用户对象
            personalization_config: 个性化配置
            
        Returns:
            List[News]: 新闻列表
        """
        try:
            start_time, end_time = time_range
            
            # 获取对应的聚合函数
            aggregator_func = self.content_type_mapping.get(content_type)
            if not aggregator_func:
                logger.warning(f"Unknown content type: {content_type}")
                return await self._get_general_news(start_time, end_time, max_items)
            
            # 调用具体的聚合函数
            news_list = await aggregator_func(
                start_time, end_time, max_items, user, personalization_config
            )
            
            # 应用个性化过滤和排序
            if user and personalization_config:
                news_list = await self._apply_personalization(
                    news_list, user, personalization_config
                )
            
            # 应用智能评分和排序
            news_list = await self._apply_intelligent_scoring(
                news_list, content_type, user
            )
            
            return news_list[:max_items]
            
        except Exception as e:
            logger.error(f"Failed to get section news for {content_type}: {e}")
            return []
    
    async def _get_market_news(
        self,
        start_time: datetime,
        end_time: datetime,
        max_items: int,
        user: Optional[User],
        personalization_config: Optional[Dict[str, Any]]
    ) -> List[News]:
        """获取市场要闻"""
        try:
            keywords = self.keyword_mapping["market_news"]
            
            # 构建查询
            query = self.db.query(News).filter(
                and_(
                    News.published_at >= start_time,
                    News.published_at <= end_time,
                    or_(*[News.title.contains(keyword) for keyword in keywords])
                )
            )
            
            # 按重要性和时间排序
            query = query.order_by(
                desc(News.importance_score),
                desc(News.published_at)
            )
            
            return query.limit(max_items * 2).all()  # 获取更多候选，后续筛选
            
        except Exception as e:
            logger.error(f"Failed to get market news: {e}")
            return []
    
    async def _get_portfolio_news(
        self,
        start_time: datetime,
        end_time: datetime,
        max_items: int,
        user: Optional[User],
        personalization_config: Optional[Dict[str, Any]]
    ) -> List[News]:
        """获取持仓公司要闻"""
        try:
            if not user:
                return []
            
            # 获取用户关注的公司
            portfolio_companies = []
            if personalization_config and "portfolio_companies" in personalization_config:
                portfolio_companies = personalization_config["portfolio_companies"]
            else:
                # 从用户订阅中获取关注公司
                subscriptions = self.db.query(Subscription).filter(
                    Subscription.user_id == user.id,
                    Subscription.is_active == True
                ).all()
                
                for sub in subscriptions:
                    if sub.companies:
                        portfolio_companies.extend(sub.companies)
            
            if not portfolio_companies:
                return []
            
            # 查询相关新闻
            company_conditions = []
            for company in portfolio_companies:
                company_conditions.append(News.title.contains(company))
                company_conditions.append(News.content.contains(company))
            
            query = self.db.query(News).filter(
                and_(
                    News.published_at >= start_time,
                    News.published_at <= end_time,
                    or_(*company_conditions)
                )
            ).order_by(
                desc(News.importance_score),
                desc(News.published_at)
            )
            
            return query.limit(max_items * 2).all()
            
        except Exception as e:
            logger.error(f"Failed to get portfolio news: {e}")
            return []
    
    async def _get_regulatory_news(
        self,
        start_time: datetime,
        end_time: datetime,
        max_items: int,
        user: Optional[User],
        personalization_config: Optional[Dict[str, Any]]
    ) -> List[News]:
        """获取监管风向新闻"""
        try:
            keywords = self.keyword_mapping["regulatory_news"]
            
            query = self.db.query(News).filter(
                and_(
                    News.published_at >= start_time,
                    News.published_at <= end_time,
                    or_(*[News.title.contains(keyword) for keyword in keywords])
                )
            ).order_by(
                desc(News.importance_score),
                desc(News.published_at)
            )
            
            return query.limit(max_items * 2).all()
            
        except Exception as e:
            logger.error(f"Failed to get regulatory news: {e}")
            return []
    
    async def _get_industry_news(
        self,
        start_time: datetime,
        end_time: datetime,
        max_items: int,
        user: Optional[User],
        personalization_config: Optional[Dict[str, Any]]
    ) -> List[News]:
        """获取行业动态新闻"""
        try:
            keywords = self.keyword_mapping["industry_news"]
            
            # 如果有用户兴趣，优先获取相关行业新闻
            if personalization_config and "user_interests" in personalization_config:
                interest_keywords = personalization_config["user_interests"]
                keywords.extend(interest_keywords)
            
            query = self.db.query(News).filter(
                and_(
                    News.published_at >= start_time,
                    News.published_at <= end_time,
                    or_(*[News.title.contains(keyword) for keyword in keywords])
                )
            ).order_by(
                desc(News.importance_score),
                desc(News.published_at)
            )
            
            return query.limit(max_items * 2).all()
            
        except Exception as e:
            logger.error(f"Failed to get industry news: {e}")
            return []
    
    async def _get_upcoming_events(
        self,
        start_time: datetime,
        end_time: datetime,
        max_items: int,
        user: Optional[User],
        personalization_config: Optional[Dict[str, Any]]
    ) -> List[News]:
        """获取即将发生的事件"""
        try:
            keywords = self.keyword_mapping["upcoming_events"]
            
            # 查询包含时间相关词汇的新闻
            time_keywords = ["今日", "明日", "本周", "下周", "即将", "计划", "预计"]
            all_keywords = keywords + time_keywords
            
            query = self.db.query(News).filter(
                and_(
                    News.published_at >= start_time,
                    News.published_at <= end_time,
                    or_(*[News.title.contains(keyword) for keyword in all_keywords])
                )
            ).order_by(
                desc(News.importance_score),
                desc(News.published_at)
            )
            
            return query.limit(max_items * 2).all()
            
        except Exception as e:
            logger.error(f"Failed to get upcoming events: {e}")
            return []
    
    async def _get_market_summary(
        self,
        start_time: datetime,
        end_time: datetime,
        max_items: int,
        user: Optional[User],
        personalization_config: Optional[Dict[str, Any]]
    ) -> List[News]:
        """获取市场收盘总结"""
        try:
            keywords = ["收盘", "收市", "收报", "收于", "收涨", "收跌", "成交额"]
            
            query = self.db.query(News).filter(
                and_(
                    News.published_at >= start_time,
                    News.published_at <= end_time,
                    or_(*[News.title.contains(keyword) for keyword in keywords])
                )
            ).order_by(
                desc(News.importance_score),
                desc(News.published_at)
            )
            
            return query.limit(max_items * 2).all()
            
        except Exception as e:
            logger.error(f"Failed to get market summary: {e}")
            return []
    
    async def _get_company_earnings(
        self,
        start_time: datetime,
        end_time: datetime,
        max_items: int,
        user: Optional[User],
        personalization_config: Optional[Dict[str, Any]]
    ) -> List[News]:
        """获取公司业绩新闻"""
        try:
            keywords = self.keyword_mapping["company_earnings"]
            
            query = self.db.query(News).filter(
                and_(
                    News.published_at >= start_time,
                    News.published_at <= end_time,
                    or_(*[News.title.contains(keyword) for keyword in keywords])
                )
            ).order_by(
                desc(News.importance_score),
                desc(News.published_at)
            )
            
            return query.limit(max_items * 2).all()
            
        except Exception as e:
            logger.error(f"Failed to get company earnings: {e}")
            return []
    
    async def _get_policy_news(
        self,
        start_time: datetime,
        end_time: datetime,
        max_items: int,
        user: Optional[User],
        personalization_config: Optional[Dict[str, Any]]
    ) -> List[News]:
        """获取政策解读新闻"""
        try:
            keywords = self.keyword_mapping["policy_news"]
            
            query = self.db.query(News).filter(
                and_(
                    News.published_at >= start_time,
                    News.published_at <= end_time,
                    or_(*[News.title.contains(keyword) for keyword in keywords])
                )
            ).order_by(
                desc(News.importance_score),
                desc(News.published_at)
            )
            
            return query.limit(max_items * 2).all()
            
        except Exception as e:
            logger.error(f"Failed to get policy news: {e}")
            return []
    
    async def _get_tomorrow_preview(
        self,
        start_time: datetime,
        end_time: datetime,
        max_items: int,
        user: Optional[User],
        personalization_config: Optional[Dict[str, Any]]
    ) -> List[News]:
        """获取明日预览新闻"""
        try:
            keywords = ["明日", "明天", "下周", "即将", "预告", "预览", "展望"]
            
            query = self.db.query(News).filter(
                and_(
                    News.published_at >= start_time,
                    News.published_at <= end_time,
                    or_(*[News.title.contains(keyword) for keyword in keywords])
                )
            ).order_by(
                desc(News.importance_score),
                desc(News.published_at)
            )
            
            return query.limit(max_items * 2).all()
            
        except Exception as e:
            logger.error(f"Failed to get tomorrow preview: {e}")
            return []
    
    async def _get_general_news(
        self,
        start_time: datetime,
        end_time: datetime,
        max_items: int
    ) -> List[News]:
        """获取通用新闻（回退方案）"""
        try:
            query = self.db.query(News).filter(
                and_(
                    News.published_at >= start_time,
                    News.published_at <= end_time
                )
            ).order_by(
                desc(News.importance_score),
                desc(News.published_at)
            )
            
            return query.limit(max_items).all()
            
        except Exception as e:
            logger.error(f"Failed to get general news: {e}")
            return []
    
    async def _apply_personalization(
        self,
        news_list: List[News],
        user: User,
        personalization_config: Dict[str, Any]
    ) -> List[News]:
        """应用个性化过滤和排序"""
        try:
            # 获取用户兴趣关键词
            user_interests = personalization_config.get("user_interests", [])
            portfolio_companies = personalization_config.get("portfolio_companies", [])
            
            # 计算个性化评分
            personalized_news = []
            for news in news_list:
                personalization_score = 0
                
                # 兴趣匹配评分
                for interest in user_interests:
                    if interest in news.title or interest in (news.content or ""):
                        personalization_score += 10
                
                # 持仓公司匹配评分
                for company in portfolio_companies:
                    if company in news.title or company in (news.content or ""):
                        personalization_score += 15
                
                # 添加个性化评分属性
                news.personalization_score = personalization_score
                personalized_news.append(news)
            
            # 按个性化评分和重要性排序
            personalized_news.sort(
                key=lambda x: (x.personalization_score, x.importance_score or 0),
                reverse=True
            )
            
            return personalized_news
            
        except Exception as e:
            logger.error(f"Failed to apply personalization: {e}")
            return news_list
    
    async def _apply_intelligent_scoring(
        self,
        news_list: List[News],
        content_type: str,
        user: Optional[User]
    ) -> List[News]:
        """应用智能评分和排序"""
        try:
            if not news_list:
                return news_list
            
            # 使用AI服务对新闻进行智能评分
            scored_news = []
            for news in news_list:
                try:
                    # 计算内容相关性评分
                    relevance_score = await self._calculate_content_relevance(
                        news, content_type
                    )
                    
                    # 计算时效性评分
                    timeliness_score = self._calculate_timeliness_score(news)
                    
                    # 综合评分
                    final_score = (
                        (news.importance_score or 0) * 0.4 +
                        relevance_score * 0.3 +
                        timeliness_score * 0.2 +
                        getattr(news, 'personalization_score', 0) * 0.1
                    )
                    
                    news.final_score = final_score
                    scored_news.append(news)
                    
                except Exception as e:
                    logger.warning(f"Failed to score news {news.id}: {e}")
                    news.final_score = news.importance_score or 0
                    scored_news.append(news)
            
            # 按最终评分排序
            scored_news.sort(key=lambda x: x.final_score, reverse=True)
            
            return scored_news
            
        except Exception as e:
            logger.error(f"Failed to apply intelligent scoring: {e}")
            return news_list
    
    async def _calculate_content_relevance(
        self,
        news: News,
        content_type: str
    ) -> float:
        """计算内容相关性评分"""
        try:
            keywords = self.keyword_mapping.get(content_type, [])
            if not keywords:
                return 50.0  # 默认评分
            
            # 计算关键词匹配度
            title_matches = sum(1 for keyword in keywords if keyword in news.title)
            content_matches = sum(1 for keyword in keywords if keyword in (news.content or ""))
            
            # 计算相关性评分
            total_keywords = len(keywords)
            title_score = (title_matches / total_keywords) * 100 if total_keywords > 0 else 0
            content_score = (content_matches / total_keywords) * 50 if total_keywords > 0 else 0
            
            relevance_score = min(100, title_score + content_score)
            
            return relevance_score
            
        except Exception as e:
            logger.error(f"Failed to calculate content relevance: {e}")
            return 50.0
    
    def _calculate_timeliness_score(self, news: News) -> float:
        """计算时效性评分"""
        try:
            if not news.published_at:
                return 0.0
            
            # 计算新闻发布时间距离现在的小时数
            hours_ago = (datetime.now() - news.published_at).total_seconds() / 3600
            
            # 时效性评分：越新的新闻评分越高
            if hours_ago <= 1:
                return 100.0
            elif hours_ago <= 6:
                return 80.0
            elif hours_ago <= 12:
                return 60.0
            elif hours_ago <= 24:
                return 40.0
            else:
                return 20.0
                
        except Exception as e:
            logger.error(f"Failed to calculate timeliness score: {e}")
            return 50.0
    
    async def get_content_statistics(
        self,
        start_time: datetime,
        end_time: datetime
    ) -> Dict[str, Any]:
        """获取内容统计信息"""
        try:
            stats = {}
            
            # 总新闻数量
            total_news = self.db.query(News).filter(
                and_(
                    News.published_at >= start_time,
                    News.published_at <= end_time
                )
            ).count()
            
            stats["total_news"] = total_news
            
            # 按内容类型统计
            for content_type in self.content_type_mapping.keys():
                keywords = self.keyword_mapping.get(content_type, [])
                if keywords:
                    count = self.db.query(News).filter(
                        and_(
                            News.published_at >= start_time,
                            News.published_at <= end_time,
                            or_(*[News.title.contains(keyword) for keyword in keywords])
                        )
                    ).count()
                    stats[content_type] = count
            
            # 按重要性统计
            high_importance = self.db.query(News).filter(
                and_(
                    News.published_at >= start_time,
                    News.published_at <= end_time,
                    News.importance_score >= 80
                )
            ).count()
            
            stats["high_importance_news"] = high_importance
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get content statistics: {e}")
            return {}
