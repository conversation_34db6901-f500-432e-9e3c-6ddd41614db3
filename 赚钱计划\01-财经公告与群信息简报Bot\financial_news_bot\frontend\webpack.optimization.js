// Webpack性能优化配置
const path = require('path');
const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
const CompressionPlugin = require('compression-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');
const { DefinePlugin } = require('webpack');

module.exports = {
  // 代码分割优化
  optimization: {
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        // 第三方库
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
          priority: 10,
          reuseExistingChunk: true,
        },
        // React相关
        react: {
          test: /[\\/]node_modules[\\/](react|react-dom|react-router)[\\/]/,
          name: 'react',
          chunks: 'all',
          priority: 20,
        },
        // Ant Design
        antd: {
          test: /[\\/]node_modules[\\/]antd[\\/]/,
          name: 'antd',
          chunks: 'all',
          priority: 15,
        },
        // 图表库
        charts: {
          test: /[\\/]node_modules[\\/](echarts|@ant-design\/charts)[\\/]/,
          name: 'charts',
          chunks: 'all',
          priority: 12,
        },
        // 工具库
        utils: {
          test: /[\\/]node_modules[\\/](lodash|moment|dayjs|axios)[\\/]/,
          name: 'utils',
          chunks: 'all',
          priority: 8,
        },
        // 公共代码
        common: {
          name: 'common',
          minChunks: 2,
          chunks: 'all',
          priority: 5,
          reuseExistingChunk: true,
        },
      },
    },
    // 运行时代码分离
    runtimeChunk: {
      name: 'runtime',
    },
    // 压缩优化
    minimizer: [
      new TerserPlugin({
        terserOptions: {
          compress: {
            drop_console: process.env.NODE_ENV === 'production',
            drop_debugger: true,
            pure_funcs: ['console.log', 'console.info'],
          },
          mangle: {
            safari10: true,
          },
          format: {
            comments: false,
          },
        },
        extractComments: false,
        parallel: true,
      }),
      new CssMinimizerPlugin({
        minimizerOptions: {
          preset: [
            'default',
            {
              discardComments: { removeAll: true },
              normalizeWhitespace: true,
              colormin: true,
              convertValues: true,
              discardDuplicates: true,
              discardEmpty: true,
              mergeRules: true,
              minifyFontValues: true,
              minifySelectors: true,
            },
          ],
        },
      }),
    ],
    // 模块连接优化
    concatenateModules: true,
    // 副作用标记
    sideEffects: false,
    // 使用确定性的模块ID
    moduleIds: 'deterministic',
    chunkIds: 'deterministic',
  },

  // 性能优化插件
  plugins: [
    // 环境变量定义
    new DefinePlugin({
      'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV),
      'process.env.REACT_APP_VERSION': JSON.stringify(process.env.npm_package_version),
      '__DEV__': process.env.NODE_ENV === 'development',
    }),

    // Gzip压缩
    new CompressionPlugin({
      algorithm: 'gzip',
      test: /\.(js|css|html|svg)$/,
      threshold: 8192,
      minRatio: 0.8,
      deleteOriginalAssets: false,
    }),

    // Brotli压缩（更好的压缩率）
    new CompressionPlugin({
      filename: '[path][base].br',
      algorithm: 'brotliCompress',
      test: /\.(js|css|html|svg)$/,
      compressionOptions: {
        level: 11,
      },
      threshold: 8192,
      minRatio: 0.8,
      deleteOriginalAssets: false,
    }),

    // 包分析器（仅在分析模式下启用）
    ...(process.env.ANALYZE === 'true' ? [
      new BundleAnalyzerPlugin({
        analyzerMode: 'static',
        openAnalyzer: false,
        reportFilename: 'bundle-report.html',
      }),
    ] : []),
  ],

  // 解析优化
  resolve: {
    // 模块查找优化
    modules: [
      path.resolve(__dirname, 'src'),
      'node_modules',
    ],
    // 扩展名优化
    extensions: ['.tsx', '.ts', '.jsx', '.js', '.json'],
    // 别名优化
    alias: {
      '@': path.resolve(__dirname, 'src'),
      // React优化
      'react': path.resolve(__dirname, 'node_modules/react'),
      'react-dom': path.resolve(__dirname, 'node_modules/react-dom'),
      // 减少包大小的别名
      'lodash': 'lodash-es',
      'moment': 'dayjs',
    },
    // 符号链接优化
    symlinks: false,
    // 缓存优化
    cache: true,
  },

  // 模块规则优化
  module: {
    rules: [
      // TypeScript/JavaScript优化
      {
        test: /\.(ts|tsx|js|jsx)$/,
        exclude: /node_modules/,
        use: [
          {
            loader: 'babel-loader',
            options: {
              cacheDirectory: true,
              cacheCompression: false,
              compact: process.env.NODE_ENV === 'production',
              presets: [
                ['@babel/preset-env', {
                  useBuiltIns: 'usage',
                  corejs: 3,
                  modules: false,
                }],
                ['@babel/preset-react', {
                  runtime: 'automatic',
                }],
                '@babel/preset-typescript',
              ],
              plugins: [
                // 按需导入
                ['import', {
                  libraryName: 'antd',
                  libraryDirectory: 'es',
                  style: true,
                }, 'antd'],
                ['import', {
                  libraryName: 'lodash',
                  libraryDirectory: '',
                  camel2DashComponentName: false,
                }, 'lodash'],
                // React优化
                '@babel/plugin-transform-runtime',
                // 开发环境热更新
                ...(process.env.NODE_ENV === 'development' ? [
                  'react-refresh/babel',
                ] : []),
              ],
            },
          },
        ],
      },

      // CSS优化
      {
        test: /\.css$/,
        use: [
          process.env.NODE_ENV === 'production' ? 'mini-css-extract-plugin/loader' : 'style-loader',
          {
            loader: 'css-loader',
            options: {
              importLoaders: 1,
              modules: {
                auto: true,
                localIdentName: process.env.NODE_ENV === 'production' 
                  ? '[hash:base64:8]' 
                  : '[name]__[local]--[hash:base64:5]',
              },
            },
          },
          {
            loader: 'postcss-loader',
            options: {
              postcssOptions: {
                plugins: [
                  'autoprefixer',
                  'cssnano',
                ],
              },
            },
          },
        ],
      },

      // 图片优化
      {
        test: /\.(png|jpe?g|gif|svg|webp)$/i,
        type: 'asset',
        parser: {
          dataUrlCondition: {
            maxSize: 8 * 1024, // 8KB以下内联
          },
        },
        generator: {
          filename: 'images/[name].[hash:8][ext]',
        },
        use: [
          {
            loader: 'image-webpack-loader',
            options: {
              mozjpeg: {
                progressive: true,
                quality: 80,
              },
              optipng: {
                enabled: false,
              },
              pngquant: {
                quality: [0.65, 0.90],
                speed: 4,
              },
              gifsicle: {
                interlaced: false,
              },
              webp: {
                quality: 80,
              },
            },
          },
        ],
      },

      // 字体优化
      {
        test: /\.(woff|woff2|eot|ttf|otf)$/i,
        type: 'asset/resource',
        generator: {
          filename: 'fonts/[name].[hash:8][ext]',
        },
      },
    ],
  },

  // 缓存优化
  cache: {
    type: 'filesystem',
    buildDependencies: {
      config: [__filename],
    },
    cacheDirectory: path.resolve(__dirname, 'node_modules/.cache/webpack'),
  },

  // 性能提示
  performance: {
    hints: process.env.NODE_ENV === 'production' ? 'warning' : false,
    maxEntrypointSize: 512000, // 500KB
    maxAssetSize: 512000, // 500KB
  },

  // 开发服务器优化
  devServer: {
    compress: true,
    hot: true,
    historyApiFallback: true,
    static: {
      directory: path.join(__dirname, 'public'),
    },
    client: {
      overlay: {
        errors: true,
        warnings: false,
      },
    },
  },

  // 统计信息优化
  stats: {
    colors: true,
    modules: false,
    children: false,
    chunks: false,
    chunkModules: false,
  },
};
