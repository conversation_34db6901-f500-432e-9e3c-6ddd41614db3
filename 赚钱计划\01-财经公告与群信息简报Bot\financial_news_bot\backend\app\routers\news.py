"""
新闻API路由
提供新闻数据的完整CRUD操作、搜索、统计等功能
"""
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc, func, text

from app.database import get_db
from app.dependencies.auth import get_current_active_user, get_optional_current_user
from app.dependencies.permissions import (
    require_permission,
    require_export_data,
    require_view_analytics,
    require_admin
)
from app.models.user import User
from app.models.news import News
from app.services.news_service import NewsService
from app.schemas.news import (
    NewsResponse, NewsListResponse, NewsCreate, NewsUpdate,
    NewsSearchParams, NewsStatistics, NewsSearchResponse
)
from app.services.data_processing_pipeline import DataProcessingPipeline
from app.services.search_service import SearchService
from app.services.cache_service import cache_service

router = APIRouter(prefix="/news", tags=["新闻管理"])

@router.get("/", response_model=NewsListResponse, summary="获取新闻列表")
async def get_news_list(
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(20, ge=1, le=100, description="每页数量"),
    source: Optional[str] = Query(None, description="新闻来源"),
    category: Optional[str] = Query(None, description="新闻分类"),
    start_date: Optional[datetime] = Query(None, description="开始时间"),
    end_date: Optional[datetime] = Query(None, description="结束时间"),
    importance_min: Optional[int] = Query(None, ge=0, le=100, description="最低重要性评分"),
    sort_by: str = Query("published_at", description="排序字段"),
    sort_order: str = Query("desc", regex="^(asc|desc)$", description="排序方向"),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permission("read_news"))
):
    """
    获取新闻列表，支持分页、过滤和排序
    """
    try:
        news_service = NewsService(db)
        
        # 构建查询条件
        filters = {}
        if source:
            filters['source'] = source
        if category:
            filters['category'] = category
        if start_date:
            filters['start_date'] = start_date
        if end_date:
            filters['end_date'] = end_date
        if importance_min is not None:
            filters['importance_min'] = importance_min
        
        # 获取新闻列表
        news_list, total = news_service.get_news_list(
            page=page,
            limit=limit,
            filters=filters,
            sort_by=sort_by,
            sort_order=sort_order
        )
        
        return NewsListResponse(
            items=[NewsResponse.from_orm(news) for news in news_list],
            total=total,
            page=page,
            limit=limit,
            pages=(total + limit - 1) // limit
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取新闻列表失败: {str(e)}"
        )

@router.get("/{news_id}", response_model=NewsResponse, summary="获取新闻详情")
async def get_news_detail(
    news_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permission("read_news"))
):
    """
    根据ID获取新闻详情
    """
    try:
        news_service = NewsService(db)
        news = news_service.get_news_by_id(news_id)
        
        if not news:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="新闻不存在"
            )
        
        return NewsResponse.from_orm(news)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取新闻详情失败: {str(e)}"
        )

@router.post("/", response_model=NewsResponse, summary="创建新闻")
async def create_news(
    news_data: NewsCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    创建新闻（仅管理员可用）
    """
    if current_user.role not in ["PRO", "ENTERPRISE"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有PRO和ENTERPRISE用户可以创建新闻"
        )
    
    try:
        news_service = NewsService(db)
        data_processor = DataProcessingPipeline()
        
        # 处理新闻数据
        processed_data = news_data.dict()
        
        # 生成摘要
        if processed_data.get('content') and not processed_data.get('summary'):
            summary = data_processor.generate_summary(processed_data['content'])
            processed_data['summary'] = summary
        
        # 提取实体
        if processed_data.get('content'):
            entities = data_processor.extract_entities(processed_data['content'])
            processed_data['entities'] = entities
        
        # 情感分析
        if processed_data.get('content'):
            sentiment = data_processor.analyze_sentiment(processed_data['content'])
            processed_data['sentiment'] = sentiment
        
        # 重要性评分
        if not processed_data.get('importance_score'):
            importance = data_processor.calculate_importance(
                processed_data.get('title', ''),
                processed_data.get('content', '')
            )
            processed_data['importance_score'] = importance
        
        # 创建新闻
        news = news_service.create_news(processed_data)
        
        return NewsResponse.from_orm(news)
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建新闻失败: {str(e)}"
        )

@router.put("/{news_id}", response_model=NewsResponse, summary="更新新闻")
async def update_news(
    news_id: int,
    news_data: NewsUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    更新新闻信息（仅管理员可用）
    """
    if current_user.role != "ENTERPRISE":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有ENTERPRISE用户可以更新新闻"
        )
    
    try:
        news_service = NewsService(db)
        
        # 检查新闻是否存在
        existing_news = news_service.get_news_by_id(news_id)
        if not existing_news:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="新闻不存在"
            )
        
        # 更新新闻
        updated_news = news_service.update_news(news_id, news_data.dict(exclude_unset=True))
        
        return NewsResponse.from_orm(updated_news)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新新闻失败: {str(e)}"
        )

@router.delete("/{news_id}", summary="删除新闻")
async def delete_news(
    news_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    删除新闻（仅管理员可用）
    """
    if current_user.role != "ENTERPRISE":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有ENTERPRISE用户可以删除新闻"
        )
    
    try:
        news_service = NewsService(db)
        
        # 检查新闻是否存在
        existing_news = news_service.get_news_by_id(news_id)
        if not existing_news:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="新闻不存在"
            )
        
        # 删除新闻
        news_service.delete_news(news_id)
        
        return {"message": "新闻删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除新闻失败: {str(e)}"
        )

@router.get("/search/", response_model=NewsSearchResponse, summary="搜索新闻")
async def search_news(
    q: str = Query(..., min_length=1, description="搜索关键词"),
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(20, ge=1, le=100, description="每页数量"),
    source: Optional[str] = Query(None, description="新闻来源"),
    category: Optional[str] = Query(None, description="新闻分类"),
    start_date: Optional[datetime] = Query(None, description="开始时间"),
    end_date: Optional[datetime] = Query(None, description="结束时间"),
    search_in: str = Query("all", regex="^(title|content|all)$", description="搜索范围"),
    sort_by: str = Query("relevance", description="排序字段"),
    sort_order: str = Query("desc", regex="^(asc|desc)$", description="排序方向"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    全文搜索新闻，支持标题、内容搜索
    """
    try:
        news_service = NewsService(db)

        # 构建搜索参数
        search_params = NewsSearchParams(
            query=q,
            page=page,
            limit=limit,
            source=source,
            category=category,
            start_date=start_date,
            end_date=end_date,
            search_in=search_in,
            sort_by=sort_by,
            sort_order=sort_order
        )

        # 执行搜索
        results, total = news_service.search_news(search_params)

        return NewsSearchResponse(
            items=[NewsResponse.from_orm(news) for news in results],
            total=total,
            page=page,
            limit=limit,
            pages=(total + limit - 1) // limit,
            query=q,
            search_time=datetime.now()
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"搜索新闻失败: {str(e)}"
        )

@router.get("/statistics/overview", response_model=NewsStatistics, summary="获取新闻统计概览")
async def get_news_statistics(
    days: int = Query(7, ge=1, le=365, description="统计天数"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取新闻统计数据概览
    """
    try:
        news_service = NewsService(db)

        # 计算时间范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        # 获取统计数据
        stats = news_service.get_news_statistics(start_date, end_date)

        return NewsStatistics(**stats)

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取统计数据失败: {str(e)}"
        )

@router.get("/statistics/by-source", summary="按来源统计新闻")
async def get_news_by_source(
    days: int = Query(7, ge=1, le=365, description="统计天数"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    按新闻来源统计数据
    """
    try:
        news_service = NewsService(db)

        # 计算时间范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        # 按来源统计
        stats = news_service.get_statistics_by_source(start_date, end_date)

        return {
            "period": f"{days}天",
            "start_date": start_date,
            "end_date": end_date,
            "statistics": stats
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取来源统计失败: {str(e)}"
        )

@router.get("/statistics/by-category", summary="按分类统计新闻")
async def get_news_by_category(
    days: int = Query(7, ge=1, le=365, description="统计天数"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    按新闻分类统计数据
    """
    try:
        news_service = NewsService(db)

        # 计算时间范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        # 按分类统计
        stats = news_service.get_statistics_by_category(start_date, end_date)

        return {
            "period": f"{days}天",
            "start_date": start_date,
            "end_date": end_date,
            "statistics": stats
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取分类统计失败: {str(e)}"
        )

@router.get("/statistics/timeline", summary="获取新闻时间线统计")
async def get_news_timeline(
    days: int = Query(7, ge=1, le=365, description="统计天数"),
    interval: str = Query("day", regex="^(hour|day|week)$", description="时间间隔"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取新闻发布时间线统计
    """
    try:
        news_service = NewsService(db)

        # 计算时间范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        # 获取时间线统计
        timeline = news_service.get_timeline_statistics(start_date, end_date, interval)

        return {
            "period": f"{days}天",
            "interval": interval,
            "start_date": start_date,
            "end_date": end_date,
            "timeline": timeline
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取时间线统计失败: {str(e)}"
        )

@router.get("/trending", summary="获取热门新闻")
async def get_trending_news(
    limit: int = Query(10, ge=1, le=50, description="返回数量"),
    hours: int = Query(24, ge=1, le=168, description="时间范围（小时）"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取热门/趋势新闻
    """
    try:
        news_service = NewsService(db)

        # 计算时间范围
        start_time = datetime.now() - timedelta(hours=hours)

        # 获取热门新闻
        trending_news = news_service.get_trending_news(start_time, limit)

        return {
            "period": f"{hours}小时",
            "count": len(trending_news),
            "news": [NewsResponse.from_orm(news) for news in trending_news]
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取热门新闻失败: {str(e)}"
        )

@router.get("/sources", summary="获取新闻来源列表")
async def get_news_sources(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取所有新闻来源列表
    """
    try:
        news_service = NewsService(db)
        sources = news_service.get_all_sources()

        return {
            "sources": sources,
            "count": len(sources)
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取新闻来源失败: {str(e)}"
        )

@router.get("/categories", summary="获取新闻分类列表")
async def get_news_categories(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取所有新闻分类列表
    """
    try:
        news_service = NewsService(db)
        categories = news_service.get_all_categories()

        return {
            "categories": categories,
            "count": len(categories)
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取新闻分类失败: {str(e)}"
        )

@router.post("/batch", summary="批量操作新闻")
async def batch_operations(
    operation: str = Query(..., regex="^(delete|update_category|update_importance)$", description="操作类型"),
    news_ids: List[int] = Query(..., description="新闻ID列表"),
    value: Optional[str] = Query(None, description="操作值"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    批量操作新闻（仅管理员可用）
    """
    if current_user.role != "ENTERPRISE":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有ENTERPRISE用户可以进行批量操作"
        )

    try:
        news_service = NewsService(db)

        # 执行批量操作
        result = news_service.batch_operations(operation, news_ids, value)

        return {
            "operation": operation,
            "affected_count": result,
            "news_ids": news_ids
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量操作失败: {str(e)}"
        )

@router.get("/search/enhanced", response_model=NewsSearchResponse, summary="增强搜索")
async def enhanced_search(
    q: str = Query(..., min_length=1, description="搜索关键词"),
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(20, ge=1, le=100, description="每页数量"),
    source: Optional[str] = Query(None, description="新闻来源"),
    category: Optional[str] = Query(None, description="新闻分类"),
    start_date: Optional[datetime] = Query(None, description="开始时间"),
    end_date: Optional[datetime] = Query(None, description="结束时间"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    增强的全文搜索，支持智能分词、相关性排序
    """
    try:
        search_service = SearchService(db)

        # 构建过滤条件
        filters = {}
        if source:
            filters['source'] = source
        if category:
            filters['category'] = category
        if start_date:
            filters['start_date'] = start_date
        if end_date:
            filters['end_date'] = end_date

        # 执行增强搜索
        results, total = search_service.enhanced_search(q, page, limit, filters)

        return NewsSearchResponse(
            items=[NewsResponse.from_orm(news) for news in results],
            total=total,
            page=page,
            limit=limit,
            pages=(total + limit - 1) // limit,
            query=q,
            search_time=datetime.now()
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"增强搜索失败: {str(e)}"
        )

@router.get("/search/suggestions", summary="搜索建议")
async def get_search_suggestions(
    q: str = Query(..., min_length=1, max_length=50, description="查询前缀"),
    limit: int = Query(10, ge=1, le=20, description="返回数量"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取搜索建议和自动补全
    """
    try:
        search_service = SearchService(db)
        suggestions = search_service.get_search_suggestions(q, limit)

        return {
            "query": q,
            "suggestions": suggestions,
            "count": len(suggestions)
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取搜索建议失败: {str(e)}"
        )

@router.get("/search/popular", summary="热门搜索")
async def get_popular_searches(
    days: int = Query(7, ge=1, le=30, description="统计天数"),
    limit: int = Query(10, ge=1, le=20, description="返回数量"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取热门搜索词
    """
    try:
        search_service = SearchService(db)
        popular_searches = search_service.get_popular_searches(days, limit)

        return {
            "period": f"{days}天",
            "popular_searches": popular_searches,
            "count": len(popular_searches)
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取热门搜索失败: {str(e)}"
        )

@router.get("/cache/stats", summary="获取缓存统计")
async def get_cache_stats(
    current_user: User = Depends(get_current_active_user)
):
    """
    获取缓存系统统计信息
    """
    if current_user.role not in ["PRO", "ENTERPRISE"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有PRO和ENTERPRISE用户可以查看缓存统计"
        )

    try:
        stats = cache_service.get_stats()
        return stats

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取缓存统计失败: {str(e)}"
        )

@router.delete("/cache/clear", summary="清除缓存")
async def clear_cache(
    pattern: str = Query("news:*", description="缓存键模式"),
    current_user: User = Depends(get_current_active_user)
):
    """
    清除指定模式的缓存
    """
    if current_user.role != "ENTERPRISE":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有ENTERPRISE用户可以清除缓存"
        )

    try:
        cleared_count = cache_service.clear_pattern(pattern)

        return {
            "message": f"已清除 {cleared_count} 个缓存项",
            "pattern": pattern,
            "cleared_count": cleared_count
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"清除缓存失败: {str(e)}"
        )
