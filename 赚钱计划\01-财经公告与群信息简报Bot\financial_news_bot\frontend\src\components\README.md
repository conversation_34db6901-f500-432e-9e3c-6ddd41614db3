# 组件结构说明

为了解决组件文件夹结构缺乏功能分组的问题，建议按照以下结构重新组织组件：

## 推荐的组件结构

```
src/components/
├── common/              # 通用组件
│   ├── Button/
│   ├── Input/
│   ├── Modal/
│   ├── Loading/
│   ├── Pagination/
│   └── Layout/
├── news/                # 新闻相关组件
│   ├── NewsList/
│   ├── NewsCard/
│   ├── NewsDetail/
│   ├── NewsFilter/
│   └── NewsSearch/
├── user/                # 用户相关组件
│   ├── UserProfile/
│   ├── UserSettings/
│   ├── LoginForm/
│   └── RegisterForm/
├── subscription/        # 订阅相关组件
│   ├── SubscriptionList/
│   ├── SubscriptionForm/
│   ├── SubscriptionCard/
│   └── SubscriptionSettings/
├── admin/               # 管理员组件
│   ├── AdminDashboard/
│   ├── UserManagement/
│   ├── SystemStats/
│   └── ContentModeration/
├── charts/              # 图表组件
│   ├── LineChart/
│   ├── BarChart/
│   ├── PieChart/
│   └── TrendChart/
└── forms/               # 表单组件
    ├── FormField/
    ├── FormValidation/
    ├── FormSubmit/
    └── FormLayout/
```

## 组件命名规范

### 1. 文件夹命名
- 使用 PascalCase（大驼峰）
- 每个组件一个文件夹
- 文件夹名与主组件名一致

### 2. 文件命名
```
ComponentName/
├── index.ts          # 导出文件
├── ComponentName.tsx # 主组件文件
├── ComponentName.test.tsx # 测试文件
├── ComponentName.stories.tsx # Storybook文件（可选）
├── ComponentName.module.css # 样式文件
└── types.ts          # 类型定义（如需要）
```

### 3. 组件分类原则

#### Common 组件
- 可在多个业务模块中复用
- 不包含业务逻辑
- 高度可配置

#### 业务组件
- 特定业务领域的组件
- 包含业务逻辑
- 可能依赖特定的数据结构

#### 页面组件
- 完整的页面级组件
- 通常放在 `src/pages/` 目录下
- 组合多个业务组件

## 重构建议

### 阶段1：创建新结构
1. 创建新的文件夹结构
2. 按业务域分类现有组件
3. 更新导入路径

### 阶段2：组件优化
1. 提取公共逻辑到 hooks
2. 统一组件接口设计
3. 添加 TypeScript 类型定义

### 阶段3：测试完善
1. 为每个组件添加单元测试
2. 添加集成测试
3. 配置 Storybook（可选）

## 导入路径配置

在 `tsconfig.json` 中配置路径映射：

```json
{
  "compilerOptions": {
    "baseUrl": "src",
    "paths": {
      "@/components/*": ["components/*"],
      "@/components/common/*": ["components/common/*"],
      "@/components/news/*": ["components/news/*"],
      "@/components/user/*": ["components/user/*"],
      "@/components/subscription/*": ["components/subscription/*"],
      "@/components/admin/*": ["components/admin/*"],
      "@/components/charts/*": ["components/charts/*"],
      "@/components/forms/*": ["components/forms/*"]
    }
  }
}
```

## 使用示例

```tsx
// 推荐的导入方式
import { Button } from '@/components/common/Button';
import { NewsList } from '@/components/news/NewsList';
import { UserProfile } from '@/components/user/UserProfile';

// 避免的导入方式
import { Button } from '../../../components/Button';
```

## 迁移检查清单

- [ ] 创建新的文件夹结构
- [ ] 移动现有组件到对应分类
- [ ] 更新所有导入路径
- [ ] 更新测试文件路径
- [ ] 验证构建无错误
- [ ] 更新文档和README
- [ ] 团队培训新的组织结构

## 维护原则

1. **单一职责**：每个组件只负责一个功能
2. **可复用性**：通用组件应该高度可配置
3. **一致性**：遵循统一的命名和结构规范
4. **可测试性**：每个组件都应该易于测试
5. **文档化**：重要组件应该有使用文档

通过这种结构化的组织方式，可以显著提高代码的可维护性和团队协作效率。
