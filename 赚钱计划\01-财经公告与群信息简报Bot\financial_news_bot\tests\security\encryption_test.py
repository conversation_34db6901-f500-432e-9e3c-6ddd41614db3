#!/usr/bin/env python3
"""
数据加密功能测试
验证数据库敏感数据加密存储、HTTPS传输加密、JWT token安全性、数据备份加密机制
"""
import os
import sys
import json
import ssl
import hashlib
import base64
import requests
import jwt
import time
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime, timedelta
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import argparse

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'backend'))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EncryptionTester:
    """加密功能测试器"""
    
    def __init__(self, base_url: str, database_url: str = None):
        self.base_url = base_url
        self.database_url = database_url
        self.test_results = []
        
        # 测试用户凭据
        self.test_user = {
            'email': '<EMAIL>',
            'password': 'TestPassword123!',
            'username': 'encryption_tester'
        }
    
    def test_https_encryption(self) -> Dict[str, Any]:
        """测试HTTPS传输加密"""
        logger.info("测试HTTPS传输加密...")
        
        results = {
            'test_name': 'HTTPS传输加密',
            'timestamp': datetime.now().isoformat(),
            'tests': []
        }
        
        # 测试HTTPS连接
        https_url = self.base_url.replace('http://', 'https://')
        
        try:
            # 测试SSL证书
            response = requests.get(f"{https_url}/health", timeout=10, verify=True)
            
            results['tests'].append({
                'name': 'HTTPS连接测试',
                'status': 'PASS' if response.status_code == 200 else 'FAIL',
                'details': f'状态码: {response.status_code}',
                'ssl_verified': True
            })
            
            # 检查SSL证书信息
            ssl_info = self._get_ssl_certificate_info(https_url)
            results['tests'].append({
                'name': 'SSL证书验证',
                'status': 'PASS' if ssl_info['valid'] else 'FAIL',
                'details': ssl_info
            })
            
        except requests.exceptions.SSLError as e:
            results['tests'].append({
                'name': 'HTTPS连接测试',
                'status': 'FAIL',
                'details': f'SSL错误: {str(e)}',
                'ssl_verified': False
            })
        except Exception as e:
            results['tests'].append({
                'name': 'HTTPS连接测试',
                'status': 'FAIL',
                'details': f'连接错误: {str(e)}',
                'ssl_verified': False
            })
        
        # 测试HTTP重定向到HTTPS
        try:
            http_response = requests.get(f"{self.base_url}/health", timeout=10, allow_redirects=False)
            
            if http_response.status_code in [301, 302, 307, 308]:
                location = http_response.headers.get('Location', '')
                https_redirect = location.startswith('https://')
                
                results['tests'].append({
                    'name': 'HTTP到HTTPS重定向',
                    'status': 'PASS' if https_redirect else 'FAIL',
                    'details': f'重定向到: {location}' if location else '无重定向'
                })
            else:
                results['tests'].append({
                    'name': 'HTTP到HTTPS重定向',
                    'status': 'FAIL',
                    'details': f'HTTP请求未重定向，状态码: {http_response.status_code}'
                })
                
        except Exception as e:
            results['tests'].append({
                'name': 'HTTP到HTTPS重定向',
                'status': 'ERROR',
                'details': f'测试失败: {str(e)}'
            })
        
        # 测试安全头
        security_headers = self._test_security_headers(https_url)
        results['tests'].append(security_headers)
        
        return results
    
    def _get_ssl_certificate_info(self, url: str) -> Dict[str, Any]:
        """获取SSL证书信息"""
        try:
            import socket
            from urllib.parse import urlparse
            
            parsed_url = urlparse(url)
            hostname = parsed_url.hostname
            port = parsed_url.port or 443
            
            context = ssl.create_default_context()
            
            with socket.create_connection((hostname, port), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                    cert = ssock.getpeercert()
                    
                    # 检查证书有效期
                    not_after = datetime.strptime(cert['notAfter'], '%b %d %H:%M:%S %Y %Z')
                    days_until_expiry = (not_after - datetime.now()).days
                    
                    return {
                        'valid': True,
                        'subject': dict(x[0] for x in cert['subject']),
                        'issuer': dict(x[0] for x in cert['issuer']),
                        'version': cert['version'],
                        'serial_number': cert['serialNumber'],
                        'not_after': cert['notAfter'],
                        'days_until_expiry': days_until_expiry,
                        'san': cert.get('subjectAltName', [])
                    }
        except Exception as e:
            return {
                'valid': False,
                'error': str(e)
            }
    
    def _test_security_headers(self, url: str) -> Dict[str, Any]:
        """测试安全头"""
        try:
            response = requests.get(f"{url}/health", timeout=10)
            headers = response.headers
            
            required_headers = {
                'Strict-Transport-Security': 'HSTS头',
                'X-Content-Type-Options': '内容类型选项',
                'X-Frame-Options': '框架选项',
                'X-XSS-Protection': 'XSS保护',
                'Content-Security-Policy': '内容安全策略'
            }
            
            missing_headers = []
            present_headers = {}
            
            for header, description in required_headers.items():
                if header in headers:
                    present_headers[header] = headers[header]
                else:
                    missing_headers.append(f"{header} ({description})")
            
            return {
                'name': '安全头检查',
                'status': 'PASS' if len(missing_headers) == 0 else 'PARTIAL' if len(present_headers) > 0 else 'FAIL',
                'details': {
                    'present_headers': present_headers,
                    'missing_headers': missing_headers
                }
            }
            
        except Exception as e:
            return {
                'name': '安全头检查',
                'status': 'ERROR',
                'details': f'测试失败: {str(e)}'
            }
    
    def test_jwt_security(self) -> Dict[str, Any]:
        """测试JWT token安全性"""
        logger.info("测试JWT token安全性...")
        
        results = {
            'test_name': 'JWT Token安全性',
            'timestamp': datetime.now().isoformat(),
            'tests': []
        }
        
        # 首先注册测试用户
        try:
            register_response = requests.post(
                f"{self.base_url}/api/v1/auth/register",
                json=self.test_user,
                timeout=10
            )
            
            if register_response.status_code not in [200, 201, 409]:  # 409表示用户已存在
                logger.warning(f"用户注册失败: {register_response.status_code}")
        except Exception as e:
            logger.warning(f"用户注册异常: {e}")
        
        # 登录获取JWT token
        try:
            login_response = requests.post(
                f"{self.base_url}/api/v1/auth/login",
                json={
                    'email': self.test_user['email'],
                    'password': self.test_user['password']
                },
                timeout=10
            )
            
            if login_response.status_code == 200:
                token_data = login_response.json()
                access_token = token_data.get('access_token')
                
                if access_token:
                    # 测试JWT token结构
                    jwt_tests = self._analyze_jwt_token(access_token)
                    results['tests'].extend(jwt_tests)
                    
                    # 测试token验证
                    auth_tests = self._test_token_authentication(access_token)
                    results['tests'].extend(auth_tests)
                else:
                    results['tests'].append({
                        'name': 'JWT Token获取',
                        'status': 'FAIL',
                        'details': '响应中未找到access_token'
                    })
            else:
                results['tests'].append({
                    'name': 'JWT Token获取',
                    'status': 'FAIL',
                    'details': f'登录失败，状态码: {login_response.status_code}'
                })
                
        except Exception as e:
            results['tests'].append({
                'name': 'JWT Token获取',
                'status': 'ERROR',
                'details': f'登录请求失败: {str(e)}'
            })
        
        return results
    
    def _analyze_jwt_token(self, token: str) -> List[Dict[str, Any]]:
        """分析JWT token结构和安全性"""
        tests = []
        
        try:
            # 解码JWT header和payload（不验证签名）
            header = jwt.get_unverified_header(token)
            payload = jwt.decode(token, options={"verify_signature": False})
            
            # 检查算法
            algorithm = header.get('alg')
            if algorithm in ['HS256', 'RS256', 'ES256']:
                tests.append({
                    'name': 'JWT算法安全性',
                    'status': 'PASS',
                    'details': f'使用安全算法: {algorithm}'
                })
            elif algorithm == 'none':
                tests.append({
                    'name': 'JWT算法安全性',
                    'status': 'FAIL',
                    'details': '使用不安全的none算法'
                })
            else:
                tests.append({
                    'name': 'JWT算法安全性',
                    'status': 'WARN',
                    'details': f'使用算法: {algorithm}，请确认安全性'
                })
            
            # 检查过期时间
            exp = payload.get('exp')
            if exp:
                exp_time = datetime.fromtimestamp(exp)
                now = datetime.now()
                time_diff = exp_time - now
                
                if time_diff.total_seconds() > 0:
                    if time_diff.days <= 7:  # 7天内过期
                        tests.append({
                            'name': 'JWT过期时间',
                            'status': 'PASS',
                            'details': f'Token将在{time_diff}后过期'
                        })
                    else:
                        tests.append({
                            'name': 'JWT过期时间',
                            'status': 'WARN',
                            'details': f'Token过期时间过长: {time_diff}'
                        })
                else:
                    tests.append({
                        'name': 'JWT过期时间',
                        'status': 'FAIL',
                        'details': 'Token已过期'
                    })
            else:
                tests.append({
                    'name': 'JWT过期时间',
                    'status': 'FAIL',
                    'details': 'Token未设置过期时间'
                })
            
            # 检查必要字段
            required_fields = ['sub', 'iat']
            missing_fields = [field for field in required_fields if field not in payload]
            
            if not missing_fields:
                tests.append({
                    'name': 'JWT必要字段',
                    'status': 'PASS',
                    'details': '包含所有必要字段'
                })
            else:
                tests.append({
                    'name': 'JWT必要字段',
                    'status': 'FAIL',
                    'details': f'缺少字段: {missing_fields}'
                })
            
            # 检查敏感信息泄露
            sensitive_fields = ['password', 'secret', 'key']
            leaked_fields = [field for field in sensitive_fields if field in payload]
            
            if not leaked_fields:
                tests.append({
                    'name': 'JWT敏感信息检查',
                    'status': 'PASS',
                    'details': '未发现敏感信息泄露'
                })
            else:
                tests.append({
                    'name': 'JWT敏感信息检查',
                    'status': 'FAIL',
                    'details': f'发现敏感字段: {leaked_fields}'
                })
                
        except Exception as e:
            tests.append({
                'name': 'JWT Token解析',
                'status': 'ERROR',
                'details': f'解析失败: {str(e)}'
            })
        
        return tests
    
    def _test_token_authentication(self, token: str) -> List[Dict[str, Any]]:
        """测试token认证"""
        tests = []
        
        # 测试有效token
        try:
            response = requests.get(
                f"{self.base_url}/api/v1/auth/me",
                headers={'Authorization': f'Bearer {token}'},
                timeout=10
            )
            
            if response.status_code == 200:
                tests.append({
                    'name': '有效Token认证',
                    'status': 'PASS',
                    'details': '有效token认证成功'
                })
            else:
                tests.append({
                    'name': '有效Token认证',
                    'status': 'FAIL',
                    'details': f'有效token认证失败，状态码: {response.status_code}'
                })
                
        except Exception as e:
            tests.append({
                'name': '有效Token认证',
                'status': 'ERROR',
                'details': f'认证请求失败: {str(e)}'
            })
        
        # 测试无效token
        try:
            invalid_token = token[:-10] + 'invalid123'  # 修改token使其无效
            response = requests.get(
                f"{self.base_url}/api/v1/auth/me",
                headers={'Authorization': f'Bearer {invalid_token}'},
                timeout=10
            )
            
            if response.status_code == 401:
                tests.append({
                    'name': '无效Token拒绝',
                    'status': 'PASS',
                    'details': '无效token被正确拒绝'
                })
            else:
                tests.append({
                    'name': '无效Token拒绝',
                    'status': 'FAIL',
                    'details': f'无效token未被拒绝，状态码: {response.status_code}'
                })
                
        except Exception as e:
            tests.append({
                'name': '无效Token拒绝',
                'status': 'ERROR',
                'details': f'测试失败: {str(e)}'
            })
        
        # 测试过期token（如果可能）
        try:
            # 创建一个已过期的token进行测试
            expired_payload = {
                'sub': 'test_user',
                'exp': int(time.time()) - 3600,  # 1小时前过期
                'iat': int(time.time()) - 7200   # 2小时前签发
            }
            
            # 注意：这里需要知道JWT密钥才能创建有效的过期token
            # 在实际测试中，可能需要等待真实token过期
            tests.append({
                'name': '过期Token处理',
                'status': 'SKIP',
                'details': '需要等待真实token过期或获取JWT密钥进行测试'
            })
            
        except Exception as e:
            tests.append({
                'name': '过期Token处理',
                'status': 'ERROR',
                'details': f'测试失败: {str(e)}'
            })
        
        return tests
    
    def test_password_encryption(self) -> Dict[str, Any]:
        """测试密码加密存储"""
        logger.info("测试密码加密存储...")
        
        results = {
            'test_name': '密码加密存储',
            'timestamp': datetime.now().isoformat(),
            'tests': []
        }
        
        if not self.database_url:
            results['tests'].append({
                'name': '数据库连接',
                'status': 'SKIP',
                'details': '未提供数据库连接信息'
            })
            return results
        
        try:
            import pymysql
            from urllib.parse import urlparse
            
            # 解析数据库URL
            parsed = urlparse(self.database_url)
            
            connection = pymysql.connect(
                host=parsed.hostname,
                port=parsed.port or 3306,
                user=parsed.username,
                password=parsed.password,
                database=parsed.path.lstrip('/'),
                charset='utf8mb4'
            )
            
            with connection.cursor() as cursor:
                # 查询用户表中的密码字段
                cursor.execute("SELECT email, password FROM users LIMIT 5")
                users = cursor.fetchall()
                
                if users:
                    # 检查密码是否被加密
                    plain_text_passwords = []
                    hashed_passwords = []
                    
                    for email, password in users:
                        if password:
                            # 检查是否为明文密码（简单启发式检查）
                            if len(password) < 20 or password.isalnum():
                                plain_text_passwords.append(email)
                            else:
                                hashed_passwords.append(email)
                    
                    if not plain_text_passwords:
                        results['tests'].append({
                            'name': '密码加密检查',
                            'status': 'PASS',
                            'details': f'检查了{len(users)}个用户，所有密码都已加密'
                        })
                    else:
                        results['tests'].append({
                            'name': '密码加密检查',
                            'status': 'FAIL',
                            'details': f'发现{len(plain_text_passwords)}个可能的明文密码'
                        })
                else:
                    results['tests'].append({
                        'name': '密码加密检查',
                        'status': 'SKIP',
                        'details': '用户表中没有数据'
                    })
            
            connection.close()
            
        except Exception as e:
            results['tests'].append({
                'name': '密码加密检查',
                'status': 'ERROR',
                'details': f'数据库查询失败: {str(e)}'
            })
        
        return results
    
    def test_data_backup_encryption(self) -> Dict[str, Any]:
        """测试数据备份加密机制"""
        logger.info("测试数据备份加密机制...")
        
        results = {
            'test_name': '数据备份加密',
            'timestamp': datetime.now().isoformat(),
            'tests': []
        }
        
        # 检查备份文件是否存在
        backup_paths = [
            '/var/backups/',
            './backups/',
            '../backups/',
            './data/backups/'
        ]
        
        backup_files_found = []
        
        for backup_path in backup_paths:
            if os.path.exists(backup_path):
                try:
                    files = os.listdir(backup_path)
                    backup_files = [f for f in files if f.endswith(('.sql', '.dump', '.bak', '.backup'))]
                    backup_files_found.extend([(backup_path, f) for f in backup_files])
                except Exception as e:
                    logger.warning(f"无法访问备份目录 {backup_path}: {e}")
        
        if backup_files_found:
            encrypted_backups = 0
            unencrypted_backups = 0
            
            for backup_path, backup_file in backup_files_found[:5]:  # 只检查前5个文件
                full_path = os.path.join(backup_path, backup_file)
                
                try:
                    # 检查文件是否加密（简单的启发式检查）
                    with open(full_path, 'rb') as f:
                        header = f.read(100)
                        
                        # 检查是否为明文SQL
                        if b'CREATE TABLE' in header or b'INSERT INTO' in header:
                            unencrypted_backups += 1
                        else:
                            encrypted_backups += 1
                            
                except Exception as e:
                    logger.warning(f"无法读取备份文件 {full_path}: {e}")
            
            if unencrypted_backups == 0:
                results['tests'].append({
                    'name': '备份文件加密检查',
                    'status': 'PASS',
                    'details': f'检查了{len(backup_files_found)}个备份文件，都已加密'
                })
            else:
                results['tests'].append({
                    'name': '备份文件加密检查',
                    'status': 'FAIL',
                    'details': f'发现{unencrypted_backups}个未加密的备份文件'
                })
        else:
            results['tests'].append({
                'name': '备份文件加密检查',
                'status': 'SKIP',
                'details': '未找到备份文件'
            })
        
        # 测试备份加密功能（如果有相关API）
        try:
            response = requests.post(
                f"{self.base_url}/api/v1/admin/backup",
                json={'encrypt': True},
                timeout=30
            )
            
            if response.status_code == 200:
                results['tests'].append({
                    'name': '备份加密API',
                    'status': 'PASS',
                    'details': '备份加密API可用'
                })
            elif response.status_code == 404:
                results['tests'].append({
                    'name': '备份加密API',
                    'status': 'SKIP',
                    'details': '备份API不存在'
                })
            else:
                results['tests'].append({
                    'name': '备份加密API',
                    'status': 'FAIL',
                    'details': f'备份API返回错误: {response.status_code}'
                })
                
        except Exception as e:
            results['tests'].append({
                'name': '备份加密API',
                'status': 'ERROR',
                'details': f'API测试失败: {str(e)}'
            })
        
        return results
    
    def run_all_encryption_tests(self) -> Dict[str, Any]:
        """运行所有加密测试"""
        logger.info("开始运行所有加密功能测试...")
        
        all_results = {
            'timestamp': datetime.now().isoformat(),
            'test_suite': '数据加密功能测试',
            'tests': []
        }
        
        # 运行各项测试
        all_results['tests'].append(self.test_https_encryption())
        all_results['tests'].append(self.test_jwt_security())
        all_results['tests'].append(self.test_password_encryption())
        all_results['tests'].append(self.test_data_backup_encryption())
        
        # 计算总体结果
        total_tests = 0
        passed_tests = 0
        failed_tests = 0
        
        for test_group in all_results['tests']:
            for test in test_group.get('tests', []):
                total_tests += 1
                if test['status'] == 'PASS':
                    passed_tests += 1
                elif test['status'] == 'FAIL':
                    failed_tests += 1
        
        all_results['summary'] = {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': failed_tests,
            'success_rate': (passed_tests / total_tests * 100) if total_tests > 0 else 0
        }
        
        return all_results
    
    def save_results(self, results: Dict[str, Any], output_file: str):
        """保存测试结果"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            logger.info(f"加密测试结果已保存: {output_file}")
        except Exception as e:
            logger.error(f"保存结果失败: {e}")


def main():
    parser = argparse.ArgumentParser(description='数据加密功能测试')
    parser.add_argument('--base-url', default='http://localhost:8000', help='目标服务URL')
    parser.add_argument('--database-url', help='数据库连接URL')
    parser.add_argument('--output', default='encryption_test_results.json', help='输出文件')
    
    args = parser.parse_args()
    
    tester = EncryptionTester(args.base_url, args.database_url)
    
    try:
        results = tester.run_all_encryption_tests()
        tester.save_results(results, args.output)
        
        # 输出摘要
        summary = results['summary']
        print(f"\n=== 数据加密功能测试摘要 ===")
        print(f"总测试数: {summary['total_tests']}")
        print(f"通过测试: {summary['passed_tests']}")
        print(f"失败测试: {summary['failed_tests']}")
        print(f"成功率: {summary['success_rate']:.1f}%")
        
        # 显示失败的测试
        failed_tests = []
        for test_group in results['tests']:
            for test in test_group.get('tests', []):
                if test['status'] == 'FAIL':
                    failed_tests.append(f"{test_group['test_name']} - {test['name']}")
        
        if failed_tests:
            print(f"\n❌ 失败的测试:")
            for i, test in enumerate(failed_tests, 1):
                print(f"{i}. {test}")
        else:
            print(f"\n✅ 所有测试通过!")
        
        print(f"\n📋 详细结果: {args.output}")
        
    except Exception as e:
        logger.error(f"加密测试失败: {e}")
        return 1
    
    return 0


if __name__ == '__main__':
    exit(main())
