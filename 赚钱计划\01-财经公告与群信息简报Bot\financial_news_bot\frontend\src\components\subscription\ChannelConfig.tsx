import React, { useState } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Switch,
  Select,
  TimePicker,
  Checkbox,
  Space,
  Typography,
  Row,
  Col,
  Alert,
  message,
  Modal,
  Spin,
  Tag,
  Divider,
} from 'antd';
import {
  MailOutlined,
  WechatOutlined,
  ApiOutlined,
  SendOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';

const { Text, Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;

interface ChannelConfig {
  email: {
    enabled: boolean;
    address: string;
    template: string;
  };
  wechat: {
    enabled: boolean;
    webhook_url: string;
    mention_users: string[];
  };
  feishu: {
    enabled: boolean;
    webhook_url: string;
    secret: string;
  };
  webhook: {
    enabled: boolean;
    url: string;
    method: 'POST' | 'GET';
    headers: Record<string, string>;
    auth_token: string;
  };
}

interface ChannelConfigProps {
  config: ChannelConfig;
  onChange: (config: ChannelConfig) => void;
}

const ChannelConfigComponent: React.FC<ChannelConfigProps> = ({ config, onChange }) => {
  const [form] = Form.useForm();
  const [testingChannel, setTestingChannel] = useState<string | null>(null);
  const [testResults, setTestResults] = useState<Record<string, boolean>>({});

  const handleConfigChange = (channel: keyof ChannelConfig, field: string, value: any) => {
    const newConfig = {
      ...config,
      [channel]: {
        ...config[channel],
        [field]: value,
      },
    };
    onChange(newConfig);
  };

  const handleTestChannel = async (channel: keyof ChannelConfig) => {
    setTestingChannel(channel);
    try {
      // 模拟测试API调用
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 模拟测试结果
      const success = Math.random() > 0.3; // 70% 成功率
      setTestResults(prev => ({ ...prev, [channel]: success }));
      
      if (success) {
        message.success(`${getChannelName(channel)}测试成功`);
      } else {
        message.error(`${getChannelName(channel)}测试失败，请检查配置`);
      }
    } catch (error) {
      message.error('测试失败');
      setTestResults(prev => ({ ...prev, [channel]: false }));
    } finally {
      setTestingChannel(null);
    }
  };

  const getChannelName = (channel: keyof ChannelConfig) => {
    const names = {
      email: '邮箱',
      wechat: '企业微信',
      feishu: '飞书',
      webhook: 'Webhook',
    };
    return names[channel];
  };

  const getChannelIcon = (channel: keyof ChannelConfig) => {
    const icons = {
      email: <MailOutlined />,
      wechat: <WechatOutlined />,
      feishu: <ApiOutlined />,
      webhook: <ApiOutlined />,
    };
    return icons[channel];
  };

  const renderEmailConfig = () => (
    <Card
      title={
        <Space>
          <MailOutlined />
          邮箱推送
          <Switch
            checked={config.email.enabled}
            onChange={(checked) => handleConfigChange('email', 'enabled', checked)}
          />
        </Space>
      }
      size="small"
      extra={
        config.email.enabled && (
          <Button
            size="small"
            icon={<SendOutlined />}
            loading={testingChannel === 'email'}
            onClick={() => handleTestChannel('email')}
          >
            测试
          </Button>
        )
      }
    >
      {config.email.enabled && (
        <Space direction="vertical" style={{ width: '100%' }}>
          <Form.Item label="邮箱地址" style={{ marginBottom: '8px' }}>
            <Input
              value={config.email.address}
              onChange={(e) => handleConfigChange('email', 'address', e.target.value)}
              placeholder="请输入接收邮箱地址"
            />
          </Form.Item>
          <Form.Item label="邮件模板" style={{ marginBottom: '8px' }}>
            <Select
              value={config.email.template}
              onChange={(value) => handleConfigChange('email', 'template', value)}
              placeholder="选择邮件模板"
            >
              <Option value="simple">简洁模板</Option>
              <Option value="detailed">详细模板</Option>
              <Option value="digest">摘要模板</Option>
            </Select>
          </Form.Item>
          {testResults.email !== undefined && (
            <Alert
              type={testResults.email ? 'success' : 'error'}
              message={testResults.email ? '邮箱配置测试成功' : '邮箱配置测试失败'}
              showIcon
              closable
            />
          )}
        </Space>
      )}
    </Card>
  );

  const renderWechatConfig = () => (
    <Card
      title={
        <Space>
          <WechatOutlined />
          企业微信
          <Switch
            checked={config.wechat.enabled}
            onChange={(checked) => handleConfigChange('wechat', 'enabled', checked)}
          />
        </Space>
      }
      size="small"
      extra={
        config.wechat.enabled && (
          <Button
            size="small"
            icon={<SendOutlined />}
            loading={testingChannel === 'wechat'}
            onClick={() => handleTestChannel('wechat')}
          >
            测试
          </Button>
        )
      }
    >
      {config.wechat.enabled && (
        <Space direction="vertical" style={{ width: '100%' }}>
          <Form.Item label="Webhook URL" style={{ marginBottom: '8px' }}>
            <Input
              value={config.wechat.webhook_url}
              onChange={(e) => handleConfigChange('wechat', 'webhook_url', e.target.value)}
              placeholder="请输入企业微信机器人Webhook地址"
            />
          </Form.Item>
          <Form.Item label="@用户" style={{ marginBottom: '8px' }}>
            <Select
              mode="tags"
              value={config.wechat.mention_users}
              onChange={(value) => handleConfigChange('wechat', 'mention_users', value)}
              placeholder="输入要@的用户ID"
              style={{ width: '100%' }}
            />
          </Form.Item>
          {testResults.wechat !== undefined && (
            <Alert
              type={testResults.wechat ? 'success' : 'error'}
              message={testResults.wechat ? '企业微信配置测试成功' : '企业微信配置测试失败'}
              showIcon
              closable
            />
          )}
        </Space>
      )}
    </Card>
  );

  const renderFeishuConfig = () => (
    <Card
      title={
        <Space>
          <ApiOutlined />
          飞书
          <Switch
            checked={config.feishu.enabled}
            onChange={(checked) => handleConfigChange('feishu', 'enabled', checked)}
          />
        </Space>
      }
      size="small"
      extra={
        config.feishu.enabled && (
          <Button
            size="small"
            icon={<SendOutlined />}
            loading={testingChannel === 'feishu'}
            onClick={() => handleTestChannel('feishu')}
          >
            测试
          </Button>
        )
      }
    >
      {config.feishu.enabled && (
        <Space direction="vertical" style={{ width: '100%' }}>
          <Form.Item label="Webhook URL" style={{ marginBottom: '8px' }}>
            <Input
              value={config.feishu.webhook_url}
              onChange={(e) => handleConfigChange('feishu', 'webhook_url', e.target.value)}
              placeholder="请输入飞书机器人Webhook地址"
            />
          </Form.Item>
          <Form.Item label="签名密钥" style={{ marginBottom: '8px' }}>
            <Input.Password
              value={config.feishu.secret}
              onChange={(e) => handleConfigChange('feishu', 'secret', e.target.value)}
              placeholder="请输入签名密钥（可选）"
            />
          </Form.Item>
          {testResults.feishu !== undefined && (
            <Alert
              type={testResults.feishu ? 'success' : 'error'}
              message={testResults.feishu ? '飞书配置测试成功' : '飞书配置测试失败'}
              showIcon
              closable
            />
          )}
        </Space>
      )}
    </Card>
  );

  const renderWebhookConfig = () => (
    <Card
      title={
        <Space>
          <ApiOutlined />
          自定义Webhook
          <Switch
            checked={config.webhook.enabled}
            onChange={(checked) => handleConfigChange('webhook', 'enabled', checked)}
          />
        </Space>
      }
      size="small"
      extra={
        config.webhook.enabled && (
          <Button
            size="small"
            icon={<SendOutlined />}
            loading={testingChannel === 'webhook'}
            onClick={() => handleTestChannel('webhook')}
          >
            测试
          </Button>
        )
      }
    >
      {config.webhook.enabled && (
        <Space direction="vertical" style={{ width: '100%' }}>
          <Row gutter={8}>
            <Col span={16}>
              <Form.Item label="Webhook URL" style={{ marginBottom: '8px' }}>
                <Input
                  value={config.webhook.url}
                  onChange={(e) => handleConfigChange('webhook', 'url', e.target.value)}
                  placeholder="请输入Webhook地址"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="请求方法" style={{ marginBottom: '8px' }}>
                <Select
                  value={config.webhook.method}
                  onChange={(value) => handleConfigChange('webhook', 'method', value)}
                >
                  <Option value="POST">POST</Option>
                  <Option value="GET">GET</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Form.Item label="认证Token" style={{ marginBottom: '8px' }}>
            <Input.Password
              value={config.webhook.auth_token}
              onChange={(e) => handleConfigChange('webhook', 'auth_token', e.target.value)}
              placeholder="请输入认证Token（可选）"
            />
          </Form.Item>
          {testResults.webhook !== undefined && (
            <Alert
              type={testResults.webhook ? 'success' : 'error'}
              message={testResults.webhook ? 'Webhook配置测试成功' : 'Webhook配置测试失败'}
              showIcon
              closable
            />
          )}
        </Space>
      )}
    </Card>
  );

  const enabledChannels = Object.entries(config).filter(([_, channelConfig]) => channelConfig.enabled);

  return (
    <div>
      <Title level={4}>推送渠道配置</Title>
      
      <Alert
        message="推送渠道说明"
        description="您可以配置多个推送渠道，系统会按照优先级依次推送。建议至少配置一个主要渠道和一个备用渠道。"
        type="info"
        showIcon
        style={{ marginBottom: '16px' }}
      />

      <Space direction="vertical" style={{ width: '100%' }} size="middle">
        {renderEmailConfig()}
        {renderWechatConfig()}
        {renderFeishuConfig()}
        {renderWebhookConfig()}
      </Space>

      {enabledChannels.length > 0 && (
        <Card title="已启用的推送渠道" size="small" style={{ marginTop: '16px' }}>
          <Space wrap>
            {enabledChannels.map(([channel, _]) => (
              <Tag
                key={channel}
                icon={getChannelIcon(channel as keyof ChannelConfig)}
                color="blue"
              >
                {getChannelName(channel as keyof ChannelConfig)}
              </Tag>
            ))}
          </Space>
          <Divider />
          <Text type="secondary">
            推送优先级：邮箱 → 企业微信 → 飞书 → Webhook
          </Text>
        </Card>
      )}
    </div>
  );
};

export default ChannelConfigComponent;
