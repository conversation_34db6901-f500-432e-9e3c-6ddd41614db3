"""
数据处理管道路由
提供数据处理管道的API接口
"""
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, Union
from datetime import datetime

from app.services.data_processing_pipeline import data_pipeline, ProcessingStage
from app.dependencies.auth import get_current_user
from app.models.user import User

router = APIRouter(prefix="/api/v1/data-processing", tags=["数据处理管道"])

class TextProcessingRequest(BaseModel):
    """文本处理请求模型"""
    text: str = Field(..., description="要处理的文本", max_length=50000)
    stages: List[str] = Field(default=[], description="要执行的处理阶段")

class NewsProcessingRequest(BaseModel):
    """新闻处理请求模型"""
    title: str = Field(..., description="新闻标题", max_length=500)
    content: str = Field(..., description="新闻内容", max_length=50000)
    stages: List[str] = Field(default=[], description="要执行的处理阶段")

class BatchProcessingRequest(BaseModel):
    """批量处理请求模型"""
    items: List[Union[str, Dict[str, str]]] = Field(..., description="要处理的项目列表", max_items=100)
    stages: List[str] = Field(default=[], description="要执行的处理阶段")

class ProcessingResponse(BaseModel):
    """处理响应模型"""
    success: bool
    input: Any
    stages: Dict[str, Any]
    errors: List[str]
    metadata: Dict[str, Any]

class BatchProcessingResponse(BaseModel):
    """批量处理响应模型"""
    results: List[ProcessingResponse]
    total_count: int
    success_count: int
    failed_count: int
    processing_time: str

def validate_stages(stages: List[str]) -> List[ProcessingStage]:
    """验证并转换处理阶段"""
    if not stages:
        return None  # 使用默认流程
    
    valid_stages = []
    available_stages = [stage.value for stage in ProcessingStage]
    
    for stage_str in stages:
        if stage_str not in available_stages:
            raise HTTPException(
                status_code=400, 
                detail=f"不支持的处理阶段: {stage_str}. 可用阶段: {available_stages}"
            )
        valid_stages.append(ProcessingStage(stage_str))
    
    return valid_stages

@router.post("/text", response_model=ProcessingResponse)
async def process_text(
    request: TextProcessingRequest,
    current_user: User = Depends(get_current_user)
):
    """
    处理单个文本
    
    对提交的文本执行指定的处理流程
    """
    try:
        stages = validate_stages(request.stages)
        result = data_pipeline.process_text(request.text, stages)
        
        return ProcessingResponse(
            success=result["success"],
            input=result["input"],
            stages=result["stages"],
            errors=result["errors"],
            metadata=result["metadata"]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文本处理异常: {str(e)}")

@router.post("/news", response_model=ProcessingResponse)
async def process_news(
    request: NewsProcessingRequest,
    current_user: User = Depends(get_current_user)
):
    """
    处理新闻文章
    
    对新闻标题和内容执行完整的处理流程
    """
    try:
        stages = validate_stages(request.stages)
        result = data_pipeline.process_news_article(request.title, request.content, stages)
        
        return ProcessingResponse(
            success=result["success"],
            input=result["input"],
            stages=result["stages"],
            errors=result["errors"],
            metadata=result["metadata"]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"新闻处理异常: {str(e)}")

@router.post("/batch", response_model=BatchProcessingResponse)
async def process_batch(
    request: BatchProcessingRequest,
    current_user: User = Depends(get_current_user)
):
    """
    批量处理
    
    批量处理多个文本或新闻项目
    """
    try:
        if len(request.items) > 100:
            raise HTTPException(status_code=400, detail="批量处理最多支持100个项目")
        
        start_time = datetime.now()
        stages = validate_stages(request.stages)
        results = data_pipeline.process_batch(request.items, stages)
        end_time = datetime.now()
        
        # 统计结果
        success_count = sum(1 for r in results if r["success"])
        failed_count = len(results) - success_count
        processing_time = str(end_time - start_time)
        
        # 转换结果格式
        response_results = []
        for result in results:
            response_results.append(ProcessingResponse(
                success=result["success"],
                input=result["input"],
                stages=result["stages"],
                errors=result["errors"],
                metadata=result.get("metadata", {})
            ))
        
        return BatchProcessingResponse(
            results=response_results,
            total_count=len(results),
            success_count=success_count,
            failed_count=failed_count,
            processing_time=processing_time
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量处理异常: {str(e)}")

@router.get("/stages")
async def get_available_stages(current_user: User = Depends(get_current_user)):
    """
    获取可用的处理阶段
    
    返回所有可用的数据处理阶段及其描述
    """
    try:
        stages_info = []
        
        for stage in ProcessingStage:
            processor_info = data_pipeline.get_processor_info(stage)
            stages_info.append({
                "stage": stage.value,
                "name": processor_info["class_name"],
                "description": processor_info["description"]
            })
        
        return {
            "stages": stages_info,
            "total_count": len(stages_info),
            "default_pipeline": [stage.value for stage in data_pipeline.default_pipeline]
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取处理阶段异常: {str(e)}")

@router.get("/pipeline/info")
async def get_pipeline_info(current_user: User = Depends(get_current_user)):
    """
    获取数据处理管道信息
    
    返回管道的配置和状态信息
    """
    try:
        return {
            "pipeline_name": "财经新闻数据处理管道",
            "version": "2.0.0",
            "total_processors": len(data_pipeline.processors),
            "available_stages": data_pipeline.get_available_stages(),
            "default_pipeline": [stage.value for stage in data_pipeline.default_pipeline],
            "features": [
                "文本清理和标准化",
                "实体提取（公司、股票代码等）",
                "情感分析",
                "重要性评分",
                "内容分类",
                "重复检测"
            ]
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取管道信息异常: {str(e)}")

@router.post("/test")
async def test_pipeline(
    test_text: str = "测试文本：某公司发布重大资产重组公告，股价上涨5%。",
    stages: List[str] = [],
    current_user: User = Depends(get_current_user)
):
    """
    测试数据处理管道
    
    使用测试数据验证管道功能
    """
    try:
        validated_stages = validate_stages(stages) if stages else None
        
        # 测试文本处理
        text_result = data_pipeline.process_text(test_text, validated_stages)
        
        # 测试新闻处理
        news_result = data_pipeline.process_news_article(
            title="测试新闻标题",
            content=test_text,
            stages=validated_stages
        )
        
        return {
            "test_results": {
                "text_processing": text_result,
                "news_processing": news_result
            },
            "pipeline_status": "healthy" if text_result["success"] and news_result["success"] else "unhealthy",
            "test_timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        return {
            "test_results": {},
            "pipeline_status": "error",
            "error": str(e),
            "test_timestamp": datetime.now().isoformat()
        }

@router.get("/health")
async def health_check():
    """
    健康检查
    
    检查数据处理管道是否正常运行
    """
    try:
        # 执行简单的处理测试
        test_result = data_pipeline.process_text("健康检查测试文本")
        
        if test_result["success"]:
            return {
                "status": "healthy",
                "processors_count": len(data_pipeline.processors),
                "last_check": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=503, detail="管道处理测试失败")
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"健康检查失败: {str(e)}")

@router.get("/stats")
async def get_processing_stats(current_user: User = Depends(get_current_user)):
    """
    获取处理统计信息
    
    返回数据处理的统计信息（如果有的话）
    """
    try:
        # 这里可以添加实际的统计逻辑
        # 目前返回基本信息
        return {
            "total_processors": len(data_pipeline.processors),
            "available_stages": len(ProcessingStage),
            "pipeline_version": "2.0.0",
            "last_updated": datetime.now().isoformat(),
            "supported_formats": ["text", "news_article", "batch"],
            "max_batch_size": 100,
            "max_text_length": 50000
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息异常: {str(e)}")
