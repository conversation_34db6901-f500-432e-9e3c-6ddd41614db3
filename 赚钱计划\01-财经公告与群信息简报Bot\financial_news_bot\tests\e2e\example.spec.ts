/**
 * 财经新闻Bot端到端测试
 * 使用Playwright进行完整的用户流程测试
 */

import { test, expect, Page } from '@playwright/test';

// 测试配置
const BASE_URL = process.env.BASE_URL || 'http://localhost:80';
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:8000';

// 测试用户数据
const TEST_USER = {
  username: `test_user_${Date.now()}`,
  email: `test_${Date.now()}@example.com`,
  password: 'Test123456!',
  fullName: '测试用户'
};

// 页面对象模式
class LoginPage {
  constructor(private page: Page) {}

  async goto() {
    await this.page.goto(`${BASE_URL}/auth/login`);
  }

  async login(username: string, password: string) {
    await this.page.fill('[data-testid="username-input"]', username);
    await this.page.fill('[data-testid="password-input"]', password);
    await this.page.click('[data-testid="login-button"]');
  }

  async expectLoginSuccess() {
    await expect(this.page).toHaveURL(`${BASE_URL}/dashboard`);
  }
}

class RegisterPage {
  constructor(private page: Page) {}

  async goto() {
    await this.page.goto(`${BASE_URL}/auth/register`);
  }

  async register(userData: typeof TEST_USER) {
    await this.page.fill('[data-testid="username-input"]', userData.username);
    await this.page.fill('[data-testid="email-input"]', userData.email);
    await this.page.fill('[data-testid="password-input"]', userData.password);
    await this.page.fill('[data-testid="confirm-password-input"]', userData.password);
    await this.page.fill('[data-testid="fullname-input"]', userData.fullName);
    await this.page.click('[data-testid="register-button"]');
  }

  async expectRegisterSuccess() {
    await expect(this.page.locator('.ant-message-success')).toBeVisible();
  }
}

class DashboardPage {
  constructor(private page: Page) {}

  async expectDashboardLoaded() {
    await expect(this.page.locator('[data-testid="dashboard-title"]')).toBeVisible();
    await expect(this.page.locator('[data-testid="stats-cards"]')).toBeVisible();
  }

  async navigateToSubscriptions() {
    await this.page.click('[data-testid="nav-subscriptions"]');
  }

  async navigateToNews() {
    await this.page.click('[data-testid="nav-news"]');
  }
}

class SubscriptionPage {
  constructor(private page: Page) {}

  async createSubscription(name: string, keywords: string[]) {
    await this.page.click('[data-testid="create-subscription-button"]');
    await this.page.fill('[data-testid="subscription-name-input"]', name);
    
    // 添加关键词
    for (const keyword of keywords) {
      await this.page.fill('[data-testid="keyword-input"]', keyword);
      await this.page.press('[data-testid="keyword-input"]', 'Enter');
    }
    
    await this.page.click('[data-testid="save-subscription-button"]');
  }

  async expectSubscriptionCreated(name: string) {
    await expect(this.page.locator(`text=${name}`)).toBeVisible();
  }
}

class NewsPage {
  constructor(private page: Page) {}

  async searchNews(keyword: string) {
    await this.page.fill('[data-testid="news-search-input"]', keyword);
    await this.page.press('[data-testid="news-search-input"]', 'Enter');
  }

  async expectSearchResults() {
    await expect(this.page.locator('[data-testid="news-list"]')).toBeVisible();
  }

  async clickFirstNews() {
    await this.page.click('[data-testid="news-item"]:first-child');
  }

  async expectNewsDetail() {
    await expect(this.page.locator('[data-testid="news-title"]')).toBeVisible();
    await expect(this.page.locator('[data-testid="news-content"]')).toBeVisible();
  }
}

// 测试套件
test.describe('财经新闻Bot端到端测试', () => {
  test.beforeEach(async ({ page }) => {
    // 设置测试环境
    await page.setViewportSize({ width: 1280, height: 720 });
  });

  test('用户注册流程', async ({ page }) => {
    const registerPage = new RegisterPage(page);
    
    await registerPage.goto();
    await registerPage.register(TEST_USER);
    await registerPage.expectRegisterSuccess();
  });

  test('用户登录流程', async ({ page }) => {
    const loginPage = new LoginPage(page);
    const dashboardPage = new DashboardPage(page);
    
    await loginPage.goto();
    await loginPage.login('demo_user', 'demo123456');
    await loginPage.expectLoginSuccess();
    await dashboardPage.expectDashboardLoaded();
  });

  test('完整订阅创建流程', async ({ page }) => {
    const loginPage = new LoginPage(page);
    const dashboardPage = new DashboardPage(page);
    const subscriptionPage = new SubscriptionPage(page);
    
    // 登录
    await loginPage.goto();
    await loginPage.login('demo_user', 'demo123456');
    await dashboardPage.expectDashboardLoaded();
    
    // 创建订阅
    await dashboardPage.navigateToSubscriptions();
    await subscriptionPage.createSubscription('测试订阅', ['财经', '科技']);
    await subscriptionPage.expectSubscriptionCreated('测试订阅');
  });

  test('新闻浏览和搜索流程', async ({ page }) => {
    const loginPage = new LoginPage(page);
    const dashboardPage = new DashboardPage(page);
    const newsPage = new NewsPage(page);
    
    // 登录
    await loginPage.goto();
    await loginPage.login('demo_user', 'demo123456');
    await dashboardPage.expectDashboardLoaded();
    
    // 浏览新闻
    await dashboardPage.navigateToNews();
    await newsPage.searchNews('央行');
    await newsPage.expectSearchResults();
    
    // 查看新闻详情
    await newsPage.clickFirstNews();
    await newsPage.expectNewsDetail();
  });

  test('响应式设计测试', async ({ page }) => {
    const loginPage = new LoginPage(page);
    
    // 测试移动端视图
    await page.setViewportSize({ width: 375, height: 667 });
    await loginPage.goto();
    
    // 检查移动端布局
    await expect(page.locator('[data-testid="mobile-menu-button"]')).toBeVisible();
    
    // 测试平板视图
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible();
    
    // 测试桌面视图
    await page.setViewportSize({ width: 1280, height: 720 });
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible();
  });

  test('性能测试', async ({ page }) => {
    // 监控页面加载性能
    const startTime = Date.now();
    
    await page.goto(BASE_URL);
    
    // 等待页面完全加载
    await page.waitForLoadState('networkidle');
    
    const loadTime = Date.now() - startTime;
    
    // 页面加载时间应小于3秒
    expect(loadTime).toBeLessThan(3000);
    
    // 检查核心Web指标
    const metrics = await page.evaluate(() => {
      return new Promise((resolve) => {
        new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lcp = entries.find(entry => entry.entryType === 'largest-contentful-paint');
          const fid = entries.find(entry => entry.entryType === 'first-input');
          
          resolve({
            lcp: lcp?.startTime || 0,
            fid: fid?.processingStart - fid?.startTime || 0
          });
        }).observe({ entryTypes: ['largest-contentful-paint', 'first-input'] });
        
        // 超时处理
        setTimeout(() => resolve({ lcp: 0, fid: 0 }), 5000);
      });
    });
    
    console.log('性能指标:', metrics);
  });

  test('错误处理测试', async ({ page }) => {
    // 测试网络错误处理
    await page.route('**/api/v1/news', route => route.abort());
    
    await page.goto(`${BASE_URL}/news`);
    
    // 检查错误提示
    await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="retry-button"]')).toBeVisible();
  });

  test('可访问性测试', async ({ page }) => {
    await page.goto(BASE_URL);
    
    // 检查页面标题
    await expect(page).toHaveTitle(/财经新闻Bot/);
    
    // 检查主要导航的可访问性
    const navigation = page.locator('nav[role="navigation"]');
    await expect(navigation).toBeVisible();
    
    // 检查表单标签
    await page.goto(`${BASE_URL}/auth/login`);
    const usernameInput = page.locator('[data-testid="username-input"]');
    await expect(usernameInput).toHaveAttribute('aria-label');
    
    // 检查键盘导航
    await page.keyboard.press('Tab');
    await expect(usernameInput).toBeFocused();
  });

  test('多浏览器兼容性', async ({ browserName, page }) => {
    await page.goto(BASE_URL);
    
    // 检查基本功能在不同浏览器中的表现
    await expect(page.locator('[data-testid="app-header"]')).toBeVisible();
    
    // 检查CSS支持
    const headerStyles = await page.locator('[data-testid="app-header"]').evaluate(el => {
      const styles = window.getComputedStyle(el);
      return {
        display: styles.display,
        flexDirection: styles.flexDirection
      };
    });
    
    expect(headerStyles.display).toBe('flex');
    
    console.log(`浏览器 ${browserName} 兼容性测试通过`);
  });
});

// 测试钩子
test.afterEach(async ({ page }, testInfo) => {
  // 测试失败时截图
  if (testInfo.status !== testInfo.expectedStatus) {
    const screenshot = await page.screenshot();
    await testInfo.attach('screenshot', { body: screenshot, contentType: 'image/png' });
  }
});

test.afterAll(async () => {
  // 清理测试数据
  console.log('清理测试数据...');
});
