#!/usr/bin/env python3
"""
最终集成测试脚本
验证所有Bug修复和优化效果
"""
import asyncio
import aiohttp
import time
import json
import logging
from typing import Dict, List, Any

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FinalIntegrationTest:
    """最终集成测试"""
    
    def __init__(self):
        self.base_urls = {
            "security": "http://localhost:8001",
            "compliance": "http://localhost:8002",
            "backend": "http://localhost:8000"
        }
        self.session = None
        self.test_results = {}
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30))
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_performance_optimization(self) -> Dict[str, Any]:
        """测试性能优化效果"""
        logger.info("🚀 测试性能优化...")
        
        results = {}
        
        # 测试CPU监控性能（应该是非阻塞的）
        start_time = time.time()
        try:
            async with self.session.post(
                f"{self.base_urls['security']}/api/v1/monitoring/system",
                json={"include_cpu": True, "include_memory": True}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    response_time = time.time() - start_time
                    results["cpu_monitoring"] = {
                        "success": True,
                        "response_time": response_time,
                        "fast_response": response_time < 2.0,  # 应该小于2秒
                        "cpu_usage": data.get("metrics", {}).get("cpu_usage")
                    }
                else:
                    results["cpu_monitoring"] = {"success": False, "status": response.status}
        except Exception as e:
            results["cpu_monitoring"] = {"success": False, "error": str(e)}
        
        # 测试合规检查性能
        start_time = time.time()
        try:
            test_content = "这是一条测试内容，包含投资建议：建议买入某股票，预期收益率20%"
            async with self.session.post(
                f"{self.base_urls['compliance']}/api/v1/compliance/check/enhanced",
                json={
                    "content": test_content,
                    "check_sensitive_words": True,
                    "check_investment_advice": True,
                    "check_financial_promise": True
                }
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    response_time = time.time() - start_time
                    results["compliance_check"] = {
                        "success": True,
                        "response_time": response_time,
                        "fast_response": response_time < 3.0,
                        "violations_detected": len(data.get("violations", []))
                    }
                else:
                    results["compliance_check"] = {"success": False, "status": response.status}
        except Exception as e:
            results["compliance_check"] = {"success": False, "error": str(e)}
        
        return results
    
    async def test_error_handling(self) -> Dict[str, Any]:
        """测试错误处理和降级机制"""
        logger.info("🛡️ 测试错误处理...")
        
        results = {}
        
        # 测试无效请求的错误处理
        try:
            async with self.session.post(
                f"{self.base_urls['compliance']}/api/v1/compliance/check/enhanced",
                json={"invalid_field": "test"}  # 缺少必需字段
            ) as response:
                results["invalid_request"] = {
                    "status_code": response.status,
                    "proper_error_handling": response.status == 422,  # 应该返回422
                    "response": await response.text()
                }
        except Exception as e:
            results["invalid_request"] = {"error": str(e)}
        
        # 测试大内容处理
        try:
            large_content = "测试内容 " * 10000  # 大约100KB的内容
            async with self.session.post(
                f"{self.base_urls['compliance']}/api/v1/privacy/check",
                json={"content": large_content, "data_type": "text"}
            ) as response:
                results["large_content"] = {
                    "status_code": response.status,
                    "handles_large_content": response.status in [200, 413],
                    "content_size": len(large_content)
                }
        except Exception as e:
            results["large_content"] = {"error": str(e)}
        
        return results
    
    async def test_data_authenticity(self) -> Dict[str, Any]:
        """测试数据真实性"""
        logger.info("🔍 测试数据真实性...")
        
        results = {}
        
        # 测试PII检测的真实性
        try:
            test_data = "用户信息：手机号13812345678，邮箱****************，身份证110101199001011234"
            async with self.session.post(
                f"{self.base_urls['compliance']}/api/v1/privacy/check",
                json={"content": test_data, "data_type": "user_data", "mask_pii": True}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    results["pii_detection"] = {
                        "success": True,
                        "pii_detected": data.get("has_pii", False),
                        "pii_types": data.get("pii_types", []),
                        "masked_content": data.get("masked_content", ""),
                        "real_detection": len(data.get("pii_types", [])) > 0
                    }
                else:
                    results["pii_detection"] = {"success": False, "status": response.status}
        except Exception as e:
            results["pii_detection"] = {"success": False, "error": str(e)}
        
        # 测试投资建议检测
        try:
            investment_content = "强烈建议买入腾讯股票，目标价500元，预期涨幅30%"
            async with self.session.post(
                f"{self.base_urls['compliance']}/api/v1/compliance/check/enhanced",
                json={"content": investment_content, "check_investment_advice": True}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    violations = data.get("violations", [])
                    investment_violations = [v for v in violations if v.get("type") == "investment_advice"]
                    results["investment_detection"] = {
                        "success": True,
                        "violations_detected": len(investment_violations) > 0,
                        "total_violations": len(violations),
                        "risk_level": data.get("risk_level")
                    }
                else:
                    results["investment_detection"] = {"success": False, "status": response.status}
        except Exception as e:
            results["investment_detection"] = {"success": False, "error": str(e)}
        
        return results
    
    async def test_monitoring_metrics(self) -> Dict[str, Any]:
        """测试监控指标的准确性"""
        logger.info("📊 测试监控指标...")
        
        results = {}
        
        # 测试系统指标
        try:
            async with self.session.post(
                f"{self.base_urls['security']}/api/v1/monitoring/system",
                json={
                    "include_cpu": True,
                    "include_memory": True,
                    "include_disk": True,
                    "include_network": True
                }
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    metrics = data.get("metrics", {})
                    results["system_metrics"] = {
                        "success": True,
                        "has_cpu": "cpu_usage" in metrics,
                        "has_memory": "memory_usage" in metrics,
                        "has_disk": "disk_usage" in metrics,
                        "has_network": "network_bytes_sent" in metrics,
                        "cpu_reasonable": 0 <= metrics.get("cpu_usage", -1) <= 100,
                        "memory_reasonable": 0 <= metrics.get("memory_usage", -1) <= 100
                    }
                else:
                    results["system_metrics"] = {"success": False, "status": response.status}
        except Exception as e:
            results["system_metrics"] = {"success": False, "error": str(e)}
        
        # 测试告警规则
        try:
            async with self.session.get(f"{self.base_urls['security']}/api/v1/monitoring/alerts") as response:
                if response.status == 200:
                    data = await response.json()
                    results["alert_system"] = {
                        "success": True,
                        "has_alerts": "alerts" in data,
                        "alert_count": data.get("count", 0),
                        "alerts_structure_valid": isinstance(data.get("alerts", []), list)
                    }
                else:
                    results["alert_system"] = {"success": False, "status": response.status}
        except Exception as e:
            results["alert_system"] = {"success": False, "error": str(e)}
        
        return results
    
    async def run_final_test(self) -> Dict[str, Any]:
        """运行最终测试"""
        logger.info("🎯 开始最终集成测试...")
        
        # 等待服务启动
        await asyncio.sleep(5)
        
        results = {
            "performance": await self.test_performance_optimization(),
            "error_handling": await self.test_error_handling(),
            "data_authenticity": await self.test_data_authenticity(),
            "monitoring": await self.test_monitoring_metrics()
        }
        
        return results
    
    def generate_report(self, results: Dict[str, Any]):
        """生成测试报告"""
        logger.info("📋 最终测试报告:")
        logger.info("=" * 60)
        
        # 性能测试
        perf = results.get("performance", {})
        logger.info("🚀 性能优化测试:")
        if perf.get("cpu_monitoring", {}).get("fast_response"):
            logger.info("  ✅ CPU监控响应时间优化成功")
        else:
            logger.warning("  ⚠️ CPU监控响应时间需要优化")
        
        if perf.get("compliance_check", {}).get("fast_response"):
            logger.info("  ✅ 合规检查响应时间良好")
        else:
            logger.warning("  ⚠️ 合规检查响应时间需要优化")
        
        # 错误处理测试
        error = results.get("error_handling", {})
        logger.info("🛡️ 错误处理测试:")
        if error.get("invalid_request", {}).get("proper_error_handling"):
            logger.info("  ✅ 无效请求错误处理正确")
        else:
            logger.warning("  ⚠️ 无效请求错误处理需要改进")
        
        # 数据真实性测试
        data = results.get("data_authenticity", {})
        logger.info("🔍 数据真实性测试:")
        if data.get("pii_detection", {}).get("real_detection"):
            logger.info("  ✅ PII检测功能真实有效")
        else:
            logger.warning("  ⚠️ PII检测功能需要验证")
        
        if data.get("investment_detection", {}).get("violations_detected"):
            logger.info("  ✅ 投资建议检测功能正常")
        else:
            logger.warning("  ⚠️ 投资建议检测功能需要验证")
        
        # 监控指标测试
        monitor = results.get("monitoring", {})
        logger.info("📊 监控指标测试:")
        if monitor.get("system_metrics", {}).get("cpu_reasonable"):
            logger.info("  ✅ 系统指标数据合理")
        else:
            logger.warning("  ⚠️ 系统指标数据需要验证")
        
        # 计算总体成功率
        total_tests = 0
        passed_tests = 0
        
        for category, tests in results.items():
            for test_name, test_result in tests.items():
                total_tests += 1
                if test_result.get("success", False):
                    passed_tests += 1
        
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        logger.info(f"📈 总体测试成功率: {success_rate:.1f}% ({passed_tests}/{total_tests})")
        
        if success_rate >= 80:
            logger.info("🎉 系统整体状态良好！")
        else:
            logger.warning("⚠️ 系统需要进一步优化")

async def main():
    """主函数"""
    async with FinalIntegrationTest() as test_suite:
        results = await test_suite.run_final_test()
        test_suite.generate_report(results)
        
        # 保存详细结果
        with open("final_test_results.json", "w", encoding="utf-8") as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        logger.info("📄 详细结果已保存到 final_test_results.json")

if __name__ == "__main__":
    asyncio.run(main())
