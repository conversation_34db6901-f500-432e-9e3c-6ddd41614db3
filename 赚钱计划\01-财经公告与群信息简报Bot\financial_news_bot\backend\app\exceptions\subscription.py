from fastapi import HTTPException, status


class SubscriptionException(HTTPException):
    """订阅相关异常基类"""
    pass


class SubscriptionNotFoundError(SubscriptionException):
    """订阅不存在异常"""
    def __init__(self, subscription_id: int):
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到ID为 {subscription_id} 的订阅"
        )


class SubscriptionAccessDeniedError(SubscriptionException):
    """订阅访问权限异常"""
    def __init__(self, subscription_id: int):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"无权访问订阅 {subscription_id}"
        )


class SubscriptionLimitExceededError(SubscriptionException):
    """订阅数量限制异常"""
    def __init__(self, limit: int):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"超出订阅数量限制。最大允许数量：{limit}"
        )


class InvalidSubscriptionConfigError(SubscriptionException):
    """无效订阅配置异常"""
    def __init__(self, message: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"无效的订阅配置：{message}"
        )


class SubscriptionValidationError(SubscriptionException):
    """订阅验证异常"""
    def __init__(self, field: str, message: str):
        super().__init__(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=f"字段 '{field}' 验证错误：{message}"
        )


class ChannelConfigurationError(SubscriptionException):
    """推送渠道配置异常"""
    def __init__(self, channel: str, message: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"推送渠道 '{channel}' 配置错误：{message}"
        )


class SubscriptionStatusError(SubscriptionException):
    """订阅状态异常"""
    def __init__(self, current_status: str, target_status: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"无法将订阅状态从 '{current_status}' 更改为 '{target_status}'"
        )
