/**
 * 错误监控和用户反馈收集系统
 * 自动收集前端错误、API调用失败、用户操作路径等信息
 */

import { message } from 'antd';

// 错误类型定义
export interface ErrorInfo {
  id: string;
  type: 'javascript' | 'api' | 'network' | 'user' | 'performance';
  message: string;
  stack?: string;
  url?: string;
  line?: number;
  column?: number;
  timestamp: number;
  userAgent: string;
  userId?: string;
  sessionId: string;
  context?: Record<string, any>;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

// 用户操作记录
export interface UserAction {
  id: string;
  type: 'click' | 'input' | 'navigation' | 'scroll' | 'form_submit';
  element?: string;
  value?: string;
  url: string;
  timestamp: number;
  sessionId: string;
  userId?: string;
}

// 性能指标
export interface PerformanceMetric {
  id: string;
  type: 'page_load' | 'api_response' | 'render' | 'interaction';
  name: string;
  value: number;
  unit: 'ms' | 'bytes' | 'count';
  timestamp: number;
  url: string;
  sessionId: string;
}

// 用户反馈
export interface UserFeedback {
  id: string;
  type: 'bug' | 'feature' | 'improvement' | 'complaint' | 'praise';
  title: string;
  description: string;
  rating?: number;
  email?: string;
  screenshot?: string;
  timestamp: number;
  userId?: string;
  sessionId: string;
  context: {
    url: string;
    userAgent: string;
    viewport: { width: number; height: number };
    errors: ErrorInfo[];
    actions: UserAction[];
  };
}

class ErrorMonitor {
  private sessionId: string;
  private userId?: string;
  private errors: ErrorInfo[] = [];
  private actions: UserAction[] = [];
  private metrics: PerformanceMetric[] = [];
  private maxStorageSize = 100; // 最大存储条数
  private reportEndpoint = '/api/v1/monitoring/errors';
  private isEnabled = true;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.init();
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateId(): string {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private init() {
    if (!this.isEnabled) return;

    // 监听JavaScript错误
    window.addEventListener('error', this.handleJavaScriptError.bind(this));
    window.addEventListener('unhandledrejection', this.handlePromiseRejection.bind(this));

    // 监听用户操作
    this.initUserActionTracking();

    // 监听性能指标
    this.initPerformanceTracking();

    // 定期上报数据
    this.startReporting();
  }

  private handleJavaScriptError(event: ErrorEvent) {
    const error: ErrorInfo = {
      id: this.generateId(),
      type: 'javascript',
      message: event.message,
      stack: event.error?.stack,
      url: event.filename,
      line: event.lineno,
      column: event.colno,
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      userId: this.userId,
      sessionId: this.sessionId,
      severity: this.determineSeverity(event.message),
    };

    this.recordError(error);
  }

  private handlePromiseRejection(event: PromiseRejectionEvent) {
    const error: ErrorInfo = {
      id: this.generateId(),
      type: 'javascript',
      message: `Unhandled Promise Rejection: ${event.reason}`,
      stack: event.reason?.stack,
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      userId: this.userId,
      sessionId: this.sessionId,
      severity: 'high',
    };

    this.recordError(error);
  }

  private initUserActionTracking() {
    // 点击事件
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      const action: UserAction = {
        id: this.generateId(),
        type: 'click',
        element: this.getElementSelector(target),
        url: window.location.href,
        timestamp: Date.now(),
        sessionId: this.sessionId,
        userId: this.userId,
      };
      this.recordAction(action);
    });

    // 表单提交
    document.addEventListener('submit', (event) => {
      const target = event.target as HTMLFormElement;
      const action: UserAction = {
        id: this.generateId(),
        type: 'form_submit',
        element: this.getElementSelector(target),
        url: window.location.href,
        timestamp: Date.now(),
        sessionId: this.sessionId,
        userId: this.userId,
      };
      this.recordAction(action);
    });

    // 页面导航
    let lastUrl = window.location.href;
    const observer = new MutationObserver(() => {
      if (window.location.href !== lastUrl) {
        const action: UserAction = {
          id: this.generateId(),
          type: 'navigation',
          url: window.location.href,
          timestamp: Date.now(),
          sessionId: this.sessionId,
          userId: this.userId,
        };
        this.recordAction(action);
        lastUrl = window.location.href;
      }
    });

    observer.observe(document.body, { childList: true, subtree: true });
  }

  private initPerformanceTracking() {
    // 页面加载性能
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        if (navigation) {
          this.recordMetric({
            id: this.generateId(),
            type: 'page_load',
            name: 'page_load_time',
            value: navigation.loadEventEnd - navigation.fetchStart,
            unit: 'ms',
            timestamp: Date.now(),
            url: window.location.href,
            sessionId: this.sessionId,
          });
        }
      }, 0);
    });

    // 监听长任务
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          list.getEntries().forEach((entry) => {
            if (entry.duration > 50) { // 长任务阈值
              this.recordMetric({
                id: this.generateId(),
                type: 'performance',
                name: 'long_task',
                value: entry.duration,
                unit: 'ms',
                timestamp: Date.now(),
                url: window.location.href,
                sessionId: this.sessionId,
              });
            }
          });
        });
        observer.observe({ entryTypes: ['longtask'] });
      } catch (e) {
        // 浏览器不支持
      }
    }
  }

  private getElementSelector(element: HTMLElement): string {
    if (element.id) {
      return `#${element.id}`;
    }
    
    if (element.className) {
      return `.${element.className.split(' ')[0]}`;
    }
    
    return element.tagName.toLowerCase();
  }

  private determineSeverity(message: string): ErrorInfo['severity'] {
    const criticalKeywords = ['cannot read property', 'is not a function', 'network error'];
    const highKeywords = ['failed to fetch', 'timeout', 'permission denied'];
    const mediumKeywords = ['warning', 'deprecated'];

    const lowerMessage = message.toLowerCase();
    
    if (criticalKeywords.some(keyword => lowerMessage.includes(keyword))) {
      return 'critical';
    }
    
    if (highKeywords.some(keyword => lowerMessage.includes(keyword))) {
      return 'high';
    }
    
    if (mediumKeywords.some(keyword => lowerMessage.includes(keyword))) {
      return 'medium';
    }
    
    return 'low';
  }

  private recordError(error: ErrorInfo) {
    this.errors.push(error);
    
    // 限制存储大小
    if (this.errors.length > this.maxStorageSize) {
      this.errors = this.errors.slice(-this.maxStorageSize);
    }

    // 严重错误立即上报
    if (error.severity === 'critical') {
      this.reportImmediately([error]);
    }

    console.error('错误记录:', error);
  }

  private recordAction(action: UserAction) {
    this.actions.push(action);
    
    // 限制存储大小
    if (this.actions.length > this.maxStorageSize) {
      this.actions = this.actions.slice(-this.maxStorageSize);
    }
  }

  private recordMetric(metric: PerformanceMetric) {
    this.metrics.push(metric);
    
    // 限制存储大小
    if (this.metrics.length > this.maxStorageSize) {
      this.metrics = this.metrics.slice(-this.maxStorageSize);
    }
  }

  private startReporting() {
    // 每5分钟上报一次
    setInterval(() => {
      this.reportData();
    }, 5 * 60 * 1000);

    // 页面卸载时上报
    window.addEventListener('beforeunload', () => {
      this.reportData(true);
    });
  }

  private async reportData(isSync = false) {
    if (this.errors.length === 0 && this.metrics.length === 0) {
      return;
    }

    const data = {
      sessionId: this.sessionId,
      userId: this.userId,
      errors: this.errors,
      actions: this.actions.slice(-20), // 只发送最近20个操作
      metrics: this.metrics,
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
    };

    try {
      if (isSync && navigator.sendBeacon) {
        // 使用sendBeacon确保数据发送
        navigator.sendBeacon(this.reportEndpoint, JSON.stringify(data));
      } else {
        await fetch(this.reportEndpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data),
        });
      }

      // 清空已上报的数据
      this.errors = [];
      this.metrics = [];
    } catch (error) {
      console.error('错误上报失败:', error);
    }
  }

  private async reportImmediately(errors: ErrorInfo[]) {
    const data = {
      sessionId: this.sessionId,
      userId: this.userId,
      errors,
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
    };

    try {
      await fetch(this.reportEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
    } catch (error) {
      console.error('紧急错误上报失败:', error);
    }
  }

  // 公共方法
  public setUserId(userId: string) {
    this.userId = userId;
  }

  public recordApiError(url: string, status: number, message: string, context?: any) {
    const error: ErrorInfo = {
      id: this.generateId(),
      type: 'api',
      message: `API Error: ${status} - ${message}`,
      url,
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      userId: this.userId,
      sessionId: this.sessionId,
      context,
      severity: status >= 500 ? 'high' : 'medium',
    };

    this.recordError(error);
  }

  public recordUserFeedback(feedback: Omit<UserFeedback, 'id' | 'timestamp' | 'sessionId' | 'context'>) {
    const fullFeedback: UserFeedback = {
      ...feedback,
      id: this.generateId(),
      timestamp: Date.now(),
      sessionId: this.sessionId,
      context: {
        url: window.location.href,
        userAgent: navigator.userAgent,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight,
        },
        errors: this.errors.slice(-5), // 最近5个错误
        actions: this.actions.slice(-10), // 最近10个操作
      },
    };

    // 发送反馈
    this.sendFeedback(fullFeedback);
  }

  private async sendFeedback(feedback: UserFeedback) {
    try {
      await fetch('/api/v1/feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(feedback),
      });

      message.success('反馈提交成功，感谢您的建议！');
    } catch (error) {
      message.error('反馈提交失败，请稍后重试');
      console.error('反馈提交失败:', error);
    }
  }

  public getRecentErrors(): ErrorInfo[] {
    return this.errors.slice(-10);
  }

  public getRecentActions(): UserAction[] {
    return this.actions.slice(-20);
  }

  public enable() {
    this.isEnabled = true;
  }

  public disable() {
    this.isEnabled = false;
  }
}

// 全局错误监控实例
export const errorMonitor = new ErrorMonitor();

// 便捷方法
export const reportError = (message: string, context?: any) => {
  const error: ErrorInfo = {
    id: errorMonitor['generateId'](),
    type: 'user',
    message,
    timestamp: Date.now(),
    userAgent: navigator.userAgent,
    sessionId: errorMonitor['sessionId'],
    context,
    severity: 'medium',
  };

  errorMonitor['recordError'](error);
};

export const reportApiError = (url: string, status: number, message: string, context?: any) => {
  errorMonitor.recordApiError(url, status, message, context);
};

export const submitFeedback = (feedback: Omit<UserFeedback, 'id' | 'timestamp' | 'sessionId' | 'context'>) => {
  errorMonitor.recordUserFeedback(feedback);
};

export const setUserId = (userId: string) => {
  errorMonitor.setUserId(userId);
};
