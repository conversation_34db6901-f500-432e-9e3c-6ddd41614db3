#!/usr/bin/env python3
"""
统一开发工具集
整合代码质量检查、格式化、测试等开发工具
"""
import os
import sys
import subprocess
import argparse
from pathlib import Path

class DevTools:
    """开发工具集"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.backend_dir = self.project_root / "backend"
    
    def setup_environment(self):
        """设置开发环境"""
        print("🔧 设置开发环境...")
        
        commands = [
            "pip install --upgrade pip",
            "pip install -r requirements.txt",
            "pip install black isort flake8 mypy pytest pytest-cov bandit autoflake autopep8"
        ]
        
        for cmd in commands:
            print(f"执行: {cmd}")
            result = subprocess.run(cmd.split(), cwd=self.backend_dir)
            if result.returncode != 0:
                print(f"❌ 命令失败: {cmd}")
                return False
        
        print("✅ 开发环境设置完成")
        return True
    
    def format_code(self, check_only=False):
        """格式化代码"""
        print("🎨 格式化代码...")
        
        # 导入并使用现有的格式化工具
        sys.path.append(str(self.backend_dir))
        try:
            from format_code import CodeFormatter
            formatter = CodeFormatter(str(self.project_root))
            results = formatter.format_all(check_only=check_only)
            
            if results.get("summary", {}).get("total_files_changed", 0) > 0:
                print(f"✅ 格式化完成，修改了 {results['summary']['total_files_changed']} 个文件")
            else:
                print("✅ 代码格式已符合规范")
            return True
        except ImportError:
            print("❌ 格式化工具未找到")
            return False
    
    def check_quality(self):
        """检查代码质量"""
        print("🔍 检查代码质量...")
        
        sys.path.append(str(self.backend_dir))
        try:
            from code_quality_check import CodeQualityChecker
            checker = CodeQualityChecker(str(self.project_root))
            results = checker.check_all()
            
            total_issues = results.get("summary", {}).get("total_issues", 0)
            if total_issues == 0:
                print("✅ 代码质量检查通过")
            else:
                print(f"⚠️ 发现 {total_issues} 个问题")
            return total_issues == 0
        except ImportError:
            print("❌ 质量检查工具未找到")
            return False
    
    def run_tests(self, coverage=True):
        """运行测试"""
        print("🧪 运行测试...")
        
        cmd = ["python", "-m", "pytest", "tests/", "-v"]
        if coverage:
            cmd.extend(["--cov=app", "--cov-report=term-missing"])
        
        result = subprocess.run(cmd, cwd=self.backend_dir)
        
        if result.returncode == 0:
            print("✅ 所有测试通过")
            return True
        else:
            print("❌ 测试失败")
            return False
    
    def security_check(self):
        """安全检查"""
        print("🔒 安全检查...")
        
        cmd = ["python", "-m", "bandit", "-r", "app/", "-f", "text"]
        result = subprocess.run(cmd, cwd=self.backend_dir, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 安全检查通过")
            return True
        else:
            print("⚠️ 发现安全问题:")
            print(result.stdout)
            return False
    
    def full_check(self):
        """完整检查流程"""
        print("🚀 开始完整检查流程...")
        
        steps = [
            ("格式化检查", lambda: self.format_code(check_only=True)),
            ("代码质量检查", self.check_quality),
            ("安全检查", self.security_check),
            ("测试运行", self.run_tests)
        ]
        
        results = []
        for name, func in steps:
            print(f"\n--- {name} ---")
            success = func()
            results.append((name, success))
        
        print(f"\n{'='*50}")
        print("📊 检查结果总结:")
        all_passed = True
        for name, success in results:
            status = "✅ 通过" if success else "❌ 失败"
            print(f"  {name}: {status}")
            if not success:
                all_passed = False
        
        if all_passed:
            print("\n🎉 所有检查通过！代码质量良好")
        else:
            print("\n⚠️ 部分检查未通过，请修复问题后重试")
        
        return all_passed
    
    def clean_cache(self):
        """清理缓存文件"""
        print("🧹 清理缓存文件...")
        
        patterns = ["**/__pycache__", "**/*.pyc", "**/*.pyo", ".pytest_cache", ".coverage", "htmlcov"]
        
        for pattern in patterns:
            for path in self.project_root.rglob(pattern):
                if path.is_file():
                    path.unlink()
                    print(f"删除文件: {path}")
                elif path.is_dir():
                    import shutil
                    shutil.rmtree(path)
                    print(f"删除目录: {path}")
        
        print("✅ 缓存清理完成")

def main():
    parser = argparse.ArgumentParser(description="财经新闻Bot开发工具集")
    parser.add_argument("command", choices=[
        "setup", "format", "check-format", "quality", "test", "security", "full", "clean"
    ], help="要执行的命令")
    
    args = parser.parse_args()
    tools = DevTools()
    
    if args.command == "setup":
        success = tools.setup_environment()
    elif args.command == "format":
        success = tools.format_code(check_only=False)
    elif args.command == "check-format":
        success = tools.format_code(check_only=True)
    elif args.command == "quality":
        success = tools.check_quality()
    elif args.command == "test":
        success = tools.run_tests()
    elif args.command == "security":
        success = tools.security_check()
    elif args.command == "full":
        success = tools.full_check()
    elif args.command == "clean":
        tools.clean_cache()
        success = True
    else:
        parser.print_help()
        success = False
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
