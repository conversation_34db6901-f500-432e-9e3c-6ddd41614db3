"""
响应中间件
统一处理API响应格式、请求ID和错误处理
"""
import uuid
import json
import logging
from typing import Callable
from fastapi import Request, Response, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from ..services.response_service import ResponseService, ErrorCode, ResponseCode

logger = logging.getLogger(__name__)


class ResponseMiddleware(BaseHTTPMiddleware):
    """响应中间件"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        处理请求和响应
        
        Args:
            request: 请求对象
            call_next: 下一个中间件或路由处理器
            
        Returns:
            响应对象
        """
        # 生成请求ID
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id
        
        # 记录请求开始
        start_time = time.time()
        logger.info(f"Request started: {request.method} {request.url} [ID: {request_id}]")
        
        try:
            # 执行请求
            response = await call_next(request)
            
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 添加响应头
            response.headers["X-Request-ID"] = request_id
            response.headers["X-Process-Time"] = str(round(process_time, 4))
            
            # 记录请求完成
            logger.info(f"Request completed: {request.method} {request.url} "
                       f"[ID: {request_id}] [Status: {response.status_code}] "
                       f"[Time: {process_time:.4f}s]")
            
            # 如果是JSON响应且不是标准格式，则包装为标准格式
            if (response.status_code == 200 and 
                response.headers.get("content-type", "").startswith("application/json")):
                
                # 检查是否已经是标准格式
                try:
                    if hasattr(response, 'body'):
                        body = response.body.decode('utf-8')
                        data = json.loads(body)
                        
                        # 如果不包含标准字段，则包装
                        if not all(key in data for key in ['code', 'message', 'timestamp']):
                            wrapped_response = ResponseService.success(
                                data=data,
                                request_id=request_id
                            )
                            
                            return JSONResponse(
                                content=wrapped_response.model_dump(),
                                status_code=response.status_code,
                                headers=dict(response.headers)
                            )
                except Exception as e:
                    logger.warning(f"Failed to wrap response: {e}")
            
            return response
            
        except HTTPException as e:
            # 处理HTTP异常
            process_time = time.time() - start_time
            
            logger.warning(f"HTTP Exception: {request.method} {request.url} "
                          f"[ID: {request_id}] [Status: {e.status_code}] "
                          f"[Detail: {e.detail}] [Time: {process_time:.4f}s]")
            
            # 映射HTTP状态码到业务错误码
            error_code = self._map_http_status_to_error_code(e.status_code)
            
            error_response = ResponseService.error(
                message=str(e.detail),
                error_code=error_code,
                code=e.status_code,
                request_id=request_id
            )
            
            return JSONResponse(
                content=error_response.model_dump(),
                status_code=e.status_code,
                headers={
                    "X-Request-ID": request_id,
                    "X-Process-Time": str(round(process_time, 4))
                }
            )
            
        except Exception as e:
            # 处理未捕获的异常
            process_time = time.time() - start_time
            
            logger.error(f"Unhandled Exception: {request.method} {request.url} "
                        f"[ID: {request_id}] [Error: {str(e)}] [Time: {process_time:.4f}s]",
                        exc_info=True)
            
            error_response = ResponseService.internal_error(
                message="服务器内部错误",
                error_code=ErrorCode.UNKNOWN_ERROR,
                request_id=request_id,
                data={"error_detail": str(e)} if logger.level <= logging.DEBUG else None
            )
            
            return JSONResponse(
                content=error_response.model_dump(),
                status_code=ResponseCode.INTERNAL_SERVER_ERROR.value,
                headers={
                    "X-Request-ID": request_id,
                    "X-Process-Time": str(round(process_time, 4))
                }
            )
    
    def _map_http_status_to_error_code(self, status_code: int) -> ErrorCode:
        """
        映射HTTP状态码到业务错误码
        
        Args:
            status_code: HTTP状态码
            
        Returns:
            业务错误码
        """
        mapping = {
            400: ErrorCode.INVALID_PARAMETER,
            401: ErrorCode.AUTHENTICATION_FAILED,
            403: ErrorCode.PERMISSION_DENIED,
            404: ErrorCode.DATA_NOT_FOUND,
            405: ErrorCode.INVALID_PARAMETER,
            409: ErrorCode.DATA_ALREADY_EXISTS,
            422: ErrorCode.DATA_VALIDATION_FAILED,
            429: ErrorCode.PUSH_RATE_LIMIT_EXCEEDED,
            500: ErrorCode.UNKNOWN_ERROR,
            502: ErrorCode.EXTERNAL_SERVICE_UNAVAILABLE,
            503: ErrorCode.EXTERNAL_SERVICE_UNAVAILABLE,
            504: ErrorCode.EXTERNAL_SERVICE_TIMEOUT
        }
        
        return mapping.get(status_code, ErrorCode.UNKNOWN_ERROR)


class RequestContextMiddleware(BaseHTTPMiddleware):
    """请求上下文中间件"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        设置请求上下文信息
        
        Args:
            request: 请求对象
            call_next: 下一个中间件或路由处理器
            
        Returns:
            响应对象
        """
        # 设置请求上下文
        request.state.client_ip = self._get_client_ip(request)
        request.state.user_agent = request.headers.get("user-agent", "")
        request.state.referer = request.headers.get("referer", "")
        request.state.accept_language = request.headers.get("accept-language", "")
        
        # 执行请求
        response = await call_next(request)
        
        return response
    
    def _get_client_ip(self, request: Request) -> str:
        """
        获取客户端真实IP地址
        
        Args:
            request: 请求对象
            
        Returns:
            客户端IP地址
        """
        # 检查代理头
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            # 取第一个IP（客户端真实IP）
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        # 回退到连接IP
        if request.client:
            return request.client.host
        
        return "unknown"


# 导入time模块
import time
