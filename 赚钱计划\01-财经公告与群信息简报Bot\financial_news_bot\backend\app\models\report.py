"""
简报数据模型
定义晨报、晚报等简报相关的数据结构
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, JSON, ForeignKey, Enum as SQLEnum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
from enum import Enum

from ..database import Base


class ReportType(str, Enum):
    """简报类型枚举"""
    MORNING = "morning"  # 晨报
    EVENING = "evening"  # 晚报
    HOURLY = "hourly"    # 小时简报
    SPECIAL = "special"  # 特别简报


class ReportStatus(str, Enum):
    """简报状态枚举"""
    DRAFT = "draft"          # 草稿
    GENERATING = "generating" # 生成中
    COMPLETED = "completed"   # 已完成
    PUBLISHED = "published"   # 已发布
    FAILED = "failed"        # 生成失败


class ReportTemplate(Base):
    """简报模板表"""
    __tablename__ = "report_templates"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, comment="模板名称")
    type = Column(SQLEnum(ReportType), nullable=False, comment="简报类型")
    description = Column(Text, comment="模板描述")
    
    # 模板配置
    template_config = Column(JSON, comment="模板配置JSON")
    sections = Column(JSON, comment="模板章节配置")
    style_config = Column(JSON, comment="样式配置")
    
    # 生成规则
    generation_rules = Column(JSON, comment="生成规则配置")
    content_filters = Column(JSON, comment="内容过滤规则")
    priority_rules = Column(JSON, comment="优先级规则")
    
    # 状态信息
    is_active = Column(Boolean, default=True, comment="是否启用")
    is_default = Column(Boolean, default=False, comment="是否为默认模板")
    version = Column(String(20), default="1.0", comment="模板版本")
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    created_by = Column(Integer, ForeignKey("users.id"), comment="创建者ID")
    
    # 关系
    creator = relationship("User")  # 移除back_populates，因为User模型中没有对应关系
    reports = relationship("Report", back_populates="template")


class Report(Base):
    """简报表"""
    __tablename__ = "reports"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(200), nullable=False, comment="简报标题")
    type = Column(SQLEnum(ReportType), nullable=False, comment="简报类型")
    status = Column(SQLEnum(ReportStatus), default=ReportStatus.DRAFT, comment="简报状态")
    
    # 内容信息
    content = Column(Text, comment="简报内容（HTML格式）")
    summary = Column(Text, comment="简报摘要")
    content_json = Column(JSON, comment="结构化内容JSON")
    
    # 模板和配置
    template_id = Column(Integer, ForeignKey("report_templates.id"), comment="使用的模板ID")
    generation_config = Column(JSON, comment="生成配置")
    
    # 统计信息
    news_count = Column(Integer, default=0, comment="包含新闻数量")
    word_count = Column(Integer, default=0, comment="字数统计")
    read_time = Column(Integer, default=0, comment="预估阅读时间（分钟）")
    
    # 质量评分
    quality_score = Column(Integer, default=0, comment="内容质量评分（0-100）")
    completeness_score = Column(Integer, default=0, comment="完整性评分")
    relevance_score = Column(Integer, default=0, comment="相关性评分")
    
    # 发布信息
    published_at = Column(DateTime, comment="发布时间")
    scheduled_at = Column(DateTime, comment="计划发布时间")
    
    # 时间范围
    report_date = Column(DateTime, nullable=False, comment="简报日期")
    start_time = Column(DateTime, comment="内容起始时间")
    end_time = Column(DateTime, comment="内容结束时间")
    
    # 生成信息
    generation_started_at = Column(DateTime, comment="生成开始时间")
    generation_completed_at = Column(DateTime, comment="生成完成时间")
    generation_duration = Column(Integer, comment="生成耗时（秒）")
    error_message = Column(Text, comment="错误信息")
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 关系
    template = relationship("ReportTemplate", back_populates="reports")
    sections = relationship("ReportSection", back_populates="report", cascade="all, delete-orphan")
    push_logs = relationship("ReportPushLog", back_populates="report")


class ReportSubscription(Base):
    """简报订阅模型"""
    __tablename__ = "report_subscriptions"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    report_type = Column(SQLEnum(ReportType), nullable=False)
    schedule = Column(String(100), nullable=False, comment="调度表达式(cron格式)")
    is_active = Column(Boolean, default=True, nullable=False)
    delivery_channels = Column(JSON, nullable=False, comment="推送渠道列表")
    template_id = Column(Integer, ForeignKey("report_templates.id"), nullable=True)
    filters = Column(JSON, nullable=True, comment="内容过滤条件")
    recipients = Column(JSON, nullable=False, comment="接收者列表")
    custom_settings = Column(JSON, nullable=True, comment="自定义设置")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_run_at = Column(DateTime(timezone=True), nullable=True)
    next_run_at = Column(DateTime(timezone=True), nullable=True)
    run_count = Column(Integer, default=0, nullable=False)

    # 关系
    # user = relationship("User", back_populates="report_subscriptions")
    # template = relationship("ReportTemplate", back_populates="subscriptions")


class ReportSection(Base):
    """简报章节表"""
    __tablename__ = "report_sections"
    
    id = Column(Integer, primary_key=True, index=True)
    report_id = Column(Integer, ForeignKey("reports.id"), nullable=False, comment="简报ID")
    
    # 章节信息
    section_name = Column(String(100), nullable=False, comment="章节名称")
    section_title = Column(String(200), comment="章节标题")
    section_order = Column(Integer, default=0, comment="章节顺序")
    
    # 内容信息
    content = Column(Text, comment="章节内容")
    content_type = Column(String(50), comment="内容类型")
    news_ids = Column(JSON, comment="包含的新闻ID列表")
    
    # 统计信息
    news_count = Column(Integer, default=0, comment="新闻数量")
    word_count = Column(Integer, default=0, comment="字数")
    importance_score = Column(Integer, default=0, comment="重要性评分")
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 关系
    report = relationship("Report", back_populates="sections")


class ReportPushLog(Base):
    """简报推送日志表"""
    __tablename__ = "report_push_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    report_id = Column(Integer, ForeignKey("reports.id"), nullable=False, comment="简报ID")
    
    # 推送信息
    push_type = Column(String(50), comment="推送类型")
    push_channel = Column(String(50), comment="推送渠道")
    target_users = Column(JSON, comment="目标用户列表")
    
    # 推送状态
    status = Column(String(20), default="pending", comment="推送状态")
    sent_count = Column(Integer, default=0, comment="发送数量")
    success_count = Column(Integer, default=0, comment="成功数量")
    failed_count = Column(Integer, default=0, comment="失败数量")
    
    # 推送结果
    push_result = Column(JSON, comment="推送结果详情")
    error_message = Column(Text, comment="错误信息")
    
    # 时间信息
    scheduled_at = Column(DateTime, comment="计划推送时间")
    started_at = Column(DateTime, comment="开始推送时间")
    completed_at = Column(DateTime, comment="完成推送时间")
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 关系
    report = relationship("Report", back_populates="push_logs")


class ReportGenerationLog(Base):
    """简报生成日志表"""
    __tablename__ = "report_generation_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    report_id = Column(Integer, ForeignKey("reports.id"), comment="简报ID")
    
    # 生成信息
    generation_type = Column(String(50), comment="生成类型")
    template_id = Column(Integer, comment="模板ID")
    config_used = Column(JSON, comment="使用的配置")
    
    # 处理步骤
    step_name = Column(String(100), comment="处理步骤名称")
    step_status = Column(String(20), comment="步骤状态")
    step_result = Column(JSON, comment="步骤结果")
    
    # 性能信息
    processing_time = Column(Integer, comment="处理时间（毫秒）")
    memory_usage = Column(Integer, comment="内存使用（MB）")
    api_calls = Column(Integer, default=0, comment="API调用次数")
    
    # 错误信息
    error_type = Column(String(100), comment="错误类型")
    error_message = Column(Text, comment="错误详情")
    stack_trace = Column(Text, comment="错误堆栈")
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    
    # 关系
    report = relationship("Report")


# 为User模型添加反向关系（需要在user.py中添加）
# User.report_templates = relationship("ReportTemplate", back_populates="creator")
