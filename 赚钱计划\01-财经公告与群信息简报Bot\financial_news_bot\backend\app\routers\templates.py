"""
推送模板管理API
提供模板的CRUD操作和渲染功能
"""
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from app.database import get_db
from app.dependencies.auth import get_current_active_user
from app.models.user import User
# 临时定义推送相关的枚举和类
from enum import Enum

class PushTier(Enum):
    T1 = "T1"
    T2 = "T2"
    T3 = "T3"

class PushChannel(Enum):
    WECHAT_WORK = "wechat_work"
    FEISHU = "feishu"
    EMAIL = "email"

# 临时推送模板类（简化版）
class PushTemplate:
    def __init__(self, name: str, tier: str, content: str):
        self.name = name
        self.tier = tier
        self.content = content

router = APIRouter(prefix="/templates", tags=["推送模板"])

# Pydantic模型
class TemplateCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=100, description="模板名称")
    tier: str = Field(PushTier.T3.value, description="推送层级")
    channel: str = Field(PushChannel.WECHAT_WORK.value, description="推送渠道")
    title_template: str = Field(..., min_length=1, description="标题模板")
    content_template: str = Field(..., min_length=1, description="内容模板")
    format_config: Optional[Dict[str, Any]] = Field(None, description="格式配置")
    is_default: bool = Field(False, description="是否默认模板")


class TemplateResponse(BaseModel):
    id: int
    name: str
    tier: str
    channel: str
    title_template: str
    content_template: str
    format_config: Optional[Dict[str, Any]]
    is_active: bool
    is_default: bool
    version: str
    usage_count: int
    created_at: str
    updated_at: str


@router.get("/", response_model=List[TemplateResponse])
async def get_templates(
    tier: Optional[str] = Query(None, description="推送层级过滤"),
    channel: Optional[str] = Query(None, description="推送渠道过滤"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取模板列表
    """
    try:
        query = db.query(PushTemplate).filter(PushTemplate.is_active == True)
        
        if tier:
            query = query.filter(PushTemplate.tier == tier)
        if channel:
            query = query.filter(PushTemplate.channel == channel)
            
        templates = query.all()
        
        return [
            TemplateResponse(
                id=template.id,
                name=template.name,
                tier=template.tier,
                channel=template.channel,
                title_template=template.title_template,
                content_template=template.content_template,
                format_config=template.format_config,
                is_active=template.is_active,
                is_default=template.is_default,
                version=template.version,
                usage_count=template.usage_count,
                created_at=template.created_at.isoformat() if template.created_at else "",
                updated_at=template.updated_at.isoformat() if template.updated_at else ""
            )
            for template in templates
        ]
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"获取模板列表失败: {str(e)}"
        )


@router.get("/{template_id}", response_model=TemplateResponse)
async def get_template(
    template_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取模板详情
    """
    try:
        template = db.query(PushTemplate).filter(PushTemplate.id == template_id).first()
        
        if not template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="模板不存在"
            )
        
        return TemplateResponse(
            id=template.id,
            name=template.name,
            tier=template.tier,
            channel=template.channel,
            title_template=template.title_template,
            content_template=template.content_template,
            format_config=template.format_config,
            is_active=template.is_active,
            is_default=template.is_default,
            version=template.version,
            usage_count=template.usage_count,
            created_at=template.created_at.isoformat() if template.created_at else "",
            updated_at=template.updated_at.isoformat() if template.updated_at else ""
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"获取模板详情失败: {str(e)}"
        )


@router.post("/", response_model=TemplateResponse, status_code=status.HTTP_201_CREATED)
async def create_template(
    template_data: TemplateCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    创建新的推送模板
    """
    try:
        # 检查模板名称是否已存在
        existing = db.query(PushTemplate).filter(PushTemplate.name == template_data.name).first()
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"模板名称 '{template_data.name}' 已存在"
            )

        # 如果设置为默认模板，需要取消同类型的其他默认模板
        if template_data.is_default:
            db.query(PushTemplate).filter(
                PushTemplate.tier == template_data.tier,
                PushTemplate.channel == template_data.channel,
                PushTemplate.is_default == True
            ).update({"is_default": False})

        # 创建新模板
        template = PushTemplate(
            name=template_data.name,
            tier=template_data.tier,
            channel=template_data.channel,
            title_template=template_data.title_template,
            content_template=template_data.content_template,
            format_config=template_data.format_config,
            is_default=template_data.is_default,
            created_by=current_user.id
        )

        db.add(template)
        db.commit()
        db.refresh(template)
        
        return TemplateResponse(
            id=template.id,
            name=template.name,
            tier=template.tier,
            channel=template.channel,
            title_template=template.title_template,
            content_template=template.content_template,
            format_config=template.format_config,
            is_active=template.is_active,
            is_default=template.is_default,
            version=template.version,
            usage_count=template.usage_count,
            created_at=template.created_at.isoformat() if template.created_at else "",
            updated_at=template.updated_at.isoformat() if template.updated_at else ""
        )
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"创建模板失败: {str(e)}"
        )


@router.get("/health", status_code=200)
async def health_check():
    """
    健康检查接口
    """
    return {"status": "ok", "message": "模板服务正常运行"}
