"""
集成监控服务
将安全监控和合规监控功能集成到主服务中，简化微服务架构
"""
import logging
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from enum import Enum
import json
import re
import hashlib

from app.services.cache_service import cache_service
from app.config import settings

logger = logging.getLogger(__name__)

class SecurityLevel(Enum):
    """安全级别枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class ComplianceStatus(Enum):
    """合规状态枚举"""
    COMPLIANT = "compliant"
    NON_COMPLIANT = "non_compliant"
    PENDING = "pending"
    UNKNOWN = "unknown"

class RiskLevel(Enum):
    """风险级别枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class SecurityEvent:
    """安全事件数据结构"""
    
    def __init__(self, 
                 event_type: str,
                 level: SecurityLevel,
                 message: str,
                 source: str = "",
                 details: Dict = None):
        self.event_type = event_type
        self.level = level
        self.message = message
        self.source = source
        self.details = details or {}
        self.timestamp = datetime.now()
        self.event_id = self._generate_event_id()
    
    def _generate_event_id(self) -> str:
        """生成事件ID"""
        content = f"{self.timestamp.isoformat()}{self.event_type}{self.message}"
        return hashlib.md5(content.encode()).hexdigest()[:16]

class ComplianceResult:
    """合规检查结果数据结构"""
    
    def __init__(self,
                 status: ComplianceStatus,
                 risk_level: RiskLevel,
                 violations: List[str] = None,
                 suggestions: List[str] = None,
                 details: Dict = None):
        self.status = status
        self.risk_level = risk_level
        self.violations = violations or []
        self.suggestions = suggestions or []
        self.details = details or {}
        self.timestamp = datetime.now()

class SecurityMonitor:
    """集成的安全监控"""
    
    def __init__(self):
        self.blocked_ips = set()
        self.security_events = []
        self.rate_limits = {}
        self.suspicious_patterns = [
            r"<script.*?>.*?</script>",
            r"javascript:",
            r"vbscript:",
            r"onload\s*=",
            r"onerror\s*=",
            r"union\s+select",
            r"drop\s+table",
            r"insert\s+into",
            r"delete\s+from",
            r"\.\./",
            r"\.\.\\",
            r"/etc/passwd",
            r"cmd\.exe",
            r"powershell"
        ]
        logger.info("安全监控模块初始化完成")
    
    def scan_content(self, content: str) -> Dict[str, Any]:
        """内容安全扫描"""
        try:
            threats = []
            risk_score = 0
            
            # 检查恶意模式
            for pattern in self.suspicious_patterns:
                if re.search(pattern, content, re.IGNORECASE):
                    threats.append(f"检测到可疑模式: {pattern}")
                    risk_score += 10
            
            # 检查内容长度异常
            if len(content) > 50000:
                threats.append("内容长度异常")
                risk_score += 5
            
            # 检查特殊字符
            special_chars = len(re.findall(r'[<>"\'\(\){}]', content))
            if special_chars > len(content) * 0.1:
                threats.append("特殊字符比例过高")
                risk_score += 5
            
            is_safe = risk_score < 20
            level = SecurityLevel.LOW
            
            if risk_score >= 50:
                level = SecurityLevel.CRITICAL
            elif risk_score >= 30:
                level = SecurityLevel.HIGH
            elif risk_score >= 20:
                level = SecurityLevel.MEDIUM
            
            # 记录安全事件
            if not is_safe:
                event = SecurityEvent(
                    event_type="content_scan",
                    level=level,
                    message=f"内容安全扫描发现威胁，风险评分: {risk_score}",
                    details={"threats": threats, "risk_score": risk_score}
                )
                self._log_security_event(event)
            
            return {
                "safe": is_safe,
                "threats": threats,
                "risk_score": risk_score,
                "level": level.value
            }
            
        except Exception as e:
            logger.error(f"内容安全扫描失败: {str(e)}")
            return {"safe": False, "threats": [f"扫描异常: {str(e)}"], "risk_score": 100}
    
    def check_request_security(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """请求安全检查"""
        try:
            warnings = []
            risk_score = 0
            
            # 检查请求频率
            client_ip = request_data.get('client_ip', 'unknown')
            if self._check_rate_limit(client_ip):
                warnings.append("请求频率过高")
                risk_score += 20
            
            # 检查用户代理
            user_agent = request_data.get('user_agent', '')
            suspicious_agents = ['sqlmap', 'nikto', 'nmap', 'masscan', 'burpsuite']
            if any(agent in user_agent.lower() for agent in suspicious_agents):
                warnings.append("可疑的用户代理")
                risk_score += 30
            
            # 检查请求参数
            params = request_data.get('params', {})
            for key, value in params.items():
                if isinstance(value, str) and any(pattern in value.lower() for pattern in ['script', 'union', 'select', 'drop']):
                    warnings.append(f"参数 {key} 包含可疑内容")
                    risk_score += 15
            
            is_secure = risk_score < 30
            
            return {
                "secure": is_secure,
                "warnings": warnings,
                "risk_score": risk_score
            }
            
        except Exception as e:
            logger.error(f"请求安全检查失败: {str(e)}")
            return {"secure": False, "warnings": [f"检查异常: {str(e)}"], "risk_score": 100}
    
    def _check_rate_limit(self, client_ip: str, limit: int = 100, window: int = 3600) -> bool:
        """检查请求频率限制"""
        now = datetime.now()
        key = f"rate_limit:{client_ip}"
        
        if key not in self.rate_limits:
            self.rate_limits[key] = []
        
        # 清理过期记录
        self.rate_limits[key] = [
            timestamp for timestamp in self.rate_limits[key]
            if (now - timestamp).total_seconds() < window
        ]
        
        # 检查是否超限
        if len(self.rate_limits[key]) >= limit:
            return True
        
        # 记录当前请求
        self.rate_limits[key].append(now)
        return False
    
    def _log_security_event(self, event: SecurityEvent):
        """记录安全事件"""
        self.security_events.append(event)
        
        # 保持事件列表大小
        if len(self.security_events) > 1000:
            self.security_events = self.security_events[-500:]
        
        logger.warning(f"安全事件: {event.event_type} - {event.message}")

class ComplianceMonitor:
    """集成的合规监控"""
    
    def __init__(self):
        self.compliance_rules = self._load_compliance_rules()
        self.violation_history = []
        logger.info("合规监控模块初始化完成")
    
    def _load_compliance_rules(self) -> List[Dict[str, Any]]:
        """加载合规规则"""
        return [
            {
                "rule_id": "content_length",
                "name": "内容长度限制",
                "pattern": None,
                "max_length": 10000,
                "description": "内容长度不得超过10000字符"
            },
            {
                "rule_id": "sensitive_keywords",
                "name": "敏感词检查",
                "keywords": ["违法", "欺诈", "洗钱", "内幕交易"],
                "description": "内容不得包含敏感关键词"
            },
            {
                "rule_id": "personal_info",
                "name": "个人信息保护",
                "pattern": r"\b\d{15,18}\b|\b\d{11}\b",  # 身份证号、手机号
                "description": "内容不得包含个人敏感信息"
            },
            {
                "rule_id": "financial_advice",
                "name": "投资建议规范",
                "keywords": ["保证收益", "稳赚不赔", "内幕消息"],
                "description": "不得提供违规投资建议"
            }
        ]
    
    def check_content_compliance(self, content: str) -> ComplianceResult:
        """内容合规检查"""
        try:
            violations = []
            suggestions = []
            risk_score = 0
            
            for rule in self.compliance_rules:
                rule_id = rule["rule_id"]
                
                if rule_id == "content_length":
                    if len(content) > rule["max_length"]:
                        violations.append(f"内容长度超限: {len(content)}/{rule['max_length']}")
                        suggestions.append("请缩短内容长度")
                        risk_score += 20
                
                elif rule_id == "sensitive_keywords":
                    found_keywords = []
                    for keyword in rule["keywords"]:
                        if keyword in content:
                            found_keywords.append(keyword)
                    
                    if found_keywords:
                        violations.append(f"包含敏感词: {', '.join(found_keywords)}")
                        suggestions.append("请移除敏感词汇")
                        risk_score += 30
                
                elif rule_id == "personal_info":
                    if rule["pattern"] and re.search(rule["pattern"], content):
                        violations.append("可能包含个人敏感信息")
                        suggestions.append("请移除个人信息")
                        risk_score += 25
                
                elif rule_id == "financial_advice":
                    found_keywords = []
                    for keyword in rule["keywords"]:
                        if keyword in content:
                            found_keywords.append(keyword)
                    
                    if found_keywords:
                        violations.append(f"包含违规投资建议: {', '.join(found_keywords)}")
                        suggestions.append("请修改投资建议表述")
                        risk_score += 35
            
            # 确定合规状态和风险级别
            if not violations:
                status = ComplianceStatus.COMPLIANT
                risk_level = RiskLevel.LOW
            else:
                status = ComplianceStatus.NON_COMPLIANT
                if risk_score >= 60:
                    risk_level = RiskLevel.CRITICAL
                elif risk_score >= 40:
                    risk_level = RiskLevel.HIGH
                elif risk_score >= 20:
                    risk_level = RiskLevel.MEDIUM
                else:
                    risk_level = RiskLevel.LOW
            
            result = ComplianceResult(
                status=status,
                risk_level=risk_level,
                violations=violations,
                suggestions=suggestions,
                details={"risk_score": risk_score}
            )
            
            # 记录违规历史
            if violations:
                self.violation_history.append({
                    "timestamp": datetime.now(),
                    "violations": violations,
                    "risk_score": risk_score
                })
                
                # 保持历史记录大小
                if len(self.violation_history) > 500:
                    self.violation_history = self.violation_history[-250:]
            
            return result
            
        except Exception as e:
            logger.error(f"合规检查失败: {str(e)}")
            return ComplianceResult(
                status=ComplianceStatus.UNKNOWN,
                risk_level=RiskLevel.HIGH,
                violations=[f"检查异常: {str(e)}"],
                suggestions=["请联系技术支持"]
            )
    
    def audit_system_compliance(self) -> Dict[str, Any]:
        """系统合规审计"""
        try:
            audit_results = {
                "audit_time": datetime.now().isoformat(),
                "total_violations": len(self.violation_history),
                "recent_violations": len([
                    v for v in self.violation_history
                    if (datetime.now() - v["timestamp"]).days <= 7
                ]),
                "compliance_score": self._calculate_compliance_score(),
                "recommendations": []
            }
            
            # 生成建议
            if audit_results["recent_violations"] > 10:
                audit_results["recommendations"].append("近期违规事件较多，建议加强内容审核")
            
            if audit_results["compliance_score"] < 80:
                audit_results["recommendations"].append("合规评分较低，建议完善合规流程")
            
            return audit_results
            
        except Exception as e:
            logger.error(f"系统合规审计失败: {str(e)}")
            return {"error": str(e)}
    
    def _calculate_compliance_score(self) -> float:
        """计算合规评分"""
        if not self.violation_history:
            return 100.0
        
        # 基于最近30天的违规情况计算评分
        recent_violations = [
            v for v in self.violation_history
            if (datetime.now() - v["timestamp"]).days <= 30
        ]
        
        if not recent_violations:
            return 100.0
        
        # 简单的评分算法
        total_risk = sum(v["risk_score"] for v in recent_violations)
        max_possible_risk = len(recent_violations) * 100
        
        score = max(0, 100 - (total_risk / max_possible_risk * 100))
        return round(score, 2)

class IntegratedMonitoringService:
    """集成监控服务 - 统一的安全和合规监控"""
    
    def __init__(self):
        self.security_monitor = SecurityMonitor()
        self.compliance_monitor = ComplianceMonitor()
        logger.info("集成监控服务初始化完成")
    
    def check_content(self, content: str) -> Dict[str, Any]:
        """综合内容检查 - 同时进行安全和合规检查"""
        try:
            # 安全检查
            security_result = self.security_monitor.scan_content(content)
            
            # 合规检查
            compliance_result = self.compliance_monitor.check_content_compliance(content)
            
            # 综合评估
            overall_approved = (
                security_result["safe"] and 
                compliance_result.status == ComplianceStatus.COMPLIANT
            )
            
            return {
                "approved": overall_approved,
                "security": security_result,
                "compliance": {
                    "status": compliance_result.status.value,
                    "risk_level": compliance_result.risk_level.value,
                    "violations": compliance_result.violations,
                    "suggestions": compliance_result.suggestions,
                    "details": compliance_result.details
                },
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"综合内容检查失败: {str(e)}")
            return {
                "approved": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def check_request(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """综合请求检查"""
        try:
            security_result = self.security_monitor.check_request_security(request_data)
            
            return {
                "approved": security_result["secure"],
                "security": security_result,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"综合请求检查失败: {str(e)}")
            return {
                "approved": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def get_monitoring_status(self) -> Dict[str, Any]:
        """获取监控服务状态"""
        try:
            return {
                "status": "healthy",
                "security_events_count": len(self.security_monitor.security_events),
                "compliance_violations_count": len(self.compliance_monitor.violation_history),
                "compliance_score": self.compliance_monitor._calculate_compliance_score(),
                "last_check": datetime.now().isoformat()
            }
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "last_check": datetime.now().isoformat()
            }

# 全局集成监控服务实例
integrated_monitoring = IntegratedMonitoringService()
