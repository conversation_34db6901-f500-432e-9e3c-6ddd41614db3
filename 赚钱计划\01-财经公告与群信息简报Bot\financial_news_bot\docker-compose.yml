# 混合架构：本地容器（Redis/Celery/FastAPI/Nginx/Frontend）+ 远程 MySQL
# 说明：本地容器通过 DATABASE_URL 连接远程 MySQL (1.95.67.162:3306)。
services:
  redis:
    image: redis:7-alpine
    command: ["redis-server", "/usr/local/etc/redis/redis.conf"]
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    environment:
      - TZ=${TZ:-Asia/Shanghai}

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    env_file: .env
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL:-redis://redis:6379/0}
      - TZ=${TZ:-Asia/Shanghai}
    depends_on:
      - redis
    ports:
      - "${BACKEND_PORT:-8000}:8000"
    volumes:
      - ./backend/app:/app/app:ro  # 开发模式：挂载代码目录

  celery:
    build:
      context: ./backend
      dockerfile: Dockerfile
    command: ["celery", "-A", "app.celery_app.celery_app", "worker", "-l", "info", "--queues=default,crawler,processor,pusher"]
    env_file: .env
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL:-redis://redis:6379/0}
      - TZ=${TZ:-Asia/Shanghai}
    depends_on:
      - redis
      - backend

  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
    command: ["celery", "-A", "app.celery_app.celery_app", "beat", "-l", "info", "--scheduler=celery.beat:PersistentScheduler"]
    env_file: .env
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL:-redis://redis:6379/0}
      - TZ=${TZ:-Asia/Shanghai}
    depends_on:
      - redis
      - backend
    volumes:
      - celery-beat-data:/app/beat-data

  frontend:
    build: ./frontend
    depends_on:
      - backend
    # 前端容器不直接暴露端口，只通过nginx代理访问
    # ports:
    #   - "${FRONTEND_PORT:-80}:80"

  # 注意：安全监控和合规监控功能已集成到backend服务中
  # 原独立的 security-monitor 和 compliance-monitor 微服务已移除
  # 相关功能现在通过 /api/v1/monitoring 接口提供

  nginx:
    build: ./nginx
    depends_on:
      - frontend
      - backend
    ports:
      - "${NGINX_PORT:-8080}:80"

volumes:
  celery-beat-data:
    driver: local
  redis_data:
    driver: local

networks:
  default:
    name: financial_news_bot_net
    driver: bridge

