"""
简化爬虫服务测试
测试简化爬虫服务的核心功能
"""
import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta

import httpx

from app.services.simplified_crawler_service import (
    SimpleCrawlerConfig,
    CrawlerStats,
    SimpleCrawler,
    CrawlerManager,
    crawler_manager
)

class TestSimpleCrawlerConfig:
    """测试简化爬虫配置"""
    
    def test_user_agents_list(self):
        """测试User-Agent列表"""
        assert len(SimpleCrawlerConfig.USER_AGENTS) == 5
        assert all("Mozilla" in ua for ua in SimpleCrawlerConfig.USER_AGENTS)
    
    def test_default_headers(self):
        """测试默认请求头"""
        headers = SimpleCrawlerConfig.DEFAULT_HEADERS
        assert "Accept" in headers
        assert "Accept-Language" in headers
        assert "Accept-Encoding" in headers
        assert "Connection" in headers
    
    def test_request_config(self):
        """测试请求配置"""
        assert SimpleCrawlerConfig.DEFAULT_TIMEOUT == 30
        assert SimpleCrawlerConfig.DEFAULT_RATE_LIMIT == 1.0
        assert SimpleCrawlerConfig.MAX_RETRIES == 3
        assert SimpleCrawlerConfig.BACKOFF_FACTOR == 2.0

class TestCrawlerStats:
    """测试爬虫统计信息"""
    
    @pytest.fixture
    def stats(self):
        return CrawlerStats()
    
    def test_stats_initialization(self, stats):
        """测试统计信息初始化"""
        assert stats.total_requests == 0
        assert stats.successful_requests == 0
        assert stats.failed_requests == 0
        assert stats.total_items == 0
        assert stats.start_time is None
        assert stats.end_time is None
    
    def test_start_and_end(self, stats):
        """测试开始和结束统计"""
        stats.start()
        assert stats.start_time is not None
        
        stats.end()
        assert stats.end_time is not None
        assert stats.end_time >= stats.start_time
    
    def test_record_request(self, stats):
        """测试记录请求"""
        stats.record_request(True)
        assert stats.total_requests == 1
        assert stats.successful_requests == 1
        assert stats.failed_requests == 0
        
        stats.record_request(False)
        assert stats.total_requests == 2
        assert stats.successful_requests == 1
        assert stats.failed_requests == 1
    
    def test_record_items(self, stats):
        """测试记录项目数量"""
        stats.record_items(10)
        assert stats.total_items == 10
        
        stats.record_items(5)
        assert stats.total_items == 15
    
    def test_get_summary(self, stats):
        """测试获取统计摘要"""
        stats.start()
        stats.record_request(True)
        stats.record_request(False)
        stats.record_items(5)
        stats.end()
        
        summary = stats.get_summary()
        
        assert summary["total_requests"] == 2
        assert summary["successful_requests"] == 1
        assert summary["failed_requests"] == 1
        assert summary["success_rate"] == 0.5
        assert summary["total_items"] == 5
        assert summary["duration_seconds"] is not None
        assert summary["start_time"] is not None
        assert summary["end_time"] is not None

class TestSimpleCrawler:
    """测试简化爬虫基类"""
    
    class TestCrawler(SimpleCrawler):
        """测试用的具体爬虫实现"""
        
        async def fetch_news_list(self, start_date=None, end_date=None):
            return [
                {"title": "测试新闻1", "content": "内容1"},
                {"title": "测试新闻2", "content": "内容2"}
            ]
    
    @pytest.fixture
    def crawler(self):
        return self.TestCrawler("TestCrawler", "https://example.com", 0.1)
    
    def test_crawler_initialization(self, crawler):
        """测试爬虫初始化"""
        assert crawler.name == "TestCrawler"
        assert crawler.base_url == "https://example.com"
        assert crawler.rate_limit == 0.1
        assert crawler.domain == "example.com"
        assert crawler.session is None
        assert isinstance(crawler.stats, CrawlerStats)
    
    @pytest.mark.asyncio
    async def test_session_management(self, crawler):
        """测试会话管理"""
        # 测试启动会话
        await crawler.start_session()
        assert crawler.session is not None
        assert isinstance(crawler.session, httpx.AsyncClient)
        
        # 测试关闭会话
        await crawler.close_session()
        assert crawler.session is None
    
    @pytest.mark.asyncio
    async def test_context_manager(self, crawler):
        """测试异步上下文管理器"""
        async with crawler:
            assert crawler.session is not None
        
        # 退出上下文后会话应该被关闭
        assert crawler.session is None
    
    @pytest.mark.asyncio
    async def test_rate_limit_wait(self, crawler):
        """测试速率限制等待"""
        start_time = asyncio.get_event_loop().time()
        
        # 第一次调用应该立即返回
        await crawler._wait_rate_limit()
        first_call_time = asyncio.get_event_loop().time()
        
        # 第二次调用应该等待
        await crawler._wait_rate_limit()
        second_call_time = asyncio.get_event_loop().time()
        
        # 验证等待时间
        assert (second_call_time - first_call_time) >= crawler.rate_limit * 0.9  # 允许一些误差
    
    @pytest.mark.asyncio
    async def test_make_request_success(self, crawler):
        """测试成功的HTTP请求"""
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        
        with patch.object(crawler, 'session') as mock_session:
            mock_session.request = AsyncMock(return_value=mock_response)
            
            response = await crawler.make_request("https://example.com/test")
            
            assert response == mock_response
            mock_session.request.assert_called_once_with(
                "GET", "https://example.com/test"
            )
    
    @pytest.mark.asyncio
    async def test_make_request_http_error(self, crawler):
        """测试HTTP错误请求"""
        mock_response = Mock()
        mock_response.status_code = 404
        http_error = httpx.HTTPStatusError("Not Found", request=Mock(), response=mock_response)
        
        with patch.object(crawler, 'session') as mock_session:
            mock_session.request = AsyncMock(side_effect=http_error)
            
            response = await crawler.make_request("https://example.com/test")
            
            assert response is None
            assert crawler.stats.failed_requests > 0
    
    @pytest.mark.asyncio
    async def test_make_request_retry_on_rate_limit(self, crawler):
        """测试速率限制时的重试"""
        mock_response = Mock()
        mock_response.status_code = 429
        rate_limit_error = httpx.HTTPStatusError("Rate Limited", request=Mock(), response=mock_response)
        
        success_response = Mock()
        success_response.raise_for_status.return_value = None
        
        with patch.object(crawler, 'session') as mock_session:
            # 第一次失败，第二次成功
            mock_session.request = AsyncMock(side_effect=[rate_limit_error, success_response])
            
            response = await crawler.make_request("https://example.com/test")
            
            assert response == success_response
            assert mock_session.request.call_count == 2
    
    def test_generate_content_hash(self, crawler):
        """测试生成内容哈希"""
        content1 = "测试内容"
        content2 = "测试内容"
        content3 = "不同内容"
        
        hash1 = crawler.generate_content_hash(content1)
        hash2 = crawler.generate_content_hash(content2)
        hash3 = crawler.generate_content_hash(content3)
        
        assert hash1 == hash2  # 相同内容应该生成相同哈希
        assert hash1 != hash3  # 不同内容应该生成不同哈希
        assert len(hash1) == 32  # MD5哈希长度
    
    def test_clean_text(self, crawler):
        """测试文本清理"""
        dirty_text = "  <p>测试文本</p>  \n\n  包含HTML标签  "
        clean_text = crawler.clean_text(dirty_text)
        
        assert clean_text == "测试文本 包含HTML标签"
        assert "<p>" not in clean_text
        assert "</p>" not in clean_text
    
    def test_calculate_importance_score(self, crawler):
        """测试重要性评分计算"""
        high_importance_item = {"title": "重大资产重组公告"}
        medium_importance_item = {"title": "董事会决议公告"}
        low_importance_item = {"title": "日常经营公告"}
        
        high_score = crawler.calculate_importance_score(high_importance_item)
        medium_score = crawler.calculate_importance_score(medium_importance_item)
        low_score = crawler.calculate_importance_score(low_importance_item)
        
        assert high_score > medium_score > low_score
        assert 0 <= high_score <= 100
        assert 0 <= medium_score <= 100
        assert 0 <= low_score <= 100
    
    @pytest.mark.asyncio
    async def test_crawl_process(self, crawler):
        """测试完整的爬取流程"""
        async with crawler:
            results = await crawler.crawl()
            
            assert len(results) == 2
            assert all("title" in item for item in results)
            assert all("content" in item for item in results)
            assert all("importance_score" in item for item in results)
            assert all("content_hash" in item for item in results)
            assert all("crawler_name" in item for item in results)
            assert all("crawl_time" in item for item in results)
    
    def test_get_stats(self, crawler):
        """测试获取统计信息"""
        stats = crawler.get_stats()
        
        assert "total_requests" in stats
        assert "successful_requests" in stats
        assert "failed_requests" in stats
        assert "total_items" in stats
        assert "duration_seconds" in stats

class TestCrawlerManager:
    """测试爬虫管理器"""
    
    @pytest.fixture
    def manager(self):
        return CrawlerManager()
    
    @pytest.fixture
    def test_crawler(self):
        class TestCrawler(SimpleCrawler):
            async def fetch_news_list(self, start_date=None, end_date=None):
                return [{"title": "测试", "content": "内容"}]
        
        return TestCrawler("TestCrawler", "https://example.com")
    
    def test_register_crawler(self, manager, test_crawler):
        """测试注册爬虫"""
        manager.register_crawler(test_crawler)
        
        assert "TestCrawler" in manager.crawlers
        assert manager.get_crawler("TestCrawler") == test_crawler
    
    def test_get_nonexistent_crawler(self, manager):
        """测试获取不存在的爬虫"""
        crawler = manager.get_crawler("NonExistent")
        assert crawler is None
    
    def test_list_crawlers(self, manager, test_crawler):
        """测试列出爬虫"""
        manager.register_crawler(test_crawler)
        crawler_list = manager.list_crawlers()
        
        assert "TestCrawler" in crawler_list
        assert len(crawler_list) == 1
    
    @pytest.mark.asyncio
    async def test_crawl_all(self, manager, test_crawler):
        """测试爬取所有爬虫"""
        manager.register_crawler(test_crawler)
        
        results = await manager.crawl_all()
        
        assert "TestCrawler" in results
        assert len(results["TestCrawler"]) == 1
        assert results["TestCrawler"][0]["title"] == "测试"
    
    def test_get_all_stats(self, manager, test_crawler):
        """测试获取所有统计信息"""
        manager.register_crawler(test_crawler)
        
        stats = manager.get_all_stats()
        
        assert "TestCrawler" in stats
        assert "total_requests" in stats["TestCrawler"]

class TestGlobalCrawlerManager:
    """测试全局爬虫管理器"""
    
    def test_global_manager_exists(self):
        """测试全局管理器存在"""
        assert crawler_manager is not None
        assert isinstance(crawler_manager, CrawlerManager)
    
    def test_global_manager_functionality(self):
        """测试全局管理器功能"""
        initial_count = len(crawler_manager.list_crawlers())
        
        # 这里不实际注册爬虫，只测试接口可用性
        crawler_list = crawler_manager.list_crawlers()
        assert isinstance(crawler_list, list)
        
        stats = crawler_manager.get_all_stats()
        assert isinstance(stats, dict)

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
