"""
用户行为追踪服务
提供用户行为数据收集、分析和洞察功能
"""
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, func
from enum import Enum
import json

from .cache_service import cache_service
from ..models.user import User
from ..models.news import News
from ..models.push_log import PushLog

logger = logging.getLogger(__name__)


class EventType(Enum):
    """事件类型枚举"""
    PUSH_SENT = "push_sent"  # 推送发送
    PUSH_DELIVERED = "push_delivered"  # 推送送达
    PUSH_OPENED = "push_opened"  # 推送打开
    PUSH_CLICKED = "push_clicked"  # 推送点击
    NEWS_VIEWED = "news_viewed"  # 新闻查看
    NEWS_SHARED = "news_shared"  # 新闻分享
    NEWS_BOOKMARKED = "news_bookmarked"  # 新闻收藏
    SUBSCRIPTION_CREATED = "subscription_created"  # 订阅创建
    SUBSCRIPTION_MODIFIED = "subscription_modified"  # 订阅修改
    FEEDBACK_SUBMITTED = "feedback_submitted"  # 反馈提交


class UserBehaviorTrackingService:
    """用户行为追踪服务"""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def track_event(
        self,
        user_id: Optional[int],
        event_type: EventType,
        event_data: Dict[str, Any],
        session_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> bool:
        """
        追踪用户行为事件
        
        Args:
            user_id: 用户ID
            event_type: 事件类型
            event_data: 事件数据
            session_id: 会话ID
            ip_address: IP地址
            user_agent: 用户代理
            
        Returns:
            是否成功
        """
        try:
            # 构建事件记录
            event_record = {
                "user_id": user_id,
                "event_type": event_type.value,
                "event_data": event_data,
                "session_id": session_id,
                "ip_address": ip_address,
                "user_agent": user_agent,
                "timestamp": datetime.now().isoformat(),
                "date": datetime.now().strftime("%Y-%m-%d"),
                "hour": datetime.now().hour
            }
            
            # 存储到缓存（用于实时分析）
            await self._store_to_cache(event_record)
            
            # 异步存储到数据库（可以考虑使用消息队列）
            await self._store_to_database(event_record)
            
            # 更新实时统计
            await self._update_real_time_stats(event_record)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to track event: {e}")
            return False
    
    async def _store_to_cache(self, event_record: Dict[str, Any]):
        """存储事件到缓存"""
        try:
            # 存储原始事件（保留24小时）
            event_key = f"event:{event_record['timestamp']}:{event_record['user_id']}"
            await cache_service.set(event_key, event_record, expire=86400)
            
            # 添加到事件列表（用于批量处理）
            list_key = f"events:{event_record['date']}"
            await cache_service.lpush(list_key, json.dumps(event_record))
            await cache_service.expire(list_key, 86400 * 7)  # 保留7天
            
        except Exception as e:
            logger.error(f"Failed to store event to cache: {e}")
    
    async def _store_to_database(self, event_record: Dict[str, Any]):
        """存储事件到数据库（这里可以扩展为专门的事件表）"""
        try:
            # 目前先记录到日志，后续可以创建专门的事件表
            logger.info(f"User behavior event: {json.dumps(event_record)}")
            
        except Exception as e:
            logger.error(f"Failed to store event to database: {e}")
    
    async def _update_real_time_stats(self, event_record: Dict[str, Any]):
        """更新实时统计"""
        try:
            event_type = event_record["event_type"]
            date = event_record["date"]
            hour = event_record["hour"]
            user_id = event_record["user_id"]
            
            # 更新日统计
            daily_key = f"stats:daily:{event_type}:{date}"
            await cache_service.incr(daily_key, expire=86400 * 30)  # 保留30天
            
            # 更新小时统计
            hourly_key = f"stats:hourly:{event_type}:{date}:{hour}"
            await cache_service.incr(hourly_key, expire=86400 * 7)  # 保留7天
            
            # 更新用户统计
            if user_id:
                user_key = f"stats:user:{user_id}:{event_type}:{date}"
                await cache_service.incr(user_key, expire=86400 * 30)
            
        except Exception as e:
            logger.error(f"Failed to update real-time stats: {e}")
    
    async def get_user_behavior_summary(
        self,
        user_id: int,
        days: int = 7
    ) -> Dict[str, Any]:
        """
        获取用户行为摘要
        
        Args:
            user_id: 用户ID
            days: 统计天数
            
        Returns:
            用户行为摘要
        """
        try:
            summary = {
                "user_id": user_id,
                "period": {
                    "days": days,
                    "start_date": (datetime.now() - timedelta(days=days)).strftime("%Y-%m-%d"),
                    "end_date": datetime.now().strftime("%Y-%m-%d")
                },
                "events": {},
                "engagement": {
                    "push_open_rate": 0.0,
                    "push_click_rate": 0.0,
                    "news_view_count": 0,
                    "active_days": 0
                },
                "preferences": {
                    "most_active_hour": 0,
                    "preferred_channels": [],
                    "top_categories": []
                }
            }
            
            # 获取各类事件统计
            for event_type in EventType:
                event_count = 0
                for i in range(days):
                    date = (datetime.now() - timedelta(days=i)).strftime("%Y-%m-%d")
                    user_key = f"stats:user:{user_id}:{event_type.value}:{date}"
                    daily_count = await cache_service.get(user_key, 0)
                    event_count += daily_count
                
                summary["events"][event_type.value] = event_count
            
            # 计算参与度指标
            push_sent = summary["events"].get("push_sent", 0)
            push_opened = summary["events"].get("push_opened", 0)
            push_clicked = summary["events"].get("push_clicked", 0)
            
            if push_sent > 0:
                summary["engagement"]["push_open_rate"] = (push_opened / push_sent) * 100
                summary["engagement"]["push_click_rate"] = (push_clicked / push_sent) * 100
            
            summary["engagement"]["news_view_count"] = summary["events"].get("news_viewed", 0)
            
            # 计算活跃天数
            active_days = 0
            for i in range(days):
                date = (datetime.now() - timedelta(days=i)).strftime("%Y-%m-%d")
                day_active = False
                for event_type in EventType:
                    user_key = f"stats:user:{user_id}:{event_type.value}:{date}"
                    if await cache_service.get(user_key, 0) > 0:
                        day_active = True
                        break
                if day_active:
                    active_days += 1
            
            summary["engagement"]["active_days"] = active_days
            
            # 分析最活跃时间
            hour_activity = {}
            for hour in range(24):
                hour_count = 0
                for i in range(days):
                    date = (datetime.now() - timedelta(days=i)).strftime("%Y-%m-%d")
                    for event_type in EventType:
                        hourly_key = f"stats:hourly:{event_type.value}:{date}:{hour}"
                        hour_count += await cache_service.get(hourly_key, 0)
                hour_activity[hour] = hour_count
            
            if hour_activity:
                summary["preferences"]["most_active_hour"] = max(hour_activity, key=hour_activity.get)
            
            return summary
            
        except Exception as e:
            logger.error(f"Failed to get user behavior summary: {e}")
            return {}
    
    async def get_push_analytics(
        self,
        days: int = 7,
        channel: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取推送分析数据
        
        Args:
            days: 统计天数
            channel: 推送渠道过滤
            
        Returns:
            推送分析数据
        """
        try:
            analytics = {
                "period": {
                    "days": days,
                    "start_date": (datetime.now() - timedelta(days=days)).strftime("%Y-%m-%d"),
                    "end_date": datetime.now().strftime("%Y-%m-%d")
                },
                "overview": {
                    "total_sent": 0,
                    "total_delivered": 0,
                    "total_opened": 0,
                    "total_clicked": 0,
                    "delivery_rate": 0.0,
                    "open_rate": 0.0,
                    "click_rate": 0.0
                },
                "daily_breakdown": {},
                "hourly_pattern": {},
                "channel_performance": {}
            }
            
            # 获取总体统计
            for i in range(days):
                date = (datetime.now() - timedelta(days=i)).strftime("%Y-%m-%d")
                
                daily_stats = {
                    "sent": await cache_service.get(f"stats:daily:push_sent:{date}", 0),
                    "delivered": await cache_service.get(f"stats:daily:push_delivered:{date}", 0),
                    "opened": await cache_service.get(f"stats:daily:push_opened:{date}", 0),
                    "clicked": await cache_service.get(f"stats:daily:push_clicked:{date}", 0)
                }
                
                analytics["daily_breakdown"][date] = daily_stats
                
                # 累加到总计
                analytics["overview"]["total_sent"] += daily_stats["sent"]
                analytics["overview"]["total_delivered"] += daily_stats["delivered"]
                analytics["overview"]["total_opened"] += daily_stats["opened"]
                analytics["overview"]["total_clicked"] += daily_stats["clicked"]
            
            # 计算比率
            total_sent = analytics["overview"]["total_sent"]
            if total_sent > 0:
                analytics["overview"]["delivery_rate"] = (analytics["overview"]["total_delivered"] / total_sent) * 100
                analytics["overview"]["open_rate"] = (analytics["overview"]["total_opened"] / total_sent) * 100
                analytics["overview"]["click_rate"] = (analytics["overview"]["total_clicked"] / total_sent) * 100
            
            # 获取小时模式
            for hour in range(24):
                hour_stats = {
                    "sent": 0,
                    "opened": 0,
                    "clicked": 0
                }
                
                for i in range(days):
                    date = (datetime.now() - timedelta(days=i)).strftime("%Y-%m-%d")
                    hour_stats["sent"] += await cache_service.get(f"stats:hourly:push_sent:{date}:{hour}", 0)
                    hour_stats["opened"] += await cache_service.get(f"stats:hourly:push_opened:{date}:{hour}", 0)
                    hour_stats["clicked"] += await cache_service.get(f"stats:hourly:push_clicked:{date}:{hour}", 0)
                
                analytics["hourly_pattern"][hour] = hour_stats
            
            return analytics
            
        except Exception as e:
            logger.error(f"Failed to get push analytics: {e}")
            return {}
    
    async def track_push_interaction(
        self,
        push_log_id: int,
        interaction_type: str,
        user_id: Optional[int] = None,
        additional_data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        追踪推送交互
        
        Args:
            push_log_id: 推送日志ID
            interaction_type: 交互类型（opened/clicked/dismissed）
            user_id: 用户ID
            additional_data: 额外数据
            
        Returns:
            是否成功
        """
        try:
            # 查询推送日志
            push_log = self.db.query(PushLog).filter(PushLog.id == push_log_id).first()
            if not push_log:
                logger.warning(f"推送日志不存在: {push_log_id}")
                return False

            # 构建事件数据
            event_data = {
                "push_log_id": push_log_id,
                "interaction_type": interaction_type,
                "channel": push_log.channel or "unknown",
                "additional_data": additional_data or {}
            }
            
            # 确定事件类型
            event_type_map = {
                "opened": EventType.PUSH_OPENED,
                "clicked": EventType.PUSH_CLICKED,
                "delivered": EventType.PUSH_DELIVERED
            }
            
            event_type = event_type_map.get(interaction_type)
            if not event_type:
                logger.warning(f"Unknown interaction type: {interaction_type}")
                return False
            
            # 追踪事件
            return await self.track_event(
                user_id=user_id or push_log.user_id,
                event_type=event_type,
                event_data=event_data
            )
            
        except Exception as e:
            logger.error(f"Failed to track push interaction: {e}")
            return False
