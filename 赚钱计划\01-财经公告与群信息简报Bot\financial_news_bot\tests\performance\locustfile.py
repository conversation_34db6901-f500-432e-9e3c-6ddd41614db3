"""
Locust负载测试配置
模拟真实用户行为，测试系统在高负载下的性能表现
"""
import random
import json
from locust import HttpUser, task, between, events
from locust.contrib.fasthttp import FastHttpUser
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FinancialNewsBotUser(FastHttpUser):
    """财经新闻Bot用户行为模拟"""
    
    # 用户行为间隔时间（秒）
    wait_time = between(1, 5)
    
    # 测试数据
    test_users = [
        {"username": f"test_user_{i}", "password": "test123456"} 
        for i in range(1, 101)
    ]
    
    def on_start(self):
        """用户开始时的初始化操作"""
        self.user_data = random.choice(self.test_users)
        self.token = None
        self.user_id = None
        
        # 尝试登录
        self.login()
    
    def login(self):
        """用户登录"""
        login_data = {
            "username": self.user_data["username"],
            "password": self.user_data["password"]
        }
        
        with self.client.post(
            "/api/v1/auth/login",
            data=login_data,
            catch_response=True,
            name="用户登录"
        ) as response:
            if response.status_code == 200:
                try:
                    result = response.json()
                    self.token = result.get("access_token")
                    self.user_id = result.get("user_id")
                    response.success()
                    logger.info(f"用户 {self.user_data['username']} 登录成功")
                except Exception as e:
                    response.failure(f"登录响应解析失败: {e}")
            elif response.status_code == 401:
                # 用户不存在，尝试注册
                self.register()
            else:
                response.failure(f"登录失败: {response.status_code}")
    
    def register(self):
        """用户注册"""
        register_data = {
            "username": self.user_data["username"],
            "email": f"{self.user_data['username']}@test.com",
            "full_name": f"Test User {self.user_data['username']}",
            "password": self.user_data["password"]
        }
        
        with self.client.post(
            "/api/v1/auth/register",
            json=register_data,
            catch_response=True,
            name="用户注册"
        ) as response:
            if response.status_code == 201:
                response.success()
                logger.info(f"用户 {self.user_data['username']} 注册成功")
                # 注册成功后登录
                self.login()
            else:
                response.failure(f"注册失败: {response.status_code}")
    
    def get_auth_headers(self):
        """获取认证头"""
        if self.token:
            return {"Authorization": f"Bearer {self.token}"}
        return {}
    
    @task(10)
    def browse_news_list(self):
        """浏览新闻列表 - 最常见的操作"""
        params = {
            "page": random.randint(1, 5),
            "size": random.choice([10, 20, 50]),
            "category": random.choice(["", "finance", "stock_market", "monetary_policy"])
        }
        
        with self.client.get(
            "/api/v1/news/",
            params=params,
            headers=self.get_auth_headers(),
            catch_response=True,
            name="浏览新闻列表"
        ) as response:
            if response.status_code == 200:
                try:
                    data = response.json()
                    if "items" in data and len(data["items"]) > 0:
                        response.success()
                    else:
                        response.failure("新闻列表为空")
                except Exception as e:
                    response.failure(f"响应解析失败: {e}")
            else:
                response.failure(f"请求失败: {response.status_code}")
    
    @task(5)
    def search_news(self):
        """搜索新闻"""
        search_terms = ["央行", "股市", "基金", "投资", "经济", "政策", "金融"]
        query = random.choice(search_terms)
        
        params = {
            "q": query,
            "page": 1,
            "size": 20
        }
        
        with self.client.get(
            "/api/v1/news/search",
            params=params,
            headers=self.get_auth_headers(),
            catch_response=True,
            name="搜索新闻"
        ) as response:
            if response.status_code == 200:
                try:
                    data = response.json()
                    response.success()
                except Exception as e:
                    response.failure(f"搜索响应解析失败: {e}")
            else:
                response.failure(f"搜索失败: {response.status_code}")
    
    @task(3)
    def view_news_detail(self):
        """查看新闻详情"""
        # 先获取新闻列表
        with self.client.get(
            "/api/v1/news/",
            params={"size": 10},
            headers=self.get_auth_headers(),
            catch_response=True,
            name="获取新闻列表(详情页)"
        ) as response:
            if response.status_code == 200:
                try:
                    data = response.json()
                    if data.get("items"):
                        news_id = random.choice(data["items"])["id"]
                        
                        # 查看新闻详情
                        with self.client.get(
                            f"/api/v1/news/{news_id}",
                            headers=self.get_auth_headers(),
                            catch_response=True,
                            name="查看新闻详情"
                        ) as detail_response:
                            if detail_response.status_code == 200:
                                detail_response.success()
                            else:
                                detail_response.failure(f"详情页加载失败: {detail_response.status_code}")
                    else:
                        response.failure("没有可用的新闻")
                except Exception as e:
                    response.failure(f"新闻列表解析失败: {e}")
    
    @task(2)
    def manage_subscriptions(self):
        """管理订阅"""
        if not self.token:
            return
        
        # 获取订阅列表
        with self.client.get(
            "/api/v1/subscriptions/",
            headers=self.get_auth_headers(),
            catch_response=True,
            name="获取订阅列表"
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"获取订阅失败: {response.status_code}")
    
    @task(1)
    def create_subscription(self):
        """创建订阅"""
        if not self.token:
            return
        
        subscription_data = {
            "name": f"测试订阅_{random.randint(1, 1000)}",
            "description": "负载测试创建的订阅",
            "keywords": random.sample(["央行", "股市", "基金", "投资", "经济"], 2),
            "categories": [random.choice(["finance", "stock_market", "monetary_policy"])],
            "is_active": True
        }
        
        with self.client.post(
            "/api/v1/subscriptions/",
            json=subscription_data,
            headers=self.get_auth_headers(),
            catch_response=True,
            name="创建订阅"
        ) as response:
            if response.status_code == 201:
                response.success()
            else:
                response.failure(f"创建订阅失败: {response.status_code}")
    
    @task(1)
    def bookmark_news(self):
        """收藏新闻"""
        if not self.token:
            return
        
        # 先获取新闻列表
        with self.client.get(
            "/api/v1/news/",
            params={"size": 5},
            headers=self.get_auth_headers(),
            catch_response=True,
            name="获取新闻列表(收藏)"
        ) as response:
            if response.status_code == 200:
                try:
                    data = response.json()
                    if data.get("items"):
                        news_id = random.choice(data["items"])["id"]
                        
                        # 收藏新闻
                        with self.client.post(
                            f"/api/v1/news/{news_id}/bookmark",
                            headers=self.get_auth_headers(),
                            catch_response=True,
                            name="收藏新闻"
                        ) as bookmark_response:
                            if bookmark_response.status_code in [200, 201]:
                                bookmark_response.success()
                            else:
                                bookmark_response.failure(f"收藏失败: {bookmark_response.status_code}")
                except Exception as e:
                    response.failure(f"收藏操作失败: {e}")
    
    @task(1)
    def get_user_profile(self):
        """获取用户资料"""
        if not self.token:
            return
        
        with self.client.get(
            "/api/v1/auth/me",
            headers=self.get_auth_headers(),
            catch_response=True,
            name="获取用户资料"
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"获取用户资料失败: {response.status_code}")


class AdminUser(FastHttpUser):
    """管理员用户行为模拟"""
    
    wait_time = between(2, 8)
    weight = 1  # 管理员用户权重较低
    
    def on_start(self):
        """管理员登录"""
        self.token = None
        self.admin_login()
    
    def admin_login(self):
        """管理员登录"""
        login_data = {
            "username": "admin",
            "password": "admin123456"
        }
        
        with self.client.post(
            "/api/v1/auth/login",
            data=login_data,
            catch_response=True,
            name="管理员登录"
        ) as response:
            if response.status_code == 200:
                try:
                    result = response.json()
                    self.token = result.get("access_token")
                    response.success()
                except Exception as e:
                    response.failure(f"管理员登录失败: {e}")
    
    def get_auth_headers(self):
        """获取认证头"""
        if self.token:
            return {"Authorization": f"Bearer {self.token}"}
        return {}
    
    @task(5)
    def view_admin_dashboard(self):
        """查看管理后台"""
        with self.client.get(
            "/api/v1/admin/dashboard",
            headers=self.get_auth_headers(),
            catch_response=True,
            name="管理后台首页"
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"管理后台访问失败: {response.status_code}")
    
    @task(3)
    def manage_users(self):
        """管理用户"""
        with self.client.get(
            "/api/v1/admin/users",
            headers=self.get_auth_headers(),
            catch_response=True,
            name="用户管理"
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"用户管理访问失败: {response.status_code}")
    
    @task(2)
    def view_system_stats(self):
        """查看系统统计"""
        with self.client.get(
            "/api/v1/admin/statistics",
            headers=self.get_auth_headers(),
            catch_response=True,
            name="系统统计"
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"系统统计访问失败: {response.status_code}")


class AnonymousUser(FastHttpUser):
    """匿名用户行为模拟"""
    
    wait_time = between(3, 10)
    weight = 2  # 匿名用户权重中等
    
    @task(10)
    def browse_public_news(self):
        """浏览公开新闻"""
        with self.client.get(
            "/api/v1/news/public",
            catch_response=True,
            name="浏览公开新闻"
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"公开新闻访问失败: {response.status_code}")
    
    @task(5)
    def view_homepage(self):
        """访问首页"""
        with self.client.get(
            "/",
            catch_response=True,
            name="访问首页"
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"首页访问失败: {response.status_code}")
    
    @task(3)
    def check_api_docs(self):
        """查看API文档"""
        with self.client.get(
            "/docs",
            catch_response=True,
            name="API文档"
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"API文档访问失败: {response.status_code}")


# 事件监听器
@events.test_start.add_listener
def on_test_start(environment, **kwargs):
    """测试开始时的初始化"""
    logger.info("🚀 负载测试开始")
    logger.info(f"目标主机: {environment.host}")
    logger.info(f"用户数: {environment.runner.target_user_count if hasattr(environment.runner, 'target_user_count') else '未知'}")

@events.test_stop.add_listener
def on_test_stop(environment, **kwargs):
    """测试结束时的清理"""
    logger.info("🏁 负载测试结束")
    
    # 输出测试统计
    stats = environment.runner.stats
    logger.info(f"总请求数: {stats.total.num_requests}")
    logger.info(f"失败请求数: {stats.total.num_failures}")
    logger.info(f"平均响应时间: {stats.total.avg_response_time:.2f}ms")
    logger.info(f"最大响应时间: {stats.total.max_response_time:.2f}ms")

@events.request_failure.add_listener
def on_request_failure(request_type, name, response_time, response_length, exception, **kwargs):
    """请求失败时的处理"""
    logger.warning(f"请求失败: {name} - {exception}")

# 自定义用户类权重配置
# FinancialNewsBotUser: 普通用户，权重最高
# AdminUser: 管理员用户，权重最低
# AnonymousUser: 匿名用户，权重中等
