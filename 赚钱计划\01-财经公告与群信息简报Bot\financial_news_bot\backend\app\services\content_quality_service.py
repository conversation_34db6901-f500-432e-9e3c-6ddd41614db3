"""
内容质量检查服务
专门负责内容质量评估和验证
"""
import re
import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

from ..utils.text_processor import get_text_processor

logger = logging.getLogger(__name__)


@dataclass
class QualityIssue:
    """质量问题"""
    type: str  # 问题类型
    severity: str  # 严重程度: low, medium, high, critical
    message: str  # 问题描述
    field: Optional[str] = None  # 相关字段
    suggestion: Optional[str] = None  # 修复建议


@dataclass
class ContentQualityResult:
    """内容质量检查结果"""
    is_valid: bool
    overall_score: float  # 0-100分
    issues: List[QualityIssue]
    missing_fields: List[str]
    warnings: List[str]
    metrics: Dict[str, Any]
    checked_at: datetime


class ContentQualityService:
    """内容质量检查服务"""
    
    def __init__(self):
        self.text_processor = get_text_processor()
        self._spam_patterns = self._load_spam_patterns()
        self._required_fields = ['title', 'content', 'source']
        self._min_content_length = 50
        self._max_content_length = 50000
        self._min_title_length = 5
        self._max_title_length = 200
    
    def _load_spam_patterns(self) -> List[str]:
        """加载垃圾内容模式"""
        return [
            r'点击.*?链接',
            r'立即.*?购买',
            r'限时.*?优惠',
            r'加.*?微信',
            r'扫.*?二维码',
            r'免费.*?领取',
            r'赚钱.*?机会',
            r'投资.*?回报.*?\d+%',
            r'保证.*?收益',
            r'无风险.*?投资'
        ]
    
    def check_content_quality(
        self, 
        title: str, 
        content: str, 
        source: Optional[str] = None,
        category: Optional[str] = None,
        **kwargs
    ) -> ContentQualityResult:
        """
        综合内容质量检查
        
        Args:
            title: 标题
            content: 内容
            source: 来源
            category: 分类
            **kwargs: 其他字段
            
        Returns:
            质量检查结果
        """
        issues = []
        missing_fields = []
        warnings = []
        metrics = {}
        
        # 检查必填字段
        field_data = {'title': title, 'content': content, 'source': source}
        for field in self._required_fields:
            if not field_data.get(field):
                missing_fields.append(field)
                issues.append(QualityIssue(
                    type='missing_field',
                    severity='critical',
                    message=f'缺少必填字段: {field}',
                    field=field,
                    suggestion=f'请提供{field}字段的值'
                ))
        
        # 标题质量检查
        title_issues, title_metrics = self._check_title_quality(title)
        issues.extend(title_issues)
        metrics.update(title_metrics)
        
        # 内容质量检查
        content_issues, content_metrics = self._check_content_quality(content)
        issues.extend(content_issues)
        metrics.update(content_metrics)
        
        # 垃圾内容检查
        spam_issues = self._check_spam_content(title, content)
        issues.extend(spam_issues)
        
        # 重复内容检查
        duplicate_issues = self._check_duplicate_content(title, content)
        issues.extend(duplicate_issues)
        
        # 计算总体分数
        overall_score = self._calculate_overall_score(issues, metrics)
        
        # 判断是否有效
        is_valid = (
            len(missing_fields) == 0 and
            overall_score >= 60 and
            not any(issue.severity == 'critical' for issue in issues)
        )
        
        return ContentQualityResult(
            is_valid=is_valid,
            overall_score=overall_score,
            issues=issues,
            missing_fields=missing_fields,
            warnings=warnings,
            metrics=metrics,
            checked_at=datetime.now()
        )
    
    def _check_title_quality(self, title: str) -> Tuple[List[QualityIssue], Dict[str, Any]]:
        """检查标题质量"""
        issues = []
        metrics = {}
        
        if not title:
            return issues, metrics
        
        title_length = len(title)
        metrics['title_length'] = title_length
        
        # 长度检查
        if title_length < self._min_title_length:
            issues.append(QualityIssue(
                type='title_too_short',
                severity='high',
                message=f'标题过短（{title_length}字符），建议至少{self._min_title_length}字符',
                field='title',
                suggestion='请提供更详细的标题'
            ))
        elif title_length > self._max_title_length:
            issues.append(QualityIssue(
                type='title_too_long',
                severity='medium',
                message=f'标题过长（{title_length}字符），建议不超过{self._max_title_length}字符',
                field='title',
                suggestion='请简化标题内容'
            ))
        
        # 特殊字符检查
        special_char_count = len(re.findall(r'[!@#$%^&*()_+={}\[\]|\\:";\'<>?,./]', title))
        metrics['title_special_chars'] = special_char_count
        
        if special_char_count > title_length * 0.3:
            issues.append(QualityIssue(
                type='too_many_special_chars',
                severity='medium',
                message='标题包含过多特殊字符',
                field='title',
                suggestion='减少特殊字符的使用'
            ))
        
        # 全大写检查
        if title.isupper() and len(title) > 10:
            issues.append(QualityIssue(
                type='all_uppercase',
                severity='low',
                message='标题全部为大写字母',
                field='title',
                suggestion='使用正常的大小写格式'
            ))
        
        return issues, metrics
    
    def _check_content_quality(self, content: str) -> Tuple[List[QualityIssue], Dict[str, Any]]:
        """检查内容质量"""
        issues = []
        metrics = {}
        
        if not content:
            return issues, metrics
        
        content_length = len(content)
        metrics['content_length'] = content_length
        
        # 长度检查
        if content_length < self._min_content_length:
            issues.append(QualityIssue(
                type='content_too_short',
                severity='high',
                message=f'内容过短（{content_length}字符），建议至少{self._min_content_length}字符',
                field='content',
                suggestion='请提供更详细的内容'
            ))
        elif content_length > self._max_content_length:
            issues.append(QualityIssue(
                type='content_too_long',
                severity='medium',
                message=f'内容过长（{content_length}字符），建议不超过{self._max_content_length}字符',
                field='content',
                suggestion='请精简内容或分段发布'
            ))
        
        # 段落结构检查
        paragraphs = content.split('\n\n')
        metrics['paragraph_count'] = len(paragraphs)
        
        if len(paragraphs) == 1 and content_length > 1000:
            issues.append(QualityIssue(
                type='poor_structure',
                severity='low',
                message='长内容缺少段落分隔',
                field='content',
                suggestion='建议将内容分成多个段落'
            ))
        
        # 重复句子检查
        sentences = re.split(r'[。！？.!?]', content)
        unique_sentences = set(s.strip() for s in sentences if s.strip())
        duplicate_ratio = 1 - (len(unique_sentences) / len(sentences)) if sentences else 0
        metrics['duplicate_sentence_ratio'] = duplicate_ratio
        
        if duplicate_ratio > 0.3:
            issues.append(QualityIssue(
                type='high_duplication',
                severity='medium',
                message=f'内容重复度较高（{duplicate_ratio:.1%}）',
                field='content',
                suggestion='减少重复内容'
            ))
        
        return issues, metrics
    
    def _check_spam_content(self, title: str, content: str) -> List[QualityIssue]:
        """检查垃圾内容"""
        issues = []
        full_text = f"{title} {content}"
        
        for pattern in self._spam_patterns:
            if re.search(pattern, full_text, re.IGNORECASE):
                issues.append(QualityIssue(
                    type='spam_content',
                    severity='high',
                    message=f'检测到可能的垃圾内容模式: {pattern}',
                    suggestion='请移除营销性质的内容'
                ))
        
        return issues
    
    def _check_duplicate_content(self, title: str, content: str) -> List[QualityIssue]:
        """检查重复内容（简化版本）"""
        issues = []
        
        # 检查标题和内容的重复
        if title and content:
            title_clean = self.text_processor.normalize_text(title)
            content_clean = self.text_processor.normalize_text(content)
            
            if title_clean in content_clean and len(title_clean) > 10:
                issues.append(QualityIssue(
                    type='title_content_duplicate',
                    severity='low',
                    message='标题内容在正文中完全重复',
                    suggestion='避免在正文中重复标题'
                ))
        
        return issues
    
    def _calculate_overall_score(self, issues: List[QualityIssue], metrics: Dict[str, Any]) -> float:
        """计算总体质量分数"""
        base_score = 100.0
        
        # 根据问题严重程度扣分
        for issue in issues:
            if issue.severity == 'critical':
                base_score -= 30
            elif issue.severity == 'high':
                base_score -= 15
            elif issue.severity == 'medium':
                base_score -= 8
            elif issue.severity == 'low':
                base_score -= 3
        
        # 根据指标调整分数
        if 'content_length' in metrics:
            length = metrics['content_length']
            if 100 <= length <= 2000:  # 理想长度范围
                base_score += 5
        
        if 'duplicate_sentence_ratio' in metrics:
            ratio = metrics['duplicate_sentence_ratio']
            if ratio < 0.1:  # 低重复率
                base_score += 5
        
        return max(0.0, min(100.0, base_score))
    
    def get_quality_summary(self, result: ContentQualityResult) -> Dict[str, Any]:
        """获取质量检查摘要"""
        return {
            'is_valid': result.is_valid,
            'score': result.overall_score,
            'grade': self._get_quality_grade(result.overall_score),
            'critical_issues': len([i for i in result.issues if i.severity == 'critical']),
            'high_issues': len([i for i in result.issues if i.severity == 'high']),
            'total_issues': len(result.issues),
            'missing_fields': len(result.missing_fields),
            'main_problems': [i.message for i in result.issues[:3]]  # 前3个主要问题
        }
    
    def _get_quality_grade(self, score: float) -> str:
        """获取质量等级"""
        if score >= 90:
            return 'A'
        elif score >= 80:
            return 'B'
        elif score >= 70:
            return 'C'
        elif score >= 60:
            return 'D'
        else:
            return 'F'


# 全局实例
quality_service = ContentQualityService()


def get_quality_service() -> ContentQualityService:
    """获取内容质量检查服务实例"""
    return quality_service
