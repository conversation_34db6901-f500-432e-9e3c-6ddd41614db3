import { test, expect, Page } from '@playwright/test';

/**
 * 用户完整旅程测试
 * 测试从注册到使用的完整流程
 */
test.describe('用户完整旅程', () => {
  let page: Page;
  const testUser = {
    username: `test_user_${Date.now()}`,
    email: `test_${Date.now()}@example.com`,
    fullName: 'Test User',
    password: 'test123456'
  };

  test.beforeEach(async ({ browser }) => {
    page = await browser.newPage();
  });

  test.afterEach(async () => {
    await page.close();
  });

  test('完整用户旅程：注册 → 登录 → 创建订阅 → 浏览新闻 → 收藏', async () => {
    // 步骤1: 用户注册
    await test.step('用户注册', async () => {
      await page.goto('/auth/register');
      
      // 等待页面加载
      await expect(page.locator('[data-testid="register-form"]')).toBeVisible();
      
      // 填写注册表单
      await page.fill('[data-testid="username-input"]', testUser.username);
      await page.fill('[data-testid="email-input"]', testUser.email);
      await page.fill('[data-testid="fullname-input"]', testUser.fullName);
      await page.fill('[data-testid="password-input"]', testUser.password);
      await page.fill('[data-testid="confirm-password-input"]', testUser.password);
      
      // 提交注册
      await page.click('[data-testid="register-button"]');
      
      // 验证注册成功
      await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
      await expect(page).toHaveURL('/auth/login');
    });

    // 步骤2: 用户登录
    await test.step('用户登录', async () => {
      // 填写登录表单
      await page.fill('[data-testid="username-input"]', testUser.username);
      await page.fill('[data-testid="password-input"]', testUser.password);
      
      // 提交登录
      await page.click('[data-testid="login-button"]');
      
      // 验证登录成功，跳转到仪表板
      await expect(page).toHaveURL('/dashboard');
      await expect(page.locator('[data-testid="user-welcome"]')).toContainText(testUser.fullName);
    });

    // 步骤3: 查看用户引导
    await test.step('查看用户引导', async () => {
      // 检查是否显示新用户引导
      const guideModal = page.locator('[data-testid="user-guide-modal"]');
      if (await guideModal.isVisible()) {
        // 跳过引导或完成引导
        await page.click('[data-testid="skip-guide-button"]');
      }
    });

    // 步骤4: 创建订阅
    await test.step('创建订阅', async () => {
      // 导航到订阅页面
      await page.click('[data-testid="nav-subscriptions"]');
      await expect(page).toHaveURL('/subscriptions');
      
      // 点击创建订阅按钮
      await page.click('[data-testid="create-subscription-button"]');
      await expect(page).toHaveURL('/subscriptions/create');
      
      // 填写订阅表单
      await page.fill('[data-testid="subscription-name"]', '我的财经订阅');
      await page.fill('[data-testid="subscription-description"]', '关注最新财经动态');
      
      // 选择关键词
      await page.fill('[data-testid="keywords-input"]', '央行,货币政策,股市');
      
      // 选择分类
      await page.click('[data-testid="category-select"]');
      await page.click('[data-testid="category-option-monetary_policy"]');
      await page.click('[data-testid="category-option-stock_market"]');
      
      // 选择推送渠道
      await page.check('[data-testid="channel-email"]');
      await page.check('[data-testid="channel-wechat"]');
      
      // 设置推送时间
      await page.selectOption('[data-testid="push-time-select"]', '09:00');
      
      // 提交创建
      await page.click('[data-testid="create-subscription-submit"]');
      
      // 验证创建成功
      await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
      await expect(page).toHaveURL('/subscriptions');
      
      // 验证订阅出现在列表中
      await expect(page.locator('[data-testid="subscription-item"]')).toContainText('我的财经订阅');
    });

    // 步骤5: 浏览新闻
    await test.step('浏览新闻', async () => {
      // 导航到新闻页面
      await page.click('[data-testid="nav-news"]');
      await expect(page).toHaveURL('/news');
      
      // 等待新闻列表加载
      await expect(page.locator('[data-testid="news-list"]')).toBeVisible();
      
      // 验证有新闻显示
      const newsItems = page.locator('[data-testid="news-item"]');
      await expect(newsItems).toHaveCountGreaterThan(0);
      
      // 点击第一条新闻
      await newsItems.first().click();
      
      // 验证新闻详情页
      await expect(page.locator('[data-testid="news-detail"]')).toBeVisible();
      await expect(page.locator('[data-testid="news-title"]')).toBeVisible();
      await expect(page.locator('[data-testid="news-content"]')).toBeVisible();
    });

    // 步骤6: 收藏新闻
    await test.step('收藏新闻', async () => {
      // 点击收藏按钮
      await page.click('[data-testid="bookmark-button"]');
      
      // 验证收藏成功
      await expect(page.locator('[data-testid="bookmark-success"]')).toBeVisible();
      await expect(page.locator('[data-testid="bookmark-button"]')).toHaveClass(/bookmarked/);
      
      // 导航到收藏页面验证
      await page.click('[data-testid="nav-bookmarks"]');
      await expect(page).toHaveURL('/bookmarks');
      
      // 验证收藏的新闻出现在列表中
      await expect(page.locator('[data-testid="bookmark-item"]')).toHaveCountGreaterThan(0);
    });

    // 步骤7: 搜索功能
    await test.step('搜索功能', async () => {
      // 使用全局搜索
      await page.keyboard.press('Control+k');
      await expect(page.locator('[data-testid="search-modal"]')).toBeVisible();
      
      // 输入搜索关键词
      await page.fill('[data-testid="search-input"]', '央行');
      
      // 等待搜索结果
      await expect(page.locator('[data-testid="search-results"]')).toBeVisible();
      
      // 点击搜索结果
      await page.click('[data-testid="search-result-item"]');
      
      // 验证跳转到搜索结果页
      await expect(page).toHaveURL(/\/news\?.*q=央行/);
    });

    // 步骤8: 个人设置
    await test.step('个人设置', async () => {
      // 点击用户头像
      await page.click('[data-testid="user-avatar"]');
      
      // 点击个人设置
      await page.click('[data-testid="user-menu-settings"]');
      await expect(page).toHaveURL('/profile/settings');
      
      // 更新个人信息
      await page.fill('[data-testid="fullname-input"]', 'Updated Test User');
      await page.click('[data-testid="save-profile-button"]');
      
      // 验证更新成功
      await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    });

    // 步骤9: 退出登录
    await test.step('退出登录', async () => {
      // 点击用户头像
      await page.click('[data-testid="user-avatar"]');
      
      // 点击退出登录
      await page.click('[data-testid="user-menu-logout"]');
      
      // 验证退出成功，跳转到登录页
      await expect(page).toHaveURL('/auth/login');
      await expect(page.locator('[data-testid="login-form"]')).toBeVisible();
    });
  });

  test('移动端用户旅程', async ({ browser }) => {
    // 创建移动端上下文
    const context = await browser.newContext({
      ...browser.contexts()[0],
      viewport: { width: 375, height: 667 }, // iPhone SE尺寸
      userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
    });
    
    const mobilePage = await context.newPage();

    await test.step('移动端注册和登录', async () => {
      await mobilePage.goto('/auth/register');
      
      // 验证移动端布局
      await expect(mobilePage.locator('[data-testid="mobile-layout"]')).toBeVisible();
      
      // 填写注册表单（移动端）
      await mobilePage.fill('[data-testid="username-input"]', `mobile_${testUser.username}`);
      await mobilePage.fill('[data-testid="email-input"]', `mobile_${testUser.email}`);
      await mobilePage.fill('[data-testid="fullname-input"]', testUser.fullName);
      await mobilePage.fill('[data-testid="password-input"]', testUser.password);
      await mobilePage.fill('[data-testid="confirm-password-input"]', testUser.password);
      
      await mobilePage.click('[data-testid="register-button"]');
      
      // 登录
      await mobilePage.fill('[data-testid="username-input"]', `mobile_${testUser.username}`);
      await mobilePage.fill('[data-testid="password-input"]', testUser.password);
      await mobilePage.click('[data-testid="login-button"]');
      
      await expect(mobilePage).toHaveURL('/dashboard');
    });

    await test.step('移动端导航测试', async () => {
      // 测试移动端底部导航
      await expect(mobilePage.locator('[data-testid="mobile-bottom-nav"]')).toBeVisible();
      
      // 点击新闻标签
      await mobilePage.click('[data-testid="bottom-nav-news"]');
      await expect(mobilePage).toHaveURL('/news');
      
      // 点击订阅标签
      await mobilePage.click('[data-testid="bottom-nav-subscriptions"]');
      await expect(mobilePage).toHaveURL('/subscriptions');
      
      // 测试侧边栏菜单
      await mobilePage.click('[data-testid="mobile-menu-button"]');
      await expect(mobilePage.locator('[data-testid="mobile-sidebar"]')).toBeVisible();
    });

    await test.step('移动端触摸操作', async () => {
      await mobilePage.goto('/news');
      
      // 测试下拉刷新
      await mobilePage.touchscreen.tap(100, 100);
      await mobilePage.mouse.move(100, 100);
      await mobilePage.mouse.down();
      await mobilePage.mouse.move(100, 200);
      await mobilePage.mouse.up();
      
      // 测试滑动操作
      const newsItem = mobilePage.locator('[data-testid="news-item"]').first();
      await newsItem.hover();
      
      // 测试长按操作
      await newsItem.click({ button: 'right' });
    });

    await context.close();
  });
});
