"""
监控API路由
提供系统监控、告警管理和指标查询功能
"""
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, HTTPException, Depends, status, Response
from pydantic import BaseModel, Field
from datetime import datetime

from app.dependencies.auth import get_current_active_user
from app.services.integrated_monitoring_service import integrated_monitoring as monitoring_service
from app.models.user import User

# 创建路由器
router = APIRouter(prefix="/monitoring", tags=["监控管理"])


class AlertRuleRequest(BaseModel):
    """告警规则请求模型"""
    name: str = Field(..., description="规则名称")
    metric: str = Field(..., description="监控指标")
    operator: str = Field(..., description="比较操作符")
    threshold: float = Field(..., description="阈值")
    duration: int = Field(default=0, description="持续时间（秒）")
    severity: str = Field(default="warning", description="严重级别")
    enabled: bool = Field(default=True, description="是否启用")
    description: str = Field(default="", description="规则描述")


@router.get("/metrics/summary", response_model=Dict[str, Any])
async def get_metrics_summary(current_user: User = Depends(get_current_active_user)):
    """
    获取监控指标摘要
    """
    try:
        summary = monitoring_service.get_metrics_summary()
        return {
            "success": True,
            "message": "获取监控指标成功",
            "data": summary
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取监控指标失败: {str(e)}"
        )


@router.get("/metrics/crawler", response_model=Dict[str, Any])
async def get_crawler_metrics(current_user: User = Depends(get_current_active_user)):
    """
    获取爬虫监控指标
    """
    try:
        metrics = monitoring_service.collect_crawler_metrics()
        
        # 转换为字典格式
        metrics_data = []
        for metric in metrics:
            metrics_data.append({
                'source': metric.source,
                'success_count': metric.success_count,
                'failure_count': metric.failure_count,
                'success_rate': metric.success_rate,
                'last_success_time': metric.last_success_time.isoformat() if metric.last_success_time else None,
                'consecutive_failures': metric.consecutive_failures,
                'avg_response_time': metric.avg_response_time,
                'total_items_collected': metric.total_items_collected
            })
        
        return {
            "success": True,
            "message": "获取爬虫指标成功",
            "data": {
                "metrics": metrics_data,
                "timestamp": datetime.now().isoformat()
            }
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取爬虫指标失败: {str(e)}"
        )


@router.get("/metrics/system", response_model=Dict[str, Any])
async def get_system_metrics(current_user: User = Depends(get_current_active_user)):
    """
    获取系统监控指标
    """
    try:
        metrics = monitoring_service.collect_system_metrics()
        
        metrics_data = {
            'cpu_usage': metrics.cpu_usage,
            'memory_usage': metrics.memory_usage,
            'disk_usage': metrics.disk_usage,
            'network_io': metrics.network_io,
            'active_connections': metrics.active_connections,
            'queue_lengths': metrics.queue_lengths,
            'timestamp': metrics.timestamp.isoformat()
        }
        
        return {
            "success": True,
            "message": "获取系统指标成功",
            "data": metrics_data
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取系统指标失败: {str(e)}"
        )


@router.get("/alerts", response_model=Dict[str, Any])
async def get_alerts(current_user: User = Depends(get_current_active_user)):
    """
    获取当前告警信息
    """
    try:
        alerts = monitoring_service.check_alerts()
        
        return {
            "success": True,
            "message": "获取告警信息成功",
            "data": {
                "alerts": alerts,
                "total": len(alerts),
                "active_count": len([a for a in alerts if a.get('active', False)]),
                "timestamp": datetime.now().isoformat()
            }
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取告警信息失败: {str(e)}"
        )


@router.get("/alerts/rules", response_model=Dict[str, Any])
async def get_alert_rules(current_user: User = Depends(get_current_active_user)):
    """
    获取告警规则配置
    """
    try:
        rules = []
        for rule in monitoring_service.alert_rules:
            rules.append({
                'name': rule.name,
                'metric': rule.metric,
                'operator': rule.operator,
                'threshold': rule.threshold,
                'duration': rule.duration,
                'severity': rule.severity,
                'enabled': rule.enabled,
                'description': rule.description
            })
        
        return {
            "success": True,
            "message": "获取告警规则成功",
            "data": {
                "rules": rules,
                "total": len(rules),
                "enabled_count": len([r for r in monitoring_service.alert_rules if r.enabled])
            }
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取告警规则失败: {str(e)}"
        )


@router.post("/alerts/rules", response_model=Dict[str, Any])
async def create_alert_rule(
    rule_request: AlertRuleRequest,
    current_user: User = Depends(get_current_active_user)
):
    """
    创建告警规则
    """
    try:
        # 检查用户权限
        if current_user.role not in ["admin", "enterprise"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，需要管理员或企业版权限"
            )
        
        # 检查规则是否已存在
        existing_rule = next((r for r in monitoring_service.alert_rules if r.name == rule_request.name), None)
        if existing_rule:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"告警规则已存在: {rule_request.name}"
            )
        
        # 创建新规则
        from app.services.monitoring_service import AlertRule
        new_rule = AlertRule(
            name=rule_request.name,
            metric=rule_request.metric,
            operator=rule_request.operator,
            threshold=rule_request.threshold,
            duration=rule_request.duration,
            severity=rule_request.severity,
            enabled=rule_request.enabled,
            description=rule_request.description
        )
        
        monitoring_service.alert_rules.append(new_rule)
        
        return {
            "success": True,
            "message": f"告警规则创建成功: {rule_request.name}",
            "data": {
                "rule": {
                    'name': new_rule.name,
                    'metric': new_rule.metric,
                    'operator': new_rule.operator,
                    'threshold': new_rule.threshold,
                    'duration': new_rule.duration,
                    'severity': new_rule.severity,
                    'enabled': new_rule.enabled,
                    'description': new_rule.description
                }
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建告警规则失败: {str(e)}"
        )


@router.put("/alerts/rules/{rule_name}/toggle", response_model=Dict[str, Any])
async def toggle_alert_rule(
    rule_name: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    切换告警规则启用状态
    """
    try:
        # 检查用户权限
        if current_user.role not in ["admin", "enterprise"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，需要管理员或企业版权限"
            )
        
        # 查找规则
        rule = next((r for r in monitoring_service.alert_rules if r.name == rule_name), None)
        if not rule:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"告警规则不存在: {rule_name}"
            )
        
        # 切换状态
        rule.enabled = not rule.enabled
        
        return {
            "success": True,
            "message": f"告警规则已{'启用' if rule.enabled else '禁用'}: {rule_name}",
            "data": {
                "rule_name": rule_name,
                "enabled": rule.enabled
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"切换告警规则失败: {str(e)}"
        )


@router.get("/metrics/prometheus")
async def get_prometheus_metrics():
    """
    获取Prometheus格式的监控指标
    """
    try:
        metrics_text = monitoring_service.get_prometheus_metrics()
        return Response(
            content=metrics_text,
            media_type="text/plain; charset=utf-8"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取Prometheus指标失败: {str(e)}"
        )


@router.get("/health", response_model=Dict[str, Any])
async def monitoring_health_check():
    """
    监控系统健康检查
    """
    try:
        # 检查各个组件状态
        health_status = {
            "monitoring_service": True,
            "metrics_collection": True,
            "alert_system": True,
            "timestamp": datetime.now().isoformat()
        }
        
        # 尝试收集指标
        try:
            monitoring_service.collect_system_metrics()
        except Exception:
            health_status["metrics_collection"] = False
        
        # 尝试检查告警
        try:
            monitoring_service.check_alerts()
        except Exception:
            health_status["alert_system"] = False
        
        overall_health = all(health_status[k] for k in health_status if k != "timestamp")
        
        return {
            "success": True,
            "message": "监控系统健康检查完成",
            "data": {
                "overall_health": overall_health,
                "components": health_status
            }
        }
        
    except Exception as e:
        return {
            "success": False,
            "message": f"监控系统健康检查失败: {str(e)}",
            "data": {
                "overall_health": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        }
