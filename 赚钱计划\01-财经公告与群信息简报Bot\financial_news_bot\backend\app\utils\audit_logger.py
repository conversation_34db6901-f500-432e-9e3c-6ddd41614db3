"""
权限审计日志工具
提供权限操作的详细审计日志记录功能
"""
import json
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import text

from ..database import get_db
from ..models.user import User, UserRole

logger = logging.getLogger(__name__)


class AuditLogger:
    """权限审计日志记录器"""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def log_permission_check(
        self,
        user_id: int,
        permission: str,
        resource: Optional[str] = None,
        granted: bool = True,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        """记录权限检查日志"""
        try:
            await self._insert_audit_log(
                user_id=user_id,
                action="permission_check",
                resource=resource,
                permission_required=permission,
                permission_granted=granted,
                ip_address=ip_address,
                user_agent=user_agent,
                details=details
            )
        except Exception as e:
            logger.error(f"Failed to log permission check: {e}")
    
    async def log_role_change(
        self,
        admin_user_id: int,
        target_user_id: int,
        old_role: UserRole,
        new_role: UserRole,
        reason: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ):
        """记录角色变更日志"""
        try:
            details = {
                "target_user_id": target_user_id,
                "old_role": old_role.value,
                "new_role": new_role.value,
                "reason": reason
            }
            
            await self._insert_audit_log(
                user_id=admin_user_id,
                action="role_change",
                resource=f"user_{target_user_id}",
                permission_required="user_management",
                permission_granted=True,
                ip_address=ip_address,
                user_agent=user_agent,
                details=details
            )
        except Exception as e:
            logger.error(f"Failed to log role change: {e}")
    
    async def log_user_action(
        self,
        user_id: int,
        action: str,
        resource: Optional[str] = None,
        permission_required: Optional[str] = None,
        permission_granted: bool = True,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        """记录用户操作日志"""
        try:
            await self._insert_audit_log(
                user_id=user_id,
                action=action,
                resource=resource,
                permission_required=permission_required,
                permission_granted=permission_granted,
                ip_address=ip_address,
                user_agent=user_agent,
                details=details
            )
        except Exception as e:
            logger.error(f"Failed to log user action: {e}")
    
    async def log_system_config_change(
        self,
        admin_user_id: int,
        config_key: str,
        old_value: Any,
        new_value: Any,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ):
        """记录系统配置变更日志"""
        try:
            details = {
                "config_key": config_key,
                "old_value": str(old_value),
                "new_value": str(new_value)
            }
            
            await self._insert_audit_log(
                user_id=admin_user_id,
                action="config_change",
                resource="system_config",
                permission_required="system_config",
                permission_granted=True,
                ip_address=ip_address,
                user_agent=user_agent,
                details=details
            )
        except Exception as e:
            logger.error(f"Failed to log system config change: {e}")
    
    async def log_login_attempt(
        self,
        username: str,
        success: bool,
        user_id: Optional[int] = None,
        failure_reason: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ):
        """记录登录尝试日志"""
        try:
            details = {
                "username": username,
                "success": success,
                "failure_reason": failure_reason
            }
            
            await self._insert_audit_log(
                user_id=user_id or 0,  # 使用0表示未知用户
                action="login_attempt",
                resource="authentication",
                permission_required="authentication",
                permission_granted=success,
                ip_address=ip_address,
                user_agent=user_agent,
                details=details
            )
        except Exception as e:
            logger.error(f"Failed to log login attempt: {e}")
    
    async def log_data_export(
        self,
        user_id: int,
        export_type: str,
        record_count: int,
        file_size: Optional[int] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ):
        """记录数据导出日志"""
        try:
            details = {
                "export_type": export_type,
                "record_count": record_count,
                "file_size": file_size
            }
            
            await self._insert_audit_log(
                user_id=user_id,
                action="data_export",
                resource=f"export_{export_type}",
                permission_required="export_data",
                permission_granted=True,
                ip_address=ip_address,
                user_agent=user_agent,
                details=details
            )
        except Exception as e:
            logger.error(f"Failed to log data export: {e}")
    
    async def log_bulk_operation(
        self,
        user_id: int,
        operation_type: str,
        affected_count: int,
        operation_details: Dict[str, Any],
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ):
        """记录批量操作日志"""
        try:
            details = {
                "operation_type": operation_type,
                "affected_count": affected_count,
                **operation_details
            }
            
            await self._insert_audit_log(
                user_id=user_id,
                action="bulk_operation",
                resource=f"bulk_{operation_type}",
                permission_required="bulk_operations",
                permission_granted=True,
                ip_address=ip_address,
                user_agent=user_agent,
                details=details
            )
        except Exception as e:
            logger.error(f"Failed to log bulk operation: {e}")
    
    async def get_audit_logs(
        self,
        user_id: Optional[int] = None,
        action: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        permission_granted: Optional[bool] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """获取审计日志"""
        try:
            query = """
                SELECT pal.*, u.username, u.email
                FROM permission_audit_logs pal
                LEFT JOIN users u ON pal.user_id = u.id
                WHERE 1=1
            """
            params = {}
            
            if user_id:
                query += " AND pal.user_id = :user_id"
                params["user_id"] = user_id
            
            if action:
                query += " AND pal.action = :action"
                params["action"] = action
            
            if start_date:
                query += " AND pal.created_at >= :start_date"
                params["start_date"] = start_date
            
            if end_date:
                query += " AND pal.created_at <= :end_date"
                params["end_date"] = end_date
            
            if permission_granted is not None:
                query += " AND pal.permission_granted = :permission_granted"
                params["permission_granted"] = permission_granted
            
            query += " ORDER BY pal.created_at DESC LIMIT :limit OFFSET :offset"
            params["limit"] = limit
            params["offset"] = offset
            
            result = self.db.execute(text(query), params).fetchall()
            
            logs = []
            for row in result:
                log_entry = {
                    "id": row.id,
                    "user_id": row.user_id,
                    "username": row.username,
                    "email": row.email,
                    "action": row.action,
                    "resource": row.resource,
                    "permission_required": row.permission_required,
                    "permission_granted": row.permission_granted,
                    "ip_address": row.ip_address,
                    "user_agent": row.user_agent,
                    "details": json.loads(row.details) if row.details else None,
                    "created_at": row.created_at
                }
                logs.append(log_entry)
            
            return logs
        except Exception as e:
            logger.error(f"Failed to get audit logs: {e}")
            return []
    
    async def get_audit_statistics(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """获取审计统计信息"""
        try:
            query = """
                SELECT 
                    action,
                    permission_granted,
                    COUNT(*) as count
                FROM permission_audit_logs
                WHERE 1=1
            """
            params = {}
            
            if start_date:
                query += " AND created_at >= :start_date"
                params["start_date"] = start_date
            
            if end_date:
                query += " AND created_at <= :end_date"
                params["end_date"] = end_date
            
            query += " GROUP BY action, permission_granted ORDER BY count DESC"
            
            result = self.db.execute(text(query), params).fetchall()
            
            stats = {
                "total_logs": 0,
                "successful_operations": 0,
                "failed_operations": 0,
                "by_action": {},
                "by_permission_status": {"granted": 0, "denied": 0}
            }
            
            for row in result:
                action = row.action
                granted = row.permission_granted
                count = row.count
                
                stats["total_logs"] += count
                
                if granted:
                    stats["successful_operations"] += count
                    stats["by_permission_status"]["granted"] += count
                else:
                    stats["failed_operations"] += count
                    stats["by_permission_status"]["denied"] += count
                
                if action not in stats["by_action"]:
                    stats["by_action"][action] = {"granted": 0, "denied": 0}
                
                if granted:
                    stats["by_action"][action]["granted"] += count
                else:
                    stats["by_action"][action]["denied"] += count
            
            return stats
        except Exception as e:
            logger.error(f"Failed to get audit statistics: {e}")
            return {}
    
    async def cleanup_old_logs(self, days_to_keep: int = 90):
        """清理旧的审计日志"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            
            result = self.db.execute("""
                DELETE FROM permission_audit_logs 
                WHERE created_at < :cutoff_date
            """, {"cutoff_date": cutoff_date})
            
            deleted_count = result.rowcount
            self.db.commit()
            
            logger.info(f"Cleaned up {deleted_count} old audit logs (older than {days_to_keep} days)")
            return deleted_count
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to cleanup old logs: {e}")
            return 0
    
    async def _insert_audit_log(
        self,
        user_id: int,
        action: str,
        resource: Optional[str] = None,
        permission_required: Optional[str] = None,
        permission_granted: bool = True,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        """插入审计日志记录"""
        try:
            self.db.execute("""
                INSERT INTO permission_audit_logs 
                (user_id, action, resource, permission_required, permission_granted, 
                 ip_address, user_agent, details, created_at)
                VALUES (:user_id, :action, :resource, :permission_required, :permission_granted,
                        :ip_address, :user_agent, :details, NOW())
            """, {
                "user_id": user_id,
                "action": action,
                "resource": resource,
                "permission_required": permission_required,
                "permission_granted": permission_granted,
                "ip_address": ip_address,
                "user_agent": user_agent,
                "details": json.dumps(details) if details else None
            })
            self.db.commit()
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to insert audit log: {e}")
            raise


# 全局审计日志实例
def get_audit_logger(db: Session = None) -> AuditLogger:
    """获取审计日志实例"""
    if db is None:
        db = next(get_db())
    return AuditLogger(db)


# 装饰器：自动记录权限检查
def audit_permission_check(permission: str, resource: Optional[str] = None):
    """权限检查审计装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # 获取用户信息
            current_user = kwargs.get('current_user')
            if current_user:
                audit_logger = get_audit_logger()
                try:
                    result = await func(*args, **kwargs)
                    # 记录成功的权限检查
                    await audit_logger.log_permission_check(
                        user_id=current_user.id,
                        permission=permission,
                        resource=resource,
                        granted=True
                    )
                    return result
                except Exception as e:
                    # 记录失败的权限检查
                    await audit_logger.log_permission_check(
                        user_id=current_user.id,
                        permission=permission,
                        resource=resource,
                        granted=False,
                        details={"error": str(e)}
                    )
                    raise
            else:
                return await func(*args, **kwargs)
        return wrapper
    return decorator
