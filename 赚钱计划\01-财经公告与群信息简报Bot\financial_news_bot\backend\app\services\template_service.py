"""
推送模板服务
提供模板管理和动态内容填充功能

注意: 此服务被push_service使用，用于推送消息的模板渲染
"""
import logging
import re
from typing import Dict, Any, List, Optional
from datetime import datetime
from jinja2 import Template, Environment, BaseLoader, TemplateError
from sqlalchemy.orm import Session

from app.models.push_layer import PushTemplate, PushTier, PushChannel
from app.models.user import User
from app.models.news import News
from app.models.subscription import Subscription

logger = logging.getLogger(__name__)

class TemplateService:
    """模板服务类"""
    
    def __init__(self, db: Session):
        self.db = db
        self.jinja_env = Environment(loader=BaseLoader())
        
        # 注册自定义过滤器
        self.jinja_env.filters['format_date'] = self._format_date
        self.jinja_env.filters['truncate_text'] = self._truncate_text
        self.jinja_env.filters['format_number'] = self._format_number
        self.jinja_env.filters['highlight'] = self._highlight_text
    
    def create_template(self, template_data: Dict[str, Any], created_by: int) -> PushTemplate:
        """
        创建新模板
        
        Args:
            template_data: 模板数据
            created_by: 创建者ID
        
        Returns:
            创建的模板对象
        """
        try:
            # 验证模板内容
            self._validate_template_content(template_data.get('content_template', ''))
            
            template = PushTemplate(
                name=template_data['name'],
                description=template_data.get('description'),
                template_type=template_data.get('template_type', TemplateType.CUSTOM),
                template_format=template_data.get('template_format', TemplateFormat.TEXT),
                title_template=template_data.get('title_template'),
                content_template=template_data['content_template'],
                channel_configs=template_data.get('channel_configs', {}),
                variables=template_data.get('variables', {}),
                default_values=template_data.get('default_values', {}),
                status=template_data.get('status', TemplateStatus.DRAFT),
                is_public=template_data.get('is_public', False),
                created_by=created_by
            )
            
            self.db.add(template)
            self.db.commit()
            self.db.refresh(template)
            
            logger.info(f"创建模板成功: {template.name} (ID: {template.id})")
            return template
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"创建模板失败: {str(e)}")
            raise
    
    def update_template(self, template_id: int, template_data: Dict[str, Any], user_id: int) -> PushTemplate:
        """
        更新模板
        
        Args:
            template_id: 模板ID
            template_data: 更新数据
            user_id: 用户ID
        
        Returns:
            更新后的模板对象
        """
        try:
            template = self.get_template_by_id(template_id)
            
            if not template:
                raise ValueError(f"模板不存在: {template_id}")
            
            # 权限检查
            if not self._can_edit_template(template, user_id):
                raise PermissionError("没有权限编辑此模板")
            
            # 验证新的模板内容
            if 'content_template' in template_data:
                self._validate_template_content(template_data['content_template'])
            
            # 更新字段
            for field, value in template_data.items():
                if hasattr(template, field) and field not in ['id', 'created_by', 'created_at']:
                    setattr(template, field, value)
            
            template.updated_at = datetime.now()
            
            self.db.commit()
            self.db.refresh(template)
            
            logger.info(f"更新模板成功: {template.name} (ID: {template.id})")
            return template
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新模板失败: {str(e)}")
            raise
    
    def get_template_by_id(self, template_id: int) -> Optional[PushTemplate]:
        """根据ID获取模板"""
        return self.db.query(PushTemplate).filter(PushTemplate.id == template_id).first()
    
    def get_templates_by_user(self, user_id: int, include_public: bool = True) -> List[PushTemplate]:
        """
        获取用户的模板列表
        
        Args:
            user_id: 用户ID
            include_public: 是否包含公共模板
        
        Returns:
            模板列表
        """
        query = self.db.query(PushTemplate)
        
        if include_public:
            query = query.filter(
                (PushTemplate.created_by == user_id) | 
                (PushTemplate.is_public == True)
            )
        else:
            query = query.filter(PushTemplate.created_by == user_id)
        
        return query.order_by(PushTemplate.created_at.desc()).all()
    
    def get_templates_by_type(self, template_type: TemplateType) -> List[PushTemplate]:
        """根据类型获取模板"""
        return self.db.query(PushTemplate).filter(
            PushTemplate.template_type == template_type,
            PushTemplate.status == TemplateStatus.ACTIVE
        ).all()
    
    def render_template(self, template: PushTemplate, context: Dict[str, Any]) -> Dict[str, str]:
        """
        渲染模板
        
        Args:
            template: 模板对象
            context: 上下文数据
        
        Returns:
            渲染结果 {"title": "...", "content": "..."}
        """
        try:
            # 合并默认值
            render_context = {}
            if template.default_values:
                render_context.update(template.default_values)
            render_context.update(context)
            
            # 渲染标题
            title = ""
            if template.title_template:
                title_tmpl = self.jinja_env.from_string(template.title_template)
                title = title_tmpl.render(**render_context)
            
            # 渲染内容
            content_tmpl = self.jinja_env.from_string(template.content_template)
            content = content_tmpl.render(**render_context)
            
            # 更新使用统计
            template.increment_usage()
            self.db.commit()
            
            return {
                "title": title.strip(),
                "content": content.strip()
            }
            
        except TemplateError as e:
            logger.error(f"模板渲染失败: {str(e)}")
            raise ValueError(f"模板渲染失败: {str(e)}")
        except Exception as e:
            logger.error(f"模板渲染异常: {str(e)}")
            raise
    
    def build_context_from_news(self, news_list: List[News], user: User, subscription: Subscription = None) -> Dict[str, Any]:
        """
        从新闻数据构建模板上下文
        
        Args:
            news_list: 新闻列表
            user: 用户对象
            subscription: 订阅对象
        
        Returns:
            模板上下文数据
        """
        context = {
            # 用户信息
            "user": {
                "username": user.username,
                "email": user.email,
                "role": user.role
            },
            
            # 时间信息
            "current_time": datetime.now(),
            "current_date": datetime.now().date(),
            
            # 新闻信息
            "news_count": len(news_list),
            "news_list": [self._news_to_dict(news) for news in news_list],
            
            # 统计信息
            "important_news_count": len([n for n in news_list if n.importance_score >= 80]),
            "sources": list(set([n.source for n in news_list])),
            "categories": list(set([n.category for n in news_list if n.category])),
        }
        
        # 订阅信息
        if subscription:
            context["subscription"] = {
                "name": subscription.name,
                "keywords": subscription.keywords or [],
                "companies": subscription.companies or [],
                "categories": subscription.categories or []
            }
        
        # 重要新闻
        if news_list:
            important_news = sorted(news_list, key=lambda x: x.importance_score, reverse=True)
            context["top_news"] = self._news_to_dict(important_news[0])
            context["important_news"] = [self._news_to_dict(n) for n in important_news[:5]]
        
        return context
    
    def _validate_template_content(self, content: str):
        """验证模板内容语法"""
        try:
            self.jinja_env.from_string(content)
        except TemplateError as e:
            raise ValueError(f"模板语法错误: {str(e)}")
    
    def _can_edit_template(self, template: PushTemplate, user_id: int) -> bool:
        """检查用户是否可以编辑模板"""
        return template.created_by == user_id or template.is_system
    
    def _news_to_dict(self, news: News) -> Dict[str, Any]:
        """将新闻对象转换为字典"""
        return {
            "id": news.id,
            "title": news.title,
            "content": news.content,
            "summary": news.summary,
            "source": news.source,
            "source_url": news.source_url,
            "category": news.category,
            "importance_score": news.importance_score,
            "published_at": news.published_at,
            "companies": news.companies or [],
            "keywords": news.keywords or []
        }
    
    # Jinja2自定义过滤器
    def _format_date(self, date_obj, format_str="%Y-%m-%d %H:%M"):
        """格式化日期"""
        if isinstance(date_obj, str):
            return date_obj
        return date_obj.strftime(format_str) if date_obj else ""
    
    def _truncate_text(self, text, length=100, suffix="..."):
        """截断文本"""
        if not text:
            return ""
        return text[:length] + suffix if len(text) > length else text
    
    def _format_number(self, number, decimal_places=2):
        """格式化数字"""
        if isinstance(number, (int, float)):
            return f"{number:.{decimal_places}f}"
        return str(number)
    
    def _highlight_text(self, text, keywords):
        """高亮关键词"""
        if not text or not keywords:
            return text
        
        if isinstance(keywords, str):
            keywords = [keywords]
        
        for keyword in keywords:
            pattern = re.compile(re.escape(keyword), re.IGNORECASE)
            text = pattern.sub(f"**{keyword}**", text)
        
        return text

# 预定义系统模板
SYSTEM_TEMPLATES = {
    "news_summary": {
        "name": "新闻摘要模板",
        "description": "标准的新闻摘要推送模板",
        "template_type": TemplateType.NEWS_SUMMARY,
        "template_format": TemplateFormat.MARKDOWN,
        "title_template": "📰 {{ subscription.name }} - 新闻推送 ({{ news_count }}条)",
        "content_template": """## 📊 推送概览
- **新闻数量**: {{ news_count }} 条
- **重要新闻**: {{ important_news_count }} 条
- **数据来源**: {{ sources | join(', ') }}
- **推送时间**: {{ current_time | format_date }}

## 🔥 重要新闻
{% for news in important_news[:3] %}
{{ loop.index }}. **{{ news.title }}**
   📊 {{ news.importance_score }}分 | 🏢 {{ news.source }} | 📅 {{ news.published_at | format_date('%m-%d %H:%M') }}
   {% if news.summary %}💡 {{ news.summary | truncate_text(80) }}{% endif %}

{% endfor %}

{% if news_count > 3 %}
📋 还有 **{{ news_count - 3 }}** 条其他新闻
{% endif %}

---
💼 财经新闻Bot | 智能推送服务""",
        "variables": {
            "news_count": {"type": "number", "description": "新闻数量"},
            "important_news_count": {"type": "number", "description": "重要新闻数量"},
            "sources": {"type": "list", "description": "新闻来源列表"},
            "important_news": {"type": "list", "description": "重要新闻列表"}
        }
    }
}
