{"info": {"name": "财经新闻Bot API测试集合", "description": "完整的API接口自动化测试集合", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// 全局前置脚本", "pm.globals.set('base_url', pm.environment.get('base_url') || 'http://localhost:8000');", "pm.globals.set('timestamp', Date.now());"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// 全局测试脚本", "pm.test('响应时间小于2秒', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});", "", "pm.test('响应格式为JSON', function () {", "    pm.response.to.have.header('Content-Type');", "    pm.expect(pm.response.headers.get('Content-Type')).to.include('application/json');", "});"]}}], "variable": [{"key": "base_url", "value": "http://localhost:8000"}, {"key": "access_token", "value": ""}, {"key": "user_id", "value": ""}, {"key": "subscription_id", "value": ""}, {"key": "news_id", "value": ""}], "item": [{"name": "认证模块", "item": [{"name": "用户注册", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"test_{{timestamp}}\",\n  \"email\": \"test_{{timestamp}}@example.com\",\n  \"password\": \"Test123456!\",\n  \"full_name\": \"测试用户\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/register", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "register"]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('注册成功', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('返回用户信息', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('user');", "    pm.expect(responseJson.user).to.have.property('id');", "    pm.globals.set('user_id', responseJson.user.id);", "});"]}}]}, {"name": "用户登录", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"test_{{timestamp}}\",\n  \"password\": \"Test123456!\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/login", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "login"]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('登录成功', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('返回访问令牌', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('access_token');", "    pm.globals.set('access_token', responseJson.access_token);", "});", "", "pm.test('令牌格式正确', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.access_token).to.be.a('string');", "    pm.expect(responseJson.access_token.length).to.be.above(10);", "});"]}}]}, {"name": "获取当前用户信息", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/auth/me", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "me"]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('获取用户信息成功', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('用户信息完整', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('id');", "    pm.expect(responseJson).to.have.property('username');", "    pm.expect(responseJson).to.have.property('email');", "});"]}}]}]}, {"name": "新闻模块", "item": [{"name": "获取新闻列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/news?page=1&limit=20", "host": ["{{base_url}}"], "path": ["api", "v1", "news"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('获取新闻列表成功', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('新闻列表格式正确', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson).to.have.property('total');", "    pm.expect(response<PERSON>son).to.have.property('page');", "    pm.expect(responseJson).to.have.property('limit');", "});", "", "pm.test('新闻项包含必要字段', function () {", "    const responseJson = pm.response.json();", "    if (responseJson.data.length > 0) {", "        const news = responseJson.data[0];", "        pm.expect(news).to.have.property('id');", "        pm.expect(news).to.have.property('title');", "        pm.expect(news).to.have.property('summary');", "        pm.expect(news).to.have.property('published_at');", "        pm.globals.set('news_id', news.id);", "    }", "});"]}}]}, {"name": "获取新闻详情", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/news/{{news_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "news", "{{news_id}}"]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('获取新闻详情成功', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('新闻详情完整', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('id');", "    pm.expect(responseJson).to.have.property('title');", "    pm.expect(responseJson).to.have.property('content');", "    pm.expect(responseJson).to.have.property('source');", "});"]}}]}, {"name": "搜索新闻", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/news/search?keyword=央行&page=1&limit=10", "host": ["{{base_url}}"], "path": ["api", "v1", "news", "search"], "query": [{"key": "keyword", "value": "央行"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('搜索新闻成功', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('搜索结果格式正确', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson).to.have.property('total');", "});"]}}]}]}, {"name": "订阅模块", "item": [{"name": "创建订阅", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"测试订阅_{{timestamp}}\",\n  \"description\": \"API测试创建的订阅\",\n  \"keywords\": [\"财经\", \"科技\"],\n  \"channels\": [\"email\"],\n  \"frequency\": \"daily\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/subscriptions", "host": ["{{base_url}}"], "path": ["api", "v1", "subscriptions"]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('创建订阅成功', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('返回订阅信息', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('id');", "    pm.expect(responseJson).to.have.property('name');", "    pm.globals.set('subscription_id', responseJson.id);", "});"]}}]}, {"name": "获取订阅列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/subscriptions", "host": ["{{base_url}}"], "path": ["api", "v1", "subscriptions"]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('获取订阅列表成功', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('订阅列表格式正确', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.be.an('array');", "});"]}}]}, {"name": "更新订阅", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"更新的测试订阅_{{timestamp}}\",\n  \"description\": \"API测试更新的订阅\",\n  \"keywords\": [\"财经\", \"科技\", \"政策\"],\n  \"frequency\": \"weekly\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/subscriptions/{{subscription_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "subscriptions", "{{subscription_id}}"]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('更新订阅成功', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('订阅信息已更新', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.name).to.include('更新的测试订阅');", "});"]}}]}, {"name": "删除订阅", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/v1/subscriptions/{{subscription_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "subscriptions", "{{subscription_id}}"]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('删除订阅成功', function () {", "    pm.response.to.have.status(204);", "});"]}}]}]}, {"name": "系统健康检查", "item": [{"name": "健康检查", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/health", "host": ["{{base_url}}"], "path": ["api", "v1", "health"]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('系统健康', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('健康检查响应正确', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('status');", "    pm.expect(responseJson.status).to.equal('healthy');", "});"]}}]}]}]}