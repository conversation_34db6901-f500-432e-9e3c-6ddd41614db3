"""
响应服务 - 统一API响应格式
"""
from enum import Enum
from typing import Any, Dict, Optional
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class ResponseCode(Enum):
    """响应状态码枚举"""
    SUCCESS = "SUCCESS"
    ERROR = "ERROR"
    WARNING = "WARNING"
    INFO = "INFO"

class ErrorCode(Enum):
    """错误代码枚举"""
    VALIDATION_ERROR = "VALIDATION_ERROR"
    AUTHENTICATION_ERROR = "AUTHENTICATION_ERROR"
    AUTHORIZATION_ERROR = "AUTHORIZATION_ERROR"
    NOT_FOUND = "NOT_FOUND"
    INTERNAL_ERROR = "INTERNAL_ERROR"
    EXTERNAL_SERVICE_ERROR = "EXTERNAL_SERVICE_ERROR"
    RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED"
    MAINTENANCE_MODE = "MAINTENANCE_MODE"

class ResponseService:
    """响应服务类"""
    
    @staticmethod
    def success(data: Any = None, message: str = "操作成功", **kwargs) -> Dict[str, Any]:
        """
        成功响应
        
        Args:
            data: 响应数据
            message: 响应消息
            **kwargs: 额外参数
            
        Returns:
            标准化的成功响应
        """
        response = {
            "code": ResponseCode.SUCCESS.value,
            "message": message,
            "data": data,
            "timestamp": datetime.utcnow().isoformat(),
            "success": True
        }
        
        # 添加额外参数
        response.update(kwargs)
        
        return response
    
    @staticmethod
    def error(
        error_code: ErrorCode = ErrorCode.INTERNAL_ERROR,
        message: str = "操作失败",
        details: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        错误响应
        
        Args:
            error_code: 错误代码
            message: 错误消息
            details: 错误详情
            **kwargs: 额外参数
            
        Returns:
            标准化的错误响应
        """
        response = {
            "code": ResponseCode.ERROR.value,
            "error_code": error_code.value,
            "message": message,
            "details": details,
            "timestamp": datetime.utcnow().isoformat(),
            "success": False
        }
        
        # 添加额外参数
        response.update(kwargs)
        
        return response
    
    @staticmethod
    def warning(message: str = "警告", data: Any = None, **kwargs) -> Dict[str, Any]:
        """
        警告响应
        
        Args:
            message: 警告消息
            data: 响应数据
            **kwargs: 额外参数
            
        Returns:
            标准化的警告响应
        """
        response = {
            "code": ResponseCode.WARNING.value,
            "message": message,
            "data": data,
            "timestamp": datetime.utcnow().isoformat(),
            "success": True
        }
        
        # 添加额外参数
        response.update(kwargs)
        
        return response
    
    @staticmethod
    def info(message: str = "信息", data: Any = None, **kwargs) -> Dict[str, Any]:
        """
        信息响应
        
        Args:
            message: 信息消息
            data: 响应数据
            **kwargs: 额外参数
            
        Returns:
            标准化的信息响应
        """
        response = {
            "code": ResponseCode.INFO.value,
            "message": message,
            "data": data,
            "timestamp": datetime.utcnow().isoformat(),
            "success": True
        }
        
        # 添加额外参数
        response.update(kwargs)
        
        return response
    
    @staticmethod
    def paginated(
        data: list,
        total: int,
        page: int = 1,
        per_page: int = 10,
        message: str = "查询成功",
        **kwargs
    ) -> Dict[str, Any]:
        """
        分页响应
        
        Args:
            data: 数据列表
            total: 总数量
            page: 当前页码
            per_page: 每页数量
            message: 响应消息
            **kwargs: 额外参数
            
        Returns:
            标准化的分页响应
        """
        has_next = (page * per_page) < total
        has_prev = page > 1
        
        response = {
            "code": ResponseCode.SUCCESS.value,
            "message": message,
            "data": data,
            "pagination": {
                "total": total,
                "page": page,
                "per_page": per_page,
                "has_next": has_next,
                "has_prev": has_prev,
                "total_pages": (total + per_page - 1) // per_page
            },
            "timestamp": datetime.utcnow().isoformat(),
            "success": True
        }
        
        # 添加额外参数
        response.update(kwargs)
        
        return response

# 创建全局实例
response_service = ResponseService()
