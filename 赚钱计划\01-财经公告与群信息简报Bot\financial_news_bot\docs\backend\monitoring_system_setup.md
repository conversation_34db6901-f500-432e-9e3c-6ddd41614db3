# 监控系统部署配置

## Prometheus + Grafana 监控栈

### 1. Prometheus 配置
```yaml
# prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

scrape_configs:
  - job_name: 'financial-news-bot'
    static_configs:
      - targets: ['localhost:8000']
    metrics_path: '/metrics'
    scrape_interval: 10s

  - job_name: 'mysql'
    static_configs:
      - targets: ['localhost:9104']

  - job_name: 'redis'
    static_configs:
      - targets: ['localhost:9121']

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['localhost:9100']

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

### 2. 应用指标暴露
```python
# app/monitoring/metrics.py
from prometheus_client import Counter, Histogram, Gauge, generate_latest
from flask import Response

# 定义指标
REQUEST_COUNT = Counter(
    'http_requests_total',
    'Total HTTP requests',
    ['method', 'endpoint', 'status']
)

REQUEST_DURATION = Histogram(
    'http_request_duration_seconds',
    'HTTP request duration',
    ['method', 'endpoint']
)

ACTIVE_USERS = Gauge(
    'active_users_total',
    'Number of active users'
)

NEWS_PROCESSED = Counter(
    'news_processed_total',
    'Total news articles processed',
    ['source', 'category']
)

PUSH_NOTIFICATIONS = Counter(
    'push_notifications_total',
    'Total push notifications sent',
    ['channel', 'status']
)

@app.route('/metrics')
def metrics():
    return Response(generate_latest(), mimetype='text/plain')
```

### 3. Grafana 仪表板配置
```json
{
  "dashboard": {
    "title": "Financial News Bot Monitoring",
    "panels": [
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{method}} {{endpoint}}"
          }
        ]
      },
      {
        "title": "Response Time",
        "type": "graph", 
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      },
      {
        "title": "Active Users",
        "type": "singlestat",
        "targets": [
          {
            "expr": "active_users_total"
          }
        ]
      },
      {
        "title": "News Processing Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(news_processed_total[5m])",
            "legendFormat": "{{source}}"
          }
        ]
      }
    ]
  }
}
```

## ELK 日志系统

### 1. Elasticsearch 配置
```yaml
# elasticsearch.yml
cluster.name: financial-news-logs
node.name: node-1
path.data: /var/lib/elasticsearch
path.logs: /var/log/elasticsearch
network.host: 0.0.0.0
http.port: 9200
discovery.type: single-node

# JVM 配置
-Xms2g
-Xmx2g
```

### 2. Logstash 配置
```ruby
# logstash.conf
input {
  beats {
    port => 5044
  }
}

filter {
  if [fields][service] == "financial-news-bot" {
    grok {
      match => { 
        "message" => "%{TIMESTAMP_ISO8601:timestamp} %{LOGLEVEL:level} %{DATA:logger} %{GREEDYDATA:message}"
      }
    }
    
    date {
      match => [ "timestamp", "ISO8601" ]
    }
    
    if [level] == "ERROR" {
      mutate {
        add_tag => [ "error" ]
      }
    }
  }
}

output {
  elasticsearch {
    hosts => ["localhost:9200"]
    index => "financial-news-bot-%{+YYYY.MM.dd}"
  }
}
```

### 3. Filebeat 配置
```yaml
# filebeat.yml
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /var/log/financial-news-bot/*.log
  fields:
    service: financial-news-bot
  fields_under_root: true

output.logstash:
  hosts: ["localhost:5044"]

logging.level: info
logging.to_files: true
logging.files:
  path: /var/log/filebeat
  name: filebeat
  keepfiles: 7
  permissions: 0644
```

### 4. Kibana 仪表板
```json
{
  "version": "7.15.0",
  "objects": [
    {
      "type": "dashboard",
      "attributes": {
        "title": "Financial News Bot Logs",
        "panelsJSON": "[{\"version\":\"7.15.0\",\"gridData\":{\"x\":0,\"y\":0,\"w\":24,\"h\":15},\"panelIndex\":\"1\",\"embeddableConfig\":{},\"panelRefName\":\"panel_1\"}]"
      }
    }
  ]
}
```

## 应用性能监控

### 1. 响应时间监控
```python
# app/monitoring/performance.py
import time
from functools import wraps

def monitor_performance(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            status = 'success'
            return result
        except Exception as e:
            status = 'error'
            raise
        finally:
            duration = time.time() - start_time
            REQUEST_DURATION.labels(
                method=request.method,
                endpoint=request.endpoint
            ).observe(duration)
            
            REQUEST_COUNT.labels(
                method=request.method,
                endpoint=request.endpoint,
                status=status
            ).inc()
    
    return wrapper
```

### 2. 数据库性能监控
```python
# app/monitoring/database.py
from sqlalchemy import event
from sqlalchemy.engine import Engine
import time

@event.listens_for(Engine, "before_cursor_execute")
def receive_before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    context._query_start_time = time.time()

@event.listens_for(Engine, "after_cursor_execute")
def receive_after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    total = time.time() - context._query_start_time
    
    # 记录慢查询
    if total > 0.1:  # 100ms
        logger.warning(f"Slow query: {total:.3f}s - {statement[:100]}")
    
    # 更新指标
    DB_QUERY_DURATION.observe(total)
```

### 3. 业务指标监控
```python
# app/monitoring/business.py
def track_news_processing(source, category, processing_time):
    NEWS_PROCESSED.labels(source=source, category=category).inc()
    NEWS_PROCESSING_TIME.labels(source=source).observe(processing_time)

def track_push_notification(channel, status):
    PUSH_NOTIFICATIONS.labels(channel=channel, status=status).inc()

def track_user_activity(user_id, action):
    USER_ACTIONS.labels(action=action).inc()
    
    # 更新活跃用户数
    redis_client.setex(f"active_user:{user_id}", 300, 1)  # 5分钟过期
    active_count = len(redis_client.keys("active_user:*"))
    ACTIVE_USERS.set(active_count)
```

## 告警配置

### 1. Prometheus 告警规则
```yaml
# alert_rules.yml
groups:
- name: financial-news-bot
  rules:
  - alert: HighErrorRate
    expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "High error rate detected"
      description: "Error rate is {{ $value }} errors per second"

  - alert: HighResponseTime
    expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High response time detected"
      description: "95th percentile response time is {{ $value }}s"

  - alert: DatabaseConnectionFailure
    expr: mysql_up == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Database connection failure"
      description: "MySQL database is down"

  - alert: LowNewsProcessingRate
    expr: rate(news_processed_total[10m]) < 0.1
    for: 10m
    labels:
      severity: warning
    annotations:
      summary: "Low news processing rate"
      description: "News processing rate is {{ $value }} per second"
```

### 2. Alertmanager 配置
```yaml
# alertmanager.yml
global:
  smtp_smarthost: 'smtp.gmail.com:587'
  smtp_from: '<EMAIL>'

route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'

receivers:
- name: 'web.hook'
  email_configs:
  - to: '<EMAIL>'
    subject: 'Financial News Bot Alert'
    body: |
      {{ range .Alerts }}
      Alert: {{ .Annotations.summary }}
      Description: {{ .Annotations.description }}
      {{ end }}
  
  webhook_configs:
  - url: 'http://localhost:8000/api/v1/alerts/webhook'
    send_resolved: true
```

## 部署脚本

### 1. Docker Compose 监控栈
```yaml
# docker-compose.monitoring.yml
version: '3.8'

services:
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./monitoring/alert_rules.yml:/etc/prometheus/alert_rules.yml

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana-storage:/var/lib/grafana

  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.15.0
    ports:
      - "9200:9200"
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"

  kibana:
    image: docker.elastic.co/kibana/kibana:7.15.0
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch

  logstash:
    image: docker.elastic.co/logstash/logstash:7.15.0
    ports:
      - "5044:5044"
    volumes:
      - ./monitoring/logstash.conf:/usr/share/logstash/pipeline/logstash.conf
    depends_on:
      - elasticsearch

volumes:
  grafana-storage:
```

### 2. 部署脚本
```bash
#!/bin/bash
# deploy_monitoring.sh

echo "部署监控系统..."

# 创建监控配置目录
mkdir -p monitoring

# 启动监控栈
docker-compose -f docker-compose.monitoring.yml up -d

# 等待服务启动
sleep 30

# 导入 Grafana 仪表板
curl -X POST \
  ************************************/api/dashboards/db \
  -H 'Content-Type: application/json' \
  -d @monitoring/grafana-dashboard.json

# 创建 Kibana 索引模式
curl -X POST \
  http://localhost:5601/api/saved_objects/index-pattern/financial-news-bot-* \
  -H 'Content-Type: application/json' \
  -d '{"attributes":{"title":"financial-news-bot-*","timeFieldName":"@timestamp"}}'

echo "监控系统部署完成！"
echo "Grafana: http://localhost:3000 (admin/admin123)"
echo "Kibana: http://localhost:5601"
echo "Prometheus: http://localhost:9090"
```
