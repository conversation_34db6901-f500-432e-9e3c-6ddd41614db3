"""
统一常量管理
解决硬编码和魔法数字问题
"""
import os
from enum import Enum
from typing import Dict, Any


class TimeConstants:
    """时间相关常量"""
    # 秒
    SECOND = 1
    MINUTE = 60
    HOUR = 3600
    DAY = 86400
    WEEK = 604800
    MONTH = 2592000  # 30天
    
    # 数据库查询超时
    DB_QUERY_TIMEOUT = int(os.getenv('DB_QUERY_TIMEOUT', '30'))  # 30秒
    DB_SLOW_QUERY_THRESHOLD = float(os.getenv('DB_SLOW_QUERY_THRESHOLD', '1.0'))  # 1秒
    
    # 缓存过期时间
    CACHE_SHORT = int(os.getenv('CACHE_SHORT_EXPIRE', '300'))  # 5分钟
    CACHE_MEDIUM = int(os.getenv('CACHE_MEDIUM_EXPIRE', '1800'))  # 30分钟
    CACHE_LONG = int(os.getenv('CACHE_LONG_EXPIRE', '3600'))  # 1小时
    CACHE_VERY_LONG = int(os.getenv('CACHE_VERY_LONG_EXPIRE', '86400'))  # 1天
    
    # JWT令牌过期时间
    ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv('ACCESS_TOKEN_EXPIRE_MINUTES', '30'))
    REFRESH_TOKEN_EXPIRE_DAYS = int(os.getenv('REFRESH_TOKEN_EXPIRE_DAYS', '7'))
    
    # 请求超时
    HTTP_REQUEST_TIMEOUT = int(os.getenv('HTTP_REQUEST_TIMEOUT', '30'))
    CRAWLER_REQUEST_TIMEOUT = int(os.getenv('CRAWLER_REQUEST_TIMEOUT', '60'))
    AI_REQUEST_TIMEOUT = int(os.getenv('AI_REQUEST_TIMEOUT', '120'))


class DatabaseConstants:
    """数据库相关常量"""
    # 连接池配置
    DEFAULT_POOL_SIZE = int(os.getenv('DB_POOL_SIZE', '10'))
    DEFAULT_MAX_OVERFLOW = int(os.getenv('DB_MAX_OVERFLOW', '20'))
    DEFAULT_POOL_TIMEOUT = int(os.getenv('DB_POOL_TIMEOUT', '30'))
    DEFAULT_POOL_RECYCLE = int(os.getenv('DB_POOL_RECYCLE', '3600'))
    
    # 远程数据库配置
    REMOTE_POOL_SIZE = int(os.getenv('REMOTE_DB_POOL_SIZE', '5'))
    REMOTE_MAX_OVERFLOW = int(os.getenv('REMOTE_DB_MAX_OVERFLOW', '10'))
    REMOTE_POOL_TIMEOUT = int(os.getenv('REMOTE_DB_POOL_TIMEOUT', '60'))
    
    # 查询限制
    MAX_QUERY_LIMIT = int(os.getenv('MAX_QUERY_LIMIT', '1000'))
    DEFAULT_PAGE_SIZE = int(os.getenv('DEFAULT_PAGE_SIZE', '20'))
    MAX_PAGE_SIZE = int(os.getenv('MAX_PAGE_SIZE', '100'))


class RedisConstants:
    """Redis相关常量"""
    # 连接配置
    DEFAULT_HOST = os.getenv('REDIS_HOST', 'redis')
    DEFAULT_PORT = int(os.getenv('REDIS_PORT', '6379'))
    DEFAULT_DB = int(os.getenv('REDIS_DB', '0'))
    DEFAULT_PASSWORD = os.getenv('REDIS_PASSWORD')
    
    # 连接池配置
    MAX_CONNECTIONS = int(os.getenv('REDIS_MAX_CONNECTIONS', '50'))
    SOCKET_TIMEOUT = int(os.getenv('REDIS_SOCKET_TIMEOUT', '5'))
    SOCKET_CONNECT_TIMEOUT = int(os.getenv('REDIS_SOCKET_CONNECT_TIMEOUT', '5'))
    
    # 重试配置
    MAX_RETRIES = int(os.getenv('REDIS_MAX_RETRIES', '3'))
    RETRY_DELAY = float(os.getenv('REDIS_RETRY_DELAY', '0.1'))


class CacheKeys:
    """缓存键名常量"""
    # 用户相关
    USER_INFO = "user:info:{user_id}"
    USER_PERMISSIONS = "user:permissions:{user_id}"
    USER_SUBSCRIPTIONS = "user:subscriptions:{user_id}"
    
    # 新闻相关
    NEWS_LIST = "news:list:{page}:{size}:{category}"
    NEWS_DETAIL = "news:detail:{news_id}"
    NEWS_STATS = "news:stats:{date}"
    NEWS_TRENDING = "news:trending:{timeframe}"
    
    # 搜索相关
    SEARCH_RESULTS = "search:results:{query}:{page}:{size}"
    SEARCH_SUGGESTIONS = "search:suggestions:{prefix}"
    
    # 系统相关
    SYSTEM_STATS = "system:stats"
    SYSTEM_HEALTH = "system:health"
    API_RATE_LIMIT = "rate_limit:{user_id}:{endpoint}"


class APIConstants:
    """API相关常量"""
    # 分页
    DEFAULT_PAGE = 1
    DEFAULT_PAGE_SIZE = 20
    MAX_PAGE_SIZE = 100
    
    # 速率限制
    RATE_LIMIT_REQUESTS = int(os.getenv('RATE_LIMIT_REQUESTS', '100'))
    RATE_LIMIT_WINDOW = int(os.getenv('RATE_LIMIT_WINDOW', '60'))  # 秒
    
    # 请求大小限制
    MAX_REQUEST_SIZE = int(os.getenv('MAX_REQUEST_SIZE', '10485760'))  # 10MB
    MAX_FILE_SIZE = int(os.getenv('MAX_FILE_SIZE', '5242880'))  # 5MB
    
    # API版本
    API_VERSION = "v1"
    API_PREFIX = f"/api/{API_VERSION}"


class SecurityConstants:
    """安全相关常量"""
    # 密码要求
    MIN_PASSWORD_LENGTH = int(os.getenv('MIN_PASSWORD_LENGTH', '8'))
    MAX_PASSWORD_LENGTH = int(os.getenv('MAX_PASSWORD_LENGTH', '128'))
    
    # 登录尝试限制
    MAX_LOGIN_ATTEMPTS = int(os.getenv('MAX_LOGIN_ATTEMPTS', '5'))
    LOGIN_LOCKOUT_DURATION = int(os.getenv('LOGIN_LOCKOUT_DURATION', '900'))  # 15分钟
    
    # CORS配置
    CORS_ORIGINS = os.getenv('CORS_ORIGINS', 'http://localhost:3000').split(',')
    CORS_METHODS = ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS']
    CORS_HEADERS = [
        'DNT', 'User-Agent', 'X-Requested-With', 'If-Modified-Since',
        'Cache-Control', 'Content-Type', 'Range', 'Authorization'
    ]


class CrawlerConstants:
    """爬虫相关常量"""
    # 请求配置
    DEFAULT_USER_AGENTS = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    ]
    
    # 爬取限制
    REQUEST_DELAY = float(os.getenv('CRAWLER_REQUEST_DELAY', '1.0'))  # 秒
    MAX_RETRIES = int(os.getenv('CRAWLER_MAX_RETRIES', '3'))
    CONCURRENT_LIMIT = int(os.getenv('CRAWLER_CONCURRENT_LIMIT', '2'))
    
    # 内容限制
    MAX_CONTENT_LENGTH = int(os.getenv('MAX_CONTENT_LENGTH', '1048576'))  # 1MB
    MIN_CONTENT_LENGTH = int(os.getenv('MIN_CONTENT_LENGTH', '100'))


class AIConstants:
    """AI服务相关常量"""
    # GLM配置
    GLM_MAX_TOKENS = int(os.getenv('GLM_MAX_TOKENS', '1000'))
    GLM_TEMPERATURE = float(os.getenv('GLM_TEMPERATURE', '0.7'))
    GLM_TOP_P = float(os.getenv('GLM_TOP_P', '0.9'))
    
    # 文本处理
    MAX_KEYWORDS = int(os.getenv('MAX_KEYWORDS', '10'))
    MIN_KEYWORD_LENGTH = int(os.getenv('MIN_KEYWORD_LENGTH', '2'))
    MAX_SUMMARY_LENGTH = int(os.getenv('MAX_SUMMARY_LENGTH', '200'))


class BusinessConstants:
    """业务相关常量"""
    # 新闻分类
    NEWS_CATEGORIES = [
        'finance', 'stock', 'bond', 'fund', 'forex', 'commodity',
        'macro', 'policy', 'company', 'market', 'other'
    ]
    
    # 推送渠道
    PUSH_CHANNELS = ['email', 'wechat', 'sms', 'webhook']
    
    # 用户角色
    USER_ROLES = ['FREE', 'PREMIUM', 'VIP', 'ADMIN']
    
    # 订阅状态
    SUBSCRIPTION_STATUS = ['ACTIVE', 'INACTIVE', 'EXPIRED', 'CANCELLED']


class LoggingConstants:
    """日志相关常量"""
    # 日志级别
    LOG_LEVELS = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
    
    # 日志格式
    LOG_FORMAT_SIMPLE = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    LOG_FORMAT_DETAILED = "%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s"
    LOG_FORMAT_JSON = "json"
    
    # 日志文件配置
    MAX_LOG_FILE_SIZE = int(os.getenv('MAX_LOG_FILE_SIZE', '10485760'))  # 10MB
    LOG_BACKUP_COUNT = int(os.getenv('LOG_BACKUP_COUNT', '5'))


# 环境相关常量
class EnvironmentConstants:
    """环境相关常量"""
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"
    
    CURRENT_ENV = os.getenv('ENVIRONMENT', PRODUCTION)
    
    @classmethod
    def is_development(cls) -> bool:
        return cls.CURRENT_ENV == cls.DEVELOPMENT
    
    @classmethod
    def is_production(cls) -> bool:
        return cls.CURRENT_ENV == cls.PRODUCTION
    
    @classmethod
    def is_testing(cls) -> bool:
        return cls.CURRENT_ENV == cls.TESTING


# 状态码常量
class StatusCodes:
    """HTTP状态码常量"""
    # 成功
    OK = 200
    CREATED = 201
    ACCEPTED = 202
    NO_CONTENT = 204
    
    # 客户端错误
    BAD_REQUEST = 400
    UNAUTHORIZED = 401
    FORBIDDEN = 403
    NOT_FOUND = 404
    METHOD_NOT_ALLOWED = 405
    CONFLICT = 409
    UNPROCESSABLE_ENTITY = 422
    TOO_MANY_REQUESTS = 429
    
    # 服务器错误
    INTERNAL_SERVER_ERROR = 500
    BAD_GATEWAY = 502
    SERVICE_UNAVAILABLE = 503
    GATEWAY_TIMEOUT = 504
