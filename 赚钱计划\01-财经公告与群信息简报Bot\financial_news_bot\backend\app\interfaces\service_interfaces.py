"""
服务接口标准化
定义清晰的接口规范，解决服务层职责不清的问题
"""
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union, Generic, TypeVar
from datetime import datetime
from dataclasses import dataclass

# 泛型类型变量
T = TypeVar('T')
K = TypeVar('K')
V = TypeVar('V')


@dataclass
class ServiceResult(Generic[T]):
    """统一的服务返回结果"""
    success: bool
    data: Optional[T] = None
    error: Optional[str] = None
    error_code: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class PaginatedResult(Generic[T]):
    """分页结果"""
    items: List[T]
    total: int
    page: int
    page_size: int
    has_next: bool
    has_prev: bool


class CacheServiceInterface(ABC):
    """缓存服务接口"""
    
    @abstractmethod
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        pass
    
    @abstractmethod
    def set(self, key: str, value: Any, expire: Optional[int] = None) -> bool:
        """设置缓存值"""
        pass
    
    @abstractmethod
    def delete(self, key: str) -> bool:
        """删除缓存"""
        pass
    
    @abstractmethod
    def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        pass
    
    @abstractmethod
    def get_smart_expire_time(self, cache_type: str) -> int:
        """获取智能过期时间"""
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """检查服务是否可用"""
        pass


class TextProcessorInterface(ABC):
    """文本处理服务接口"""
    
    @abstractmethod
    def extract_keywords(self, text: str, max_keywords: int = 10) -> List[str]:
        """提取关键词"""
        pass
    
    @abstractmethod
    def clean_text(self, text: str) -> str:
        """清理文本"""
        pass
    
    @abstractmethod
    def normalize_text(self, text: str) -> str:
        """标准化文本"""
        pass
    
    @abstractmethod
    def segment_text(self, text: str) -> List[str]:
        """文本分词"""
        pass
    
    @abstractmethod
    def calculate_text_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度"""
        pass


class KeywordExtractionInterface(ABC):
    """关键词提取服务接口"""
    
    @abstractmethod
    def extract_keywords_basic(self, text: str, max_keywords: int = 10) -> List[Any]:
        """基础关键词提取"""
        pass
    
    @abstractmethod
    def extract_keywords_advanced(self, title: str, content: str, max_keywords: int = 10) -> List[Any]:
        """高级关键词提取"""
        pass
    
    @abstractmethod
    def extract_keywords_for_search(self, text: str, max_keywords: int = 20) -> List[str]:
        """搜索优化的关键词提取"""
        pass
    
    @abstractmethod
    def extract_keywords_for_recommendation(self, title: str, content: str, max_keywords: int = 15) -> Dict[str, float]:
        """推荐系统优化的关键词提取"""
        pass


class ContentQualityInterface(ABC):
    """内容质量检查服务接口"""
    
    @abstractmethod
    def check_content_quality(self, title: str, content: str, **kwargs) -> Any:
        """检查内容质量"""
        pass
    
    @abstractmethod
    def get_quality_summary(self, result: Any) -> Dict[str, Any]:
        """获取质量摘要"""
        pass


class MonitoringServiceInterface(ABC):
    """监控服务接口"""
    
    @abstractmethod
    def get_system_metrics(self) -> Dict[str, Any]:
        """获取系统指标"""
        pass
    
    @abstractmethod
    def check_health(self) -> Dict[str, Any]:
        """健康检查"""
        pass
    
    @abstractmethod
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        pass
    
    @abstractmethod
    def record_metric(self, name: str, value: float, tags: Optional[Dict[str, str]] = None) -> bool:
        """记录指标"""
        pass


class SearchServiceInterface(ABC):
    """搜索服务接口"""
    
    @abstractmethod
    def search(self, query: str, filters: Optional[Dict[str, Any]] = None, 
              page: int = 1, page_size: int = 20) -> PaginatedResult[Any]:
        """搜索内容"""
        pass
    
    @abstractmethod
    def get_suggestions(self, prefix: str, limit: int = 10) -> List[str]:
        """获取搜索建议"""
        pass
    
    @abstractmethod
    def index_content(self, content_id: str, content: Dict[str, Any]) -> bool:
        """索引内容"""
        pass
    
    @abstractmethod
    def remove_from_index(self, content_id: str) -> bool:
        """从索引中移除内容"""
        pass


class RecommendationServiceInterface(ABC):
    """推荐服务接口"""
    
    @abstractmethod
    def get_recommendations(self, user_id: int, limit: int = 10) -> List[Any]:
        """获取推荐内容"""
        pass
    
    @abstractmethod
    def get_similar_content(self, content_id: int, limit: int = 5) -> List[Any]:
        """获取相似内容"""
        pass
    
    @abstractmethod
    def update_user_preferences(self, user_id: int, preferences: Dict[str, Any]) -> bool:
        """更新用户偏好"""
        pass
    
    @abstractmethod
    def record_user_interaction(self, user_id: int, content_id: int, interaction_type: str) -> bool:
        """记录用户交互"""
        pass


class NotificationServiceInterface(ABC):
    """通知服务接口"""
    
    @abstractmethod
    def send_notification(self, user_id: int, message: str, channel: str, **kwargs) -> ServiceResult[bool]:
        """发送通知"""
        pass
    
    @abstractmethod
    def send_bulk_notification(self, user_ids: List[int], message: str, channel: str, **kwargs) -> ServiceResult[Dict[str, Any]]:
        """批量发送通知"""
        pass
    
    @abstractmethod
    def get_notification_history(self, user_id: int, limit: int = 50) -> List[Any]:
        """获取通知历史"""
        pass
    
    @abstractmethod
    def mark_as_read(self, notification_id: int) -> bool:
        """标记为已读"""
        pass


class AnalyticsServiceInterface(ABC):
    """分析服务接口"""
    
    @abstractmethod
    def track_event(self, event_name: str, properties: Dict[str, Any], user_id: Optional[int] = None) -> bool:
        """跟踪事件"""
        pass
    
    @abstractmethod
    def get_user_analytics(self, user_id: int, date_range: Optional[tuple] = None) -> Dict[str, Any]:
        """获取用户分析数据"""
        pass
    
    @abstractmethod
    def get_content_analytics(self, content_id: int, date_range: Optional[tuple] = None) -> Dict[str, Any]:
        """获取内容分析数据"""
        pass
    
    @abstractmethod
    def get_system_analytics(self, date_range: Optional[tuple] = None) -> Dict[str, Any]:
        """获取系统分析数据"""
        pass


class CrawlerServiceInterface(ABC):
    """爬虫服务接口"""
    
    @abstractmethod
    def crawl_source(self, source_url: str, **kwargs) -> ServiceResult[List[Dict[str, Any]]]:
        """爬取数据源"""
        pass
    
    @abstractmethod
    def schedule_crawl(self, source_url: str, schedule: str, **kwargs) -> ServiceResult[str]:
        """调度爬取任务"""
        pass
    
    @abstractmethod
    def get_crawl_status(self, task_id: str) -> Dict[str, Any]:
        """获取爬取状态"""
        pass
    
    @abstractmethod
    def cancel_crawl(self, task_id: str) -> bool:
        """取消爬取任务"""
        pass


class AIServiceInterface(ABC):
    """AI服务接口"""
    
    @abstractmethod
    def generate_summary(self, content: str, max_length: int = 200) -> ServiceResult[str]:
        """生成摘要"""
        pass
    
    @abstractmethod
    def classify_content(self, content: str) -> ServiceResult[Dict[str, Any]]:
        """内容分类"""
        pass
    
    @abstractmethod
    def analyze_sentiment(self, content: str) -> ServiceResult[Dict[str, Any]]:
        """情感分析"""
        pass
    
    @abstractmethod
    def extract_entities(self, content: str) -> ServiceResult[List[Dict[str, Any]]]:
        """实体提取"""
        pass


# 服务注册表接口
class ServiceRegistryInterface(ABC):
    """服务注册表接口"""
    
    @abstractmethod
    def register_service(self, name: str, service: Any) -> bool:
        """注册服务"""
        pass
    
    @abstractmethod
    def get_service(self, name: str) -> Optional[Any]:
        """获取服务"""
        pass
    
    @abstractmethod
    def unregister_service(self, name: str) -> bool:
        """注销服务"""
        pass
    
    @abstractmethod
    def list_services(self) -> List[str]:
        """列出所有服务"""
        pass


# 服务工厂接口
class ServiceFactoryInterface(ABC):
    """服务工厂接口"""
    
    @abstractmethod
    def create_cache_service(self) -> CacheServiceInterface:
        """创建缓存服务"""
        pass
    
    @abstractmethod
    def create_text_processor(self) -> TextProcessorInterface:
        """创建文本处理器"""
        pass
    
    @abstractmethod
    def create_monitoring_service(self) -> MonitoringServiceInterface:
        """创建监控服务"""
        pass
    
    @abstractmethod
    def create_search_service(self) -> SearchServiceInterface:
        """创建搜索服务"""
        pass
