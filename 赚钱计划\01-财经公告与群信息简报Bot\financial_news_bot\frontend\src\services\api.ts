import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { ApiResponse } from '@/types';

// 创建axios实例
const api: AxiosInstance = axios.create({
  baseURL: process.env.REACT_APP_API_BASE_URL || '/api/v1',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加认证token
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // 添加请求时间戳
    config.headers['X-Request-Time'] = new Date().toISOString();

    // 添加用户代理信息
    config.headers['X-Client-Version'] = process.env.REACT_APP_VERSION || '1.0.0';

    // 开发环境下记录API请求日志
    if (process.env.NODE_ENV === 'development') {
      console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`, {
        params: config.params,
        data: config.data,
      });
    }

    return config;
  },
  (error) => {
    console.error('[API Request Error]', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    // 开发环境下记录API响应日志
    if (process.env.NODE_ENV === 'development') {
      console.log(`[API Response] ${response.config.method?.toUpperCase()} ${response.config.url}`, {
        status: response.status,
        data: response.data,
      });
    }

    // 检查业务状态码
    if (response.data && response.data.code !== undefined) {
      if (response.data.code >= 400) {
        const error = new Error(response.data.message || '请求失败');
        (error as any).code = response.data.code;
        (error as any).response = response;
        throw error;
      }
    }

    return response;
  },
  (error) => {
    console.error('[API Response Error]', error);

    // 处理网络错误
    if (!error.response) {
      if (error.code === 'ECONNABORTED') {
        error.message = '请求超时，请检查网络连接';
      } else if (error.message === 'Network Error') {
        error.message = '网络连接失败，请检查网络设置';
      }
      return Promise.reject(error);
    }

    const { status, data } = error.response;

    // 处理HTTP状态码
    switch (status) {
      case 401:
        // 未授权，清除token并跳转到登录页
        localStorage.removeItem('access_token');
        if (window.location.pathname !== '/login') {
          window.location.href = '/login';
        }
        error.message = '登录已过期，请重新登录';
        break;
      case 403:
        error.message = '权限不足，无法访问该资源';
        break;
      case 404:
        error.message = '请求的资源不存在';
        break;
      case 422:
        error.message = data?.message || '请求参数验证失败';
        break;
      case 429:
        error.message = '请求过于频繁，请稍后再试';
        break;
      case 500:
        error.message = '服务器内部错误，请稍后再试';
        break;
      case 502:
        error.message = '网关错误，请稍后再试';
        break;
      case 503:
        error.message = '服务暂时不可用，请稍后再试';
        break;
      default:
        error.message = data?.message || `请求失败 (${status})`;
    }

    return Promise.reject(error);
  }
);

// API请求方法封装
export class ApiClient {
  // GET请求
  static async get<T = any>(
    url: string,
    params?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const response = await api.get<ApiResponse<T>>(url, { params, ...config });
    return response.data.data;
  }

  // POST请求
  static async post<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const response = await api.post<ApiResponse<T>>(url, data, config);
    return response.data.data;
  }

  // PUT请求
  static async put<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const response = await api.put<ApiResponse<T>>(url, data, config);
    return response.data.data;
  }

  // PATCH请求
  static async patch<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const response = await api.patch<ApiResponse<T>>(url, data, config);
    return response.data.data;
  }

  // DELETE请求
  static async delete<T = any>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const response = await api.delete<ApiResponse<T>>(url, config);
    return response.data.data;
  }

  // 上传文件
  static async upload<T = any>(
    url: string,
    file: File,
    onProgress?: (progress: number) => void,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await api.post<ApiResponse<T>>(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
      ...config,
    });

    return response.data.data;
  }

  // 下载文件
  static async download(
    url: string,
    filename?: string,
    config?: AxiosRequestConfig
  ): Promise<void> {
    const response = await api.get(url, {
      responseType: 'blob',
      ...config,
    });

    const blob = new Blob([response.data]);
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename || 'download';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);
  }

  // 获取原始响应（包含完整的响应信息）
  static async getRaw<T = any>(
    url: string,
    params?: any,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<ApiResponse<T>>> {
    return api.get<ApiResponse<T>>(url, { params, ...config });
  }

  // 批量请求
  static async batch<T = any>(
    requests: Array<() => Promise<T>>
  ): Promise<T[]> {
    return Promise.all(requests.map(request => request()));
  }

  // 取消请求
  static createCancelToken() {
    return axios.CancelToken.source();
  }

  // 检查是否为取消请求的错误
  static isCancelError(error: any): boolean {
    return axios.isCancel(error);
  }
}

export default api;
