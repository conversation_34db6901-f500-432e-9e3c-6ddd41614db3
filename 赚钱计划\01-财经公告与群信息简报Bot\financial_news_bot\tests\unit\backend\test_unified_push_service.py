"""
统一推送服务测试
测试统一推送服务的核心功能
"""
import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime

from app.services.unified_push_service import (
    UnifiedPushService,
    PushMessage,
    PushResult,
    PushMode,
    PushChannel,
    MessageType,
    BasicPushStrategy,
    EnhancedPushStrategy,
    LayeredPushStrategy,
    AnalyticsPushStrategy
)

class TestPushMessage:
    """测试推送消息数据结构"""
    
    def test_push_message_creation(self):
        """测试推送消息创建"""
        message = PushMessage(
            title="测试标题",
            content="测试内容",
            message_type=MessageType.TEXT,
            channels=[PushChannel.WECHAT_WORK],
            targets=["user1", "user2"]
        )
        
        assert message.title == "测试标题"
        assert message.content == "测试内容"
        assert message.message_type == MessageType.TEXT
        assert message.channels == [PushChannel.WECHAT_WORK]
        assert message.targets == ["user1", "user2"]
        assert isinstance(message.created_at, datetime)
    
    def test_push_message_defaults(self):
        """测试推送消息默认值"""
        message = PushMessage(title="标题", content="内容")
        
        assert message.message_type == MessageType.TEXT
        assert message.channels == [PushChannel.WECHAT_WORK]
        assert message.targets == []
        assert message.extra_data == {}

class TestPushResult:
    """测试推送结果数据结构"""
    
    def test_push_result_creation(self):
        """测试推送结果创建"""
        result = PushResult(
            success=True,
            message="推送成功",
            channel=PushChannel.WECHAT_WORK,
            target="user1",
            response_data={"status": "ok"}
        )
        
        assert result.success is True
        assert result.message == "推送成功"
        assert result.channel == PushChannel.WECHAT_WORK
        assert result.target == "user1"
        assert result.response_data == {"status": "ok"}
        assert isinstance(result.timestamp, datetime)

class TestBasicPushStrategy:
    """测试基础推送策略"""
    
    @pytest.fixture
    def strategy(self):
        return BasicPushStrategy()
    
    @pytest.fixture
    def mock_providers(self):
        mock_provider = AsyncMock()
        mock_provider.send_message.return_value = {
            'success': True,
            'message': '推送成功'
        }
        return {PushChannel.WECHAT_WORK: mock_provider}
    
    @pytest.fixture
    def test_message(self):
        return PushMessage(
            title="测试标题",
            content="测试内容",
            channels=[PushChannel.WECHAT_WORK]
        )
    
    @pytest.mark.asyncio
    async def test_basic_push_success(self, strategy, mock_providers, test_message):
        """测试基础推送成功"""
        results = await strategy.execute(test_message, mock_providers)
        
        assert len(results) == 1
        assert results[0].success is True
        assert results[0].channel == PushChannel.WECHAT_WORK
        
        # 验证提供商被调用
        mock_providers[PushChannel.WECHAT_WORK].send_message.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_basic_push_provider_not_configured(self, strategy, test_message):
        """测试推送提供商未配置"""
        empty_providers = {}
        results = await strategy.execute(test_message, empty_providers)
        
        assert len(results) == 1
        assert results[0].success is False
        assert "未配置" in results[0].message
    
    @pytest.mark.asyncio
    async def test_basic_push_provider_exception(self, strategy, test_message):
        """测试推送提供商异常"""
        mock_provider = AsyncMock()
        mock_provider.send_message.side_effect = Exception("网络错误")
        providers = {PushChannel.WECHAT_WORK: mock_provider}
        
        results = await strategy.execute(test_message, providers)
        
        assert len(results) == 1
        assert results[0].success is False
        assert "网络错误" in results[0].message

class TestEnhancedPushStrategy:
    """测试增强推送策略"""
    
    @pytest.fixture
    def strategy(self):
        return EnhancedPushStrategy(max_retries=2, retry_delay=0.1)
    
    @pytest.mark.asyncio
    async def test_enhanced_push_retry_success(self, strategy):
        """测试增强推送重试成功"""
        mock_provider = AsyncMock()
        # 第一次失败，第二次成功
        mock_provider.send_message.side_effect = [
            {'success': False, 'message': '临时失败'},
            {'success': True, 'message': '推送成功'}
        ]
        
        providers = {PushChannel.WECHAT_WORK: mock_provider}
        message = PushMessage(
            title="测试",
            content="内容",
            channels=[PushChannel.WECHAT_WORK]
        )
        
        results = await strategy.execute(message, providers)
        
        assert len(results) == 1
        assert results[0].success is True
        assert "尝试 2" in results[0].message
        assert mock_provider.send_message.call_count == 2
    
    @pytest.mark.asyncio
    async def test_enhanced_push_max_retries_exceeded(self, strategy):
        """测试增强推送超过最大重试次数"""
        mock_provider = AsyncMock()
        mock_provider.send_message.return_value = {'success': False, 'message': '持续失败'}
        
        providers = {PushChannel.WECHAT_WORK: mock_provider}
        message = PushMessage(
            title="测试",
            content="内容",
            channels=[PushChannel.WECHAT_WORK]
        )
        
        results = await strategy.execute(message, providers)
        
        assert len(results) == 1
        assert results[0].success is False
        assert "已重试" in results[0].message
        assert mock_provider.send_message.call_count == 3  # 初始 + 2次重试

class TestLayeredPushStrategy:
    """测试分层推送策略"""
    
    @pytest.fixture
    def strategy(self):
        return LayeredPushStrategy()
    
    @pytest.mark.asyncio
    async def test_layered_push_priority_order(self, strategy):
        """测试分层推送优先级顺序"""
        # 创建多个提供商
        providers = {}
        call_order = []
        
        for channel in [PushChannel.EMAIL, PushChannel.WECHAT_WORK, PushChannel.FEISHU]:
            mock_provider = AsyncMock()
            mock_provider.send_message.side_effect = lambda **kwargs, ch=channel: (
                call_order.append(ch),
                {'success': True, 'message': f'{ch.value}推送成功'}
            )[1]
            providers[channel] = mock_provider
        
        message = PushMessage(
            title="测试",
            content="内容",
            channels=[PushChannel.EMAIL, PushChannel.WECHAT_WORK, PushChannel.FEISHU]
        )
        
        results = await strategy.execute(message, providers)
        
        assert len(results) == 3
        # 验证调用顺序：WECHAT_WORK(1) -> FEISHU(2) -> EMAIL(4)
        assert call_order[0] == PushChannel.WECHAT_WORK
        assert call_order[1] == PushChannel.FEISHU
        assert call_order[2] == PushChannel.EMAIL
    
    @pytest.mark.asyncio
    async def test_layered_push_stop_on_success(self, strategy):
        """测试分层推送成功后停止"""
        providers = {}
        for channel in [PushChannel.WECHAT_WORK, PushChannel.EMAIL]:
            mock_provider = AsyncMock()
            mock_provider.send_message.return_value = {'success': True, 'message': '成功'}
            providers[channel] = mock_provider
        
        message = PushMessage(
            title="测试",
            content="内容",
            channels=[PushChannel.WECHAT_WORK, PushChannel.EMAIL],
            extra_data={'stop_on_success': True}
        )
        
        results = await strategy.execute(message, providers)
        
        assert len(results) == 1  # 只有第一个成功的结果
        assert results[0].channel == PushChannel.WECHAT_WORK
        # EMAIL提供商不应该被调用
        providers[PushChannel.EMAIL].send_message.assert_not_called()

class TestAnalyticsPushStrategy:
    """测试分析推送策略"""
    
    @pytest.fixture
    def strategy(self):
        return AnalyticsPushStrategy()
    
    @pytest.mark.asyncio
    @patch('app.services.unified_push_service.cache_service')
    async def test_analytics_push_records_stats(self, mock_cache, strategy):
        """测试分析推送记录统计信息"""
        mock_cache.set = AsyncMock()
        
        mock_provider = AsyncMock()
        mock_provider.send_message.return_value = {'success': True, 'message': '成功'}
        providers = {PushChannel.WECHAT_WORK: mock_provider}
        
        message = PushMessage(
            title="测试",
            content="内容",
            channels=[PushChannel.WECHAT_WORK]
        )
        
        results = await strategy.execute(message, providers)
        
        assert len(results) == 1
        assert results[0].success is True
        
        # 验证缓存被调用来存储分析数据
        mock_cache.set.assert_called_once()
        cache_key, analytics_data, expire = mock_cache.set.call_args[0]
        
        assert cache_key.startswith("push_analytics:")
        assert 'start_time' in analytics_data
        assert 'end_time' in analytics_data
        assert 'success_count' in analytics_data
        assert 'failed_count' in analytics_data
        assert analytics_data['success_count'] == 1
        assert analytics_data['failed_count'] == 0

class TestUnifiedPushService:
    """测试统一推送服务"""
    
    @pytest.fixture
    def service(self):
        with patch('app.services.unified_push_service.WeChatWorkProvider'), \
             patch('app.services.unified_push_service.FeishuProvider'), \
             patch('app.services.unified_push_service.EmailProvider'), \
             patch('app.services.unified_push_service.WeChatGroupProvider'):
            return UnifiedPushService()
    
    @pytest.mark.asyncio
    async def test_send_message_basic_mode(self, service):
        """测试基础模式发送消息"""
        # Mock策略执行
        mock_result = [PushResult(success=True, message="成功")]
        service.strategies[PushMode.BASIC].execute = AsyncMock(return_value=mock_result)
        
        results = await service.send_message(
            title="测试标题",
            content="测试内容",
            mode=PushMode.BASIC
        )
        
        assert len(results) == 1
        assert results[0].success is True
        assert results[0].message == "成功"
    
    @pytest.mark.asyncio
    async def test_send_message_with_custom_channels(self, service):
        """测试自定义渠道发送消息"""
        mock_result = [PushResult(success=True, message="成功")]
        service.strategies[PushMode.BASIC].execute = AsyncMock(return_value=mock_result)
        
        results = await service.send_message(
            title="测试标题",
            content="测试内容",
            channels=[PushChannel.FEISHU, PushChannel.EMAIL]
        )
        
        # 验证策略被调用时传入了正确的渠道
        call_args = service.strategies[PushMode.BASIC].execute.call_args
        message = call_args[0][0]
        assert message.channels == [PushChannel.FEISHU, PushChannel.EMAIL]
    
    @pytest.mark.asyncio
    async def test_send_message_exception_handling(self, service):
        """测试发送消息异常处理"""
        service.strategies[PushMode.BASIC].execute = AsyncMock(side_effect=Exception("策略执行失败"))
        
        results = await service.send_message(
            title="测试标题",
            content="测试内容"
        )
        
        assert len(results) == 1
        assert results[0].success is False
        assert "推送服务异常" in results[0].message

if __name__ == "__main__":
    pytest.main([__file__])
