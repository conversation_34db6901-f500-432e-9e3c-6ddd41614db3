"""
数据采集调度服务
提供动态调度管理、任务优先级控制和资源分配功能
"""
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from celery import Celery
from celery.schedules import crontab
from sqlalchemy.orm import Session

from app.celery_app import celery_app
from app.database import get_db

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SchedulerService:
    """调度服务管理器"""
    
    def __init__(self):
        self.celery_app = celery_app
        
        # 默认调度配置
        self.default_schedules = {
            'crawl_sse': {
                'task': 'app.tasks.crawler_tasks.crawl_sse',
                'schedule': crontab(minute='*/5'),  # 每5分钟
                'options': {'queue': 'crawler', 'priority': 8},
                'description': '上交所数据采集',
                'enabled': True
            },
            'crawl_szse': {
                'task': 'app.tasks.crawler_tasks.crawl_szse', 
                'schedule': crontab(minute='*/5'),  # 每5分钟
                'options': {'queue': 'crawler', 'priority': 8},
                'description': '深交所数据采集',
                'enabled': True
            },
            'crawl_csrc': {
                'task': 'app.tasks.crawler_tasks.crawl_csrc',
                'schedule': crontab(minute='*/30'),  # 每30分钟
                'options': {'queue': 'crawler', 'priority': 6},
                'description': '证监会数据采集',
                'enabled': True
            },
            'crawl_rss': {
                'task': 'app.tasks.crawler_tasks.crawl_rss',
                'schedule': crontab(minute='*/15'),  # 每15分钟
                'options': {'queue': 'crawler', 'priority': 7},
                'description': 'RSS数据采集',
                'enabled': True
            },
            'process_news_batch': {
                'task': 'app.tasks.crawler_tasks.process_news_batch',
                'schedule': crontab(minute='*/10'),  # 每10分钟
                'options': {'queue': 'processor', 'priority': 5},
                'description': '批量新闻处理',
                'enabled': True
            },
            'send_subscription_notifications': {
                'task': 'app.tasks.push_tasks.send_subscription_notifications',
                'schedule': crontab(minute='*/15'),  # 每15分钟
                'options': {'queue': 'pusher', 'priority': 9},
                'description': '订阅推送通知',
                'enabled': True
            },
            'cleanup_old_push_logs': {
                'task': 'app.tasks.push_tasks.cleanup_old_push_logs',
                'schedule': crontab(hour=2, minute=0),  # 每天凌晨2点
                'options': {'queue': 'processor', 'priority': 1},
                'description': '清理推送日志',
                'enabled': True,
                'kwargs': {'days': 30}
            },
            'health_check_crawlers': {
                'task': 'app.tasks.crawler_tasks.health_check_crawlers',
                'schedule': crontab(minute='*/30'),  # 每30分钟
                'options': {'queue': 'crawler', 'priority': 3},
                'description': '爬虫健康检查',
                'enabled': True
            }
        }
        
        # 任务优先级配置
        self.priority_levels = {
            'urgent': 10,      # 紧急任务
            'high': 8,         # 高优先级
            'normal': 5,       # 普通优先级
            'low': 3,          # 低优先级
            'maintenance': 1   # 维护任务
        }
        
        # 资源分配配置
        self.queue_limits = {
            'crawler': {'max_workers': 4, 'max_concurrent': 2},
            'processor': {'max_workers': 2, 'max_concurrent': 5},
            'pusher': {'max_workers': 3, 'max_concurrent': 10},
            'default': {'max_workers': 2, 'max_concurrent': 3}
        }
    
    def get_current_schedules(self) -> Dict[str, Any]:
        """获取当前调度配置"""
        try:
            # 从Celery Beat获取当前配置
            current_schedules = self.celery_app.conf.beat_schedule.copy()
            
            # 添加状态信息
            for name, config in current_schedules.items():
                config['status'] = 'active' if config.get('enabled', True) else 'disabled'
                config['last_run'] = self._get_last_run_time(name)
                config['next_run'] = self._get_next_run_time(name)
            
            return current_schedules
            
        except Exception as e:
            logger.error(f"获取调度配置失败: {str(e)}")
            return {}
    
    def update_schedule(self, task_name: str, schedule_config: Dict[str, Any]) -> bool:
        """更新调度配置"""
        try:
            # 验证配置
            if not self._validate_schedule_config(schedule_config):
                logger.error(f"调度配置验证失败: {task_name}")
                return False
            
            # 更新配置
            current_schedules = self.celery_app.conf.beat_schedule.copy()
            current_schedules[task_name] = schedule_config
            
            # 应用新配置
            self.celery_app.conf.beat_schedule = current_schedules
            
            logger.info(f"调度配置已更新: {task_name}")
            return True
            
        except Exception as e:
            logger.error(f"更新调度配置失败: {task_name} - {str(e)}")
            return False
    
    def enable_task(self, task_name: str) -> bool:
        """启用任务"""
        try:
            current_schedules = self.celery_app.conf.beat_schedule.copy()
            if task_name in current_schedules:
                current_schedules[task_name]['enabled'] = True
                self.celery_app.conf.beat_schedule = current_schedules
                logger.info(f"任务已启用: {task_name}")
                return True
            else:
                logger.warning(f"任务不存在: {task_name}")
                return False
                
        except Exception as e:
            logger.error(f"启用任务失败: {task_name} - {str(e)}")
            return False
    
    def disable_task(self, task_name: str) -> bool:
        """禁用任务"""
        try:
            current_schedules = self.celery_app.conf.beat_schedule.copy()
            if task_name in current_schedules:
                current_schedules[task_name]['enabled'] = False
                self.celery_app.conf.beat_schedule = current_schedules
                logger.info(f"任务已禁用: {task_name}")
                return True
            else:
                logger.warning(f"任务不存在: {task_name}")
                return False
                
        except Exception as e:
            logger.error(f"禁用任务失败: {task_name} - {str(e)}")
            return False
    
    def get_task_statistics(self) -> Dict[str, Any]:
        """获取任务统计信息"""
        try:
            stats = {
                'total_tasks': 0,
                'active_tasks': 0,
                'disabled_tasks': 0,
                'queue_stats': {},
                'priority_distribution': {},
                'last_updated': datetime.now().isoformat()
            }
            
            current_schedules = self.get_current_schedules()
            stats['total_tasks'] = len(current_schedules)
            
            for name, config in current_schedules.items():
                # 统计状态
                if config.get('enabled', True):
                    stats['active_tasks'] += 1
                else:
                    stats['disabled_tasks'] += 1
                
                # 统计队列
                queue = config.get('options', {}).get('queue', 'default')
                if queue not in stats['queue_stats']:
                    stats['queue_stats'][queue] = 0
                stats['queue_stats'][queue] += 1
                
                # 统计优先级
                priority = config.get('options', {}).get('priority', 5)
                priority_level = self._get_priority_level(priority)
                if priority_level not in stats['priority_distribution']:
                    stats['priority_distribution'][priority_level] = 0
                stats['priority_distribution'][priority_level] += 1
            
            return stats
            
        except Exception as e:
            logger.error(f"获取任务统计失败: {str(e)}")
            return {}
    
    def reset_to_defaults(self) -> bool:
        """重置为默认配置"""
        try:
            self.celery_app.conf.beat_schedule = self.default_schedules.copy()
            logger.info("调度配置已重置为默认值")
            return True
            
        except Exception as e:
            logger.error(f"重置配置失败: {str(e)}")
            return False
    
    def _validate_schedule_config(self, config: Dict[str, Any]) -> bool:
        """验证调度配置"""
        required_fields = ['task', 'schedule']
        
        for field in required_fields:
            if field not in config:
                logger.error(f"缺少必需字段: {field}")
                return False
        
        # 验证任务名称
        if not isinstance(config['task'], str):
            logger.error("任务名称必须是字符串")
            return False
        
        # 验证调度表达式
        if not hasattr(config['schedule'], 'remaining_estimate'):
            logger.error("无效的调度表达式")
            return False
        
        return True
    
    def _get_last_run_time(self, task_name: str) -> Optional[str]:
        """获取任务最后运行时间"""
        # TODO: 从Celery结果后端获取最后运行时间
        return None
    
    def _get_next_run_time(self, task_name: str) -> Optional[str]:
        """获取任务下次运行时间"""
        # TODO: 计算下次运行时间
        return None
    
    def _get_priority_level(self, priority: int) -> str:
        """根据优先级数值获取优先级级别"""
        if priority >= 9:
            return 'urgent'
        elif priority >= 7:
            return 'high'
        elif priority >= 4:
            return 'normal'
        elif priority >= 2:
            return 'low'
        else:
            return 'maintenance'


# 全局调度服务实例
scheduler_service = SchedulerService()
