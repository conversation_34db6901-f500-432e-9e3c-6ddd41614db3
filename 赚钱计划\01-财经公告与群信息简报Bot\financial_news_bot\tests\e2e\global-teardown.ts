import { chromium, FullConfig } from '@playwright/test';
import fs from 'fs';
import path from 'path';

/**
 * 全局测试清理
 * 在所有测试结束后执行
 */
async function globalTeardown(config: FullConfig) {
  console.log('🧹 开始端到端测试全局清理...');

  const browser = await chromium.launch();
  const page = await browser.newPage();

  try {
    // 清理测试数据
    await cleanupTestData(page);

    // 清理认证文件
    await cleanupAuthFiles();

    // 生成测试报告
    await generateTestReport();

    console.log('✅ 全局清理完成');
  } catch (error) {
    console.error('❌ 全局清理失败:', error);
  } finally {
    await browser.close();
  }
}

/**
 * 清理测试数据
 */
async function cleanupTestData(page: any) {
  console.log('🗑️ 清理测试数据...');

  try {
    // 删除测试用户创建的数据
    await page.request.delete('/api/v1/admin/test-data', {
      headers: {
        'Authorization': 'Bearer admin_test_token'
      }
    });

    console.log('✅ 测试数据清理完成');
  } catch (error) {
    console.warn('⚠️ 测试数据清理失败:', error);
  }
}

/**
 * 清理认证文件
 */
async function cleanupAuthFiles() {
  console.log('🔐 清理认证文件...');

  const authFile = path.join(__dirname, 'auth.json');
  
  try {
    if (fs.existsSync(authFile)) {
      fs.unlinkSync(authFile);
      console.log('✅ 认证文件清理完成');
    }
  } catch (error) {
    console.warn('⚠️ 认证文件清理失败:', error);
  }
}

/**
 * 生成测试报告
 */
async function generateTestReport() {
  console.log('📊 生成测试报告...');

  try {
    const reportData = {
      timestamp: new Date().toISOString(),
      summary: '端到端测试完成',
      environment: {
        node_version: process.version,
        platform: process.platform,
        ci: !!process.env.CI,
      }
    };

    const reportPath = path.join(__dirname, '../../test-reports/e2e-summary.json');
    
    // 确保目录存在
    const reportDir = path.dirname(reportPath);
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
    }

    fs.writeFileSync(reportPath, JSON.stringify(reportData, null, 2));
    console.log('✅ 测试报告生成完成');
  } catch (error) {
    console.warn('⚠️ 测试报告生成失败:', error);
  }
}

export default globalTeardown;
