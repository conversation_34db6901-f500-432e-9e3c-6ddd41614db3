# 导入所有模型以确保它们被注册到Base.metadata
from .user import User
from .subscription import Subscription
from .news import News
from .sensitive_word import SensitiveWord
from .entity import Entity
from .push_log import PushLog

# 第4周新增模型
from .permission import Permission, Role, UserRole, RolePermission, PermissionGroup, AuditLog
from .report import Report, ReportTemplate, ReportSection, ReportPushLog, ReportGenerationLog

__all__ = [
    "User",
    "Subscription",
    "News",
    "SensitiveWord",
    "Entity",
    "PushLog",
    # 权限管理
    "Permission",
    "Role",
    "UserRole",
    "RolePermission",
    "PermissionGroup",
    "AuditLog",
    # 简报系统
    "Report",
    "ReportTemplate",
    "ReportSection",
    "ReportPushLog",
    "ReportGenerationLog"
]
