import React, { useState, useMemo } from 'react';
import { Table, Card, List, But<PERSON>, Drawer, Space, Typography, Tag } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { EyeOutlined, MoreOutlined } from '@ant-design/icons';
import { useResponsive } from '@/hooks/useResponsive';

const { Text, Title } = Typography;

interface ResponsiveTableProps<T = any> {
  dataSource: T[];
  columns: ColumnsType<T>;
  loading?: boolean;
  pagination?: any;
  rowKey?: string | ((record: T) => string);
  onRow?: (record: T, index?: number) => any;
  className?: string;
  style?: React.CSSProperties;
  // 移动端配置
  mobileCardRender?: (record: T, index: number) => React.ReactNode;
  mobileTitle?: (record: T) => string;
  mobileDescription?: (record: T) => string;
  mobileExtra?: (record: T) => React.ReactNode;
  // 响应式配置
  breakpoint?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  hideColumnsOnMobile?: string[]; // 在移动端隐藏的列的key
  compactColumnsOnTablet?: string[]; // 在平板端压缩的列的key
}

function ResponsiveTable<T extends Record<string, any>>({
  dataSource,
  columns,
  loading = false,
  pagination,
  rowKey = 'id',
  onRow,
  className = '',
  style = {},
  mobileCardRender,
  mobileTitle,
  mobileDescription,
  mobileExtra,
  breakpoint = 'md',
  hideColumnsOnMobile = [],
  compactColumnsOnTablet = [],
}: ResponsiveTableProps<T>) {
  const responsive = useResponsive();
  const [detailVisible, setDetailVisible] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<T | null>(null);

  // 判断是否使用移动端布局
  const useMobileLayout = useMemo(() => {
    switch (breakpoint) {
      case 'xs':
        return responsive.width < 480;
      case 'sm':
        return responsive.width < 576;
      case 'md':
        return responsive.width < 768;
      case 'lg':
        return responsive.width < 1024;
      case 'xl':
        return responsive.width < 1200;
      default:
        return responsive.isMobile;
    }
  }, [responsive, breakpoint]);

  // 处理列配置
  const processedColumns = useMemo(() => {
    if (useMobileLayout) {
      // 移动端：只显示关键列
      return columns.filter(col => 
        col.key && !hideColumnsOnMobile.includes(col.key as string)
      );
    } else if (responsive.isTablet) {
      // 平板端：压缩某些列
      return columns.map(col => {
        if (col.key && compactColumnsOnTablet.includes(col.key as string)) {
          return {
            ...col,
            width: col.width ? (col.width as number) * 0.7 : undefined,
            ellipsis: true,
          };
        }
        return col;
      });
    }
    return columns;
  }, [columns, useMobileLayout, responsive.isTablet, hideColumnsOnMobile, compactColumnsOnTablet]);

  // 移动端卡片渲染
  const renderMobileCard = (record: T, index: number) => {
    if (mobileCardRender) {
      return mobileCardRender(record, index);
    }

    const title = mobileTitle ? mobileTitle(record) : record.title || record.name || `项目 ${index + 1}`;
    const description = mobileDescription ? mobileDescription(record) : record.description || '';
    const extra = mobileExtra ? mobileExtra(record) : null;

    return (
      <Card
        size="small"
        style={{ marginBottom: 8 }}
        bodyStyle={{ padding: 12 }}
        onClick={() => onRow?.(record, index)?.onClick?.()}
      >
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <div style={{ flex: 1, minWidth: 0 }}>
            <Title level={5} style={{ margin: 0, marginBottom: 4 }} ellipsis>
              {title}
            </Title>
            {description && (
              <Text type="secondary" style={{ fontSize: 12 }} ellipsis>
                {description}
              </Text>
            )}
            
            {/* 显示其他重要字段 */}
            <div style={{ marginTop: 8 }}>
              {columns.slice(0, 3).map((col, colIndex) => {
                if (!col.dataIndex || col.dataIndex === 'title' || col.dataIndex === 'name') {
                  return null;
                }
                
                const value = record[col.dataIndex as string];
                if (!value) return null;
                
                return (
                  <div key={colIndex} style={{ marginBottom: 4 }}>
                    <Text style={{ fontSize: 11, color: '#999' }}>
                      {col.title as string}:
                    </Text>
                    <Text style={{ fontSize: 12, marginLeft: 4 }}>
                      {typeof value === 'string' ? value : JSON.stringify(value)}
                    </Text>
                  </div>
                );
              })}
            </div>
          </div>
          
          <div style={{ marginLeft: 8, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            {extra}
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                setSelectedRecord(record);
                setDetailVisible(true);
              }}
            />
          </div>
        </div>
      </Card>
    );
  };

  // 详情抽屉渲染
  const renderDetailDrawer = () => (
    <Drawer
      title="详细信息"
      placement="bottom"
      height="70%"
      open={detailVisible}
      onClose={() => setDetailVisible(false)}
      bodyStyle={{ padding: 16 }}
    >
      {selectedRecord && (
        <div>
          {columns.map((col, index) => {
            if (!col.dataIndex) return null;
            
            const value = selectedRecord[col.dataIndex as string];
            if (!value) return null;
            
            return (
              <div key={index} style={{ marginBottom: 16 }}>
                <Text strong style={{ display: 'block', marginBottom: 4 }}>
                  {col.title as string}
                </Text>
                <Text>
                  {typeof value === 'string' ? value : JSON.stringify(value)}
                </Text>
              </div>
            );
          })}
        </div>
      )}
    </Drawer>
  );

  // 移动端列表渲染
  if (useMobileLayout) {
    return (
      <>
        <List
          className={`responsive-table-mobile ${className}`}
          style={style}
          loading={loading}
          dataSource={dataSource}
          pagination={pagination ? {
            ...pagination,
            showSizeChanger: false,
            showQuickJumper: false,
            simple: true,
          } : false}
          renderItem={renderMobileCard}
          rowKey={rowKey}
        />
        {renderDetailDrawer()}
      </>
    );
  }

  // 桌面端/平板端表格渲染
  return (
    <div className={`responsive-table ${className}`} style={style}>
      <Table
        dataSource={dataSource}
        columns={processedColumns}
        loading={loading}
        pagination={pagination}
        rowKey={rowKey}
        onRow={onRow}
        scroll={{ x: 'max-content' }}
        size={responsive.isTablet ? 'small' : 'middle'}
      />
    </div>
  );
}

export default ResponsiveTable;

// 预设的列配置
export const commonColumnConfigs = {
  // 移动端隐藏的列
  mobileHidden: ['createdAt', 'updatedAt', 'description', 'tags'],
  // 平板端压缩的列
  tabletCompact: ['description', 'tags', 'actions'],
};

// 常用的移动端渲染函数
export const mobileRenderers = {
  // 新闻列表
  news: (record: any) => record.title,
  newsDescription: (record: any) => record.summary || record.content?.substring(0, 100),
  newsExtra: (record: any) => (
    <Space direction="vertical" size="small" align="end">
      <Text style={{ fontSize: 10, color: '#999' }}>
        {new Date(record.publishedAt).toLocaleDateString()}
      </Text>
      {record.category && <Tag size="small">{record.category}</Tag>}
    </Space>
  ),
  
  // 订阅列表
  subscription: (record: any) => record.name,
  subscriptionDescription: (record: any) => `${record.keywords?.join(', ') || ''} - ${record.channels?.length || 0}个渠道`,
  subscriptionExtra: (record: any) => (
    <Tag color={record.active ? 'green' : 'red'}>
      {record.active ? '启用' : '禁用'}
    </Tag>
  ),
};
