"""
权限依赖注入模块
提供FastAPI依赖注入的权限控制功能
"""
import logging
from typing import List, Callable, Optional
from fastapi import Depends, HTTPException, status

from ..models.user import User, UserRole
from ..dependencies.auth import get_current_user, get_current_active_user
from ..utils.permissions import (
    has_permission, 
    has_all_permissions, 
    has_any_permission, 
    is_role_higher_or_equal,
    get_user_permissions
)
from ..exceptions.permissions import (
    InsufficientPermissionError,
    MultiplePermissionsRequiredError,
    RoleHierarchyError,
    AuthenticationRequiredError,
    UserInactiveError
)

logger = logging.getLogger(__name__)


def require_permission(permission: str) -> Callable:
    """
    创建需要特定权限的依赖注入函数
    
    Args:
        permission: 所需权限名称
        
    Returns:
        依赖注入函数
    """
    async def permission_dependency(
        current_user: User = Depends(get_current_active_user)
    ) -> User:
        if not has_permission(current_user.role, permission):
            logger.warning(
                f"Permission denied: User {current_user.id} (role: {current_user.role}) "
                f"lacks permission '{permission}'"
            )
            raise InsufficientPermissionError(
                required_permission=permission,
                user_role=current_user.role.value
            )
        
        logger.debug(
            f"Permission granted: User {current_user.id} has permission '{permission}'"
        )
        return current_user
    
    return permission_dependency


def require_permissions(permissions: List[str], require_all: bool = True) -> Callable:
    """
    创建需要多个权限的依赖注入函数
    
    Args:
        permissions: 所需权限列表
        require_all: 是否需要所有权限（True）或任意权限（False）
        
    Returns:
        依赖注入函数
    """
    async def permissions_dependency(
        current_user: User = Depends(get_current_active_user)
    ) -> User:
        if require_all:
            has_perms = has_all_permissions(current_user.role, permissions)
            missing_perms = [
                perm for perm in permissions 
                if not has_permission(current_user.role, perm)
            ]
        else:
            has_perms = has_any_permission(current_user.role, permissions)
            missing_perms = permissions if not has_perms else []
        
        if not has_perms:
            logger.warning(
                f"Permission denied: User {current_user.id} (role: {current_user.role}) "
                f"lacks required permissions {permissions} (require_all: {require_all})"
            )
            raise MultiplePermissionsRequiredError(
                required_permissions=permissions,
                missing_permissions=missing_perms,
                user_role=current_user.role.value
            )
        
        logger.debug(
            f"Permission granted: User {current_user.id} has required permissions"
        )
        return current_user
    
    return permissions_dependency


def require_role(min_role: UserRole) -> Callable:
    """
    创建需要最低角色级别的依赖注入函数
    
    Args:
        min_role: 最低要求的角色
        
    Returns:
        依赖注入函数
    """
    async def role_dependency(
        current_user: User = Depends(get_current_active_user)
    ) -> User:
        if not is_role_higher_or_equal(current_user.role, min_role):
            logger.warning(
                f"Role check failed: User {current_user.id} (role: {current_user.role}) "
                f"requires minimum role '{min_role}'"
            )
            raise RoleHierarchyError(
                user_role=current_user.role.value,
                required_role=min_role.value
            )
        
        logger.debug(
            f"Role check passed: User {current_user.id} meets minimum role requirement"
        )
        return current_user
    
    return role_dependency


# 常用权限依赖注入快捷方式
def require_admin() -> Callable:
    """要求管理员权限的依赖注入"""
    return require_role(UserRole.ADMIN)


def require_enterprise_or_admin() -> Callable:
    """要求企业版或管理员权限的依赖注入"""
    return require_role(UserRole.ENTERPRISE)


def require_pro_or_higher() -> Callable:
    """要求专业版或更高权限的依赖注入"""
    return require_role(UserRole.PRO)


def require_basic_subscription() -> Callable:
    """要求基础订阅权限的依赖注入"""
    return require_permission("basic_subscription")


def require_advanced_subscription() -> Callable:
    """要求高级订阅权限的依赖注入"""
    return require_permission("advanced_subscription")


def require_pro_subscription() -> Callable:
    """要求专业版订阅权限的依赖注入"""
    return require_permission("pro_subscription")


def require_export_data() -> Callable:
    """要求数据导出权限的依赖注入"""
    return require_permission("export_data")


def require_view_analytics() -> Callable:
    """要求查看分析权限的依赖注入"""
    return require_permission("view_analytics")


def require_user_management() -> Callable:
    """要求用户管理权限的依赖注入"""
    return require_permission("user_management")


def require_system_config() -> Callable:
    """要求系统配置权限的依赖注入"""
    return require_permission("system_config")


def require_monitoring_access() -> Callable:
    """要求监控访问权限的依赖注入"""
    return require_permission("monitoring_access")


class PermissionDependency:
    """权限依赖注入类，用于复杂的权限逻辑"""
    
    def __init__(
        self,
        permissions: Optional[List[str]] = None,
        min_role: Optional[UserRole] = None,
        require_all_permissions: bool = True,
        allow_inactive: bool = False
    ):
        self.permissions = permissions or []
        self.min_role = min_role
        self.require_all_permissions = require_all_permissions
        self.allow_inactive = allow_inactive
    
    async def __call__(
        self,
        current_user: User = Depends(get_current_active_user)
    ) -> User:
        """执行权限检查"""
        
        # 检查角色层级
        if self.min_role and not is_role_higher_or_equal(current_user.role, self.min_role):
            logger.warning(
                f"Role check failed: User {current_user.id} (role: {current_user.role}) "
                f"requires minimum role '{self.min_role}'"
            )
            raise RoleHierarchyError(
                user_role=current_user.role.value,
                required_role=self.min_role.value
            )
        
        # 检查权限
        if self.permissions:
            if self.require_all_permissions:
                has_perms = has_all_permissions(current_user.role, self.permissions)
                missing_perms = [
                    perm for perm in self.permissions 
                    if not has_permission(current_user.role, perm)
                ]
            else:
                has_perms = has_any_permission(current_user.role, self.permissions)
                missing_perms = self.permissions if not has_perms else []
            
            if not has_perms:
                logger.warning(
                    f"Permission denied: User {current_user.id} (role: {current_user.role}) "
                    f"lacks required permissions {self.permissions}"
                )
                raise MultiplePermissionsRequiredError(
                    required_permissions=self.permissions,
                    missing_permissions=missing_perms,
                    user_role=current_user.role.value
                )
        
        logger.debug(f"Permission check passed for user {current_user.id}")
        return current_user


def create_permission_dependency(
    permissions: Optional[List[str]] = None,
    min_role: Optional[UserRole] = None,
    require_all_permissions: bool = True,
    allow_inactive: bool = False
) -> PermissionDependency:
    """
    创建自定义权限依赖注入
    
    Args:
        permissions: 所需权限列表
        min_role: 最低角色要求
        require_all_permissions: 是否需要所有权限
        allow_inactive: 是否允许未激活用户
        
    Returns:
        PermissionDependency: 权限依赖注入实例
    """
    return PermissionDependency(
        permissions=permissions,
        min_role=min_role,
        require_all_permissions=require_all_permissions,
        allow_inactive=allow_inactive
    )


async def get_user_permission_info(
    current_user: User = Depends(get_current_active_user)
) -> dict:
    """
    获取用户权限信息的依赖注入
    
    Args:
        current_user: 当前用户
        
    Returns:
        dict: 用户权限信息
    """
    return {
        "user_id": current_user.id,
        "username": current_user.username,
        "role": current_user.role.value,
        "permissions": get_user_permissions(current_user.role)
    }


def require_bulk_operations() -> Callable:
    """
    创建需要批量操作权限的依赖注入函数

    Returns:
        依赖注入函数
    """
    async def bulk_operations_dependency(
        current_user: User = Depends(get_current_active_user)
    ) -> User:
        # 检查是否有批量操作权限（通常需要管理员或高级权限）
        required_permissions = [
            "subscription:bulk_create",
            "subscription:bulk_update",
            "subscription:bulk_delete"
        ]

        if not has_any_permission(current_user.role, required_permissions):
            logger.warning(
                f"Bulk operations denied: User {current_user.id} (role: {current_user.role}) "
                f"lacks bulk operation permissions"
            )
            raise InsufficientPermissionError(
                required_permission="bulk_operations",
                user_role=current_user.role.value
            )

        logger.info(f"Bulk operations granted to user {current_user.id}")
        return current_user

    return bulk_operations_dependency
