from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from typing import Optional, List
from datetime import datetime, timedelta
import logging

from ..models.user import User, UserRole
from ..schemas.user import UserCreate, UserUpdate, UserLogin
from ..utils.security import get_password_hash, verify_password, create_access_token, validate_password_strength
from ..exceptions.user import UserAlreadyExistsError, UserNotFoundError, InvalidCredentialsError, WeakPasswordError

logger = logging.getLogger(__name__)

class UserService:
    def __init__(self, db: Session):
        self.db = db

    def create_user(self, user_data: UserCreate) -> User:
        """创建新用户"""
        # 验证密码强度
        is_strong, message = validate_password_strength(user_data.password)
        if not is_strong:
            raise WeakPasswordError(message)
        
        # 检查用户名和邮箱是否已存在
        existing_user = self.db.query(User).filter(
            (User.username == user_data.username) | (User.email == user_data.email)
        ).first()
        
        if existing_user:
            if existing_user.username == user_data.username:
                raise UserAlreadyExistsError("用户名已存在")
            else:
                raise UserAlreadyExistsError("邮箱已存在")
        
        # 创建用户
        hashed_password = get_password_hash(user_data.password)
        db_user = User(
            username=user_data.username,
            email=user_data.email,
            password_hash=hashed_password
        )
        
        try:
            self.db.add(db_user)
            self.db.commit()
            self.db.refresh(db_user)
            return db_user
        except IntegrityError:
            self.db.rollback()
            raise UserAlreadyExistsError("用户创建失败，用户名或邮箱已存在")

    def authenticate_user(self, login_data: UserLogin) -> tuple[User, str]:
        """用户认证并返回用户和访问令牌"""
        user = self.db.query(User).filter(User.email == login_data.email).first()
        
        if not user or not verify_password(login_data.password, user.password_hash):
            raise InvalidCredentialsError("邮箱或密码错误")
        
        # 更新最后登录时间
        user.last_login_at = datetime.utcnow()
        self.db.commit()
        
        # 创建访问令牌
        access_token_expires = timedelta(minutes=30)
        access_token = create_access_token(
            data={"sub": str(user.id), "email": user.email},
            expires_delta=access_token_expires
        )
        
        return user, access_token

    def get_user_by_id(self, user_id: int) -> Optional[User]:
        """根据ID获取用户"""
        return self.db.query(User).filter(User.id == user_id).first()

    def get_user_by_email(self, email: str) -> Optional[User]:
        """根据邮箱获取用户"""
        return self.db.query(User).filter(User.email == email).first()

    def get_user_by_username(self, username: str) -> Optional[User]:
        """根据用户名获取用户"""
        return self.db.query(User).filter(User.username == username).first()

    def update_user(self, user_id: int, user_data: UserUpdate) -> User:
        """更新用户信息"""
        user = self.get_user_by_id(user_id)
        if not user:
            raise UserNotFoundError("用户不存在")
        
        # 检查用户名和邮箱是否被其他用户使用
        if user_data.username and user_data.username != user.username:
            existing_user = self.get_user_by_username(user_data.username)
            if existing_user:
                raise UserAlreadyExistsError("用户名已存在")
            user.username = user_data.username
        
        if user_data.email and user_data.email != user.email:
            existing_user = self.get_user_by_email(user_data.email)
            if existing_user:
                raise UserAlreadyExistsError("邮箱已存在")
            user.email = user_data.email
        
        user.updated_at = datetime.utcnow()
        
        try:
            self.db.commit()
            self.db.refresh(user)
            return user
        except IntegrityError:
            self.db.rollback()
            raise UserAlreadyExistsError("更新失败，用户名或邮箱已存在")

    def delete_user(self, user_id: int) -> bool:
        """删除用户"""
        user = self.get_user_by_id(user_id)
        if not user:
            raise UserNotFoundError("用户不存在")

        self.db.delete(user)
        self.db.commit()
        return True

    def get_users(self, skip: int = 0, limit: int = 100) -> List[User]:
        """获取用户列表（管理员功能）"""
        return self.db.query(User).offset(skip).limit(limit).all()

    def update_user_role(self, user_id: int, new_role: UserRole, admin_user_id: int) -> User:
        """更新用户角色（管理员功能）"""
        user = self.get_user_by_id(user_id)
        if not user:
            raise UserNotFoundError("用户不存在")

        old_role = user.role
        user.role = new_role
        user.updated_at = datetime.now()

        try:
            self.db.commit()
            self.db.refresh(user)

            # 记录角色变更历史
            self._log_role_change(user_id, old_role, new_role, admin_user_id)

            logger.info(f"User {user_id} role updated from {old_role} to {new_role} by admin {admin_user_id}")
            return user

        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to update user role: {e}")
            raise

    def _log_role_change(self, user_id: int, old_role: UserRole, new_role: UserRole, admin_user_id: int):
        """记录角色变更历史"""
        try:
            # 插入角色变更记录
            self.db.execute("""
                INSERT INTO user_role_history (user_id, old_role, new_role, changed_by_user_id, created_at)
                VALUES (:user_id, :old_role, :new_role, :changed_by_user_id, NOW())
            """, {
                "user_id": user_id,
                "old_role": old_role.value,
                "new_role": new_role.value,
                "changed_by_user_id": admin_user_id
            })
            self.db.commit()
        except Exception as e:
            logger.warning(f"Failed to log role change: {e}")

    def get_users_by_role(self, role: UserRole) -> List[User]:
        """根据角色获取用户列表"""
        return self.db.query(User).filter(User.role == role).all()

    def count_users_by_role(self) -> dict:
        """统计各角色用户数量"""
        result = {}
        for role in UserRole:
            count = self.db.query(User).filter(User.role == role).count()
            result[role.value] = count
        return result
