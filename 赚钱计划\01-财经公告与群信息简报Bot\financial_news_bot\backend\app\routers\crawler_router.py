"""
简化爬虫路由
提供简化爬虫服务的API接口
"""
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta

from app.services.simplified_crawler_service import crawler_manager
from app.dependencies.auth import get_current_user
from app.models.user import User

router = APIRouter(prefix="/api/v1/crawler", tags=["简化爬虫服务"])

class CrawlRequest(BaseModel):
    """爬取请求模型"""
    crawler_name: Optional[str] = Field(None, description="爬虫名称，为空则爬取所有")
    start_date: Optional[datetime] = Field(None, description="开始日期")
    end_date: Optional[datetime] = Field(None, description="结束日期")
    days_back: Optional[int] = Field(7, description="向前爬取天数（如果未指定日期）", ge=1, le=30)

class CrawlResponse(BaseModel):
    """爬取响应模型"""
    success: bool
    message: str
    results: Dict[str, List[Dict[str, Any]]]
    stats: Dict[str, Dict[str, Any]]
    total_items: int
    crawl_time: str

class CrawlerInfo(BaseModel):
    """爬虫信息模型"""
    name: str
    base_url: str
    domain: str
    rate_limit: float
    status: str

@router.get("/list")
async def list_crawlers(current_user: User = Depends(get_current_user)):
    """
    获取所有注册的爬虫列表
    
    返回系统中所有可用的爬虫信息
    """
    try:
        crawler_names = crawler_manager.list_crawlers()
        
        crawler_info = []
        for name in crawler_names:
            crawler = crawler_manager.get_crawler(name)
            if crawler:
                crawler_info.append(CrawlerInfo(
                    name=crawler.name,
                    base_url=crawler.base_url,
                    domain=crawler.domain,
                    rate_limit=crawler.rate_limit,
                    status="available"
                ))
        
        return {
            "crawlers": crawler_info,
            "total_count": len(crawler_info),
            "available_crawlers": crawler_names
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取爬虫列表异常: {str(e)}")

@router.post("/crawl", response_model=CrawlResponse)
async def crawl_news(
    request: CrawlRequest,
    current_user: User = Depends(get_current_user)
):
    """
    执行新闻爬取任务
    
    支持单个爬虫或所有爬虫的爬取
    """
    try:
        # 处理日期参数
        if not request.start_date and not request.end_date:
            # 如果没有指定日期，使用days_back参数
            end_date = datetime.now()
            start_date = end_date - timedelta(days=request.days_back)
        else:
            start_date = request.start_date
            end_date = request.end_date or datetime.now()
        
        # 执行爬取
        if request.crawler_name:
            # 单个爬虫爬取
            crawler = crawler_manager.get_crawler(request.crawler_name)
            if not crawler:
                raise HTTPException(
                    status_code=404, 
                    detail=f"爬虫 {request.crawler_name} 不存在"
                )
            
            async with crawler:
                news_list = await crawler.crawl(start_date, end_date)
                results = {request.crawler_name: news_list}
                stats = {request.crawler_name: crawler.get_stats()}
        else:
            # 所有爬虫爬取
            results = await crawler_manager.crawl_all(start_date, end_date)
            stats = crawler_manager.get_all_stats()
        
        # 统计总数
        total_items = sum(len(news_list) for news_list in results.values())
        
        return CrawlResponse(
            success=True,
            message=f"爬取完成，共获取 {total_items} 条新闻",
            results=results,
            stats=stats,
            total_items=total_items,
            crawl_time=datetime.now().isoformat()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"爬取任务异常: {str(e)}")

@router.post("/crawl/background")
async def crawl_news_background(
    request: CrawlRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user)
):
    """
    后台执行新闻爬取任务
    
    适用于大量数据的爬取，不会阻塞请求
    """
    try:
        # 处理日期参数
        if not request.start_date and not request.end_date:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=request.days_back)
        else:
            start_date = request.start_date
            end_date = request.end_date or datetime.now()
        
        # 添加后台任务
        async def background_crawl():
            try:
                if request.crawler_name:
                    crawler = crawler_manager.get_crawler(request.crawler_name)
                    if crawler:
                        async with crawler:
                            await crawler.crawl(start_date, end_date)
                else:
                    await crawler_manager.crawl_all(start_date, end_date)
            except Exception as e:
                logger.error(f"后台爬取任务失败: {str(e)}")
        
        background_tasks.add_task(background_crawl)
        
        return {
            "success": True,
            "message": "后台爬取任务已启动",
            "task_info": {
                "crawler_name": request.crawler_name or "all",
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "submitted_at": datetime.now().isoformat()
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"启动后台爬取任务异常: {str(e)}")

@router.get("/stats")
async def get_crawler_stats(current_user: User = Depends(get_current_user)):
    """
    获取所有爬虫的统计信息
    
    返回各爬虫的运行统计数据
    """
    try:
        stats = crawler_manager.get_all_stats()
        
        # 计算总体统计
        total_stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "total_items": 0,
            "active_crawlers": len([s for s in stats.values() if s.get("total_requests", 0) > 0])
        }
        
        for crawler_stats in stats.values():
            total_stats["total_requests"] += crawler_stats.get("total_requests", 0)
            total_stats["successful_requests"] += crawler_stats.get("successful_requests", 0)
            total_stats["failed_requests"] += crawler_stats.get("failed_requests", 0)
            total_stats["total_items"] += crawler_stats.get("total_items", 0)
        
        # 计算成功率
        if total_stats["total_requests"] > 0:
            total_stats["success_rate"] = total_stats["successful_requests"] / total_stats["total_requests"]
        else:
            total_stats["success_rate"] = 0
        
        return {
            "crawler_stats": stats,
            "total_stats": total_stats,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息异常: {str(e)}")

@router.get("/stats/{crawler_name}")
async def get_crawler_stats_by_name(
    crawler_name: str,
    current_user: User = Depends(get_current_user)
):
    """
    获取指定爬虫的统计信息
    
    返回单个爬虫的详细统计数据
    """
    try:
        crawler = crawler_manager.get_crawler(crawler_name)
        if not crawler:
            raise HTTPException(status_code=404, detail=f"爬虫 {crawler_name} 不存在")
        
        stats = crawler.get_stats()
        
        return {
            "crawler_name": crawler_name,
            "stats": stats,
            "crawler_info": {
                "name": crawler.name,
                "base_url": crawler.base_url,
                "domain": crawler.domain,
                "rate_limit": crawler.rate_limit
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取爬虫统计信息异常: {str(e)}")

@router.get("/health")
async def health_check():
    """
    健康检查
    
    检查爬虫服务是否正常运行
    """
    try:
        crawler_count = len(crawler_manager.list_crawlers())
        
        return {
            "status": "healthy",
            "registered_crawlers": crawler_count,
            "service_name": "简化爬虫服务",
            "version": "2.0.0",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"健康检查失败: {str(e)}")

@router.get("/config")
async def get_crawler_config(current_user: User = Depends(get_current_user)):
    """
    获取爬虫配置信息
    
    返回当前的爬虫配置参数
    """
    try:
        from app.services.simplified_crawler_service import SimpleCrawlerConfig
        
        return {
            "config": {
                "user_agents_count": len(SimpleCrawlerConfig.USER_AGENTS),
                "default_timeout": SimpleCrawlerConfig.DEFAULT_TIMEOUT,
                "default_rate_limit": SimpleCrawlerConfig.DEFAULT_RATE_LIMIT,
                "max_retries": SimpleCrawlerConfig.MAX_RETRIES,
                "backoff_factor": SimpleCrawlerConfig.BACKOFF_FACTOR
            },
            "features": [
                "简化的反爬虫机制",
                "智能速率限制",
                "自动重试机制",
                "统计信息收集",
                "内容去重",
                "重要性评分"
            ],
            "supported_operations": [
                "单个爬虫爬取",
                "批量爬虫爬取",
                "后台异步爬取",
                "统计信息查询",
                "健康状态检查"
            ]
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取配置信息异常: {str(e)}")

@router.post("/test/{crawler_name}")
async def test_crawler(
    crawler_name: str,
    current_user: User = Depends(get_current_user)
):
    """
    测试指定爬虫
    
    执行一个简单的测试爬取来验证爬虫是否正常工作
    """
    try:
        crawler = crawler_manager.get_crawler(crawler_name)
        if not crawler:
            raise HTTPException(status_code=404, detail=f"爬虫 {crawler_name} 不存在")
        
        # 执行测试爬取（最近1天的数据）
        test_start_date = datetime.now() - timedelta(days=1)
        test_end_date = datetime.now()
        
        async with crawler:
            # 只获取新闻列表，不获取详情
            news_list = await crawler.fetch_news_list(test_start_date, test_end_date)
            
            # 限制测试结果数量
            test_results = news_list[:5] if news_list else []
            
            return {
                "success": True,
                "crawler_name": crawler_name,
                "test_results": test_results,
                "total_found": len(news_list),
                "test_period": {
                    "start_date": test_start_date.isoformat(),
                    "end_date": test_end_date.isoformat()
                },
                "crawler_stats": crawler.get_stats(),
                "test_time": datetime.now().isoformat()
            }
        
    except HTTPException:
        raise
    except Exception as e:
        return {
            "success": False,
            "crawler_name": crawler_name,
            "error": str(e),
            "test_time": datetime.now().isoformat()
        }
