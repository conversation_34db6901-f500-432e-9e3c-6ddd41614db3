# API参考文档

## 概述

财经新闻Bot提供RESTful API接口，支持JSON格式的请求和响应。所有API都需要认证，除非特别说明。

### 基础信息

- **基础URL**: `http://localhost:8000`
- **认证方式**: Bearer <PERSON> (JWT)
- **内容类型**: `application/json`
- **字符编码**: UTF-8

### 认证

大多数API需要在请求头中包含认证令牌：

```http
Authorization: Bearer <your-jwt-token>
```

获取令牌：

```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "your-username",
  "password": "your-password"
}
```

## 核心服务API

### 1. 统一推送服务

#### 发送推送消息

```http
POST /api/v1/push/send
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "推送标题",
  "content": "推送内容",
  "mode": "basic",
  "channels": ["wechat_work", "email"],
  "targets": ["user1", "user2"],
  "message_type": "text",
  "extra_data": {}
}
```

**参数说明**：
- `title` (string, 必需): 推送标题
- `content` (string, 必需): 推送内容
- `mode` (string, 可选): 推送模式 (`basic`, `enhanced`, `layered`, `analytics`)
- `channels` (array, 可选): 推送渠道列表
- `targets` (array, 可选): 目标用户列表
- `message_type` (string, 可选): 消息类型 (`text`, `markdown`, `card`)
- `extra_data` (object, 可选): 额外数据

**响应示例**：
```json
{
  "success": true,
  "results": [
    {
      "success": true,
      "channel": "wechat_work",
      "target": "user1",
      "message": "推送成功",
      "timestamp": "2024-01-01T12:00:00Z"
    }
  ],
  "total_count": 1,
  "success_count": 1,
  "failed_count": 0
}
```

#### 获取推送统计

```http
GET /api/v1/push/stats?hours=24
Authorization: Bearer <token>
```

#### 获取推送模式

```http
GET /api/v1/push/modes
Authorization: Bearer <token>
```

### 2. AI服务

#### 文本摘要

```http
POST /api/v1/ai/summarize
Authorization: Bearer <token>
Content-Type: application/json

{
  "text": "需要摘要的长文本内容...",
  "max_length": 200
}
```

**响应示例**：
```json
{
  "success": true,
  "summary": "生成的摘要内容",
  "original_length": 1000,
  "summary_length": 150,
  "compression_ratio": 0.85
}
```

#### 内容分类

```http
POST /api/v1/ai/classify
Authorization: Bearer <token>
Content-Type: application/json

{
  "text": "需要分类的文本内容"
}
```

#### 关键词提取

```http
POST /api/v1/ai/keywords
Authorization: Bearer <token>
Content-Type: application/json

{
  "text": "需要提取关键词的文本",
  "max_keywords": 10
}
```

#### 情感分析

```http
POST /api/v1/ai/sentiment
Authorization: Bearer <token>
Content-Type: application/json

{
  "text": "需要分析情感的文本"
}
```

#### 内容安全检测

```http
POST /api/v1/ai/safety
Authorization: Bearer <token>
Content-Type: application/json

{
  "text": "需要检测的文本内容"
}
```

### 3. 数据处理管道

#### 处理文本

```http
POST /api/v1/data-processing/text
Authorization: Bearer <token>
Content-Type: application/json

{
  "text": "需要处理的文本",
  "stages": ["text_cleaning", "sentiment_analysis"]
}
```

**可用处理阶段**：
- `text_cleaning`: 文本清理
- `entity_extraction`: 实体提取
- `sentiment_analysis`: 情感分析
- `importance_scoring`: 重要性评分
- `content_classification`: 内容分类
- `duplicate_detection`: 重复检测

#### 处理新闻文章

```http
POST /api/v1/data-processing/news
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "新闻标题",
  "content": "新闻内容",
  "stages": ["text_cleaning", "entity_extraction", "sentiment_analysis"]
}
```

#### 批量处理

```http
POST /api/v1/data-processing/batch
Authorization: Bearer <token>
Content-Type: application/json

{
  "items": [
    "文本1",
    {"title": "标题", "content": "内容"},
    "文本3"
  ],
  "stages": ["text_cleaning"]
}
```

#### 获取可用阶段

```http
GET /api/v1/data-processing/stages
Authorization: Bearer <token>
```

### 4. 爬虫服务

#### 执行爬取任务

```http
POST /api/v1/crawler/crawl
Authorization: Bearer <token>
Content-Type: application/json

{
  "crawler_name": "sse_crawler",
  "start_date": "2024-01-01T00:00:00Z",
  "end_date": "2024-01-02T00:00:00Z",
  "days_back": 7
}
```

#### 后台爬取任务

```http
POST /api/v1/crawler/crawl/background
Authorization: Bearer <token>
Content-Type: application/json

{
  "crawler_name": "all",
  "days_back": 1
}
```

#### 获取爬虫列表

```http
GET /api/v1/crawler/list
Authorization: Bearer <token>
```

#### 获取爬虫统计

```http
GET /api/v1/crawler/stats
Authorization: Bearer <token>
```

#### 测试爬虫

```http
POST /api/v1/crawler/test/sse_crawler
Authorization: Bearer <token>
```

### 5. 告警管理

#### 触发告警

```http
POST /api/v1/alerts/trigger
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "告警标题",
  "message": "告警详细信息",
  "level": "warning",
  "category": "system",
  "source": "manual",
  "metadata": {"key": "value"}
}
```

**告警级别**：
- `info`: 信息
- `warning`: 警告
- `error`: 错误
- `critical`: 严重

**告警分类**：
- `system`: 系统
- `security`: 安全
- `performance`: 性能
- `business`: 业务
- `compliance`: 合规

#### 获取活跃告警

```http
GET /api/v1/alerts/active?level=warning&category=system
Authorization: Bearer <token>
```

#### 获取告警历史

```http
GET /api/v1/alerts/history?hours=24&level=error
Authorization: Bearer <token>
```

#### 解决告警

```http
POST /api/v1/alerts/resolve/{alert_id}
Authorization: Bearer <token>
```

#### 获取告警统计

```http
GET /api/v1/alerts/statistics?hours=24
Authorization: Bearer <token>
```

### 6. 集成监控服务

#### 运行所有检查

```http
POST /api/v1/monitoring/check/all
Authorization: Bearer <token>
```

#### 获取系统状态

```http
GET /api/v1/monitoring/status
Authorization: Bearer <token>
```

#### 获取告警

```http
GET /api/v1/monitoring/alerts
Authorization: Bearer <token>
```

#### 运行特定检查

```http
POST /api/v1/monitoring/check/security
POST /api/v1/monitoring/check/performance
POST /api/v1/monitoring/check/compliance
POST /api/v1/monitoring/check/health
Authorization: Bearer <token>
```

## 系统API

### 健康检查

```http
GET /health
```

**响应示例**：
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00Z",
  "version": "1.0.0",
  "uptime": 3600
}
```

### 数据库检查

```http
GET /db-check
```

### API信息

```http
GET /api/v1/info
```

## 错误处理

### 错误响应格式

```json
{
  "detail": "错误描述",
  "error_code": "ERROR_CODE",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 常见错误码

- `400 Bad Request`: 请求参数错误
- `401 Unauthorized`: 未认证或令牌无效
- `403 Forbidden`: 权限不足
- `404 Not Found`: 资源不存在
- `422 Unprocessable Entity`: 数据验证失败
- `429 Too Many Requests`: 请求频率超限
- `500 Internal Server Error`: 服务器内部错误

## 限流

API实施了速率限制：

- **默认限制**: 每分钟100次请求
- **认证用户**: 每分钟200次请求
- **管理员**: 每分钟500次请求

超出限制时返回 `429 Too Many Requests`。

## 分页

支持分页的API使用以下参数：

- `page`: 页码（从1开始）
- `size`: 每页大小（默认20，最大100）

响应包含分页信息：

```json
{
  "items": [...],
  "total": 100,
  "page": 1,
  "size": 20,
  "pages": 5
}
```

## WebSocket

系统支持WebSocket连接用于实时通知：

```javascript
const ws = new WebSocket('ws://localhost:8000/ws');
ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('收到通知:', data);
};
```

## SDK和示例

### Python示例

```python
import requests

# 登录获取令牌
response = requests.post('http://localhost:8000/api/v1/auth/login', json={
    'username': 'admin',
    'password': 'password'
})
token = response.json()['access_token']

# 使用令牌调用API
headers = {'Authorization': f'Bearer {token}'}
response = requests.post('http://localhost:8000/api/v1/push/send', 
    headers=headers,
    json={
        'title': '测试推送',
        'content': '这是一条测试消息'
    }
)
print(response.json())
```

### JavaScript示例

```javascript
// 登录获取令牌
const loginResponse = await fetch('http://localhost:8000/api/v1/auth/login', {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    body: JSON.stringify({
        username: 'admin',
        password: 'password'
    })
});
const {access_token} = await loginResponse.json();

// 使用令牌调用API
const response = await fetch('http://localhost:8000/api/v1/push/send', {
    method: 'POST',
    headers: {
        'Authorization': `Bearer ${access_token}`,
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        title: '测试推送',
        content: '这是一条测试消息'
    })
});
const result = await response.json();
console.log(result);
```

---

更多详细信息请参考：
- [部署指南](DEPLOYMENT.md)
- [开发指南](DEVELOPMENT.md)
- [故障排除](TROUBLESHOOTING.md)
