"""
订阅API路由（权限控制版本）
提供订阅管理功能，集成完整的权限控制
"""
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from typing import Optional, List

from ..database import get_db
from ..dependencies.auth import get_current_active_user
from ..dependencies.permissions import (
    require_basic_subscription,
    require_advanced_subscription,
    require_admin,
    require_bulk_operations
)
from ..models.user import User
from ..schemas.subscription import (
    SubscriptionCreate, SubscriptionUpdate, SubscriptionResponse,
    SubscriptionListResponse, SubscriptionStats, SubscriptionStatus
)
from ..services.subscription_service import SubscriptionService

router = APIRouter(prefix="/subscriptions", tags=["订阅管理"])


@router.post("/basic", response_model=SubscriptionResponse, status_code=status.HTTP_201_CREATED)
def create_basic_subscription(
    subscription_data: SubscriptionCreate,
    current_user: User = Depends(require_basic_subscription()),
    db: Session = Depends(get_db)
):
    """
    创建基础订阅（所有用户角色可用）
    
    基础订阅功能：
    - 最多3个关键词
    - 最多5个公司关注
    - 基础推送渠道
    """
    try:
        service = SubscriptionService(db)
        
        # 基础订阅限制
        if len(subscription_data.keywords or []) > 3:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="基础订阅最多支持3个关键词"
            )
        
        if len(subscription_data.companies or []) > 5:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="基础订阅最多支持5个公司关注"
            )
        
        return service.create_subscription(current_user.id, subscription_data, subscription_type="basic")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建基础订阅失败: {str(e)}"
        )


@router.post("/advanced", response_model=SubscriptionResponse, status_code=status.HTTP_201_CREATED)
def create_advanced_subscription(
    subscription_data: SubscriptionCreate,
    current_user: User = Depends(require_advanced_subscription()),
    db: Session = Depends(get_db)
):
    """
    创建高级订阅（PRO、ENTERPRISE、ADMIN角色可用）
    
    高级订阅功能：
    - 无限关键词
    - 无限公司关注
    - 高级推送渠道
    - 自定义推送规则
    - 智能推荐
    """
    try:
        service = SubscriptionService(db)
        return service.create_subscription(current_user.id, subscription_data, subscription_type="advanced")
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建高级订阅失败: {str(e)}"
        )


@router.get("/", response_model=SubscriptionListResponse)
def get_user_subscriptions(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    status: Optional[SubscriptionStatus] = Query(None, description="订阅状态过滤"),
    current_user: User = Depends(require_basic_subscription()),
    db: Session = Depends(get_db)
):
    """
    获取当前用户的订阅列表（需要basic_subscription权限）
    """
    try:
        service = SubscriptionService(db)
        return service.get_user_subscriptions(
            user_id=current_user.id,
            page=page,
            size=size,
            status=status
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取订阅列表失败: {str(e)}"
        )


@router.get("/{subscription_id}", response_model=SubscriptionResponse)
def get_subscription_detail(
    subscription_id: int,
    current_user: User = Depends(require_basic_subscription()),
    db: Session = Depends(get_db)
):
    """
    获取订阅详情（需要basic_subscription权限）
    """
    try:
        service = SubscriptionService(db)
        subscription = service.get_subscription_by_id(subscription_id)
        
        if not subscription:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="订阅不存在"
            )
        
        # 检查订阅所有权
        if subscription.user_id != current_user.id and current_user.role.value != "ADMIN":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此订阅"
            )
        
        return subscription
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取订阅详情失败: {str(e)}"
        )


@router.put("/{subscription_id}", response_model=SubscriptionResponse)
def update_subscription(
    subscription_id: int,
    subscription_data: SubscriptionUpdate,
    current_user: User = Depends(require_basic_subscription()),
    db: Session = Depends(get_db)
):
    """
    更新订阅（需要basic_subscription权限）
    """
    try:
        service = SubscriptionService(db)
        subscription = service.get_subscription_by_id(subscription_id)
        
        if not subscription:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="订阅不存在"
            )
        
        # 检查订阅所有权
        if subscription.user_id != current_user.id and current_user.role.value != "ADMIN":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权修改此订阅"
            )
        
        return service.update_subscription(subscription_id, subscription_data)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新订阅失败: {str(e)}"
        )


@router.delete("/{subscription_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_subscription(
    subscription_id: int,
    current_user: User = Depends(require_basic_subscription()),
    db: Session = Depends(get_db)
):
    """
    删除订阅（需要basic_subscription权限）
    """
    try:
        service = SubscriptionService(db)
        subscription = service.get_subscription_by_id(subscription_id)
        
        if not subscription:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="订阅不存在"
            )
        
        # 检查订阅所有权
        if subscription.user_id != current_user.id and current_user.role.value != "ADMIN":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权删除此订阅"
            )
        
        service.delete_subscription(subscription_id)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除订阅失败: {str(e)}"
        )


@router.post("/bulk-create", response_model=List[SubscriptionResponse])
def bulk_create_subscriptions(
    subscriptions_data: List[SubscriptionCreate],
    current_user: User = Depends(require_bulk_operations()),
    db: Session = Depends(get_db)
):
    """
    批量创建订阅（需要bulk_operations权限）
    ENTERPRISE、ADMIN角色可用
    """
    try:
        service = SubscriptionService(db)
        results = []
        
        for subscription_data in subscriptions_data:
            subscription = service.create_subscription(
                current_user.id, 
                subscription_data, 
                subscription_type="advanced"
            )
            results.append(subscription)
        
        return results
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量创建订阅失败: {str(e)}"
        )


@router.get("/stats/summary", response_model=SubscriptionStats)
def get_subscription_stats(
    current_user: User = Depends(require_basic_subscription()),
    db: Session = Depends(get_db)
):
    """
    获取订阅统计信息（需要basic_subscription权限）
    """
    try:
        service = SubscriptionService(db)
        return service.get_user_subscription_stats(current_user.id)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取订阅统计失败: {str(e)}"
        )


# 管理员专用API
@router.get("/admin/all", response_model=List[SubscriptionResponse])
def get_all_subscriptions(
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(100, ge=1, le=1000, description="每页数量"),
    current_user: User = Depends(require_admin()),
    db: Session = Depends(get_db)
):
    """
    获取所有订阅（管理员权限）
    """
    try:
        service = SubscriptionService(db)
        return service.get_all_subscriptions(skip=skip, limit=limit)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取所有订阅失败: {str(e)}"
        )


@router.get("/admin/stats", response_model=dict)
def get_admin_subscription_stats(
    current_user: User = Depends(require_admin()),
    db: Session = Depends(get_db)
):
    """
    获取订阅系统统计信息（管理员权限）
    """
    try:
        service = SubscriptionService(db)
        return service.get_admin_stats()
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取管理员统计失败: {str(e)}"
        )
