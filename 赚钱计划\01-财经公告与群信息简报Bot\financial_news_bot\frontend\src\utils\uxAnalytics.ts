/**
 * 用户体验分析工具
 * 分析用户任务完成率、耗时数据、流失节点等，识别用户体验痛点
 */

import { userAnalytics, AnalyticsEvent } from './userAnalytics';

// 用户任务定义
export interface UserTask {
  id: string;
  name: string;
  description: string;
  steps: TaskStep[];
  expectedDuration: number; // 预期完成时间（秒）
  criticalPath: string[]; // 关键路径事件
}

// 任务步骤
export interface TaskStep {
  id: string;
  name: string;
  description: string;
  expectedDuration: number;
  requiredEvents: string[]; // 必需的事件类型
  optionalEvents?: string[]; // 可选的事件类型
}

// 任务完成数据
export interface TaskCompletion {
  taskId: string;
  userId: string;
  sessionId: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  completed: boolean;
  abandonedAt?: string; // 放弃的步骤
  steps: StepCompletion[];
  errors: string[];
  retries: number;
}

// 步骤完成数据
export interface StepCompletion {
  stepId: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  completed: boolean;
  attempts: number;
  errors: string[];
}

// 用户体验痛点
export interface UXPainPoint {
  id: string;
  type: 'high_abandonment' | 'long_duration' | 'high_error_rate' | 'low_satisfaction';
  severity: 'low' | 'medium' | 'high' | 'critical';
  location: string; // 页面或功能位置
  description: string;
  affectedUsers: number;
  impactScore: number; // 影响分数 1-100
  evidence: {
    metrics: Record<string, number>;
    userFeedback: string[];
    screenshots?: string[];
  };
  recommendations: string[];
}

// 改进机会
export interface ImprovementOpportunity {
  id: string;
  title: string;
  description: string;
  category: 'usability' | 'performance' | 'accessibility' | 'content' | 'design';
  priority: 'low' | 'medium' | 'high' | 'critical';
  effort: 'small' | 'medium' | 'large';
  impact: 'low' | 'medium' | 'high';
  roi: number; // 投资回报率估算
  relatedPainPoints: string[];
  proposedSolution: string;
  acceptanceCriteria: string[];
  estimatedDuration: number; // 开发时间（天）
}

class UXAnalyzer {
  private tasks: Map<string, UserTask> = new Map();
  private completions: TaskCompletion[] = [];
  private painPoints: UXPainPoint[] = [];
  private opportunities: ImprovementOpportunity[] = [];

  constructor() {
    this.initializeTasks();
  }

  private initializeTasks() {
    // 定义核心用户任务
    const tasks: UserTask[] = [
      {
        id: 'user_registration',
        name: '用户注册',
        description: '新用户完成账户注册流程',
        expectedDuration: 180, // 3分钟
        criticalPath: ['page_view:/auth/register', 'form_submit', 'page_view:/dashboard'],
        steps: [
          {
            id: 'visit_register',
            name: '访问注册页面',
            description: '用户访问注册页面',
            expectedDuration: 10,
            requiredEvents: ['page_view:/auth/register'],
          },
          {
            id: 'fill_form',
            name: '填写注册表单',
            description: '用户填写注册信息',
            expectedDuration: 120,
            requiredEvents: ['input', 'form_validation'],
            optionalEvents: ['form_error'],
          },
          {
            id: 'submit_registration',
            name: '提交注册',
            description: '用户提交注册表单',
            expectedDuration: 30,
            requiredEvents: ['form_submit'],
          },
          {
            id: 'verify_email',
            name: '邮箱验证',
            description: '用户完成邮箱验证',
            expectedDuration: 20,
            requiredEvents: ['email_verification'],
          },
        ],
      },
      {
        id: 'create_subscription',
        name: '创建订阅',
        description: '用户创建第一个新闻订阅',
        expectedDuration: 300, // 5分钟
        criticalPath: ['page_view:/subscriptions/create', 'form_submit', 'subscription_created'],
        steps: [
          {
            id: 'access_subscription',
            name: '进入订阅页面',
            description: '用户访问订阅创建页面',
            expectedDuration: 15,
            requiredEvents: ['page_view:/subscriptions/create'],
          },
          {
            id: 'configure_subscription',
            name: '配置订阅',
            description: '用户配置订阅参数',
            expectedDuration: 240,
            requiredEvents: ['input', 'keyword_selection', 'channel_config'],
          },
          {
            id: 'test_subscription',
            name: '测试订阅',
            description: '用户测试订阅配置',
            expectedDuration: 30,
            requiredEvents: ['test_push'],
            optionalEvents: ['test_error'],
          },
          {
            id: 'save_subscription',
            name: '保存订阅',
            description: '用户保存订阅配置',
            expectedDuration: 15,
            requiredEvents: ['form_submit', 'subscription_created'],
          },
        ],
      },
      {
        id: 'browse_news',
        name: '浏览新闻',
        description: '用户浏览和搜索新闻内容',
        expectedDuration: 600, // 10分钟
        criticalPath: ['page_view:/news', 'search', 'news_click'],
        steps: [
          {
            id: 'visit_news',
            name: '访问新闻页面',
            description: '用户访问新闻中心',
            expectedDuration: 10,
            requiredEvents: ['page_view:/news'],
          },
          {
            id: 'browse_list',
            name: '浏览新闻列表',
            description: '用户浏览新闻列表',
            expectedDuration: 180,
            requiredEvents: ['scroll', 'news_view'],
          },
          {
            id: 'search_news',
            name: '搜索新闻',
            description: '用户搜索特定新闻',
            expectedDuration: 60,
            requiredEvents: ['search'],
            optionalEvents: ['search_filter'],
          },
          {
            id: 'read_news',
            name: '阅读新闻详情',
            description: '用户点击查看新闻详情',
            expectedDuration: 300,
            requiredEvents: ['news_click', 'page_view:/news/:id'],
          },
          {
            id: 'interact_news',
            name: '新闻互动',
            description: '用户进行点赞、收藏等操作',
            expectedDuration: 50,
            requiredEvents: [],
            optionalEvents: ['like', 'bookmark', 'share'],
          },
        ],
      },
    ];

    tasks.forEach(task => this.tasks.set(task.id, task));
  }

  // 分析用户任务完成情况
  public analyzeTaskCompletions(events: AnalyticsEvent[]): TaskCompletion[] {
    const sessionTasks = new Map<string, Map<string, TaskCompletion>>();

    // 按会话和任务分组事件
    events.forEach(event => {
      const sessionId = event.sessionId;
      if (!sessionTasks.has(sessionId)) {
        sessionTasks.set(sessionId, new Map());
      }

      const sessionMap = sessionTasks.get(sessionId)!;
      
      // 检查事件是否匹配任务
      this.tasks.forEach((task, taskId) => {
        if (this.isEventRelevantToTask(event, task)) {
          if (!sessionMap.has(taskId)) {
            sessionMap.set(taskId, {
              taskId,
              userId: event.userId || 'anonymous',
              sessionId,
              startTime: event.timestamp,
              completed: false,
              steps: [],
              errors: [],
              retries: 0,
            });
          }

          const completion = sessionMap.get(taskId)!;
          this.updateTaskCompletion(completion, event, task);
        }
      });
    });

    // 收集所有任务完成数据
    const completions: TaskCompletion[] = [];
    sessionTasks.forEach(taskMap => {
      taskMap.forEach(completion => {
        completions.push(completion);
      });
    });

    this.completions = completions;
    return completions;
  }

  private isEventRelevantToTask(event: AnalyticsEvent, task: UserTask): boolean {
    // 检查事件是否与任务相关
    return task.criticalPath.some(path => {
      if (path.includes(':')) {
        const [eventType, url] = path.split(':');
        return event.type === eventType && event.url.includes(url);
      }
      return event.action === path || event.type === path;
    });
  }

  private updateTaskCompletion(completion: TaskCompletion, event: AnalyticsEvent, task: UserTask) {
    // 更新任务完成状态
    completion.endTime = event.timestamp;
    completion.duration = completion.endTime - completion.startTime;

    // 检查是否完成了关键路径
    const completedEvents = new Set<string>();
    // 这里需要更复杂的逻辑来跟踪步骤完成情况
    // 简化实现：如果包含所有关键路径事件，则认为完成
    if (task.criticalPath.every(path => completedEvents.has(path))) {
      completion.completed = true;
    }
  }

  // 识别用户体验痛点
  public identifyPainPoints(): UXPainPoint[] {
    const painPoints: UXPainPoint[] = [];

    // 分析任务放弃率
    const abandonmentPainPoints = this.analyzeAbandonmentRates();
    painPoints.push(...abandonmentPainPoints);

    // 分析任务耗时
    const durationPainPoints = this.analyzeDurationIssues();
    painPoints.push(...durationPainPoints);

    // 分析错误率
    const errorPainPoints = this.analyzeErrorRates();
    painPoints.push(...errorPainPoints);

    this.painPoints = painPoints;
    return painPoints;
  }

  private analyzeAbandonmentRates(): UXPainPoint[] {
    const painPoints: UXPainPoint[] = [];
    
    this.tasks.forEach((task, taskId) => {
      const taskCompletions = this.completions.filter(c => c.taskId === taskId);
      if (taskCompletions.length === 0) return;

      const completionRate = taskCompletions.filter(c => c.completed).length / taskCompletions.length;
      
      if (completionRate < 0.7) { // 完成率低于70%
        painPoints.push({
          id: `abandonment_${taskId}`,
          type: 'high_abandonment',
          severity: completionRate < 0.5 ? 'critical' : 'high',
          location: task.name,
          description: `${task.name}任务完成率过低 (${(completionRate * 100).toFixed(1)}%)`,
          affectedUsers: taskCompletions.length,
          impactScore: Math.round((1 - completionRate) * 100),
          evidence: {
            metrics: {
              completionRate,
              totalAttempts: taskCompletions.length,
              completedTasks: taskCompletions.filter(c => c.completed).length,
            },
            userFeedback: [],
          },
          recommendations: [
            '简化任务流程',
            '改进用户引导',
            '优化表单设计',
            '添加进度指示器',
          ],
        });
      }
    });

    return painPoints;
  }

  private analyzeDurationIssues(): UXPainPoint[] {
    const painPoints: UXPainPoint[] = [];
    
    this.tasks.forEach((task, taskId) => {
      const completedTasks = this.completions.filter(c => c.taskId === taskId && c.completed && c.duration);
      if (completedTasks.length === 0) return;

      const avgDuration = completedTasks.reduce((sum, c) => sum + (c.duration || 0), 0) / completedTasks.length;
      const expectedDuration = task.expectedDuration * 1000; // 转换为毫秒

      if (avgDuration > expectedDuration * 1.5) { // 超过预期时间50%
        painPoints.push({
          id: `duration_${taskId}`,
          type: 'long_duration',
          severity: avgDuration > expectedDuration * 2 ? 'high' : 'medium',
          location: task.name,
          description: `${task.name}任务耗时过长 (平均${(avgDuration / 1000).toFixed(1)}秒，预期${task.expectedDuration}秒)`,
          affectedUsers: completedTasks.length,
          impactScore: Math.round((avgDuration / expectedDuration - 1) * 50),
          evidence: {
            metrics: {
              avgDuration: avgDuration / 1000,
              expectedDuration: task.expectedDuration,
              slowestTask: Math.max(...completedTasks.map(c => c.duration || 0)) / 1000,
            },
            userFeedback: [],
          },
          recommendations: [
            '优化页面加载速度',
            '简化操作步骤',
            '改进界面布局',
            '添加快捷操作',
          ],
        });
      }
    });

    return painPoints;
  }

  private analyzeErrorRates(): UXPainPoint[] {
    const painPoints: UXPainPoint[] = [];
    
    this.tasks.forEach((task, taskId) => {
      const taskCompletions = this.completions.filter(c => c.taskId === taskId);
      if (taskCompletions.length === 0) return;

      const totalErrors = taskCompletions.reduce((sum, c) => sum + c.errors.length, 0);
      const errorRate = totalErrors / taskCompletions.length;

      if (errorRate > 1) { // 平均每个任务超过1个错误
        painPoints.push({
          id: `error_${taskId}`,
          type: 'high_error_rate',
          severity: errorRate > 3 ? 'high' : 'medium',
          location: task.name,
          description: `${task.name}任务错误率过高 (平均${errorRate.toFixed(1)}个错误/任务)`,
          affectedUsers: taskCompletions.length,
          impactScore: Math.round(errorRate * 20),
          evidence: {
            metrics: {
              errorRate,
              totalErrors,
              totalTasks: taskCompletions.length,
            },
            userFeedback: [],
          },
          recommendations: [
            '改进表单验证',
            '优化错误提示',
            '增强用户引导',
            '修复常见错误',
          ],
        });
      }
    });

    return painPoints;
  }

  // 生成改进机会
  public generateImprovementOpportunities(): ImprovementOpportunity[] {
    const opportunities: ImprovementOpportunity[] = [];

    this.painPoints.forEach(painPoint => {
      const opportunity = this.createOpportunityFromPainPoint(painPoint);
      if (opportunity) {
        opportunities.push(opportunity);
      }
    });

    // 添加主动改进机会
    const proactiveOpportunities = this.generateProactiveOpportunities();
    opportunities.push(...proactiveOpportunities);

    this.opportunities = opportunities;
    return opportunities;
  }

  private createOpportunityFromPainPoint(painPoint: UXPainPoint): ImprovementOpportunity | null {
    const baseOpportunity = {
      id: `improve_${painPoint.id}`,
      relatedPainPoints: [painPoint.id],
      category: 'usability' as const,
      priority: painPoint.severity === 'critical' ? 'critical' as const : 'high' as const,
    };

    switch (painPoint.type) {
      case 'high_abandonment':
        return {
          ...baseOpportunity,
          title: '降低任务放弃率',
          description: `优化${painPoint.location}的用户流程，提高任务完成率`,
          effort: 'medium' as const,
          impact: 'high' as const,
          roi: 85,
          proposedSolution: '重新设计用户流程，简化操作步骤，添加进度指示和帮助提示',
          acceptanceCriteria: [
            '任务完成率提升至80%以上',
            '用户满意度评分提升0.5分',
            '平均任务时间减少20%',
          ],
          estimatedDuration: 10,
        };

      case 'long_duration':
        return {
          ...baseOpportunity,
          title: '优化任务执行效率',
          description: `减少${painPoint.location}的执行时间，提升用户体验`,
          effort: 'medium' as const,
          impact: 'medium' as const,
          roi: 70,
          proposedSolution: '优化页面性能，简化操作流程，添加快捷操作选项',
          acceptanceCriteria: [
            '平均任务时间减少30%',
            '页面加载时间小于2秒',
            '用户操作步骤减少20%',
          ],
          estimatedDuration: 8,
        };

      case 'high_error_rate':
        return {
          ...baseOpportunity,
          title: '减少用户操作错误',
          description: `改进${painPoint.location}的错误处理和用户引导`,
          effort: 'small' as const,
          impact: 'medium' as const,
          roi: 60,
          proposedSolution: '改进表单验证，优化错误提示，增强用户引导',
          acceptanceCriteria: [
            '错误率降低50%',
            '错误恢复时间减少40%',
            '用户帮助请求减少30%',
          ],
          estimatedDuration: 5,
        };

      default:
        return null;
    }
  }

  private generateProactiveOpportunities(): ImprovementOpportunity[] {
    return [
      {
        id: 'mobile_optimization',
        title: '移动端体验优化',
        description: '全面优化移动端用户体验，提升移动用户满意度',
        category: 'usability',
        priority: 'high',
        effort: 'large',
        impact: 'high',
        roi: 90,
        relatedPainPoints: [],
        proposedSolution: '重新设计移动端界面，优化触摸交互，改进响应式布局',
        acceptanceCriteria: [
          '移动端任务完成率提升25%',
          '移动端用户留存率提升20%',
          '移动端页面加载速度提升30%',
        ],
        estimatedDuration: 15,
      },
      {
        id: 'personalization',
        title: '个性化推荐优化',
        description: '基于用户行为数据优化内容推荐算法',
        category: 'content',
        priority: 'medium',
        effort: 'large',
        impact: 'high',
        roi: 80,
        relatedPainPoints: [],
        proposedSolution: '实现基于机器学习的个性化推荐系统',
        acceptanceCriteria: [
          '用户点击率提升40%',
          '用户停留时间增加30%',
          '订阅转化率提升25%',
        ],
        estimatedDuration: 20,
      },
    ];
  }

  // 生成UX分析报告
  public generateUXReport(): {
    summary: any;
    painPoints: UXPainPoint[];
    opportunities: ImprovementOpportunity[];
    recommendations: string[];
  } {
    const completedTasks = this.completions.filter(c => c.completed);
    const totalTasks = this.completions.length;
    const overallCompletionRate = totalTasks > 0 ? completedTasks.length / totalTasks : 0;

    const avgDuration = completedTasks.length > 0 
      ? completedTasks.reduce((sum, c) => sum + (c.duration || 0), 0) / completedTasks.length / 1000
      : 0;

    const totalErrors = this.completions.reduce((sum, c) => sum + c.errors.length, 0);
    const avgErrorRate = totalTasks > 0 ? totalErrors / totalTasks : 0;

    const criticalPainPoints = this.painPoints.filter(p => p.severity === 'critical').length;
    const highPriorityOpportunities = this.opportunities.filter(o => o.priority === 'critical' || o.priority === 'high').length;

    return {
      summary: {
        totalTasks,
        completedTasks: completedTasks.length,
        overallCompletionRate,
        avgDuration,
        avgErrorRate,
        criticalPainPoints,
        highPriorityOpportunities,
      },
      painPoints: this.painPoints,
      opportunities: this.opportunities,
      recommendations: this.generateRecommendations(),
    };
  }

  private generateRecommendations(): string[] {
    const recommendations: string[] = [];

    if (this.painPoints.some(p => p.type === 'high_abandonment')) {
      recommendations.push('优先解决高放弃率问题，重新设计关键用户流程');
    }

    if (this.painPoints.some(p => p.type === 'long_duration')) {
      recommendations.push('优化页面性能和操作流程，减少用户等待时间');
    }

    if (this.painPoints.some(p => p.type === 'high_error_rate')) {
      recommendations.push('改进表单验证和错误处理，提升操作成功率');
    }

    recommendations.push('建立持续的用户体验监控机制');
    recommendations.push('定期进行A/B测试验证改进效果');
    recommendations.push('收集更多用户定性反馈补充数据分析');

    return recommendations;
  }

  // 获取痛点
  public getPainPoints(): UXPainPoint[] {
    return this.painPoints;
  }

  // 获取改进机会
  public getOpportunities(): ImprovementOpportunity[] {
    return this.opportunities;
  }
}

// 全局UX分析器实例
export const uxAnalyzer = new UXAnalyzer();

// 便捷方法
export const analyzeUserExperience = (events: AnalyticsEvent[]) => {
  const completions = uxAnalyzer.analyzeTaskCompletions(events);
  const painPoints = uxAnalyzer.identifyPainPoints();
  const opportunities = uxAnalyzer.generateImprovementOpportunities();
  
  return uxAnalyzer.generateUXReport();
};
