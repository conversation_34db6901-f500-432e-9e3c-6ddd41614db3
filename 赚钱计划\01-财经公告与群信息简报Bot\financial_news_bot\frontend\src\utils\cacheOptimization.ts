// 缓存优化工具
interface CacheConfig {
  maxAge: number; // 缓存时间（毫秒）
  maxSize: number; // 最大缓存条目数
  strategy: 'LRU' | 'LFU' | 'FIFO'; // 缓存策略
}

interface CacheItem<T> {
  data: T;
  timestamp: number;
  accessCount: number;
  lastAccessed: number;
}

// 内存缓存管理器
class MemoryCache<T = any> {
  private cache = new Map<string, CacheItem<T>>();
  private config: CacheConfig;

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      maxAge: 5 * 60 * 1000, // 默认5分钟
      maxSize: 100, // 默认100条
      strategy: 'LRU',
      ...config,
    };
  }

  set(key: string, data: T): void {
    const now = Date.now();
    
    // 如果缓存已满，根据策略清理
    if (this.cache.size >= this.config.maxSize) {
      this.evict();
    }

    this.cache.set(key, {
      data,
      timestamp: now,
      accessCount: 0,
      lastAccessed: now,
    });
  }

  get(key: string): T | null {
    const item = this.cache.get(key);
    
    if (!item) {
      return null;
    }

    // 检查是否过期
    if (Date.now() - item.timestamp > this.config.maxAge) {
      this.cache.delete(key);
      return null;
    }

    // 更新访问信息
    item.accessCount++;
    item.lastAccessed = Date.now();

    return item.data;
  }

  has(key: string): boolean {
    return this.get(key) !== null;
  }

  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }

  // 根据策略清理缓存
  private evict(): void {
    if (this.cache.size === 0) return;

    let keyToDelete: string;

    switch (this.config.strategy) {
      case 'LRU': // 最近最少使用
        keyToDelete = this.findLRUKey();
        break;
      case 'LFU': // 最少使用频率
        keyToDelete = this.findLFUKey();
        break;
      case 'FIFO': // 先进先出
        keyToDelete = this.findFIFOKey();
        break;
      default:
        keyToDelete = this.cache.keys().next().value;
    }

    this.cache.delete(keyToDelete);
  }

  private findLRUKey(): string {
    let oldestKey = '';
    let oldestTime = Date.now();

    for (const [key, item] of this.cache) {
      if (item.lastAccessed < oldestTime) {
        oldestTime = item.lastAccessed;
        oldestKey = key;
      }
    }

    return oldestKey;
  }

  private findLFUKey(): string {
    let leastUsedKey = '';
    let leastCount = Infinity;

    for (const [key, item] of this.cache) {
      if (item.accessCount < leastCount) {
        leastCount = item.accessCount;
        leastUsedKey = key;
      }
    }

    return leastUsedKey;
  }

  private findFIFOKey(): string {
    let oldestKey = '';
    let oldestTime = Date.now();

    for (const [key, item] of this.cache) {
      if (item.timestamp < oldestTime) {
        oldestTime = item.timestamp;
        oldestKey = key;
      }
    }

    return oldestKey;
  }

  // 获取缓存统计信息
  getStats() {
    const items = Array.from(this.cache.values());
    const now = Date.now();
    
    return {
      size: this.cache.size,
      maxSize: this.config.maxSize,
      hitRate: this.calculateHitRate(),
      averageAge: items.reduce((sum, item) => sum + (now - item.timestamp), 0) / items.length || 0,
      totalAccesses: items.reduce((sum, item) => sum + item.accessCount, 0),
    };
  }

  private calculateHitRate(): number {
    // 简化的命中率计算，实际应用中需要更复杂的统计
    const items = Array.from(this.cache.values());
    const totalAccesses = items.reduce((sum, item) => sum + item.accessCount, 0);
    return totalAccesses > 0 ? (totalAccesses / (totalAccesses + this.cache.size)) : 0;
  }
}

// API响应缓存
class APICache {
  private cache = new MemoryCache<any>();
  private pendingRequests = new Map<string, Promise<any>>();

  constructor(config?: Partial<CacheConfig>) {
    this.cache = new MemoryCache(config);
  }

  async get<T>(key: string, fetcher: () => Promise<T>): Promise<T> {
    // 检查缓存
    const cached = this.cache.get(key);
    if (cached) {
      return cached;
    }

    // 检查是否有正在进行的请求
    const pending = this.pendingRequests.get(key);
    if (pending) {
      return pending;
    }

    // 发起新请求
    const promise = fetcher().then(data => {
      this.cache.set(key, data);
      this.pendingRequests.delete(key);
      return data;
    }).catch(error => {
      this.pendingRequests.delete(key);
      throw error;
    });

    this.pendingRequests.set(key, promise);
    return promise;
  }

  invalidate(pattern?: string): void {
    if (!pattern) {
      this.cache.clear();
      return;
    }

    // 支持通配符模式
    const regex = new RegExp(pattern.replace(/\*/g, '.*'));
    const keysToDelete: string[] = [];

    for (const [key] of this.cache['cache']) {
      if (regex.test(key)) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key));
  }

  getStats() {
    return this.cache.getStats();
  }
}

// 本地存储缓存
class LocalStorageCache {
  private prefix: string;
  private maxAge: number;

  constructor(prefix = 'app_cache_', maxAge = 24 * 60 * 60 * 1000) {
    this.prefix = prefix;
    this.maxAge = maxAge;
  }

  set(key: string, data: any): void {
    try {
      const item = {
        data,
        timestamp: Date.now(),
      };
      localStorage.setItem(this.prefix + key, JSON.stringify(item));
    } catch (error) {
      console.warn('Failed to set localStorage cache:', error);
    }
  }

  get<T>(key: string): T | null {
    try {
      const item = localStorage.getItem(this.prefix + key);
      if (!item) return null;

      const parsed = JSON.parse(item);
      
      // 检查是否过期
      if (Date.now() - parsed.timestamp > this.maxAge) {
        this.delete(key);
        return null;
      }

      return parsed.data;
    } catch (error) {
      console.warn('Failed to get localStorage cache:', error);
      return null;
    }
  }

  delete(key: string): void {
    localStorage.removeItem(this.prefix + key);
  }

  clear(): void {
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
      if (key.startsWith(this.prefix)) {
        localStorage.removeItem(key);
      }
    });
  }

  // 清理过期缓存
  cleanup(): void {
    const keys = Object.keys(localStorage);
    const now = Date.now();

    keys.forEach(key => {
      if (key.startsWith(this.prefix)) {
        try {
          const item = JSON.parse(localStorage.getItem(key) || '');
          if (now - item.timestamp > this.maxAge) {
            localStorage.removeItem(key);
          }
        } catch (error) {
          // 删除无效的缓存项
          localStorage.removeItem(key);
        }
      }
    });
  }
}

// 缓存管理器单例
export const cacheManager = {
  // API缓存（5分钟）
  api: new APICache({ maxAge: 5 * 60 * 1000, maxSize: 50 }),
  
  // 用户数据缓存（30分钟）
  user: new APICache({ maxAge: 30 * 60 * 1000, maxSize: 20 }),
  
  // 静态数据缓存（1小时）
  static: new APICache({ maxAge: 60 * 60 * 1000, maxSize: 100 }),
  
  // 本地存储缓存（24小时）
  localStorage: new LocalStorageCache(),
};

// 缓存装饰器
export function cached(cacheKey: string, cache: APICache = cacheManager.api) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = function (...args: any[]) {
      const key = `${cacheKey}_${JSON.stringify(args)}`;
      return cache.get(key, () => method.apply(this, args));
    };

    return descriptor;
  };
}

// 定期清理过期缓存
setInterval(() => {
  cacheManager.localStorage.cleanup();
}, 60 * 60 * 1000); // 每小时清理一次

export { MemoryCache, APICache, LocalStorageCache };
