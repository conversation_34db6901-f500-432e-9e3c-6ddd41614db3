#!/usr/bin/env python3
"""
访问权限控制测试
测试用户角色权限隔离、API接口权限验证、管理后台访问控制、敏感操作二次验证、会话超时和强制登出
"""
import requests
import time
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import argparse

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AccessControlTester:
    """访问权限控制测试器"""
    
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.test_users = {
            'admin': {
                'email': '<EMAIL>',
                'password': 'AdminPass123!',
                'username': 'admin_tester',
                'role': 'admin'
            },
            'user': {
                'email': '<EMAIL>',
                'password': 'UserPass123!',
                'username': 'user_tester',
                'role': 'user'
            },
            'guest': {
                'email': '<EMAIL>',
                'password': 'GuestPass123!',
                'username': 'guest_tester',
                'role': 'guest'
            }
        }
        self.tokens = {}
        self.test_results = []
    
    def setup_test_users(self) -> Dict[str, Any]:
        """设置测试用户"""
        logger.info("设置测试用户...")
        
        results = {
            'test_name': '测试用户设置',
            'timestamp': datetime.now().isoformat(),
            'users_created': [],
            'errors': []
        }
        
        for role, user_data in self.test_users.items():
            try:
                # 尝试注册用户
                response = requests.post(
                    f"{self.base_url}/api/v1/auth/register",
                    json=user_data,
                    timeout=10
                )
                
                if response.status_code in [200, 201]:
                    results['users_created'].append(role)
                    logger.info(f"用户 {role} 创建成功")
                elif response.status_code == 409:
                    results['users_created'].append(f"{role} (已存在)")
                    logger.info(f"用户 {role} 已存在")
                else:
                    results['errors'].append(f"{role}: HTTP {response.status_code}")
                    logger.warning(f"用户 {role} 创建失败: {response.status_code}")
                
                # 尝试登录获取token
                login_response = requests.post(
                    f"{self.base_url}/api/v1/auth/login",
                    json={
                        'email': user_data['email'],
                        'password': user_data['password']
                    },
                    timeout=10
                )
                
                if login_response.status_code == 200:
                    token_data = login_response.json()
                    self.tokens[role] = token_data.get('access_token')
                    logger.info(f"用户 {role} 登录成功")
                else:
                    results['errors'].append(f"{role} 登录失败: HTTP {login_response.status_code}")
                    logger.warning(f"用户 {role} 登录失败: {login_response.status_code}")
                
            except Exception as e:
                results['errors'].append(f"{role}: {str(e)}")
                logger.error(f"设置用户 {role} 时出错: {e}")
        
        return results
    
    def test_role_based_access(self) -> Dict[str, Any]:
        """测试基于角色的访问控制"""
        logger.info("测试基于角色的访问控制...")
        
        results = {
            'test_name': '角色权限隔离测试',
            'timestamp': datetime.now().isoformat(),
            'tests': []
        }
        
        # 定义不同角色应该能访问的端点
        role_permissions = {
            'admin': {
                'allowed': [
                    '/api/v1/admin/dashboard',
                    '/api/v1/admin/users',
                    '/api/v1/admin/crawl/rss',
                    '/api/v1/news/',
                    '/api/v1/subscriptions/',
                    '/api/v1/auth/me'
                ],
                'forbidden': []
            },
            'user': {
                'allowed': [
                    '/api/v1/news/',
                    '/api/v1/subscriptions/',
                    '/api/v1/auth/me',
                    '/api/v1/bookmarks/'
                ],
                'forbidden': [
                    '/api/v1/admin/dashboard',
                    '/api/v1/admin/users',
                    '/api/v1/admin/crawl/rss'
                ]
            },
            'guest': {
                'allowed': [
                    '/api/v1/news/',
                    '/api/v1/news/categories'
                ],
                'forbidden': [
                    '/api/v1/admin/dashboard',
                    '/api/v1/admin/users',
                    '/api/v1/subscriptions/',
                    '/api/v1/auth/me',
                    '/api/v1/bookmarks/'
                ]
            }
        }
        
        for role, permissions in role_permissions.items():
            if role not in self.tokens:
                results['tests'].append({
                    'role': role,
                    'status': 'SKIP',
                    'details': f'用户 {role} 未设置或登录失败'
                })
                continue
            
            token = self.tokens[role]
            headers = {'Authorization': f'Bearer {token}'}
            
            # 测试允许访问的端点
            for endpoint in permissions['allowed']:
                try:
                    response = requests.get(
                        f"{self.base_url}{endpoint}",
                        headers=headers,
                        timeout=10
                    )
                    
                    if response.status_code in [200, 201]:
                        results['tests'].append({
                            'role': role,
                            'endpoint': endpoint,
                            'expected': 'ALLOW',
                            'actual': 'ALLOW',
                            'status': 'PASS',
                            'status_code': response.status_code
                        })
                    else:
                        results['tests'].append({
                            'role': role,
                            'endpoint': endpoint,
                            'expected': 'ALLOW',
                            'actual': 'DENY',
                            'status': 'FAIL',
                            'status_code': response.status_code
                        })
                        
                except Exception as e:
                    results['tests'].append({
                        'role': role,
                        'endpoint': endpoint,
                        'expected': 'ALLOW',
                        'actual': 'ERROR',
                        'status': 'ERROR',
                        'error': str(e)
                    })
            
            # 测试禁止访问的端点
            for endpoint in permissions['forbidden']:
                try:
                    response = requests.get(
                        f"{self.base_url}{endpoint}",
                        headers=headers,
                        timeout=10
                    )
                    
                    if response.status_code in [401, 403]:
                        results['tests'].append({
                            'role': role,
                            'endpoint': endpoint,
                            'expected': 'DENY',
                            'actual': 'DENY',
                            'status': 'PASS',
                            'status_code': response.status_code
                        })
                    else:
                        results['tests'].append({
                            'role': role,
                            'endpoint': endpoint,
                            'expected': 'DENY',
                            'actual': 'ALLOW',
                            'status': 'FAIL',
                            'status_code': response.status_code
                        })
                        
                except Exception as e:
                    results['tests'].append({
                        'role': role,
                        'endpoint': endpoint,
                        'expected': 'DENY',
                        'actual': 'ERROR',
                        'status': 'ERROR',
                        'error': str(e)
                    })
        
        return results
    
    def test_api_authentication(self) -> Dict[str, Any]:
        """测试API接口权限验证"""
        logger.info("测试API接口权限验证...")
        
        results = {
            'test_name': 'API接口权限验证',
            'timestamp': datetime.now().isoformat(),
            'tests': []
        }
        
        # 需要认证的端点
        protected_endpoints = [
            '/api/v1/auth/me',
            '/api/v1/subscriptions/',
            '/api/v1/bookmarks/',
            '/api/v1/admin/dashboard'
        ]
        
        # 测试无token访问
        for endpoint in protected_endpoints:
            try:
                response = requests.get(
                    f"{self.base_url}{endpoint}",
                    timeout=10
                )
                
                if response.status_code == 401:
                    results['tests'].append({
                        'endpoint': endpoint,
                        'test_type': '无token访问',
                        'status': 'PASS',
                        'details': '正确拒绝无认证访问'
                    })
                else:
                    results['tests'].append({
                        'endpoint': endpoint,
                        'test_type': '无token访问',
                        'status': 'FAIL',
                        'details': f'未拒绝无认证访问，状态码: {response.status_code}'
                    })
                    
            except Exception as e:
                results['tests'].append({
                    'endpoint': endpoint,
                    'test_type': '无token访问',
                    'status': 'ERROR',
                    'details': f'测试失败: {str(e)}'
                })
        
        # 测试无效token访问
        invalid_token = "invalid.token.here"
        for endpoint in protected_endpoints:
            try:
                response = requests.get(
                    f"{self.base_url}{endpoint}",
                    headers={'Authorization': f'Bearer {invalid_token}'},
                    timeout=10
                )
                
                if response.status_code == 401:
                    results['tests'].append({
                        'endpoint': endpoint,
                        'test_type': '无效token访问',
                        'status': 'PASS',
                        'details': '正确拒绝无效token'
                    })
                else:
                    results['tests'].append({
                        'endpoint': endpoint,
                        'test_type': '无效token访问',
                        'status': 'FAIL',
                        'details': f'未拒绝无效token，状态码: {response.status_code}'
                    })
                    
            except Exception as e:
                results['tests'].append({
                    'endpoint': endpoint,
                    'test_type': '无效token访问',
                    'status': 'ERROR',
                    'details': f'测试失败: {str(e)}'
                })
        
        # 测试过期token访问（模拟）
        # 注意：这里需要实际的过期token，或者等待token过期
        results['tests'].append({
            'endpoint': 'all_protected',
            'test_type': '过期token访问',
            'status': 'SKIP',
            'details': '需要实际过期的token进行测试'
        })
        
        return results
    
    def test_admin_access_control(self) -> Dict[str, Any]:
        """测试管理后台访问控制"""
        logger.info("测试管理后台访问控制...")
        
        results = {
            'test_name': '管理后台访问控制',
            'timestamp': datetime.now().isoformat(),
            'tests': []
        }
        
        admin_endpoints = [
            '/api/v1/admin/dashboard',
            '/api/v1/admin/users',
            '/api/v1/admin/crawl/rss',
            '/api/v1/admin/system/status',
            '/api/v1/admin/logs'
        ]
        
        # 测试管理员访问
        if 'admin' in self.tokens:
            admin_token = self.tokens['admin']
            admin_headers = {'Authorization': f'Bearer {admin_token}'}
            
            for endpoint in admin_endpoints:
                try:
                    response = requests.get(
                        f"{self.base_url}{endpoint}",
                        headers=admin_headers,
                        timeout=10
                    )
                    
                    if response.status_code in [200, 201]:
                        results['tests'].append({
                            'endpoint': endpoint,
                            'user_type': 'admin',
                            'status': 'PASS',
                            'details': '管理员正确访问管理端点'
                        })
                    elif response.status_code == 404:
                        results['tests'].append({
                            'endpoint': endpoint,
                            'user_type': 'admin',
                            'status': 'SKIP',
                            'details': '端点不存在'
                        })
                    else:
                        results['tests'].append({
                            'endpoint': endpoint,
                            'user_type': 'admin',
                            'status': 'FAIL',
                            'details': f'管理员访问失败，状态码: {response.status_code}'
                        })
                        
                except Exception as e:
                    results['tests'].append({
                        'endpoint': endpoint,
                        'user_type': 'admin',
                        'status': 'ERROR',
                        'details': f'测试失败: {str(e)}'
                    })
        
        # 测试普通用户访问管理端点
        if 'user' in self.tokens:
            user_token = self.tokens['user']
            user_headers = {'Authorization': f'Bearer {user_token}'}
            
            for endpoint in admin_endpoints:
                try:
                    response = requests.get(
                        f"{self.base_url}{endpoint}",
                        headers=user_headers,
                        timeout=10
                    )
                    
                    if response.status_code == 403:
                        results['tests'].append({
                            'endpoint': endpoint,
                            'user_type': 'user',
                            'status': 'PASS',
                            'details': '正确拒绝普通用户访问管理端点'
                        })
                    elif response.status_code == 404:
                        results['tests'].append({
                            'endpoint': endpoint,
                            'user_type': 'user',
                            'status': 'SKIP',
                            'details': '端点不存在'
                        })
                    else:
                        results['tests'].append({
                            'endpoint': endpoint,
                            'user_type': 'user',
                            'status': 'FAIL',
                            'details': f'未拒绝普通用户访问，状态码: {response.status_code}'
                        })
                        
                except Exception as e:
                    results['tests'].append({
                        'endpoint': endpoint,
                        'user_type': 'user',
                        'status': 'ERROR',
                        'details': f'测试失败: {str(e)}'
                    })
        
        return results
    
    def test_sensitive_operations(self) -> Dict[str, Any]:
        """测试敏感操作二次验证"""
        logger.info("测试敏感操作二次验证...")
        
        results = {
            'test_name': '敏感操作二次验证',
            'timestamp': datetime.now().isoformat(),
            'tests': []
        }
        
        # 敏感操作端点
        sensitive_operations = [
            {
                'endpoint': '/api/v1/auth/change-password',
                'method': 'POST',
                'data': {'old_password': 'old', 'new_password': 'new'}
            },
            {
                'endpoint': '/api/v1/auth/delete-account',
                'method': 'DELETE',
                'data': {}
            },
            {
                'endpoint': '/api/v1/admin/users/delete',
                'method': 'DELETE',
                'data': {'user_id': 'test'}
            }
        ]
        
        if 'user' in self.tokens:
            user_token = self.tokens['user']
            user_headers = {'Authorization': f'Bearer {user_token}'}
            
            for operation in sensitive_operations:
                try:
                    if operation['method'] == 'POST':
                        response = requests.post(
                            f"{self.base_url}{operation['endpoint']}",
                            headers=user_headers,
                            json=operation['data'],
                            timeout=10
                        )
                    elif operation['method'] == 'DELETE':
                        response = requests.delete(
                            f"{self.base_url}{operation['endpoint']}",
                            headers=user_headers,
                            json=operation['data'],
                            timeout=10
                        )
                    
                    # 检查是否要求二次验证
                    if response.status_code == 400:
                        response_data = response.json() if response.headers.get('content-type', '').startswith('application/json') else {}
                        if 'verification' in str(response_data).lower() or 'confirm' in str(response_data).lower():
                            results['tests'].append({
                                'operation': operation['endpoint'],
                                'status': 'PASS',
                                'details': '正确要求二次验证'
                            })
                        else:
                            results['tests'].append({
                                'operation': operation['endpoint'],
                                'status': 'FAIL',
                                'details': '未要求二次验证'
                            })
                    elif response.status_code == 404:
                        results['tests'].append({
                            'operation': operation['endpoint'],
                            'status': 'SKIP',
                            'details': '端点不存在'
                        })
                    else:
                        results['tests'].append({
                            'operation': operation['endpoint'],
                            'status': 'WARN',
                            'details': f'状态码: {response.status_code}，需要人工确认是否有二次验证'
                        })
                        
                except Exception as e:
                    results['tests'].append({
                        'operation': operation['endpoint'],
                        'status': 'ERROR',
                        'details': f'测试失败: {str(e)}'
                    })
        
        return results
    
    def test_session_timeout(self) -> Dict[str, Any]:
        """测试会话超时和强制登出"""
        logger.info("测试会话超时和强制登出...")
        
        results = {
            'test_name': '会话超时和强制登出',
            'timestamp': datetime.now().isoformat(),
            'tests': []
        }
        
        # 测试登出功能
        if 'user' in self.tokens:
            user_token = self.tokens['user']
            user_headers = {'Authorization': f'Bearer {user_token}'}
            
            # 首先验证token有效
            try:
                response = requests.get(
                    f"{self.base_url}/api/v1/auth/me",
                    headers=user_headers,
                    timeout=10
                )
                
                if response.status_code == 200:
                    # 执行登出
                    logout_response = requests.post(
                        f"{self.base_url}/api/v1/auth/logout",
                        headers=user_headers,
                        timeout=10
                    )
                    
                    if logout_response.status_code in [200, 204]:
                        # 验证token是否失效
                        verify_response = requests.get(
                            f"{self.base_url}/api/v1/auth/me",
                            headers=user_headers,
                            timeout=10
                        )
                        
                        if verify_response.status_code == 401:
                            results['tests'].append({
                                'test_type': '登出功能',
                                'status': 'PASS',
                                'details': 'token在登出后正确失效'
                            })
                        else:
                            results['tests'].append({
                                'test_type': '登出功能',
                                'status': 'FAIL',
                                'details': 'token在登出后仍然有效'
                            })
                    else:
                        results['tests'].append({
                            'test_type': '登出功能',
                            'status': 'FAIL',
                            'details': f'登出失败，状态码: {logout_response.status_code}'
                        })
                else:
                    results['tests'].append({
                        'test_type': '登出功能',
                        'status': 'SKIP',
                        'details': 'token已失效，无法测试登出'
                    })
                    
            except Exception as e:
                results['tests'].append({
                    'test_type': '登出功能',
                    'status': 'ERROR',
                    'details': f'测试失败: {str(e)}'
                })
        
        # 测试会话超时（需要长时间等待，这里只做模拟）
        results['tests'].append({
            'test_type': '会话超时',
            'status': 'SKIP',
            'details': '会话超时测试需要长时间等待，建议手动测试'
        })
        
        # 测试强制登出所有会话
        try:
            if 'admin' in self.tokens:
                admin_headers = {'Authorization': f'Bearer {self.tokens["admin"]}'}
                
                response = requests.post(
                    f"{self.base_url}/api/v1/admin/force-logout-all",
                    headers=admin_headers,
                    timeout=10
                )
                
                if response.status_code in [200, 204]:
                    results['tests'].append({
                        'test_type': '强制登出所有会话',
                        'status': 'PASS',
                        'details': '强制登出功能可用'
                    })
                elif response.status_code == 404:
                    results['tests'].append({
                        'test_type': '强制登出所有会话',
                        'status': 'SKIP',
                        'details': '强制登出端点不存在'
                    })
                else:
                    results['tests'].append({
                        'test_type': '强制登出所有会话',
                        'status': 'FAIL',
                        'details': f'强制登出失败，状态码: {response.status_code}'
                    })
        except Exception as e:
            results['tests'].append({
                'test_type': '强制登出所有会话',
                'status': 'ERROR',
                'details': f'测试失败: {str(e)}'
            })
        
        return results
    
    def run_all_access_control_tests(self) -> Dict[str, Any]:
        """运行所有访问控制测试"""
        logger.info("开始运行所有访问权限控制测试...")
        
        all_results = {
            'timestamp': datetime.now().isoformat(),
            'test_suite': '访问权限控制测试',
            'setup': None,
            'tests': []
        }
        
        # 设置测试用户
        setup_result = self.setup_test_users()
        all_results['setup'] = setup_result
        
        # 运行各项测试
        all_results['tests'].append(self.test_role_based_access())
        all_results['tests'].append(self.test_api_authentication())
        all_results['tests'].append(self.test_admin_access_control())
        all_results['tests'].append(self.test_sensitive_operations())
        all_results['tests'].append(self.test_session_timeout())
        
        # 计算总体结果
        total_tests = 0
        passed_tests = 0
        failed_tests = 0
        
        for test_group in all_results['tests']:
            for test in test_group.get('tests', []):
                total_tests += 1
                if test['status'] == 'PASS':
                    passed_tests += 1
                elif test['status'] == 'FAIL':
                    failed_tests += 1
        
        all_results['summary'] = {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': failed_tests,
            'success_rate': (passed_tests / total_tests * 100) if total_tests > 0 else 0
        }
        
        return all_results
    
    def save_results(self, results: Dict[str, Any], output_file: str):
        """保存测试结果"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            logger.info(f"访问控制测试结果已保存: {output_file}")
        except Exception as e:
            logger.error(f"保存结果失败: {e}")


def main():
    parser = argparse.ArgumentParser(description='访问权限控制测试')
    parser.add_argument('--base-url', default='http://localhost:8000', help='目标服务URL')
    parser.add_argument('--output', default='access_control_test_results.json', help='输出文件')
    
    args = parser.parse_args()
    
    tester = AccessControlTester(args.base_url)
    
    try:
        results = tester.run_all_access_control_tests()
        tester.save_results(results, args.output)
        
        # 输出摘要
        summary = results['summary']
        print(f"\n=== 访问权限控制测试摘要 ===")
        print(f"总测试数: {summary['total_tests']}")
        print(f"通过测试: {summary['passed_tests']}")
        print(f"失败测试: {summary['failed_tests']}")
        print(f"成功率: {summary['success_rate']:.1f}%")
        
        # 显示设置结果
        setup = results['setup']
        print(f"\n=== 测试用户设置 ===")
        print(f"创建用户: {', '.join(setup['users_created'])}")
        if setup['errors']:
            print(f"设置错误: {', '.join(setup['errors'])}")
        
        # 显示失败的测试
        failed_tests = []
        for test_group in results['tests']:
            for test in test_group.get('tests', []):
                if test['status'] == 'FAIL':
                    test_name = f"{test_group['test_name']} - "
                    if 'endpoint' in test:
                        test_name += test['endpoint']
                    elif 'operation' in test:
                        test_name += test['operation']
                    elif 'test_type' in test:
                        test_name += test['test_type']
                    failed_tests.append(test_name)
        
        if failed_tests:
            print(f"\n❌ 失败的测试:")
            for i, test in enumerate(failed_tests, 1):
                print(f"{i}. {test}")
        else:
            print(f"\n✅ 所有测试通过!")
        
        print(f"\n📋 详细结果: {args.output}")
        
    except Exception as e:
        logger.error(f"访问控制测试失败: {e}")
        return 1
    
    return 0


if __name__ == '__main__':
    exit(main())
