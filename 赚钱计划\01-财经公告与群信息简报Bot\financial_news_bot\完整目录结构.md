---
type: "always_apply"
description: "便于理解文件功能"
---
# 项目目录结构

## 完整目录树
```
financial_news_bot/
├── .env / .env.dev / .env.example          # 环境变量配置
├── docker-compose.yml / docker-compose.dev.yml # Docker编排文件
├── warning.log                             # 系统日志文件
│
├── backend/                                # 后端服务 (Python 3.13 + FastAPI) - 提供RESTful API服务，处理业务逻辑
│   ├── alembic.ini                        # 数据库迁移配置 - 管理数据库版本升级和回滚
│   ├── init_database.py                   # 数据库初始化脚本 - 创建数据库表结构和初始数据
│   ├── openapi.json                       # API文档规范 - 自动生成的API接口文档，支持Swagger UI
│   ├── requirements.txt / requirements-dev.txt # 依赖管理 - 生产环境和开发环境的Python包依赖列表
│   ├── Dockerfile / Dockerfile.dev        # 容器镜像配置 - 生产和开发环境的Docker镜像构建文件
│   │
│   ├── alembic/                           # 数据库迁移管理 - 管理数据库结构变更的版本控制系统
│   │   ├── env.py                         # Alembic环境配置 - 配置数据库连接和迁移环境参数
│   │   └── versions/                      # 迁移版本文件 - 存储每个数据库版本的升级脚本
│   │       ├── 001_initial_schema.py      # 初始数据库结构 - 创建用户、新闻、订阅等核心表
│   │       ├── 002_add_permission_system.py # 权限系统 - 添加RBAC权限控制相关表结构
│   │       ├── 003_add_report_system.py   # 简报系统 - 添加智能简报生成和模板管理表
│   │       └── 004_add_push_layer_system.py # 推送分层 - 添加T1/T2/T3分层推送和冷却机制表
│   │
│   ├── app/                               # FastAPI应用核心 - 包含所有业务逻辑和API接口的主要代码
│   │   ├── main.py                        # 应用入口点 - FastAPI应用启动文件，配置路由和中间件
│   │   ├── config.py                      # 应用配置 - 环境变量管理，数据库连接配置，第三方服务配置
│   │   ├── database.py                    # 数据库连接 - SQLAlchemy连接池配置，会话管理
│   │   ├── celery_app.py                  # Celery任务队列 - 异步任务处理，爬虫调度，推送任务
│   │   ├── redis_client.py                # Redis连接池 - 缓存服务，会话存储，任务队列后端
│   │   │
│   │   ├── models/                        # 数据模型 (SQLAlchemy ORM) - 定义数据库表结构和关系映射
│   │   │   ├── user.py                    # 用户模型 - 用户账户信息，角色权限，登录状态管理
│   │   │   ├── news.py                    # 新闻模型 - 财经新闻内容，分类标签，AI分析结果
│   │   │   ├── subscription.py            # 订阅模型 - 用户订阅配置，关键词匹配，推送规则
│   │   │   ├── push_analytics.py          # 推送分析模型 - 推送效果统计，A/B测试数据，用户行为分析 🆕
│   │   │   ├── push_log.py                # 推送日志 - 推送记录，成功失败状态，重试机制
│   │   │   ├── push_layer.py              # 推送分层 - T1/T2/T3推送层级，冷却时间，免打扰设置
│   │   │   ├── report.py                  # 简报模型 - 智能简报内容，模板配置，定时生成
│   │   │   ├── permission.py              # 权限模型 - RBAC权限控制，角色定义，API访问控制
│   │   │   ├── entity.py                  # 实体模型 - 命名实体识别结果，公司机构信息
│   │   │   └── sensitive_word.py          # 敏感词库 - 合规检查词库，风险等级分类
│   │   │
│   │   ├── routers/                       # API路由 (18个路由文件) - 定义RESTful API接口和请求处理逻辑
│   │   │   ├── user.py                    # 用户管理API - 注册登录，个人资料，密码管理，OAuth集成
│   │   │   ├── news.py                    # 新闻管理API - 新闻列表查询，详情获取，收藏管理，搜索功能
│   │   │   ├── subscription_with_permissions.py # 订阅管理API - 创建订阅，关键词配置，推送设置，权限控制
│   │   │   ├── push_with_permissions.py   # 推送管理API - 手动推送，批量推送，推送历史，权限验证
│   │   │   ├── enhanced_push.py           # 增强推送API - 签名校验，速率限制，失败重试，异常告警
│   │   │   ├── user_behavior.py           # 用户行为追踪API - 点击统计，阅读时长，偏好分析，行为记录
│   │   │   ├── reports.py                 # 简报管理API - 简报生成，模板管理，定时发送，内容聚合
│   │   │   ├── analytics.py               # 数据分析API - 用户统计，内容分析，趋势报告，性能指标
│   │   │   ├── admin.py                   # 管理员API - 用户管理，系统配置，数据维护，权限分配
│   │   │   ├── monitoring.py              # 系统监控API - 健康检查，性能监控，错误统计，资源使用
│   │   │   ├── security.py                # 安全相关API - 安全审计，风险评估，访问控制，合规检查
│   │   │   ├── scheduler.py               # 调度管理API - 任务调度，定时任务，执行状态，调度策略
│   │   │   ├── tasks.py                   # 任务管理API - 异步任务，任务状态，任务队列，执行结果
│   │   │   ├── templates.py               # 模板管理API - 简报模板，推送模板，模板编辑，版本管理
│   │   │   ├── rules.py                   # 规则管理API - 推送规则，过滤规则，匹配规则，规则引擎
│   │   │   ├── infrastructure.py          # 基础设施API - 系统信息，配置管理，服务状态，环境检查
│   │   │   ├── push_analytics.py          # 推送分析API - 推送统计，成功率分析，用户反馈，效果评估 🆕
│   │   │   ├── push_management.py         # 推送管理API - 推送配置，渠道管理，批量操作，推送策略
│   │   │   └── push_monitoring.py         # 推送监控API - 推送状态监控，异常告警，性能指标 🆕
│   │   │
│   │   ├── services/                      # 业务服务层 (30个服务模块) - 核心业务逻辑实现，提供可复用的服务组件
│   │   │   ├── ai_service.py              # GLM-4.5 Flash AI集成 - 文本分类，摘要生成，情感分析，实体识别
│   │   │   ├── enhanced_push_service.py   # 增强推送服务 - 签名校验，速率限制，失败重试，异常告警机制
│   │   │   ├── user_behavior_tracking_service.py # 用户行为追踪 - 点击统计，阅读行为，偏好分析，行为画像
│   │   │   ├── response_service.py        # 统一响应服务 - 标准化API响应格式，错误码管理，请求ID追踪
│   │   │   ├── news_service.py            # 新闻服务 - 新闻CRUD操作，内容过滤，分类管理，搜索索引
│   │   │   ├── subscription_service.py    # 订阅服务 - 订阅配置管理，关键词匹配，推送规则设置
│   │   │   ├── push_service.py            # 推送聚合服务 - 多渠道推送统一接口，推送状态管理
│   │   │   ├── layered_push_service.py    # 分层推送服务 - T1/T2/T3层级推送，冷却机制，免打扰时段
│   │   │   ├── push_scheduler.py          # 推送调度服务 - 定时推送，批量推送，推送队列管理
│   │   │   ├── report_service.py          # 简报生成服务 - 智能简报生成，内容聚合，模板渲染
│   │   │   ├── report_scheduler.py        # 简报调度服务 - 定时简报生成，晨报晚报调度，发送管理
│   │   │   ├── report_template_engine.py  # 简报模板引擎 - 模板解析，变量替换，格式化输出
│   │   │   ├── template_service.py        # 模板服务 - 模板CRUD，版本管理，模板验证
│   │   │   ├── rule_engine.py             # 规则引擎 - 业务规则执行，条件匹配，动作触发
│   │   │   ├── scheduler_service.py       # 调度服务 - 任务调度管理，执行状态监控，调度策略
│   │   │   ├── search_service.py          # 搜索服务 - 全文搜索，关键词匹配，搜索结果排序
│   │   │   ├── cache_service.py           # 缓存服务 - Redis缓存管理，缓存策略，缓存失效
│   │   │   ├── content_processor.py       # 内容处理服务 - 文本清洗，格式化，内容验证
│   │   │   ├── content_aggregator.py      # 内容聚合服务 - 多源内容聚合，去重处理，内容分发
│   │   │   ├── entity_recognition_service.py # 实体识别服务 - 命名实体识别，公司机构识别，关键人物提取
│   │   │   ├── enhanced_compliance_service.py # 增强合规服务 - 多层次风险评估，敏感词检测，合规审查
│   │   │   ├── permission_service.py      # 权限服务 - RBAC权限管理，角色分配，API访问控制
│   │   │   ├── personalized_subscription_service.py # 个性化订阅 - 用户偏好分析，智能推荐，个性化配置
│   │   │   ├── recommendation_service.py  # 智能推荐服务 - 内容推荐算法，相似度计算，推荐排序
│   │   │   ├── monitoring_service.py      # 监控服务 - 系统健康监控，性能指标收集，告警机制
│   │   │   ├── privacy_service.py         # 隐私保护服务 - 数据脱敏，隐私合规，用户数据保护
│   │   │   ├── audit_service.py           # 审计服务 - 操作日志记录，审计追踪，合规报告
│   │   │   ├── function_calling_service.py # AI函数调用服务 - AI工具调用，函数参数解析，结果处理
│   │   │   ├── admin_service.py           # 管理服务 - 系统管理功能，用户管理，配置管理
│   │   │   ├── analytics_service.py       # 分析服务 - 数据统计分析，报表生成，趋势分析
│   │   │   ├── push_analytics_service.py  # 推送分析服务 - 推送效果分析，用户反馈统计，优化建议
│   │   │   ├── push_layer_service.py      # 推送层级服务 - 推送层级管理，优先级控制，层级策略
│   │   │   ├── user.py                    # 用户服务 - 用户账户管理，认证授权，个人资料维护
│   │   │   └── push_providers/            # 推送提供商 - 多渠道推送实现，统一推送接口
│   │   │       ├── email_provider.py      # 邮件推送 - SMTP邮件发送，模板渲染，发送状态跟踪
│   │   │       ├── feishu_provider.py     # 飞书推送 - 飞书机器人消息推送，卡片消息，群组通知
│   │   │       ├── wechat_group_provider.py # 微信群推送 - 微信群机器人，消息格式化，群组管理
│   │   │       └── wechat_work_provider.py # 企业微信推送 - 企业微信应用消息，部门通知，员工推送
│   │   │
│   │   ├── schemas/                       # Pydantic数据模型 (9个Schema文件)
│   │   │   ├── user.py                    # 用户Schema
│   │   │   ├── news.py                    # 新闻Schema
│   │   │   ├── subscription.py            # 订阅Schema
│   │   │   ├── permission.py              # 权限Schema
│   │   │   ├── push.py                    # 推送Schema
│   │   │   ├── push_analytics.py          # 推送分析Schema - 推送统计数据模型，A/B测试结果 🆕
│   │   │   ├── push_layer.py              # 推送分层Schema
│   │   │   ├── report.py                  # 简报Schema
│   │   │   └── admin.py                   # 管理Schema
│   │   │
│   │   ├── tasks/                         # Celery异步任务
│   │   │   ├── crawler_tasks.py           # 爬虫任务
│   │   │   └── push_tasks.py              # 推送任务
│   │   │
│   │   ├── middleware/                    # 中间件
│   │   │   ├── rate_limiter.py            # 限流中间件
│   │   │   ├── permission_middleware.py   # 权限中间件
│   │   │   └── response_middleware.py     # 响应中间件
│   │   │
│   │   ├── dependencies/                  # FastAPI依赖注入
│   │   │   ├── auth.py                    # 鉴权依赖
│   │   │   └── permissions.py             # 权限依赖
│   │   │
│   │   ├── utils/                         # 工具函数
│   │   │   ├── security.py                # 安全工具
│   │   │   ├── data_processor.py          # 数据处理
│   │   │   ├── decorators.py              # 装饰器
│   │   │   ├── deduplicator.py            # 去重工具
│   │   │   ├── permissions.py             # 权限工具
│   │   │   ├── text_utils.py              # 文本工具
│   │   │   └── audit_logger.py            # 审计日志
│   │   │
│   │   └── exceptions/                    # 异常定义
│   │       ├── user.py                    # 用户异常
│   │       ├── subscription.py            # 订阅异常
│   │       └── permissions.py             # 权限异常
│   │
│   ├── crawlers/                          # 数据采集爬虫 (5个数据源) - 多源财经数据自动采集系统
│   │   ├── base_crawler.py                # 爬虫基类 - 通用爬虫框架，反爬机制，错误处理，调度管理 (浏览器指纹增强) 🆕
│   │   ├── sse_crawler.py                 # 上交所爬虫 - 上海证券交易所公告采集，实时数据获取
│   │   ├── szse_crawler.py                # 深交所爬虫 - 深圳证券交易所公告采集，数据解析处理
│   │   ├── csrc_crawler.py                # 证监会爬虫 - 中国证监会公告采集，监管信息获取
│   │   └── rss_crawler.py                 # RSS爬虫 - RSS源订阅采集，多源聚合，内容去重
│   │
│   └── docs/                              # 技术文档
│       ├── api_permissions.md             # API权限文档
│       ├── backup_disaster_recovery.md    # 备份恢复文档
│       ├── database_naming_standards.md   # 数据库命名规范
│       ├── database_optimization_strategy.md # 数据库优化策略
│       ├── monitoring_system_setup.md     # 监控系统配置
│       ├── permission_system.md           # 权限系统文档
│       └── security_authentication_standards.md # 安全认证标准
│
├── frontend/                              # 前端应用 (React 18 + TypeScript) - 现代化Web界面，响应式设计
│   ├── package.json                       # 前端依赖管理 - NPM包依赖，构建脚本，开发工具配置
│   ├── tsconfig.json                      # TypeScript配置 - 类型检查，编译选项，路径映射
│   ├── Dockerfile / Dockerfile.simple     # 前端容器配置 - 多阶段构建，Nginx静态文件服务
│   ├── nginx.conf                         # Nginx配置 - 反向代理，静态资源服务，缓存策略
│   │
│   ├── public/                            # 静态资源
│   │   ├── index.html                     # 主页面模板
│   │   └── manifest.json                  # PWA配置
│   │
│   ├── src/                               # 前端源码
│   │   ├── App.tsx                        # 主应用组件
│   │   ├── index.tsx                      # 应用入口
│   │   ├── index.css                      # 全局样式
│   │   │
│   │   ├── components/                    # React组件 - 可复用的UI组件库，模块化设计
│   │   │   ├── auth/                      # 认证组件 - 用户认证相关的UI组件
│   │   │   │   └── SocialLogin.tsx        # 社交登录组件 - OAuth登录，微信QQ登录，第三方认证
│   │   │   ├── common/                    # 通用组件 - 跨页面复用的基础UI组件
│   │   │   │   ├── ErrorBoundary.tsx      # 错误边界 - React错误捕获，友好错误提示，错误上报
│   │   │   │   ├── LoadingSpinner.tsx     # 加载动画 - 统一加载状态显示，骨架屏，进度指示
│   │   │   │   ├── NotificationPanel.tsx  # 通知面板 - 系统通知显示，消息提醒，状态更新
│   │   │   │   ├── ProtectedRoute.tsx     # 受保护路由 - 权限验证，登录检查，访问控制
│   │   │   │   ├── PublicRoute.tsx        # 公开路由 - 公开页面路由，无需认证访问
│   │   │   │   ├── ResponsiveImage.tsx    # 响应式图片 - 自适应图片显示，懒加载，性能优化
│   │   │   │   ├── TouchOptimized.tsx     # 触摸优化 - 移动端触摸交互，手势支持，触摸反馈
│   │   │   │   └── Breadcrumb.tsx         # 面包屑导航 - 页面路径导航，层级显示，快速跳转
│   │   │   ├── guide/                     # 用户指南 - 用户帮助和引导组件
│   │   │   │   └── UserGuide.tsx          # 用户指南组件 - 功能介绍，使用教程，帮助文档
│   │   │   ├── layout/                    # 布局组件 - 页面布局和结构组件
│   │   │   │   ├── MainLayout.tsx         # 主布局 - 主要页面布局，导航栏，侧边栏，内容区域
│   │   │   │   └── AuthLayout.tsx         # 认证布局 - 登录注册页面布局，简洁设计
│   │   │   ├── search/                    # 搜索组件 - 搜索功能相关组件
│   │   │   │   └── GlobalSearch.tsx       # 全局搜索 - 全站搜索功能，智能提示，搜索历史
│   │   │   └── subscription/              # 订阅组件 - 订阅管理相关组件
│   │   │       ├── ChannelConfig.tsx      # 渠道配置 - 推送渠道设置，多渠道管理，配置验证
│   │   │       ├── KeywordManager.tsx     # 关键词管理 - 关键词添加删除，智能推荐，匹配规则
│   │   │       └── SubscriptionStats.tsx  # 订阅统计 - 订阅数据统计，图表展示，趋势分析
│   │   │
│   │   ├── pages/                         # 页面组件 - 完整的页面级组件，业务功能实现
│   │   │   ├── auth/                      # 认证页面 - 用户认证相关页面
│   │   │   │   ├── LoginPage.tsx          # 登录页面 - 用户登录，记住密码，社交登录入口
│   │   │   │   ├── RegisterPage.tsx       # 注册页面 - 用户注册，邮箱验证，条款同意
│   │   │   │   ├── ForgotPasswordPage.tsx # 忘记密码页面 - 密码重置，邮箱验证，安全验证
│   │   │   │   └── OAuthCallbackPage.tsx  # OAuth回调页面 - 第三方登录回调处理，状态验证
│   │   │   ├── dashboard/                 # 仪表板页面 - 系统主页和数据概览
│   │   │   │   └── DashboardPage.tsx      # 主仪表板 - 数据统计，快捷操作，系统状态概览
│   │   │   ├── news/                      # 新闻页面 - 新闻浏览和管理功能
│   │   │   │   ├── NewsPage.tsx           # 新闻列表 - 新闻浏览，分类筛选，搜索功能
│   │   │   │   ├── NewsDetailPage.tsx     # 新闻详情 - 新闻内容展示，相关推荐，分享收藏
│   │   │   │   └── BookmarksPage.tsx      # 收藏页面 - 收藏管理，标签分类，批量操作
│   │   │   ├── subscription/              # 订阅页面 - 订阅管理功能页面
│   │   │   │   ├── SubscriptionPage.tsx   # 订阅列表 - 订阅管理，状态控制，统计信息
│   │   │   │   ├── SubscriptionCreatePage.tsx # 创建订阅 - 订阅配置，关键词设置，推送设置
│   │   │   │   └── SubscriptionEditPage.tsx # 编辑订阅 - 订阅修改，规则调整，历史记录
│   │   │   ├── profile/                   # 个人资料页面 - 用户个人信息管理
│   │   │   │   └── ProfilePage.tsx        # 个人资料 - 个人信息编辑，头像上传，偏好设置
│   │   │   ├── settings/                  # 设置页面 - 系统设置和配置
│   │   │   │   └── SettingsPage.tsx       # 系统设置 - 通知设置，隐私设置，账户安全
│   │   │   └── error/                     # 错误页面 - 错误状态处理页面
│   │   │       └── NotFoundPage.tsx       # 404页面 - 页面未找到，友好错误提示，导航建议
│   │   │
│   │   ├── services/                      # 前端服务 - 前端业务逻辑和API调用封装
│   │   │   ├── api.ts                     # API服务 - HTTP请求封装，拦截器，错误处理，统一接口
│   │   │   └── authService.ts             # 认证服务 - 登录状态管理，Token处理，权限验证
│   │   │
│   │   ├── store/                         # Redux状态管理 - 全局状态管理，数据流控制
│   │   │   ├── index.ts                   # Store配置 - Redux store配置，中间件设置，开发工具
│   │   │   └── slices/                    # Redux切片 - 模块化状态管理
│   │   │       ├── authSlice.ts           # 认证状态 - 用户登录状态，权限信息，会话管理
│   │   │       ├── newsSlice.ts           # 新闻状态 - 新闻数据缓存，列表状态，搜索结果
│   │   │       ├── subscriptionSlice.ts   # 订阅状态 - 订阅配置，推送设置，订阅统计
│   │   │       └── uiSlice.ts             # UI状态 - 界面状态，主题设置，交互状态
│   │   │
│   │   ├── hooks/                         # 自定义Hook - 可复用的React逻辑封装
│   │   │   ├── useFormValidation.ts       # 表单验证Hook - 表单校验逻辑，错误提示，实时验证
│   │   │   └── useKeyboardShortcuts.ts    # 键盘快捷键Hook - 快捷键绑定，组合键处理，快捷操作
│   │   │
│   │   ├── utils/                         # 前端工具函数 - 通用工具和辅助函数
│   │   │   ├── errorMonitoring.ts         # 错误监控 - 前端错误捕获，错误上报，性能监控
│   │   │   ├── userAnalytics.ts           # 用户分析 - 用户行为统计，页面访问跟踪，事件埋点
│   │   │   ├── uxAnalytics.ts             # UX分析 - 用户体验分析，交互统计，界面优化
│   │   │   ├── networkRetry.ts            # 网络重试 - 网络请求重试，断网检测，离线处理
│   │   │   ├── imageOptimization.ts       # 图片优化 - 图片压缩，懒加载，格式转换
│   │   │   ├── pushNotificationAnalytics.js # 推送通知分析 - 客户端推送统计，点击率分析，转化追踪 🆕
│   │   │   ├── cacheOptimization.ts       # 缓存优化 - 前端缓存策略，数据缓存，性能优化 🆕
│   │   │   ├── lazyLoading.ts             # 懒加载 - 组件懒加载，路由懒加载，性能优化 🆕
│   │   │   ├── performanceMonitor.ts      # 性能监控 - 前端性能监控，用户体验指标 🆕
│   │   │   ├── usabilityReportGenerator.ts # 可用性报告生成 - UX数据收集，可用性分析 🆕
│   │   │   └── abTesting.ts               # A/B测试 - 功能测试，用户分组，效果统计
│   │   │
│   │   ├── types/                         # TypeScript类型定义 - 类型安全和代码提示
│   │   │   └── index.ts                   # 类型定义 - 全局类型定义，接口声明，枚举类型
│   │   │
│   │   └── tests/                         # 前端测试 - 前端代码质量保证和功能验证 🆕
│   │       ├── usability.test.ts          # 可用性测试 - 用户界面可用性测试，交互体验验证 🆕
│   │       └── performance.test.ts        # 性能测试 - 前端性能测试，加载速度，渲染性能 🆕
│   │
│   └── docs/                              # 前端文档
│       ├── user-flow-wireframes.md        # 用户流程线框图
│       └── user-testing-plan.md           # 用户测试计划
│
├── config/                                # 服务配置文件
│   ├── nginx/                             # Nginx配置
│   │   └── default.conf                   # 生产环境Nginx配置
│   └── redis/                             # Redis配置
│       └── redis.conf                     # Redis服务配置
│
├── docker/                                # Docker配置文件
│   ├── backend/                           # 后端Docker配置
│   │   └── Dockerfile                     # 后端镜像配置
│   ├── frontend/                          # 前端Docker配置
│   │   └── Dockerfile                     # 前端镜像配置
│   └── nginx/                             # Nginx Docker配置
│       ├── Dockerfile                     # Nginx镜像配置
│       └── dev.conf                       # 开发环境配置
│
├── scripts/                               # 管理脚本 - 系统部署和运维自动化脚本
│   ├── deploy.sh                          # 部署脚本 - 生产环境部署，服务启动，健康检查
│   ├── start.sh                           # 启动脚本 - 系统启动，服务编排，依赖检查
│   ├── stop.sh                            # 停止脚本 - 优雅停机，服务清理，资源释放
│   ├── update.sh                          # 更新脚本 - 系统更新，数据迁移，版本升级
│   ├── dev-setup.sh                       # 开发环境配置脚本 - 开发环境初始化，依赖安装
│   ├── dev-manage.sh                      # 开发环境管理脚本 - 开发工具管理，调试辅助
│   ├── init_permissions.py                # 权限系统初始化脚本 - RBAC权限初始化，角色创建
│   ├── code_quality_check.py              # 代码质量检查工具 - 代码规范检查，质量评估 🆕
│   ├── final_integration_test.py          # 最终集成测试 - 完整系统集成测试，验收测试 🆕
│   ├── api-performance-optimizer.py       # API性能优化工具 - 接口性能分析，优化建议 🆕
│   ├── database-optimization.py           # 数据库优化工具 - 数据库性能调优，索引优化 🆕
│   ├── monitor-performance.py             # 性能监控脚本 - 系统性能实时监控，指标收集 🆕
│   ├── performance-test.sh                # 性能测试脚本 - 自动化性能测试，压力测试 🆕
│   ├── regression-test.sh                 # 回归测试脚本 - 自动化回归测试，质量保证 🆕
│   └── security-audit.sh                  # 安全审计脚本 - 安全漏洞扫描，合规检查 🆕
│
├── tests/                                 # 测试文件 - 完整的测试体系，保证代码质量 🆕
│   ├── api/                               # API测试 - 接口功能和集成测试
│   │   └── postman_collection.json        # Postman测试集合 - API接口测试用例，自动化测试
│   ├── e2e/                               # 端到端测试 - 用户场景完整流程测试
│   │   └── example.spec.ts                # E2E测试示例 - 用户操作流程，界面交互测试
│   ├── integration/                       # 集成测试 - 系统组件集成测试 🆕
│   │   └── test_week6_integration.py      # 第6周集成测试 - 完整系统功能验证 🆕
│   ├── performance/                       # 性能测试 - 系统性能和负载测试 🆕
│   │   ├── concurrent_performance_test.py # 并发性能测试 - 多用户并发测试 🆕
│   │   ├── jmeter-test-plan.jmx           # JMeter测试计划 - 图形化性能测试 🆕
│   │   └── locustfile.py                  # Locust性能测试 - 分布式负载测试 🆕
│   ├── perf/                              # 性能测试 - 系统性能和负载测试
│   │   └── locustfile.py                  # Locust性能测试 - 并发测试，压力测试，性能基准
│   ├── security/                          # 安全测试 - 安全漏洞检测和防护测试 🆕
│   │   ├── security_scanner.py            # 安全扫描器 - 漏洞扫描，安全检测 🆕
│   │   ├── encryption_test.py             # 加密测试 - 数据加密，传输安全测试 🆕
│   │   ├── access_control_test.py         # 权限控制测试 - 访问权限，角色验证 🆕
│   │   ├── audit_log_validator.py         # 审计日志验证 - 日志完整性，合规检查 🆕
│   │   ├── protection_mechanisms_test.py  # 防护机制测试 - 防刷，防攻击测试 🆕
│   │   └── security_config_checker.py     # 安全配置检查 - 配置安全性验证 🆕
│   ├── stability/                         # 稳定性测试 - 系统稳定性和可靠性测试 🆕
│   │   └── stability_test.py              # 稳定性测试 - 长时间运行，故障恢复 🆕
│   └── qa/                                # 质量保证 - 代码质量检查和评估
│       └── summary_evaluation.py          # 总结评估脚本 - 代码质量评估，测试覆盖率统计
│
├── security/                              # 安全模块 - 系统安全防护和监控 🆕
│   └── security_monitoring.py             # 安全监控系统 - 实时安全监控，威胁检测 🆕
│
├── compliance/                            # 合规模块 - 法规合规和数据保护 🆕
│   ├── compliance_monitor.py              # 合规监控 - 合规检查，风险评估 🆕
│   ├── content_filter.py                  # 内容过滤 - 敏感词过滤，内容审核 🆕
│   ├── data_protection.py                 # 数据保护 - 数据安全，隐私保护 🆕
│   ├── privacy_policy.md                  # 隐私政策 - 用户隐私保护政策 🆕
│   └── terms_of_service.md                # 服务条款 - 用户服务协议 🆕
│
├── docs/                                  # 项目文档
│   ├── bug-tracking/                      # Bug跟踪文档 - Bug管理和跟踪流程 🆕
│   ├── user-acceptance-testing/           # 用户验收测试 - UAT流程和文档 🆕
│   ├── ux-optimization-plan.md            # UX优化计划
│   └── ux-pain-points-analysis.md         # UX痛点分析
│
├── monitoring/                            # 监控配置目录
├── redis/                                 # Redis配置目录
│   └── redis.conf                         # Redis配置文件
│
└── .gitignore                             # Git忽略文件配置
```