#!/usr/bin/env python3
"""
代码质量检查工具
检查代码规范、类型注解、文档字符串等
"""
import os
import ast
import sys
import subprocess
from pathlib import Path
from typing import List, Dict, Any, Tuple
import re

class CodeQualityChecker:
    """代码质量检查器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.app_dir = self.project_root / "app"
        self.issues = []
        
    def check_all(self) -> Dict[str, Any]:
        """执行所有质量检查"""
        print("🔍 开始代码质量检查...")
        
        results = {
            "type_annotations": self.check_type_annotations(),
            "docstrings": self.check_docstrings(),
            "code_style": self.check_code_style(),
            "imports": self.check_imports(),
            "complexity": self.check_complexity(),
            "security": self.check_security(),
            "performance": self.check_performance(),
            "summary": {}
        }
        
        # 生成总结
        total_issues = sum(len(issues) for issues in results.values() if isinstance(issues, list))
        results["summary"] = {
            "total_files_checked": len(list(self.app_dir.rglob("*.py"))),
            "total_issues": total_issues,
            "issues_by_category": {
                category: len(issues) for category, issues in results.items() 
                if isinstance(issues, list)
            }
        }
        
        return results
    
    def check_type_annotations(self) -> List[Dict[str, Any]]:
        """检查类型注解"""
        print("📝 检查类型注解...")
        issues = []
        
        for py_file in self.app_dir.rglob("*.py"):
            if py_file.name.startswith("__"):
                continue
                
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                tree = ast.parse(content)
                
                for node in ast.walk(tree):
                    if isinstance(node, ast.FunctionDef):
                        # 检查函数参数类型注解
                        for arg in node.args.args:
                            if arg.annotation is None and arg.arg != 'self':
                                issues.append({
                                    "file": str(py_file.relative_to(self.project_root)),
                                    "line": node.lineno,
                                    "type": "missing_parameter_annotation",
                                    "message": f"函数 {node.name} 的参数 {arg.arg} 缺少类型注解",
                                    "severity": "warning"
                                })
                        
                        # 检查返回值类型注解
                        if node.returns is None and not node.name.startswith("_"):
                            issues.append({
                                "file": str(py_file.relative_to(self.project_root)),
                                "line": node.lineno,
                                "type": "missing_return_annotation",
                                "message": f"函数 {node.name} 缺少返回值类型注解",
                                "severity": "warning"
                            })
                            
            except Exception as e:
                issues.append({
                    "file": str(py_file.relative_to(self.project_root)),
                    "line": 0,
                    "type": "parse_error",
                    "message": f"解析文件失败: {str(e)}",
                    "severity": "error"
                })
        
        return issues
    
    def check_docstrings(self) -> List[Dict[str, Any]]:
        """检查文档字符串"""
        print("📚 检查文档字符串...")
        issues = []
        
        for py_file in self.app_dir.rglob("*.py"):
            if py_file.name.startswith("__"):
                continue
                
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                tree = ast.parse(content)
                
                for node in ast.walk(tree):
                    if isinstance(node, (ast.FunctionDef, ast.ClassDef, ast.AsyncFunctionDef)):
                        # 跳过私有方法和特殊方法
                        if node.name.startswith("_"):
                            continue
                            
                        # 检查是否有文档字符串
                        docstring = ast.get_docstring(node)
                        if not docstring:
                            node_type = "类" if isinstance(node, ast.ClassDef) else "函数"
                            issues.append({
                                "file": str(py_file.relative_to(self.project_root)),
                                "line": node.lineno,
                                "type": "missing_docstring",
                                "message": f"{node_type} {node.name} 缺少文档字符串",
                                "severity": "warning"
                            })
                        elif len(docstring.strip()) < 10:
                            issues.append({
                                "file": str(py_file.relative_to(self.project_root)),
                                "line": node.lineno,
                                "type": "short_docstring",
                                "message": f"{node.name} 的文档字符串过短",
                                "severity": "info"
                            })
                            
            except Exception as e:
                issues.append({
                    "file": str(py_file.relative_to(self.project_root)),
                    "line": 0,
                    "type": "parse_error",
                    "message": f"解析文件失败: {str(e)}",
                    "severity": "error"
                })
        
        return issues
    
    def check_code_style(self) -> List[Dict[str, Any]]:
        """检查代码风格"""
        print("🎨 检查代码风格...")
        issues = []
        
        # 使用flake8检查代码风格
        try:
            result = subprocess.run([
                "python", "-m", "flake8", 
                str(self.app_dir),
                "--max-line-length=120",
                "--ignore=E203,W503,E501",
                "--format=%(path)s:%(row)d:%(col)d: %(code)s %(text)s"
            ], capture_output=True, text=True, cwd=self.project_root)
            
            for line in result.stdout.strip().split('\n'):
                if line:
                    parts = line.split(': ', 2)
                    if len(parts) >= 3:
                        file_info = parts[0]
                        error_code = parts[1]
                        message = parts[2] if len(parts) > 2 else ""
                        
                        file_path, line_col = file_info.rsplit(':', 2)
                        line_num = line_col.split(':')[0]
                        
                        issues.append({
                            "file": file_path,
                            "line": int(line_num),
                            "type": "style_violation",
                            "message": f"{error_code}: {message}",
                            "severity": "warning"
                        })
                        
        except FileNotFoundError:
            issues.append({
                "file": "system",
                "line": 0,
                "type": "tool_missing",
                "message": "flake8 未安装，跳过代码风格检查",
                "severity": "info"
            })
        
        return issues
    
    def check_imports(self) -> List[Dict[str, Any]]:
        """检查导入语句"""
        print("📦 检查导入语句...")
        issues = []
        
        for py_file in self.app_dir.rglob("*.py"):
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                tree = ast.parse(content)
                
                imports = []
                for node in ast.walk(tree):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            imports.append((node.lineno, alias.name))
                    elif isinstance(node, ast.ImportFrom):
                        module = node.module or ""
                        for alias in node.names:
                            imports.append((node.lineno, f"{module}.{alias.name}"))
                
                # 检查未使用的导入
                for line_num, import_name in imports:
                    # 简单检查：如果导入的模块名在代码中没有出现
                    module_name = import_name.split('.')[0]
                    if module_name not in content.replace(f"import {module_name}", ""):
                        issues.append({
                            "file": str(py_file.relative_to(self.project_root)),
                            "line": line_num,
                            "type": "unused_import",
                            "message": f"可能未使用的导入: {import_name}",
                            "severity": "info"
                        })
                        
            except Exception as e:
                continue
        
        return issues
    
    def check_complexity(self) -> List[Dict[str, Any]]:
        """检查代码复杂度"""
        print("🧮 检查代码复杂度...")
        issues = []
        
        for py_file in self.app_dir.rglob("*.py"):
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                tree = ast.parse(content)
                
                for node in ast.walk(tree):
                    if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                        # 计算圈复杂度（简化版）
                        complexity = self._calculate_complexity(node)
                        
                        if complexity > 10:
                            issues.append({
                                "file": str(py_file.relative_to(self.project_root)),
                                "line": node.lineno,
                                "type": "high_complexity",
                                "message": f"函数 {node.name} 复杂度过高 ({complexity})",
                                "severity": "warning"
                            })
                        
                        # 检查函数长度
                        if hasattr(node, 'end_lineno'):
                            func_length = node.end_lineno - node.lineno
                            if func_length > 50:
                                issues.append({
                                    "file": str(py_file.relative_to(self.project_root)),
                                    "line": node.lineno,
                                    "type": "long_function",
                                    "message": f"函数 {node.name} 过长 ({func_length} 行)",
                                    "severity": "warning"
                                })
                                
            except Exception as e:
                continue
        
        return issues
    
    def _calculate_complexity(self, node: ast.AST) -> int:
        """计算圈复杂度（简化版）"""
        complexity = 1  # 基础复杂度
        
        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            elif isinstance(child, ast.ExceptHandler):
                complexity += 1
            elif isinstance(child, (ast.And, ast.Or)):
                complexity += 1
        
        return complexity
    
    def check_security(self) -> List[Dict[str, Any]]:
        """检查安全问题"""
        print("🔒 检查安全问题...")
        issues = []
        
        security_patterns = [
            (r'password\s*=\s*["\'][^"\']+["\']', "硬编码密码"),
            (r'secret\s*=\s*["\'][^"\']+["\']', "硬编码密钥"),
            (r'api_key\s*=\s*["\'][^"\']+["\']', "硬编码API密钥"),
            (r'eval\s*\(', "使用eval函数"),
            (r'exec\s*\(', "使用exec函数"),
            (r'shell\s*=\s*True', "shell注入风险"),
        ]
        
        for py_file in self.app_dir.rglob("*.py"):
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                lines = content.split('\n')
                for line_num, line in enumerate(lines, 1):
                    for pattern, message in security_patterns:
                        if re.search(pattern, line, re.IGNORECASE):
                            issues.append({
                                "file": str(py_file.relative_to(self.project_root)),
                                "line": line_num,
                                "type": "security_risk",
                                "message": f"安全风险: {message}",
                                "severity": "error"
                            })
                            
            except Exception as e:
                continue
        
        return issues
    
    def check_performance(self) -> List[Dict[str, Any]]:
        """检查性能问题"""
        print("⚡ 检查性能问题...")
        issues = []
        
        performance_patterns = [
            (r'\.append\s*\([^)]+\)\s*for\s+', "使用列表推导式替代append循环"),
            (r'len\s*\([^)]+\)\s*==\s*0', "使用 'not list' 替代 'len(list) == 0'"),
            (r'\.keys\s*\(\)\s*in\s+', "直接检查字典键"),
        ]
        
        for py_file in self.app_dir.rglob("*.py"):
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                lines = content.split('\n')
                for line_num, line in enumerate(lines, 1):
                    for pattern, suggestion in performance_patterns:
                        if re.search(pattern, line):
                            issues.append({
                                "file": str(py_file.relative_to(self.project_root)),
                                "line": line_num,
                                "type": "performance_issue",
                                "message": f"性能建议: {suggestion}",
                                "severity": "info"
                            })
                            
            except Exception as e:
                continue
        
        return issues
    
    def generate_report(self, results: Dict[str, Any]) -> str:
        """生成检查报告"""
        report = []
        report.append("# 代码质量检查报告")
        report.append(f"检查时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # 总结
        summary = results["summary"]
        report.append("## 📊 总结")
        report.append(f"- 检查文件数: {summary['total_files_checked']}")
        report.append(f"- 发现问题数: {summary['total_issues']}")
        report.append("")
        
        # 按类别统计
        report.append("### 问题分类统计")
        for category, count in summary["issues_by_category"].items():
            if count > 0:
                report.append(f"- {category}: {count} 个问题")
        report.append("")
        
        # 详细问题
        for category, issues in results.items():
            if category == "summary" or not isinstance(issues, list) or not issues:
                continue
                
            report.append(f"## {category.replace('_', ' ').title()}")
            
            # 按严重程度分组
            by_severity = {}
            for issue in issues:
                severity = issue.get("severity", "info")
                if severity not in by_severity:
                    by_severity[severity] = []
                by_severity[severity].append(issue)
            
            for severity in ["error", "warning", "info"]:
                if severity in by_severity:
                    report.append(f"### {severity.upper()} ({len(by_severity[severity])} 个)")
                    for issue in by_severity[severity][:10]:  # 只显示前10个
                        report.append(f"- **{issue['file']}:{issue['line']}** - {issue['message']}")
                    
                    if len(by_severity[severity]) > 10:
                        report.append(f"- ... 还有 {len(by_severity[severity]) - 10} 个类似问题")
                    report.append("")
        
        return "\n".join(report)

def main():
    """主函数"""
    checker = CodeQualityChecker()
    results = checker.check_all()
    
    # 生成报告
    report = checker.generate_report(results)
    
    # 保存报告
    report_file = Path("code_quality_report.md")
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n📋 代码质量检查完成！")
    print(f"📄 报告已保存到: {report_file}")
    print(f"📊 总计发现 {results['summary']['total_issues']} 个问题")
    
    # 返回退出码
    return 1 if results['summary']['total_issues'] > 0 else 0

if __name__ == "__main__":
    sys.exit(main())
