"""
RSS源解析爬虫

功能：支持多个财经媒体RSS源，实现通用RSS解析功能
"""

import asyncio
import feedparser
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import time
import logging
from urllib.parse import urljoin

from .base_crawler import BaseCrawler
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../'))
from app.models.news import NewsSource, NewsCategory

logger = logging.getLogger(__name__)


class RSSCrawler(BaseCrawler):
    """RSS源解析爬虫"""
    
    def __init__(self, rate_limit: float = 1.0):
        """
        初始化RSS爬虫
        
        Args:
            rate_limit: 请求间隔（秒），默认1秒
        """
        super().__init__(
            name="RSS_Crawler",
            base_url="",  # RSS爬虫没有固定的base_url
            rate_limit=rate_limit
        )
        
        # 财经媒体RSS源配置
        self.rss_sources = {
            'sina_finance': {
                'name': '新浪财经',
                'url': 'https://feed.mix.sina.com.cn/api/roll/get?pageid=153&lid=1686&k=&num=50&page=1',
                'encoding': 'utf-8'
            },
            'netease_money': {
                'name': '网易财经',
                'url': 'http://money.163.com/special/002557S6/rss_newstop.xml',
                'encoding': 'utf-8'
            },
            'eastmoney': {
                'name': '东方财富',
                'url': 'http://feed.eastmoney.com/rssapi.aspx?type=web',
                'encoding': 'utf-8'
            },
            'caixin': {
                'name': '财新网',
                'url': 'http://www.caixin.com/rss/all.xml',
                'encoding': 'utf-8'
            },
            'yicai': {
                'name': '第一财经',
                'url': 'https://www.yicai.com/rss/all.xml',
                'encoding': 'utf-8'
            }
        }
    
    def _parse_rss_date(self, date_str: str) -> Optional[datetime]:
        """
        解析RSS日期字符串
        
        Args:
            date_str: RSS日期字符串
            
        Returns:
            datetime对象或None
        """
        if not date_str:
            return None
        
        try:
            # feedparser通常会解析时间为time.struct_time
            if hasattr(date_str, 'tm_year'):
                return datetime(*date_str[:6])
            
            # 如果是字符串，尝试解析
            if isinstance(date_str, str):
                # 尝试使用feedparser的时间解析
                parsed_time = feedparser._parse_date(date_str)
                if parsed_time:
                    return datetime(*parsed_time[:6])
                
                # 手动解析常见格式
                formats = [
                    "%a, %d %b %Y %H:%M:%S %z",
                    "%a, %d %b %Y %H:%M:%S GMT",
                    "%Y-%m-%d %H:%M:%S",
                    "%Y-%m-%dT%H:%M:%S",
                    "%Y-%m-%dT%H:%M:%SZ"
                ]
                
                for fmt in formats:
                    try:
                        return datetime.strptime(date_str, fmt)
                    except ValueError:
                        continue
            
            logger.warning(f"无法解析RSS日期: {date_str}")
            return None
            
        except Exception as e:
            logger.error(f"RSS日期解析错误: {e}")
            return None
    
    def _categorize_rss_news(self, title: str, content: str = "", tags: List[str] = None) -> NewsCategory:
        """
        根据RSS新闻内容进行分类
        
        Args:
            title: 标题
            content: 内容
            tags: 标签列表
            
        Returns:
            新闻分类
        """
        text = (title + " " + content).lower()
        tags = tags or []
        tag_text = " ".join(tags).lower()
        
        # 财务报告类
        if any(keyword in text for keyword in ["财报", "年报", "半年报", "季报", "业绩", "盈利", "亏损"]):
            return NewsCategory.FINANCE
        
        # 监管类
        elif any(keyword in text for keyword in ["监管", "处罚", "违规", "调查", "证监会", "银保监"]):
            return NewsCategory.REGULATION
        
        # 政策类
        elif any(keyword in text for keyword in ["政策", "央行", "降准", "加息", "货币政策", "财政政策"]):
            return NewsCategory.POLICY
        
        # 市场动态类
        elif any(keyword in text for keyword in ["股市", "股价", "涨跌", "指数", "交易", "成交"]):
            return NewsCategory.MARKET
        
        # 公司治理类
        elif any(keyword in text for keyword in ["董事会", "股东大会", "高管", "任免"]):
            return NewsCategory.GOVERNANCE
        
        # 根据标签分类
        elif any(tag in tag_text for tag in ["finance", "financial", "economy", "economic"]):
            return NewsCategory.FINANCE
        elif any(tag in tag_text for tag in ["policy", "regulation", "government"]):
            return NewsCategory.POLICY
        elif any(tag in tag_text for tag in ["market", "stock", "trading"]):
            return NewsCategory.MARKET
        
        # 默认为市场动态
        else:
            return NewsCategory.MARKET
    
    async def fetch_rss_feed(self, source_key: str, source_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        获取单个RSS源的数据
        
        Args:
            source_key: RSS源键名
            source_config: RSS源配置
            
        Returns:
            新闻项目列表
        """
        news_list = []
        
        try:
            logger.info(f"[{self.name}] 开始获取RSS源: {source_config['name']}")
            
            # 发起HTTP请求获取RSS内容
            headers = {
                'User-Agent': self.ua.random,
                'Accept': 'application/rss+xml, application/xml, text/xml',
                'Accept-Language': 'zh-CN,zh;q=0.9',
            }
            
            response = await self.make_request(source_config['url'], headers=headers)
            
            if not response:
                logger.error(f"[{self.name}] RSS源请求失败: {source_config['name']}")
                return news_list
            
            # 使用feedparser解析RSS内容
            feed_content = response.text
            
            # 设置编码
            if source_config.get('encoding'):
                try:
                    feed_content = feed_content.encode('utf-8').decode(source_config['encoding'])
                except:
                    pass  # 如果编码转换失败，使用原始内容
            
            # 解析RSS
            feed = feedparser.parse(feed_content)
            
            if feed.bozo:
                logger.warning(f"[{self.name}] RSS解析警告: {source_config['name']} - {feed.bozo_exception}")
            
            logger.info(f"[{self.name}] {source_config['name']} 解析到 {len(feed.entries)} 条条目")
            
            # 处理每个RSS条目
            for entry in feed.entries:
                try:
                    # 提取基本信息
                    title = entry.get('title', '').strip()
                    if not title:
                        continue
                    
                    link = entry.get('link', '')
                    summary = entry.get('summary', '') or entry.get('description', '')
                    
                    # 解析发布时间
                    published_at = None
                    if hasattr(entry, 'published_parsed') and entry.published_parsed:
                        published_at = datetime(*entry.published_parsed[:6])
                    elif hasattr(entry, 'updated_parsed') and entry.updated_parsed:
                        published_at = datetime(*entry.updated_parsed[:6])
                    elif hasattr(entry, 'published'):
                        published_at = self._parse_rss_date(entry.published)
                    elif hasattr(entry, 'updated'):
                        published_at = self._parse_rss_date(entry.updated)
                    
                    # 提取标签
                    tags = []
                    if hasattr(entry, 'tags'):
                        tags = [tag.term for tag in entry.tags if hasattr(tag, 'term')]
                    
                    # 提取作者
                    author = entry.get('author', '')
                    
                    # 构建新闻项目
                    item = {
                        'title': title,
                        'content': summary,  # RSS通常只有摘要
                        'summary': summary[:200] + "..." if len(summary) > 200 else summary,
                        'source': NewsSource.RSS.value,
                        'source_url': link,
                        'source_id': entry.get('id', '') or link,
                        'published_at': published_at,
                        'rss_source': source_key,
                        'rss_source_name': source_config['name'],
                        'tags': tags,
                        'author': author,
                        'raw_data': {
                            'entry': dict(entry),
                            'feed_title': feed.feed.get('title', ''),
                            'feed_description': feed.feed.get('description', '')
                        }
                    }
                    
                    news_list.append(item)
                    
                except Exception as e:
                    logger.error(f"[{self.name}] 处理RSS条目失败: {e}")
                    continue
            
        except Exception as e:
            logger.error(f"[{self.name}] RSS源处理失败 {source_config['name']}: {e}")
        
        return news_list
    
    async def fetch_news_list(self, start_date: Optional[datetime] = None, 
                             end_date: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """
        获取新闻列表
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            新闻项目列表
        """
        if not start_date:
            start_date = datetime.now() - timedelta(days=1)  # RSS通常获取最近的数据
        if not end_date:
            end_date = datetime.now()
        
        all_news = []
        
        logger.info(f"[{self.name}] 开始获取RSS新闻，日期范围: {start_date.date()} 到 {end_date.date()}")
        
        # 并发获取所有RSS源
        tasks = []
        for source_key, source_config in self.rss_sources.items():
            task = self.fetch_rss_feed(source_key, source_config)
            tasks.append(task)
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                source_key = list(self.rss_sources.keys())[i]
                logger.error(f"[{self.name}] RSS源 {source_key} 获取失败: {result}")
                continue
            
            if isinstance(result, list):
                all_news.extend(result)
        
        # 按日期过滤
        filtered_news = []
        for news in all_news:
            pub_date = news.get('published_at')
            if pub_date:
                if start_date <= pub_date <= end_date:
                    filtered_news.append(news)
            else:
                # 如果没有发布时间，默认包含
                filtered_news.append(news)
        
        logger.info(f"[{self.name}] 共获取到 {len(filtered_news)} 条RSS新闻")
        return filtered_news
    
    async def fetch_news_detail(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取新闻详情
        
        对于RSS源，通常已经包含了主要内容，这里主要是进行数据处理和分类
        
        Args:
            item: 新闻项目基本信息
            
        Returns:
            完整的新闻数据
        """
        try:
            # RSS通常已经包含内容，主要进行数据处理
            title = item.get('title', '')
            content = item.get('content', '')
            summary = item.get('summary', '')
            
            # 如果有详情链接，可以尝试获取完整内容（可选）
            detail_url = item.get('source_url')
            if detail_url and len(content) < 200:  # 内容较短时尝试获取详情
                try:
                    headers = {
                        'User-Agent': self.ua.random,
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    }
                    
                    response = await self.make_request(detail_url, headers=headers)
                    if response:
                        from bs4 import BeautifulSoup
                        soup = BeautifulSoup(response.text, 'html.parser')
                        
                        # 尝试提取正文
                        article_content = self._extract_article_content(soup)
                        if article_content and len(article_content) > len(content):
                            content = article_content
                            summary = content[:200] + "..." if len(content) > 200 else content
                
                except Exception as e:
                    logger.debug(f"[{self.name}] 获取详情页失败，使用RSS内容: {e}")
            
            # 清理内容
            content = self.clean_text(content)
            summary = self.clean_text(summary)
            
            # 计算重要性评分
            importance_score = self.calculate_importance_score(item)
            
            # 提取公司和股票代码
            companies, stock_codes = self.extract_companies_and_codes(title + " " + content)
            
            # 新闻分类
            category = self._categorize_rss_news(
                title, 
                content, 
                item.get('tags', [])
            )
            
            # 生成内容哈希
            content_hash = self.generate_content_hash(title + content)
            
            # 构建完整数据
            detailed_item = {
                'title': title,
                'content': content,
                'summary': summary,
                'source': item['source'],
                'source_url': item.get('source_url', ''),
                'source_id': item.get('source_id', ''),
                'published_at': item.get('published_at'),
                'category': category.value,
                'importance_score': importance_score,
                'companies': companies,
                'stock_codes': stock_codes,
                'content_hash': content_hash,
                'word_count': len(content),
                'raw_data': {
                    'rss_source': item.get('rss_source', ''),
                    'rss_source_name': item.get('rss_source_name', ''),
                    'tags': item.get('tags', []),
                    'author': item.get('author', ''),
                    'original_data': item.get('raw_data', {})
                }
            }
            
            logger.debug(f"[{self.name}] 成功处理RSS新闻: {title[:50]}...")
            return detailed_item
            
        except Exception as e:
            logger.error(f"[{self.name}] 处理RSS新闻详情失败: {e}")
            
            # 返回基本信息
            return {
                'title': item.get('title', ''),
                'content': item.get('content', ''),
                'summary': item.get('summary', ''),
                'source': item['source'],
                'source_url': item.get('source_url', ''),
                'source_id': item.get('source_id', ''),
                'published_at': item.get('published_at'),
                'category': NewsCategory.MARKET.value,
                'importance_score': 50,
                'companies': [],
                'stock_codes': [],
                'content_hash': self.generate_content_hash(item.get('title', '')),
                'word_count': len(item.get('content', '')),
                'raw_data': item.get('raw_data', {})
            }
    
    def _extract_article_content(self, soup) -> str:
        """从网页中提取文章内容"""
        content = ""
        
        # 尝试多种内容选择器
        content_selectors = [
            'article',
            '.article-content',
            '.content',
            '.post-content',
            '.entry-content',
            '.main-content',
            '#content',
            '.text-content'
        ]
        
        for selector in content_selectors:
            content_elem = soup.select_one(selector)
            if content_elem:
                # 移除脚本和样式
                for script in content_elem(["script", "style"]):
                    script.decompose()
                content = content_elem.get_text(strip=True)
                break
        
        return content
