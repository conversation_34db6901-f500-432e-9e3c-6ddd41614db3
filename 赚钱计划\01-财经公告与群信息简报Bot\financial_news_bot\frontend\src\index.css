/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

#root {
  height: 100%;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 通用工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.full-height {
  height: 100%;
}

.full-width {
  width: 100%;
}

/* 间距工具类 */
.m-0 { margin: 0; }
.m-1 { margin: 8px; }
.m-2 { margin: 16px; }
.m-3 { margin: 24px; }
.m-4 { margin: 32px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }
.mt-4 { margin-top: 32px; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }
.mb-4 { margin-bottom: 32px; }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: 8px; }
.ml-2 { margin-left: 16px; }
.ml-3 { margin-left: 24px; }
.ml-4 { margin-left: 32px; }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: 8px; }
.mr-2 { margin-right: 16px; }
.mr-3 { margin-right: 24px; }
.mr-4 { margin-right: 32px; }

.p-0 { padding: 0; }
.p-1 { padding: 8px; }
.p-2 { padding: 16px; }
.p-3 { padding: 24px; }
.p-4 { padding: 32px; }

.pt-0 { padding-top: 0; }
.pt-1 { padding-top: 8px; }
.pt-2 { padding-top: 16px; }
.pt-3 { padding-top: 24px; }
.pt-4 { padding-top: 32px; }

.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: 8px; }
.pb-2 { padding-bottom: 16px; }
.pb-3 { padding-bottom: 24px; }
.pb-4 { padding-bottom: 32px; }

.pl-0 { padding-left: 0; }
.pl-1 { padding-left: 8px; }
.pl-2 { padding-left: 16px; }
.pl-3 { padding-left: 24px; }
.pl-4 { padding-left: 32px; }

.pr-0 { padding-right: 0; }
.pr-1 { padding-right: 8px; }
.pr-2 { padding-right: 16px; }
.pr-3 { padding-right: 24px; }
.pr-4 { padding-right: 32px; }

/* 响应式断点 - 增强版 */
/* 移动端 - 小屏幕 */
@media (max-width: 480px) {
  .mobile-xs-hidden {
    display: none !important;
  }

  .page-container {
    padding: 12px;
  }

  .page-title {
    font-size: 20px;
  }

  /* 触摸优化 */
  .ant-btn {
    min-height: 44px;
    min-width: 44px;
  }

  .ant-input {
    min-height: 44px;
  }

  .ant-select-selector {
    min-height: 44px !important;
  }

  /* 间距调整 */
  .ant-space-item {
    margin-bottom: 8px !important;
  }
}

/* 移动端 - 标准 */
@media (max-width: 576px) {
  .mobile-hidden {
    display: none !important;
  }

  .mobile-full-width {
    width: 100% !important;
  }

  .mobile-text-center {
    text-align: center !important;
  }

  /* 表格响应式 */
  .ant-table-wrapper {
    overflow-x: auto;
  }

  .ant-table {
    min-width: 600px;
  }

  /* 卡片间距 */
  .ant-card {
    margin-bottom: 12px;
  }

  /* 表单优化 */
  .ant-form-item {
    margin-bottom: 16px;
  }

  .ant-form-item-label {
    padding-bottom: 4px;
  }
}

/* 平板端 - 竖屏 */
@media (min-width: 577px) and (max-width: 768px) {
  .tablet-hidden {
    display: none !important;
  }

  .tablet-full-width {
    width: 100% !important;
  }

  .page-container {
    padding: 20px;
  }

  /* 网格布局调整 */
  .ant-col-md-12 {
    width: 100% !important;
  }

  .ant-col-md-8 {
    width: 100% !important;
  }
}

/* 平板端 - 横屏 */
@media (min-width: 769px) and (max-width: 1024px) {
  .tablet-landscape-hidden {
    display: none !important;
  }

  .page-container {
    padding: 24px;
  }
}

/* 桌面端 - 小屏 */
@media (min-width: 1025px) and (max-width: 1200px) {
  .desktop-sm-hidden {
    display: none !important;
  }
}

/* 桌面端 - 标准 */
@media (min-width: 1201px) {
  .desktop-hidden {
    display: none !important;
  }

  .desktop-large-padding {
    padding: 32px !important;
  }
}

/* 高分辨率屏幕 */
@media (min-width: 1441px) {
  .page-container {
    max-width: 1400px;
    margin: 0 auto;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .ant-btn:hover {
    transform: none;
  }

  .ant-card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border-color: #f0f0f0;
  }

  /* 增大触摸目标 */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
    padding: 12px;
  }
}

/* 横屏模式优化 */
@media (orientation: landscape) and (max-height: 600px) {
  .landscape-compact {
    padding: 8px !important;
  }

  .page-title {
    font-size: 18px;
    margin-bottom: 4px;
  }
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-in {
  animation: slideIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 自定义组件样式 */
.page-container {
  padding: 24px;
  background: #fff;
  min-height: calc(100vh - 64px);
}

.page-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 8px;
}

.page-description {
  color: #8c8c8c;
  font-size: 14px;
}

/* 卡片样式增强 */
.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.ant-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  border-color: #d9d9d9;
}

/* 表格样式增强 */
.ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
}

/* 按钮样式增强 */
.ant-btn {
  transition: all 0.3s ease;
}

.ant-btn:hover {
  transform: translateY(-1px);
}

/* 表单样式增强 */
.ant-form-item-label > label {
  font-weight: 500;
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

/* 响应式工具类 */
.responsive-grid {
  display: grid;
  gap: 16px;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

@media (max-width: 576px) {
  .responsive-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

.responsive-flex {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

@media (max-width: 576px) {
  .responsive-flex {
    flex-direction: column;
    gap: 12px;
  }
}

/* 移动端专用样式 */
.mobile-sticky-header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.mobile-bottom-action {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 12px 16px;
  border-top: 1px solid #f0f0f0;
  z-index: 100;
}

.mobile-safe-area {
  padding-bottom: env(safe-area-inset-bottom);
}

/* 滑动手势支持 */
.swipe-container {
  touch-action: pan-x;
  overflow-x: hidden;
}

.swipe-item {
  transition: transform 0.3s ease;
}

/* 无障碍优化 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .ant-card {
    border: 2px solid #000;
  }

  .ant-btn {
    border: 2px solid currentColor;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-color: #141414;
    --text-color: #ffffff;
    --border-color: #434343;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }

  .page-container {
    padding: 0;
    background: white;
  }

  .ant-card {
    box-shadow: none;
    border: 1px solid #000;
  }
}
