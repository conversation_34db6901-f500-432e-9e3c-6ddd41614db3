"""
AI服务路由
提供GLM-4.5 Flash AI服务的API接口
"""
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime

from app.services.ai_service import GLMService
from app.dependencies.auth import get_current_user
from app.models.user import User

router = APIRouter(prefix="/api/v1/ai", tags=["AI服务"])

# 全局GLM服务实例
glm_service = None

def get_glm_service() -> GLMService:
    """获取GLM服务实例"""
    global glm_service
    if glm_service is None:
        try:
            glm_service = GLMService()
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"AI服务初始化失败: {str(e)}")
    return glm_service

class SummarizeRequest(BaseModel):
    """文本摘要请求模型"""
    content: str = Field(..., description="要摘要的文本内容", max_length=10000)
    max_length: int = Field(default=200, description="摘要最大长度", ge=50, le=500)

class SummarizeResponse(BaseModel):
    """文本摘要响应模型"""
    summary: str
    original_length: int
    summary_length: int
    compression_ratio: float
    timestamp: datetime

class ClassifyRequest(BaseModel):
    """内容分类请求模型"""
    title: str = Field(..., description="文章标题", max_length=200)
    content: str = Field(..., description="文章内容", max_length=10000)

class ClassifyResponse(BaseModel):
    """内容分类响应模型"""
    category: str
    confidence: float
    subcategory: Optional[str] = None
    tags: List[str] = []
    timestamp: datetime

class KeywordsRequest(BaseModel):
    """关键词提取请求模型"""
    title: str = Field(..., description="文章标题", max_length=200)
    content: str = Field(..., description="文章内容", max_length=10000)
    max_keywords: int = Field(default=10, description="最大关键词数量", ge=1, le=20)

class KeywordsResponse(BaseModel):
    """关键词提取响应模型"""
    keywords: List[Dict[str, Any]]
    total_count: int
    timestamp: datetime

class SentimentRequest(BaseModel):
    """情感分析请求模型"""
    title: str = Field(..., description="文章标题", max_length=200)
    content: str = Field(..., description="文章内容", max_length=10000)

class SentimentResponse(BaseModel):
    """情感分析响应模型"""
    sentiment: str
    confidence: float
    score: float
    details: Dict[str, Any] = {}
    timestamp: datetime

class ContentSafetyRequest(BaseModel):
    """内容安全检测请求模型"""
    title: str = Field(..., description="文章标题", max_length=200)
    content: str = Field(..., description="文章内容", max_length=10000)

class ContentSafetyResponse(BaseModel):
    """内容安全检测响应模型"""
    is_safe: bool
    risk_level: str
    violations: List[str] = []
    suggestions: List[str] = []
    timestamp: datetime

@router.post("/summarize", response_model=SummarizeResponse)
async def summarize_text(
    request: SummarizeRequest,
    current_user: User = Depends(get_current_user)
):
    """
    文本摘要生成
    
    使用GLM-4.5 Flash模型生成文本摘要
    """
    try:
        service = get_glm_service()
        summary = await service.generate_summary(
            content=request.content,
            max_length=request.max_length
        )
        
        if not summary:
            raise HTTPException(status_code=500, detail="摘要生成失败")
        
        original_length = len(request.content)
        summary_length = len(summary)
        compression_ratio = summary_length / original_length if original_length > 0 else 0
        
        return SummarizeResponse(
            summary=summary,
            original_length=original_length,
            summary_length=summary_length,
            compression_ratio=compression_ratio,
            timestamp=datetime.now()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"摘要生成异常: {str(e)}")

@router.post("/classify", response_model=ClassifyResponse)
async def classify_content(
    request: ClassifyRequest,
    current_user: User = Depends(get_current_user)
):
    """
    内容智能分类
    
    对文章内容进行智能分类，识别主题和标签
    """
    try:
        service = get_glm_service()
        result = await service.classify_content(
            title=request.title,
            content=request.content
        )
        
        if not result:
            raise HTTPException(status_code=500, detail="内容分类失败")
        
        return ClassifyResponse(
            category=result.get('category', '其他'),
            confidence=result.get('confidence', 0.0),
            subcategory=result.get('subcategory'),
            tags=result.get('tags', []),
            timestamp=datetime.now()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"内容分类异常: {str(e)}")

@router.post("/keywords", response_model=KeywordsResponse)
async def extract_keywords(
    request: KeywordsRequest,
    current_user: User = Depends(get_current_user)
):
    """
    关键词提取
    
    从文章中提取重要的关键词和短语
    """
    try:
        service = get_glm_service()
        keywords = await service.extract_keywords(
            title=request.title,
            content=request.content
        )
        
        if not keywords:
            raise HTTPException(status_code=500, detail="关键词提取失败")
        
        # 限制返回的关键词数量
        limited_keywords = keywords[:request.max_keywords] if isinstance(keywords, list) else []
        
        return KeywordsResponse(
            keywords=limited_keywords,
            total_count=len(limited_keywords),
            timestamp=datetime.now()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"关键词提取异常: {str(e)}")

@router.post("/sentiment", response_model=SentimentResponse)
async def analyze_sentiment(
    request: SentimentRequest,
    current_user: User = Depends(get_current_user)
):
    """
    情感分析
    
    分析文章的情感倾向和情绪色彩
    """
    try:
        service = get_glm_service()
        result = await service.analyze_sentiment(
            title=request.title,
            content=request.content
        )
        
        if not result:
            raise HTTPException(status_code=500, detail="情感分析失败")
        
        return SentimentResponse(
            sentiment=result.get('sentiment', 'neutral'),
            confidence=result.get('confidence', 0.0),
            score=result.get('score', 0.0),
            details=result.get('details', {}),
            timestamp=datetime.now()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"情感分析异常: {str(e)}")

@router.post("/content-safety", response_model=ContentSafetyResponse)
async def check_content_safety(
    request: ContentSafetyRequest,
    current_user: User = Depends(get_current_user)
):
    """
    内容安全检测
    
    检测内容是否包含不当信息
    """
    try:
        service = get_glm_service()
        result = await service.check_content_safety(
            title=request.title,
            content=request.content
        )
        
        if result is None:
            raise HTTPException(status_code=500, detail="内容安全检测失败")
        
        return ContentSafetyResponse(
            is_safe=result.get('is_safe', True),
            risk_level=result.get('risk_level', 'low'),
            violations=result.get('violations', []),
            suggestions=result.get('suggestions', []),
            timestamp=datetime.now()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"内容安全检测异常: {str(e)}")

@router.get("/status")
async def get_ai_service_status(current_user: User = Depends(get_current_user)):
    """
    获取AI服务状态
    
    检查AI服务是否正常运行
    """
    try:
        service = get_glm_service()
        
        # 执行一个简单的测试请求
        test_result = await service.generate_summary(
            content="这是一个测试文本，用于检查AI服务是否正常工作。",
            max_length=50
        )
        
        return {
            "status": "healthy" if test_result else "unhealthy",
            "model": service.model,
            "max_tokens": service.max_tokens,
            "temperature": service.temperature,
            "test_result": bool(test_result),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

@router.get("/capabilities")
async def get_ai_capabilities(current_user: User = Depends(get_current_user)):
    """
    获取AI服务能力列表
    
    返回当前支持的AI功能
    """
    return {
        "capabilities": [
            {
                "name": "文本摘要",
                "endpoint": "/api/v1/ai/summarize",
                "description": "生成文本摘要，支持自定义长度"
            },
            {
                "name": "内容分类",
                "endpoint": "/api/v1/ai/classify",
                "description": "智能分类文章内容，识别主题和标签"
            },
            {
                "name": "关键词提取",
                "endpoint": "/api/v1/ai/keywords",
                "description": "提取文章中的重要关键词"
            },
            {
                "name": "情感分析",
                "endpoint": "/api/v1/ai/sentiment",
                "description": "分析文章的情感倾向"
            },
            {
                "name": "内容安全检测",
                "endpoint": "/api/v1/ai/content-safety",
                "description": "检测内容是否包含不当信息"
            }
        ],
        "model_info": {
            "name": "GLM-4.5 Flash",
            "provider": "智谱AI",
            "version": "4.5"
        }
    }
