"""
统一文本处理模块
解决jieba重复初始化和停用词重复定义问题
"""
import jieba
import re
from typing import List, Set, Dict, Any
from functools import lru_cache
import logging

logger = logging.getLogger(__name__)


class TextProcessor:
    """统一文本处理类，解决jieba重复初始化和停用词重复定义问题"""
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self._initialize_jieba()
            self._initialize_stopwords()
            TextProcessor._initialized = True
            logger.info("TextProcessor 初始化完成")
    
    def _initialize_jieba(self):
        """初始化jieba分词器"""
        jieba.initialize()
        logger.info("jieba 分词器初始化完成")
    
    def _initialize_stopwords(self):
        """初始化停用词集合"""
        self.stopwords = {
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', 
            '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', 
            '自己', '这', '那', '它', '他', '她', '们', '这个', '那个', '什么', '怎么',
            '为什么', '因为', '所以', '但是', '然后', '如果', '虽然', '可以', '能够',
            '应该', '可能', '或者', '而且', '并且', '以及', '以上', '以下', '之前',
            '之后', '当时', '现在', '将来', '过去', '今天', '明天', '昨天'
        }
    
    def extract_keywords(self, text: str, max_keywords: int = 10) -> List[str]:
        """
        统一的关键词提取方法
        
        Args:
            text: 输入文本
            max_keywords: 最大关键词数量
            
        Returns:
            关键词列表
        """
        if not text:
            return []
        
        try:
            # 使用jieba分词
            words = jieba.lcut(text)
            
            # 过滤停用词和短词
            filtered_words = [
                word for word in words 
                if len(word) > 1 and word not in self.stopwords and word.strip()
            ]
            
            # 统计词频
            word_freq = {}
            for word in filtered_words:
                word_freq[word] = word_freq.get(word, 0) + 1
            
            # 按频率排序并返回前N个
            sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
            return [word for word, freq in sorted_words[:max_keywords]]
            
        except Exception as e:
            logger.error(f"关键词提取失败: {str(e)}")
            return []
    
    def clean_text(self, text: str) -> str:
        """
        统一的文本清理方法
        
        Args:
            text: 输入文本
            
        Returns:
            清理后的文本
        """
        if not text:
            return ""
        
        try:
            # 移除多余的空白字符
            text = re.sub(r'\s+', ' ', text)
            
            # 移除特殊字符，保留中文、英文、数字和基本标点
            text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s\.,;:!?()（）【】""''、。，；：！？]', '', text)
            
            return text.strip()
            
        except Exception as e:
            logger.error(f"文本清理失败: {str(e)}")
            return text
    
    def normalize_text(self, text: str) -> str:
        """
        标准化文本（用于去重和相似度计算）
        
        Args:
            text: 输入文本
            
        Returns:
            标准化后的文本
        """
        if not text:
            return ""
        
        try:
            # 转换为小写
            text = text.lower()
            
            # 移除标点符号
            text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s]', '', text)
            
            # 移除多余空格
            text = re.sub(r'\s+', ' ', text)
            
            return text.strip()
            
        except Exception as e:
            logger.error(f"文本标准化失败: {str(e)}")
            return text
    
    def segment_text(self, text: str) -> List[str]:
        """
        文本分词
        
        Args:
            text: 输入文本
            
        Returns:
            分词结果列表
        """
        if not text:
            return []
        
        try:
            return jieba.lcut(text)
        except Exception as e:
            logger.error(f"文本分词失败: {str(e)}")
            return []
    
    def calculate_text_similarity(self, text1: str, text2: str) -> float:
        """
        计算文本相似度（基于关键词Jaccard相似度）
        
        Args:
            text1: 文本1
            text2: 文本2
            
        Returns:
            相似度（0-1）
        """
        if not text1 or not text2:
            return 0.0
        
        try:
            # 提取关键词
            keywords1 = set(self.extract_keywords(text1, 20))
            keywords2 = set(self.extract_keywords(text2, 20))
            
            if not keywords1 or not keywords2:
                return 0.0
            
            # 计算Jaccard相似度
            intersection = len(keywords1.intersection(keywords2))
            union = len(keywords1.union(keywords2))
            
            return intersection / union if union > 0 else 0.0
            
        except Exception as e:
            logger.error(f"相似度计算失败: {str(e)}")
            return 0.0


# 全局实例
text_processor = TextProcessor()


def get_text_processor() -> TextProcessor:
    """获取文本处理器实例"""
    return text_processor
