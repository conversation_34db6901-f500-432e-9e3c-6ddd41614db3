"""
推送日志模型
用于记录推送消息的发送状态和统计信息
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, JSON, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum

from ..database import Base


class PushStatus(enum.Enum):
    """推送状态枚举"""
    SUCCESS = "success"
    FAILED = "failed"
    RETRY = "retry"


class PushLog(Base):
    """推送日志模型"""
    __tablename__ = "push_logs"

    id = Column(Integer, primary_key=True, index=True)

    # 关联信息
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, comment="用户ID")
    subscription_id = Column(Integer, ForeignKey("subscriptions.id", ondelete="SET NULL"), nullable=True, comment="订阅ID")
    news_id = Column(Integer, ForeignKey("news.id", ondelete="SET NULL"), nullable=True, comment="新闻ID")

    # 推送信息（根据实际使用调整字段）
    channel = Column(String(50), nullable=True, comment="推送渠道")
    status = Column(String(20), nullable=True, comment="推送状态")
    error_message = Column(Text, nullable=True, comment="错误信息")

    # 推送统计字段（根据push_tasks.py中的使用）
    news_count = Column(Integer, default=0, comment="新闻数量")
    push_channels = Column(JSON, nullable=True, comment="推送渠道列表")
    success_count = Column(Integer, default=0, comment="成功数量")
    total_count = Column(Integer, default=0, comment="总数量")
    is_urgent = Column(Boolean, default=False, comment="是否紧急推送")
    push_result = Column(JSON, nullable=True, comment="推送结果详情")

    # 时间信息
    sent_at = Column(DateTime, default=func.now(), comment="发送时间")
    opened_at = Column(DateTime, nullable=True, comment="打开时间")
    clicked_at = Column(DateTime, nullable=True, comment="点击时间")
    created_at = Column(DateTime, default=func.now(), comment="创建时间")

    # 关系定义
    user = relationship("User", back_populates="push_logs")
    subscription = relationship("Subscription", back_populates="push_logs")
    news = relationship("News", back_populates="push_logs")

    def __repr__(self):
        return f"<PushLog(id={self.id}, user_id={self.user_id}, status='{self.status}')>"
