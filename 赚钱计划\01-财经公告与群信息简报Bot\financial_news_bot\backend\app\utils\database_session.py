"""
统一数据库会话管理
解决数据库连接管理混乱问题
"""
from contextlib import contextmanager
from typing import Generator
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
import logging

from ..database import SessionLocal, get_db

logger = logging.getLogger(__name__)


class DatabaseSessionManager:
    """统一数据库会话管理器"""
    
    @staticmethod
    @contextmanager
    def get_session() -> Generator[Session, None, None]:
        """
        获取数据库会话的上下文管理器
        自动处理会话的创建、提交和关闭
        """
        db = SessionLocal()
        try:
            yield db
            db.commit()
        except SQLAlchemyError as e:
            db.rollback()
            logger.error(f"数据库操作失败: {str(e)}")
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"未知错误: {str(e)}")
            raise
        finally:
            db.close()
    
    @staticmethod
    def get_session_for_dependency() -> Generator[Session, None, None]:
        """
        为FastAPI依赖注入提供数据库会话
        """
        return get_db()
    
    @staticmethod
    @contextmanager
    def get_readonly_session() -> Generator[Session, None, None]:
        """
        获取只读数据库会话
        用于查询操作，不会自动提交
        """
        db = SessionLocal()
        try:
            yield db
        except Exception as e:
            logger.error(f"只读会话错误: {str(e)}")
            raise
        finally:
            db.close()
    
    @staticmethod
    @contextmanager
    def get_transaction_session() -> Generator[Session, None, None]:
        """
        获取事务会话
        支持手动控制事务提交和回滚
        """
        db = SessionLocal()
        transaction = db.begin()
        try:
            yield db
            transaction.commit()
        except Exception as e:
            transaction.rollback()
            logger.error(f"事务执行失败: {str(e)}")
            raise
        finally:
            db.close()


# 便捷函数
def with_db_session(func):
    """
    装饰器：为函数提供数据库会话
    """
    def wrapper(*args, **kwargs):
        with DatabaseSessionManager.get_session() as db:
            return func(db, *args, **kwargs)
    return wrapper


def with_readonly_session(func):
    """
    装饰器：为函数提供只读数据库会话
    """
    def wrapper(*args, **kwargs):
        with DatabaseSessionManager.get_readonly_session() as db:
            return func(db, *args, **kwargs)
    return wrapper


def with_transaction_session(func):
    """
    装饰器：为函数提供事务会话
    """
    def wrapper(*args, **kwargs):
        with DatabaseSessionManager.get_transaction_session() as db:
            return func(db, *args, **kwargs)
    return wrapper


# 全局实例
db_session_manager = DatabaseSessionManager()


# 向后兼容的函数
def get_database_session() -> Generator[Session, None, None]:
    """获取数据库会话（向后兼容）"""
    return DatabaseSessionManager.get_session()


def get_db_session() -> Generator[Session, None, None]:
    """获取数据库会话（简化版）"""
    return DatabaseSessionManager.get_session()


# 使用示例：
"""
# 方式1：使用上下文管理器
with DatabaseSessionManager.get_session() as db:
    user = db.query(User).filter(User.id == user_id).first()
    # 自动提交和关闭

# 方式2：使用装饰器
@with_db_session
def create_user(db: Session, user_data: dict):
    user = User(**user_data)
    db.add(user)
    return user

# 方式3：只读操作
with DatabaseSessionManager.get_readonly_session() as db:
    users = db.query(User).all()

# 方式4：事务控制
with DatabaseSessionManager.get_transaction_session() as db:
    # 复杂的多表操作
    user = User(**user_data)
    db.add(user)
    db.flush()  # 获取ID但不提交
    
    profile = UserProfile(user_id=user.id, **profile_data)
    db.add(profile)
    # 自动提交所有操作
"""
