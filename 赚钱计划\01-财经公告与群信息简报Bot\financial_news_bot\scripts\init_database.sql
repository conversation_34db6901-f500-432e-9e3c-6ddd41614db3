-- 财经新闻Bot数据库初始化脚本
-- 此脚本在MySQL容器启动时自动执行

-- 设置字符集和排序规则
SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS `financial_news_bot` 
    CHARACTER SET utf8mb4 
    COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE `financial_news_bot`;

-- 创建用户（如果不存在）
CREATE USER IF NOT EXISTS 'velen'@'%' IDENTIFIED BY 'Lovejq4ever.';

-- 授予权限
GRANT ALL PRIVILEGES ON `financial_news_bot`.* TO 'velen'@'%';

-- 刷新权限
FLUSH PRIVILEGES;

-- 设置时区
SET time_zone = '+08:00';

-- 优化配置
SET GLOBAL innodb_buffer_pool_size = 134217728; -- 128MB
SET GLOBAL max_connections = 200;
SET GLOBAL wait_timeout = 28800;
SET GLOBAL interactive_timeout = 28800;

-- 记录初始化完成
INSERT INTO mysql.general_log (event_time, user_host, thread_id, server_id, command_type, argument) 
VALUES (NOW(), 'init_script', 0, 1, 'Query', 'Database initialization completed') 
ON DUPLICATE KEY UPDATE argument = 'Database initialization completed';
