import { chromium, FullConfig } from '@playwright/test';

/**
 * 全局测试设置
 * 在所有测试开始前执行
 */
async function globalSetup(config: FullConfig) {
  console.log('🚀 开始端到端测试全局设置...');

  const browser = await chromium.launch();
  const page = await browser.newPage();

  try {
    // 等待应用启动
    console.log('⏳ 等待应用启动...');
    await page.goto(config.projects[0].use.baseURL || 'http://localhost:3000');
    await page.waitForLoadState('networkidle');

    // 检查应用是否正常运行
    const title = await page.title();
    console.log(`📱 应用标题: ${title}`);

    // 创建测试用户（如果需要）
    await setupTestData(page);

    // 保存认证状态
    await saveAuthState(page);

    console.log('✅ 全局设置完成');
  } catch (error) {
    console.error('❌ 全局设置失败:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

/**
 * 设置测试数据
 */
async function setupTestData(page: any) {
  console.log('📊 设置测试数据...');

  try {
    // 检查是否已有测试用户
    const response = await page.request.get('/api/v1/auth/test-user-exists');
    const { exists } = await response.json();

    if (!exists) {
      // 创建测试用户
      await page.request.post('/api/v1/auth/register', {
        data: {
          username: 'e2e_test_user',
          email: '<EMAIL>',
          full_name: 'E2E Test User',
          password: 'test123456'
        }
      });
      console.log('👤 测试用户创建成功');
    } else {
      console.log('👤 测试用户已存在');
    }

    // 创建测试新闻数据
    await createTestNews(page);

  } catch (error) {
    console.warn('⚠️ 测试数据设置失败:', error);
  }
}

/**
 * 创建测试新闻
 */
async function createTestNews(page: any) {
  const testNews = [
    {
      title: '央行发布最新货币政策报告',
      content: '中国人民银行今日发布了最新的货币政策执行报告...',
      summary: '央行发布货币政策报告，强调稳健货币政策',
      source: '央行官网',
      url: 'https://test.example.com/news/1',
      category: 'monetary_policy',
      tags: ['央行', '货币政策', '金融'],
      published_at: new Date().toISOString()
    },
    {
      title: '股市今日大涨创新高',
      content: '今日A股市场表现强劲，上证指数收盘上涨3.2%...',
      summary: '股市大涨，投资者信心增强',
      source: '财经网',
      url: 'https://test.example.com/news/2',
      category: 'stock_market',
      tags: ['股市', '上证指数', '投资'],
      published_at: new Date().toISOString()
    },
    {
      title: '银行业监管新规正式实施',
      content: '银保监会发布的银行业监管新规今日正式实施...',
      summary: '银行业监管新规实施，加强风险管控',
      source: '监管部门',
      url: 'https://test.example.com/news/3',
      category: 'banking',
      tags: ['银行', '监管', '风险'],
      published_at: new Date().toISOString()
    }
  ];

  for (const news of testNews) {
    try {
      await page.request.post('/api/v1/admin/news', {
        data: news,
        headers: {
          'Authorization': 'Bearer admin_test_token'
        }
      });
    } catch (error) {
      console.warn('创建测试新闻失败:', error);
    }
  }

  console.log('📰 测试新闻创建完成');
}

/**
 * 保存认证状态
 */
async function saveAuthState(page: any) {
  try {
    // 登录测试用户
    await page.goto('/auth/login');
    await page.fill('[data-testid="username-input"]', 'e2e_test_user');
    await page.fill('[data-testid="password-input"]', 'test123456');
    await page.click('[data-testid="login-button"]');
    
    // 等待登录完成
    await page.waitForURL('/dashboard');
    
    // 保存认证状态
    await page.context().storageState({ path: 'tests/e2e/auth.json' });
    console.log('🔐 认证状态已保存');
  } catch (error) {
    console.warn('⚠️ 保存认证状态失败:', error);
  }
}

export default globalSetup;
