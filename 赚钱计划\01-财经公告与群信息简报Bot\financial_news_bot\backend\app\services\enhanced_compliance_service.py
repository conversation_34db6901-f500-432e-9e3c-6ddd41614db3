"""
增强版合规检查服务
集成智谱AI官方内容安全API和自定义合规规则
"""
import asyncio
import logging
import re
from typing import Dict, List, Optional, Any, Set, Tuple
from dataclasses import dataclass
from datetime import datetime
from enum import Enum

from app.config import settings
from app.services.ai_service import get_glm_service

logger = logging.getLogger(__name__)


class RiskLevel(Enum):
    """风险等级枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ViolationType(Enum):
    """违规类型枚举"""
    INVESTMENT_ADVICE = "investment_advice"     # 投资建议
    SENSITIVE_WORD = "sensitive_word"           # 敏感词
    FINANCIAL_PROMISE = "financial_promise"     # 收益承诺
    INSIDER_INFO = "insider_info"               # 内幕信息
    MARKET_MANIPULATION = "market_manipulation" # 市场操纵
    ILLEGAL_CONTENT = "illegal_content"         # 违法内容
    OFFICIAL_FLAGGED = "official_flagged"       # 官方API标记


@dataclass
class ComplianceResult:
    """合规检查结果"""
    is_compliant: bool
    risk_level: RiskLevel
    violations: List[Dict[str, Any]]
    official_safety_result: Optional[Dict[str, Any]]
    filtered_content: str
    warnings: List[str]
    risk_tags: List[str]
    compliance_score: float
    suggestions: List[str]


class EnhancedComplianceService:
    """增强版合规检查服务"""
    
    def __init__(self):
        """初始化增强合规服务"""
        self.glm_service = get_glm_service()
        self.sensitive_words = self._load_sensitive_words()
        self.investment_patterns = self._load_investment_patterns()
        self.risk_keywords = self._load_risk_keywords()
        self.disclaimer_templates = self._load_disclaimer_templates()
        
        logger.info("增强版合规检查服务初始化完成")
    
    def _load_sensitive_words(self) -> Dict[str, Set[str]]:
        """加载敏感词库"""
        return {
            "forbidden": {
                # 禁用词 - 直接过滤
                "内幕消息", "内幕信息", "小道消息", "绝密信息",
                "操纵股价", "坐庄", "拉升", "砸盘", "割韭菜",
                "黑幕", "暗箱操作", "利益输送", "关联交易造假",
                "财务造假", "虚假陈述", "欺诈发行", "信息披露违规"
            },
            
            "warning": {
                # 警示词 - 标注提醒
                "重大利好", "重大利空", "爆发式增长", "暴涨", "暴跌",
                "翻倍", "腰斩", "崩盘", "抄底", "逃顶",
                "牛市", "熊市", "泡沫", "风口", "热点"
            },
            
            "investment": {
                # 投资建议类
                "建议买入", "建议卖出", "强烈推荐", "重点关注",
                "推荐持有", "建议减持", "建议增持", "值得投资",
                "必涨", "必跌", "稳赚不赔", "保证收益",
                "预期收益", "目标价", "止损位", "买点", "卖点"
            },
            
            "financial_promise": {
                # 收益承诺类
                "保证收益", "稳赚不赔", "零风险", "无风险套利",
                "年化收益率", "固定收益", "保本保息", "承诺回报"
            }
        }
    
    def _load_investment_patterns(self) -> List[str]:
        """加载投资建议模式"""
        return [
            r'建议.*?买入',
            r'建议.*?卖出',
            r'推荐.*?持有',
            r'目标价.*?\d+',
            r'预期.*?涨.*?\d+%',
            r'预期.*?跌.*?\d+%',
            r'收益率.*?\d+%',
            r'回报率.*?\d+%',
            r'投资.*?机会',
            r'投资.*?价值',
            r'买入.*?时机',
            r'卖出.*?时机',
            r'保证.*?收益.*?\d+%',
            r'年化.*?\d+%.*?收益'
        ]
    
    def _load_risk_keywords(self) -> Dict[str, RiskLevel]:
        """加载风险关键词及其等级"""
        return {
            # 严重风险关键词
            "退市": RiskLevel.CRITICAL,
            "破产": RiskLevel.CRITICAL,
            "违约": RiskLevel.CRITICAL,
            "造假": RiskLevel.CRITICAL,
            "欺诈": RiskLevel.CRITICAL,
            "立案": RiskLevel.CRITICAL,
            "司法冻结": RiskLevel.CRITICAL,
            "内幕交易": RiskLevel.CRITICAL,
            
            # 高风险关键词
            "业绩预警": RiskLevel.HIGH,
            "重大亏损": RiskLevel.HIGH,
            "债务危机": RiskLevel.HIGH,
            "监管处罚": RiskLevel.HIGH,
            "违规操作": RiskLevel.HIGH,
            
            # 中风险关键词
            "亏损": RiskLevel.MEDIUM,
            "减持": RiskLevel.MEDIUM,
            "停牌": RiskLevel.MEDIUM,
            "重组": RiskLevel.MEDIUM,
            "诉讼": RiskLevel.MEDIUM,
            
            # 低风险关键词
            "波动": RiskLevel.LOW,
            "调整": RiskLevel.LOW,
            "回调": RiskLevel.LOW,
            "震荡": RiskLevel.LOW
        }
    
    def _load_disclaimer_templates(self) -> Dict[str, str]:
        """加载免责声明模板"""
        return {
            "investment_advice": """
【风险提示】本内容不构成投资建议，投资有风险，入市需谨慎。
请投资者根据自身风险承受能力谨慎决策，并自行承担投资风险。
""",
            "high_risk": """
【重大风险提示】本内容涉及重大风险事项，请投资者特别关注相关风险，
谨慎做出投资决策。
""",
            "critical_risk": """
【严重风险警告】本内容涉及严重风险事项，可能对投资者造成重大损失，
请务必谨慎对待，建议咨询专业投资顾问。
""",
            "general": """
【免责声明】本内容仅供参考，不构成任何投资建议。
投资者据此操作，风险自担。
"""
        }
    
    async def comprehensive_check(self, title: str, content: str) -> ComplianceResult:
        """
        综合合规检查
        
        Args:
            title: 文章标题
            content: 文章内容
            
        Returns:
            综合合规检查结果
        """
        text = f"{title} {content}"
        
        # 并行执行多种检查
        tasks = [
            self._check_official_safety(text),
            self._check_sensitive_words(text),
            self._check_investment_advice(text),
            self._assess_risk_level(text)
        ]
        
        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            official_safety = results[0] if not isinstance(results[0], Exception) else None
            sensitive_words = results[1] if not isinstance(results[1], Exception) else []
            investment_advice = results[2] if not isinstance(results[2], Exception) else False
            risk_level = results[3] if not isinstance(results[3], Exception) else RiskLevel.LOW
            
            # 整合所有违规信息
            violations = []
            
            # 官方安全检测违规
            if official_safety and official_safety.get('flagged'):
                violations.append({
                    "type": ViolationType.OFFICIAL_FLAGGED.value,
                    "severity": "high",
                    "details": official_safety.get('categories', {}),
                    "source": "official_api"
                })
            
            # 敏感词违规
            for word_info in sensitive_words:
                violations.append({
                    "type": ViolationType.SENSITIVE_WORD.value,
                    "severity": word_info.get("severity", "medium"),
                    "details": word_info,
                    "source": "custom_rules"
                })
            
            # 投资建议违规
            if investment_advice:
                violations.append({
                    "type": ViolationType.INVESTMENT_ADVICE.value,
                    "severity": "high",
                    "details": {"detected": True},
                    "source": "pattern_matching"
                })
            
            # 内容过滤
            filtered_content = self._filter_content(content, sensitive_words)
            
            # 生成警告和建议
            warnings = self._generate_warnings(violations, official_safety)
            suggestions = self._generate_suggestions(violations, risk_level)
            risk_tags = self._generate_risk_tags(text, risk_level, violations)
            
            # 计算合规得分
            compliance_score = self._calculate_compliance_score(
                violations, official_safety, risk_level
            )
            
            # 判断是否合规
            is_compliant = self._is_content_compliant(violations, risk_level)
            
            return ComplianceResult(
                is_compliant=is_compliant,
                risk_level=risk_level,
                violations=violations,
                official_safety_result=official_safety,
                filtered_content=filtered_content,
                warnings=warnings,
                risk_tags=risk_tags,
                compliance_score=compliance_score,
                suggestions=suggestions
            )
            
        except Exception as e:
            logger.error(f"综合合规检查失败: {str(e)}")
            # 返回保守的结果
            return ComplianceResult(
                is_compliant=False,
                risk_level=RiskLevel.HIGH,
                violations=[{
                    "type": "system_error",
                    "severity": "high",
                    "details": {"error": str(e)},
                    "source": "system"
                }],
                official_safety_result=None,
                filtered_content=content,
                warnings=["系统检查失败，建议人工审核"],
                risk_tags=["系统异常"],
                compliance_score=0.0,
                suggestions=["请联系技术支持"]
            )
    
    async def _check_official_safety(self, content: str) -> Optional[Dict[str, Any]]:
        """使用官方API检查内容安全"""
        try:
            return await self.glm_service.check_content_safety(content)
        except Exception as e:
            logger.error(f"官方安全检查失败: {str(e)}")
            return None
    
    def _check_sensitive_words(self, text: str) -> List[Dict[str, Any]]:
        """检测敏感词"""
        detected_words = []
        
        for category, words in self.sensitive_words.items():
            for word in words:
                if word in text:
                    count = text.count(word)
                    positions = []
                    start = 0
                    while True:
                        pos = text.find(word, start)
                        if pos == -1:
                            break
                        positions.append(pos)
                        start = pos + 1
                    
                    severity = "high" if category == "forbidden" else "medium"
                    if category == "warning":
                        severity = "low"
                    
                    detected_words.append({
                        "word": word,
                        "category": category,
                        "count": count,
                        "positions": positions,
                        "severity": severity
                    })
        
        return detected_words
    
    def _check_investment_advice(self, text: str) -> bool:
        """检测投资建议"""
        # 检查投资建议模式
        for pattern in self.investment_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                return True
        
        # 检查投资建议类敏感词
        investment_words = self.sensitive_words.get("investment", set())
        for word in investment_words:
            if word in text:
                return True
        
        return False
    
    def _assess_risk_level(self, text: str) -> RiskLevel:
        """评估风险等级"""
        max_risk = RiskLevel.LOW
        
        for keyword, risk_level in self.risk_keywords.items():
            if keyword in text:
                if risk_level == RiskLevel.CRITICAL:
                    return RiskLevel.CRITICAL
                elif risk_level == RiskLevel.HIGH and max_risk != RiskLevel.CRITICAL:
                    max_risk = RiskLevel.HIGH
                elif risk_level == RiskLevel.MEDIUM and max_risk == RiskLevel.LOW:
                    max_risk = RiskLevel.MEDIUM
        
        return max_risk

    def _filter_content(self, content: str, sensitive_words: List[Dict[str, Any]]) -> str:
        """过滤内容中的敏感词"""
        filtered_content = content

        # 过滤禁用词
        for word_info in sensitive_words:
            if word_info.get("category") == "forbidden":
                word = word_info["word"]
                replacement = "*" * len(word)
                filtered_content = filtered_content.replace(word, replacement)

        return filtered_content

    def _generate_warnings(self, violations: List[Dict[str, Any]],
                          official_safety: Optional[Dict[str, Any]]) -> List[str]:
        """生成警告信息"""
        warnings = []

        # 官方API警告
        if official_safety and official_safety.get('flagged'):
            warnings.append("内容被官方安全API标记为不当内容")

        # 违规类型统计
        violation_counts = {}
        for violation in violations:
            v_type = violation.get("type", "unknown")
            violation_counts[v_type] = violation_counts.get(v_type, 0) + 1

        for v_type, count in violation_counts.items():
            if v_type == ViolationType.SENSITIVE_WORD.value:
                warnings.append(f"检测到{count}个敏感词，已自动过滤")
            elif v_type == ViolationType.INVESTMENT_ADVICE.value:
                warnings.append("检测到疑似投资建议内容，建议添加风险提示")
            elif v_type == ViolationType.FINANCIAL_PROMISE.value:
                warnings.append("检测到收益承诺内容，存在合规风险")

        return warnings

    def _generate_suggestions(self, violations: List[Dict[str, Any]],
                            risk_level: RiskLevel) -> List[str]:
        """生成改进建议"""
        suggestions = []

        if risk_level == RiskLevel.CRITICAL:
            suggestions.append("内容存在严重合规风险，建议重新编写")
            suggestions.append("请咨询法务部门确认内容合规性")
        elif risk_level == RiskLevel.HIGH:
            suggestions.append("建议添加风险提示和免责声明")
            suggestions.append("考虑调整表述方式，避免直接的投资建议")
        elif risk_level == RiskLevel.MEDIUM:
            suggestions.append("建议添加适当的风险提示")
            suggestions.append("注意用词的客观性和中性化")

        # 针对特定违规类型的建议
        violation_types = [v.get("type") for v in violations]

        if ViolationType.INVESTMENT_ADVICE.value in violation_types:
            suggestions.append("将投资建议改为客观分析和信息披露")
            suggestions.append("添加'不构成投资建议'的免责声明")

        if ViolationType.SENSITIVE_WORD.value in violation_types:
            suggestions.append("使用更中性和客观的表述")
            suggestions.append("避免使用可能误导投资者的词汇")

        return suggestions

    def _generate_risk_tags(self, text: str, risk_level: RiskLevel,
                           violations: List[Dict[str, Any]]) -> List[str]:
        """生成风险标签"""
        tags = []

        # 基于风险等级添加标签
        if risk_level == RiskLevel.CRITICAL:
            tags.append("严重风险")
        elif risk_level == RiskLevel.HIGH:
            tags.append("高风险")
        elif risk_level == RiskLevel.MEDIUM:
            tags.append("中等风险")

        # 基于违规类型添加标签
        violation_types = [v.get("type") for v in violations]

        if ViolationType.INVESTMENT_ADVICE.value in violation_types:
            tags.append("投资建议")

        if ViolationType.SENSITIVE_WORD.value in violation_types:
            tags.append("敏感词汇")

        if ViolationType.OFFICIAL_FLAGGED.value in violation_types:
            tags.append("官方标记")

        # 基于内容特征添加标签
        if any(keyword in text for keyword in ["业绩预警", "亏损", "退市"]):
            tags.append("业绩风险")

        if any(keyword in text for keyword in ["违规", "处罚", "立案"]):
            tags.append("合规风险")

        if any(keyword in text for keyword in ["重组", "并购", "收购"]):
            tags.append("重组风险")

        return list(set(tags))  # 去重

    def _calculate_compliance_score(self, violations: List[Dict[str, Any]],
                                   official_safety: Optional[Dict[str, Any]],
                                   risk_level: RiskLevel) -> float:
        """计算合规得分"""
        score = 100.0

        # 官方API扣分
        if official_safety and official_safety.get('flagged'):
            score -= 30.0

        # 违规扣分
        for violation in violations:
            severity = violation.get("severity", "medium")
            if severity == "high":
                score -= 20.0
            elif severity == "medium":
                score -= 10.0
            elif severity == "low":
                score -= 5.0

        # 风险等级扣分
        if risk_level == RiskLevel.CRITICAL:
            score -= 40.0
        elif risk_level == RiskLevel.HIGH:
            score -= 25.0
        elif risk_level == RiskLevel.MEDIUM:
            score -= 10.0

        return max(0.0, min(100.0, score))

    def _is_content_compliant(self, violations: List[Dict[str, Any]],
                             risk_level: RiskLevel) -> bool:
        """判断内容是否合规"""
        # 严重风险直接不合规
        if risk_level == RiskLevel.CRITICAL:
            return False

        # 检查是否有高严重性违规
        for violation in violations:
            if violation.get("severity") == "high":
                v_type = violation.get("type")
                # 官方API标记或禁用词直接不合规
                if v_type in [ViolationType.OFFICIAL_FLAGGED.value,
                             ViolationType.SENSITIVE_WORD.value]:
                    if violation.get("details", {}).get("category") == "forbidden":
                        return False

        return True

    def add_disclaimer(self, content: str, risk_level: RiskLevel,
                      violations: List[Dict[str, Any]]) -> str:
        """添加免责声明"""
        violation_types = [v.get("type") for v in violations]

        if risk_level == RiskLevel.CRITICAL:
            disclaimer = self.disclaimer_templates["critical_risk"]
        elif ViolationType.INVESTMENT_ADVICE.value in violation_types:
            disclaimer = self.disclaimer_templates["investment_advice"]
        elif risk_level == RiskLevel.HIGH:
            disclaimer = self.disclaimer_templates["high_risk"]
        else:
            disclaimer = self.disclaimer_templates["general"]

        return f"{content}\n\n{disclaimer}"

    def get_compliance_statistics(self) -> Dict[str, Any]:
        """获取合规检查统计信息"""
        try:
            from datetime import datetime, timedelta
            from ..redis_client import redis_client

            # 从Redis缓存中获取真实的统计数据
            stats = {
                "total_checks": 0,
                "violations_found": 0,
                "compliance_rate": 0.0,
                "risk_distribution": {"low": 0, "medium": 0, "high": 0, "critical": 0},
                "violation_types": {"sensitive_word": 0, "investment_advice": 0, "financial_promise": 0, "insider_info": 0},
                "last_updated": datetime.utcnow().isoformat(),
                "service_status": "active"
            }

            if redis_client.is_connected():
                # 获取今日统计数据
                today_key = f"compliance_stats:{datetime.now().strftime('%Y%m%d')}"
                today_stats = redis_client.redis_client.hgetall(today_key)

                if today_stats:
                    stats["total_checks"] = int(today_stats.get(b'total_checks', 0))
                    stats["violations_found"] = int(today_stats.get(b'violations_found', 0))

                    # 计算合规率
                    if stats["total_checks"] > 0:
                        stats["compliance_rate"] = round(
                            ((stats["total_checks"] - stats["violations_found"]) / stats["total_checks"]) * 100, 2
                        )

                    # 获取风险分布
                    for risk_level in ["low", "medium", "high", "critical"]:
                        key = f'risk_{risk_level}'.encode()
                        stats["risk_distribution"][risk_level] = int(today_stats.get(key, 0))

                    # 获取违规类型分布
                    for violation_type in ["sensitive_word", "investment_advice", "financial_promise", "insider_info"]:
                        key = f'violation_{violation_type}'.encode()
                        stats["violation_types"][violation_type] = int(today_stats.get(key, 0))

                # 获取历史7天的统计趋势
                weekly_stats = []
                for i in range(7):
                    date = datetime.now() - timedelta(days=i)
                    day_key = f"compliance_stats:{date.strftime('%Y%m%d')}"
                    day_data = redis_client.redis_client.hgetall(day_key)

                    if day_data:
                        daily_total = int(day_data.get(b'total_checks', 0))
                        daily_violations = int(day_data.get(b'violations_found', 0))
                        daily_rate = round(((daily_total - daily_violations) / daily_total) * 100, 2) if daily_total > 0 else 100.0

                        weekly_stats.append({
                            "date": date.strftime('%Y-%m-%d'),
                            "total_checks": daily_total,
                            "violations": daily_violations,
                            "compliance_rate": daily_rate
                        })

                stats["weekly_trend"] = weekly_stats

            # 添加服务配置信息
            stats["sensitive_words_count"] = len(self.sensitive_words)
            stats["service_version"] = "enhanced_v1.0"

            return stats

        except Exception as e:
            logger.error(f"获取合规统计失败: {e}")
            # 即使出错也不返回模拟数据，返回错误状态
            return {
                "error": f"获取统计数据失败: {str(e)}",
                "service_status": "error",
                "last_updated": datetime.utcnow().isoformat()
            }


# 全局增强合规服务实例
enhanced_compliance_service = None

def get_enhanced_compliance_service() -> EnhancedComplianceService:
    """获取增强合规服务实例"""
    global enhanced_compliance_service
    if enhanced_compliance_service is None:
        enhanced_compliance_service = EnhancedComplianceService()
    return enhanced_compliance_service
