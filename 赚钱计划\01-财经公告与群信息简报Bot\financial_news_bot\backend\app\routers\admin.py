"""
临时管理路由 - 修复导入问题
"""
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from sqlalchemy import func, desc, and_, or_

from ..database import get_db
from ..dependencies.auth import get_current_active_user
from ..dependencies.permissions import (
    require_admin,
    require_user_management,
    require_system_config
)
from ..models.user import User, UserRole
from ..schemas.user import UserResponse, UserRoleUpdate
from ..schemas.admin import (
    SystemStatsResponse, UserManagementResponse, PermissionAuditResponse,
    SystemHealthResponse
)
from ..models.permission import AuditLog
from ..services.cache_service import cache_service
from ..services.integrated_monitoring_service import integrated_monitoring as monitoring_service

router = APIRouter(prefix="/admin", tags=["管理"])

@router.get("/stats", response_model=SystemStatsResponse)
async def get_system_stats(
    current_user: User = Depends(require_admin()),
    db: Session = Depends(get_db)
):
    """
    获取系统统计信息（需要admin权限）
    """
    try:
        # 用户统计
        total_users = db.query(User).count()
        active_users = db.query(User).filter(User.is_active == True).count()
        
        # 今日新增用户
        today = datetime.utcnow().date()
        new_users_today = db.query(User).filter(
            func.date(User.created_at) == today
        ).count()
        
        # 系统健康状态
        health_status = await monitoring_service.get_system_health()
        
        return SystemStatsResponse(
            total_users=total_users,
            active_users=active_users,
            new_users_today=new_users_today,
            system_health=health_status.get("status", "unknown"),
            cache_status="healthy" if cache_service.is_connected() else "error",
            database_status="healthy",  # 如果能查询到这里说明数据库是健康的
            last_updated=datetime.utcnow()
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取系统统计信息失败: {str(e)}"
        )

@router.get("/users", response_model=UserManagementResponse)
async def get_users_management(
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(100, ge=1, le=1000, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索用户名或邮箱"),
    role_filter: Optional[str] = Query(None, description="角色过滤"),
    status_filter: Optional[bool] = Query(None, description="状态过滤"),
    current_user: User = Depends(require_user_management()),
    db: Session = Depends(get_db)
):
    """
    获取用户管理列表（需要user_management权限）
    """
    try:
        query = db.query(User)
        
        # 搜索过滤
        if search:
            query = query.filter(
                or_(
                    User.username.contains(search),
                    User.email.contains(search)
                )
            )
        
        # 状态过滤
        if status_filter is not None:
            query = query.filter(User.is_active == status_filter)
        
        # 角色过滤
        if role_filter:
            query = query.join(UserRole).filter(UserRole.role_name == role_filter)
        
        # 总数
        total = query.count()
        
        # 分页查询
        users = query.offset(skip).limit(limit).all()
        
        return UserManagementResponse(
            users=[UserResponse.from_orm(user) for user in users],
            total=total,
            page=skip // limit + 1,
            per_page=limit,
            has_next=skip + limit < total
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取用户列表失败: {str(e)}"
        )

@router.get("/audit-logs", response_model=List[PermissionAuditResponse])
async def get_permission_audit_logs(
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(100, ge=1, le=1000, description="每页数量"),
    user_id: Optional[int] = Query(None, description="用户ID过滤"),
    action: Optional[str] = Query(None, description="操作类型过滤"),
    start_date: Optional[datetime] = Query(None, description="开始时间"),
    end_date: Optional[datetime] = Query(None, description="结束时间"),
    current_user: User = Depends(require_admin()),  # 临时使用admin权限
    db: Session = Depends(get_db)
):
    """
    获取权限审计日志（需要monitoring_access权限）
    """
    try:
        query = db.query(AuditLog)
        
        # 用户过滤
        if user_id:
            query = query.filter(AuditLog.user_id == user_id)
        
        # 操作类型过滤
        if action:
            query = query.filter(AuditLog.action.contains(action))
        
        # 时间范围过滤
        if start_date:
            query = query.filter(AuditLog.created_at >= start_date)
        if end_date:
            query = query.filter(AuditLog.created_at <= end_date)
        
        # 按时间倒序
        logs = query.order_by(desc(AuditLog.created_at)).offset(skip).limit(limit).all()
        
        return [
            PermissionAuditResponse(
                id=log.id,
                user_id=log.user_id,
                action=log.action,
                resource=log.resource or "",
                details=log.details or {},
                ip_address=log.ip_address or "",
                user_agent=log.user_agent or "",
                created_at=log.created_at
            )
            for log in logs
        ]
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取审计日志失败: {str(e)}"
        )

@router.get("/health/detailed", response_model=Dict[str, Any])
async def get_detailed_health_check(
    current_user: User = Depends(require_admin()),  # 临时使用admin权限
    db: Session = Depends(get_db)
):
    """
    获取详细健康检查信息（需要monitoring_access权限）
    """
    try:
        # 数据库健康检查
        db_health = True
        try:
            db.execute("SELECT 1")
        except:
            db_health = False
        
        # 缓存健康检查
        cache_health = cache_service.is_connected()
        
        # 系统监控信息
        system_health = await monitoring_service.get_system_health()
        
        return {
            "status": "healthy" if all([db_health, cache_health]) else "unhealthy",
            "timestamp": datetime.utcnow().isoformat(),
            "components": {
                "database": {
                    "status": "healthy" if db_health else "unhealthy",
                    "response_time": "< 1ms" if db_health else "timeout"
                },
                "cache": {
                    "status": "healthy" if cache_health else "unhealthy",
                    "connected": cache_health
                },
                "monitoring": system_health
            },
            "version": "1.0.0",
            "environment": "production"
        }
        
    except Exception as e:
        return {
            "status": "error",
            "timestamp": datetime.utcnow().isoformat(),
            "error": str(e)
        }
