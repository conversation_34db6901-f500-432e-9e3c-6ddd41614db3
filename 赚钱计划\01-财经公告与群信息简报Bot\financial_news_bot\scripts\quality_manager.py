#!/usr/bin/env python3
"""
代码质量管理器
集成代码检查、格式化、测试等功能的综合工具
"""
import os
import sys
import subprocess
import argparse
from pathlib import Path
from typing import Dict, Any, List
import json

# 导入自定义工具
from code_quality_check import CodeQuality<PERSON>he<PERSON>
from format_code import CodeFormatter

class QualityManager:
    """代码质量管理器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.checker = CodeQualityChecker(project_root)
        self.formatter = CodeFormatter(project_root)
        
    def install_tools(self) -> Dict[str, Any]:
        """安装代码质量工具"""
        print("🔧 安装代码质量工具...")
        
        tools = [
            "black>=23.0.0",
            "isort>=5.12.0", 
            "flake8>=6.0.0",
            "mypy>=1.0.0",
            "autopep8>=2.0.0",
            "autoflake>=2.0.0",
            "pytest>=7.0.0",
            "pytest-cov>=4.0.0",
            "pytest-asyncio>=0.21.0",
            "bandit>=1.7.0"
        ]
        
        results = {}
        
        for tool in tools:
            try:
                result = subprocess.run([
                    sys.executable, "-m", "pip", "install", tool
                ], capture_output=True, text=True)
                
                results[tool] = {
                    "success": result.returncode == 0,
                    "output": result.stdout if result.returncode == 0 else result.stderr
                }
                
                status = "✅" if result.returncode == 0 else "❌"
                print(f"{status} {tool}")
                
            except Exception as e:
                results[tool] = {
                    "success": False,
                    "error": str(e)
                }
                print(f"❌ {tool} - {str(e)}")
        
        return results
    
    def run_security_check(self) -> Dict[str, Any]:
        """运行安全检查"""
        print("🔒 运行安全检查...")
        
        try:
            result = subprocess.run([
                "python", "-m", "bandit", 
                "-r", str(self.project_root / "app"),
                "-f", "json"
            ], capture_output=True, text=True, cwd=self.project_root)
            
            if result.stdout:
                try:
                    bandit_results = json.loads(result.stdout)
                    issues = bandit_results.get("results", [])
                    
                    return {
                        "success": True,
                        "issues_count": len(issues),
                        "high_severity": len([i for i in issues if i.get("issue_severity") == "HIGH"]),
                        "medium_severity": len([i for i in issues if i.get("issue_severity") == "MEDIUM"]),
                        "low_severity": len([i for i in issues if i.get("issue_severity") == "LOW"]),
                        "details": issues[:10]  # 只保留前10个问题的详情
                    }
                except json.JSONDecodeError:
                    return {
                        "success": False,
                        "error": "无法解析bandit输出"
                    }
            else:
                return {
                    "success": True,
                    "issues_count": 0,
                    "message": "未发现安全问题"
                }
                
        except FileNotFoundError:
            return {
                "success": False,
                "error": "bandit未安装，请运行: pip install bandit"
            }
    
    def run_type_check(self) -> Dict[str, Any]:
        """运行类型检查"""
        print("📝 运行类型检查...")
        
        try:
            result = subprocess.run([
                "python", "-m", "mypy",
                str(self.project_root / "app"),
                "--ignore-missing-imports",
                "--no-strict-optional"
            ], capture_output=True, text=True, cwd=self.project_root)
            
            errors = []
            if result.stdout:
                for line in result.stdout.strip().split('\n'):
                    if line and ':' in line:
                        errors.append(line)
            
            return {
                "success": result.returncode == 0,
                "errors_count": len(errors),
                "errors": errors[:20],  # 只保留前20个错误
                "summary": f"发现 {len(errors)} 个类型错误" if errors else "类型检查通过"
            }
            
        except FileNotFoundError:
            return {
                "success": False,
                "error": "mypy未安装，请运行: pip install mypy"
            }
    
    def run_tests_with_coverage(self) -> Dict[str, Any]:
        """运行测试并生成覆盖率报告"""
        print("🧪 运行测试和覆盖率检查...")
        
        try:
            # 运行测试
            result = subprocess.run([
                "python", "-m", "pytest",
                "tests/",
                "--cov=app",
                "--cov-report=term-missing",
                "--cov-report=json",
                "-v"
            ], capture_output=True, text=True, cwd=self.project_root)
            
            # 解析覆盖率
            coverage_data = {}
            coverage_file = self.project_root / "coverage.json"
            if coverage_file.exists():
                try:
                    with open(coverage_file, 'r') as f:
                        coverage_data = json.load(f)
                except:
                    pass
            
            # 解析测试结果
            test_summary = self._parse_pytest_output(result.stdout)
            
            return {
                "success": result.returncode == 0,
                "test_results": test_summary,
                "coverage": {
                    "total_coverage": coverage_data.get("totals", {}).get("percent_covered", 0),
                    "lines_covered": coverage_data.get("totals", {}).get("covered_lines", 0),
                    "lines_total": coverage_data.get("totals", {}).get("num_statements", 0)
                },
                "output": result.stdout[-1000:] if result.stdout else ""  # 最后1000字符
            }
            
        except FileNotFoundError:
            return {
                "success": False,
                "error": "pytest未安装，请运行: pip install pytest pytest-cov"
            }
    
    def _parse_pytest_output(self, output: str) -> Dict[str, Any]:
        """解析pytest输出"""
        lines = output.split('\n')
        
        summary = {
            "total_tests": 0,
            "passed": 0,
            "failed": 0,
            "skipped": 0,
            "errors": 0
        }
        
        for line in lines:
            if "passed" in line and "failed" in line:
                # 解析测试结果行
                parts = line.split()
                for i, part in enumerate(parts):
                    if part == "passed":
                        summary["passed"] = int(parts[i-1]) if i > 0 else 0
                    elif part == "failed":
                        summary["failed"] = int(parts[i-1]) if i > 0 else 0
                    elif part == "skipped":
                        summary["skipped"] = int(parts[i-1]) if i > 0 else 0
                    elif part == "error":
                        summary["errors"] = int(parts[i-1]) if i > 0 else 0
        
        summary["total_tests"] = summary["passed"] + summary["failed"] + summary["skipped"] + summary["errors"]
        
        return summary
    
    def run_full_check(self, fix_issues: bool = False) -> Dict[str, Any]:
        """运行完整的代码质量检查"""
        print("🚀 开始完整的代码质量检查...")
        
        results = {}
        
        # 1. 代码格式化（如果需要修复）
        if fix_issues:
            print("\n📝 格式化代码...")
            results["formatting"] = self.formatter.format_all(check_only=False, add_missing=True)
        else:
            results["formatting"] = self.formatter.format_all(check_only=True)
        
        # 2. 代码质量检查
        print("\n🔍 检查代码质量...")
        results["quality"] = self.checker.check_all()
        
        # 3. 安全检查
        print("\n🔒 安全检查...")
        results["security"] = self.run_security_check()
        
        # 4. 类型检查
        print("\n📝 类型检查...")
        results["type_check"] = self.run_type_check()
        
        # 5. 测试和覆盖率
        print("\n🧪 测试和覆盖率...")
        results["tests"] = self.run_tests_with_coverage()
        
        # 生成总结
        results["summary"] = self._generate_summary(results)
        
        return results
    
    def _generate_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """生成检查总结"""
        summary = {
            "overall_score": 0,
            "total_issues": 0,
            "critical_issues": 0,
            "recommendations": []
        }
        
        # 计算总问题数
        if "quality" in results:
            summary["total_issues"] += results["quality"].get("summary", {}).get("total_issues", 0)
        
        if "security" in results:
            security_issues = results["security"].get("issues_count", 0)
            summary["total_issues"] += security_issues
            summary["critical_issues"] += results["security"].get("high_severity", 0)
        
        if "type_check" in results:
            summary["total_issues"] += results["type_check"].get("errors_count", 0)
        
        # 计算总体评分 (0-100)
        base_score = 100
        
        # 扣分规则
        base_score -= min(summary["total_issues"] * 2, 50)  # 每个问题扣2分，最多扣50分
        base_score -= summary["critical_issues"] * 10  # 每个严重问题扣10分
        
        # 测试覆盖率加分
        if "tests" in results:
            coverage = results["tests"].get("coverage", {}).get("total_coverage", 0)
            if coverage >= 80:
                base_score += 10
            elif coverage >= 60:
                base_score += 5
        
        summary["overall_score"] = max(0, min(100, base_score))
        
        # 生成建议
        if summary["critical_issues"] > 0:
            summary["recommendations"].append("🚨 立即修复高危安全问题")
        
        if summary["total_issues"] > 20:
            summary["recommendations"].append("📝 代码质量问题较多，建议逐步改进")
        
        if "tests" in results:
            coverage = results["tests"].get("coverage", {}).get("total_coverage", 0)
            if coverage < 60:
                summary["recommendations"].append("🧪 测试覆盖率偏低，建议增加测试")
        
        if not summary["recommendations"]:
            summary["recommendations"].append("🎉 代码质量良好，继续保持！")
        
        return summary
    
    def generate_comprehensive_report(self, results: Dict[str, Any]) -> str:
        """生成综合报告"""
        report = []
        report.append("# 代码质量综合报告")
        report.append(f"检查时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # 总体评分
        summary = results.get("summary", {})
        score = summary.get("overall_score", 0)
        
        if score >= 90:
            grade = "A (优秀)"
        elif score >= 80:
            grade = "B (良好)"
        elif score >= 70:
            grade = "C (一般)"
        elif score >= 60:
            grade = "D (需改进)"
        else:
            grade = "F (较差)"
        
        report.append(f"## 📊 总体评分: {score}/100 ({grade})")
        report.append("")
        
        # 问题统计
        report.append("### 问题统计")
        report.append(f"- 总问题数: {summary.get('total_issues', 0)}")
        report.append(f"- 严重问题: {summary.get('critical_issues', 0)}")
        report.append("")
        
        # 建议
        report.append("### 改进建议")
        for rec in summary.get("recommendations", []):
            report.append(f"- {rec}")
        report.append("")
        
        # 详细结果
        for category, result in results.items():
            if category == "summary":
                continue
                
            report.append(f"## {category.replace('_', ' ').title()}")
            
            if isinstance(result, dict):
                if result.get("success"):
                    report.append("✅ 通过")
                else:
                    report.append("❌ 失败")
                
                # 添加具体信息
                if category == "tests":
                    test_results = result.get("test_results", {})
                    coverage = result.get("coverage", {})
                    report.append(f"- 测试通过: {test_results.get('passed', 0)}")
                    report.append(f"- 测试失败: {test_results.get('failed', 0)}")
                    report.append(f"- 覆盖率: {coverage.get('total_coverage', 0):.1f}%")
                
                elif category == "security":
                    report.append(f"- 安全问题: {result.get('issues_count', 0)}")
                    report.append(f"- 高危: {result.get('high_severity', 0)}")
                    report.append(f"- 中危: {result.get('medium_severity', 0)}")
                
                elif category == "type_check":
                    report.append(f"- 类型错误: {result.get('errors_count', 0)}")
            
            report.append("")
        
        return "\n".join(report)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="代码质量管理器")
    parser.add_argument("--install", action="store_true", help="安装代码质量工具")
    parser.add_argument("--check", action="store_true", help="运行完整检查")
    parser.add_argument("--fix", action="store_true", help="修复可自动修复的问题")
    parser.add_argument("--report", help="报告输出文件", default="quality_report.md")
    
    args = parser.parse_args()
    
    manager = QualityManager()
    
    if args.install:
        print("🔧 安装代码质量工具...")
        install_results = manager.install_tools()
        success_count = sum(1 for r in install_results.values() if r.get("success"))
        print(f"\n✅ 成功安装 {success_count}/{len(install_results)} 个工具")
        return 0
    
    if args.check:
        results = manager.run_full_check(fix_issues=args.fix)
        
        # 生成报告
        report = manager.generate_comprehensive_report(results)
        
        # 保存报告
        with open(args.report, 'w', encoding='utf-8') as f:
            f.write(report)
        
        # 显示结果
        summary = results.get("summary", {})
        score = summary.get("overall_score", 0)
        
        print(f"\n🎯 代码质量检查完成！")
        print(f"📊 总体评分: {score}/100")
        print(f"📄 详细报告: {args.report}")
        
        if args.fix:
            print("🔧 已自动修复部分问题")
        
        return 0 if score >= 70 else 1
    
    else:
        parser.print_help()
        return 1

if __name__ == "__main__":
    sys.exit(main())
