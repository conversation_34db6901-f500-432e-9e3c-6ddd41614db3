"""
资源监控和耗尽处理机制
监控系统资源使用情况，在资源不足时采取保护措施
"""
import psutil
import logging
import threading
import time
from typing import Dict, Any, Callable, Optional, List
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum

logger = logging.getLogger(__name__)


class ResourceType(Enum):
    """资源类型"""
    MEMORY = "memory"
    CPU = "cpu"
    DISK = "disk"
    NETWORK = "network"
    DATABASE_CONNECTIONS = "database_connections"
    REDIS_CONNECTIONS = "redis_connections"


class AlertLevel(Enum):
    """告警级别"""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"


@dataclass
class ResourceThreshold:
    """资源阈值配置"""
    warning_threshold: float  # 警告阈值（百分比）
    critical_threshold: float  # 严重阈值（百分比）
    emergency_threshold: float  # 紧急阈值（百分比）
    check_interval: int = 30  # 检查间隔（秒）


@dataclass
class ResourceStatus:
    """资源状态"""
    resource_type: ResourceType
    current_usage: float
    threshold_config: ResourceThreshold
    alert_level: AlertLevel
    last_check: datetime
    trend: str  # "increasing", "decreasing", "stable"


class ResourceMonitor:
    """资源监控器"""
    
    def __init__(self):
        self.thresholds = self._get_default_thresholds()
        self.status_history: Dict[ResourceType, List[ResourceStatus]] = {}
        self.alert_callbacks: Dict[AlertLevel, List[Callable]] = {
            AlertLevel.WARNING: [],
            AlertLevel.CRITICAL: [],
            AlertLevel.EMERGENCY: []
        }
        self.monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
        self.protection_enabled = True
        
    def _get_default_thresholds(self) -> Dict[ResourceType, ResourceThreshold]:
        """获取默认资源阈值配置"""
        return {
            ResourceType.MEMORY: ResourceThreshold(70.0, 85.0, 95.0, 30),
            ResourceType.CPU: ResourceThreshold(70.0, 85.0, 95.0, 30),
            ResourceType.DISK: ResourceThreshold(80.0, 90.0, 95.0, 60),
            ResourceType.DATABASE_CONNECTIONS: ResourceThreshold(70.0, 85.0, 95.0, 30),
            ResourceType.REDIS_CONNECTIONS: ResourceThreshold(70.0, 85.0, 95.0, 30),
        }
    
    def set_threshold(self, resource_type: ResourceType, threshold: ResourceThreshold):
        """设置资源阈值"""
        self.thresholds[resource_type] = threshold
        logger.info(f"已更新资源阈值: {resource_type.value}")
    
    def register_alert_callback(self, alert_level: AlertLevel, callback: Callable[[ResourceStatus], None]):
        """注册告警回调"""
        self.alert_callbacks[alert_level].append(callback)
    
    def start_monitoring(self):
        """开始监控"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        logger.info("资源监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        logger.info("资源监控已停止")
    
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                self._check_all_resources()
                time.sleep(10)  # 每10秒检查一次
            except Exception as e:
                logger.error(f"资源监控异常: {str(e)}")
                time.sleep(30)  # 出错时延长间隔
    
    def _check_all_resources(self):
        """检查所有资源"""
        current_time = datetime.now()
        
        for resource_type, threshold in self.thresholds.items():
            # 检查是否到了检查时间
            if self._should_check_resource(resource_type, current_time, threshold.check_interval):
                usage = self._get_resource_usage(resource_type)
                if usage is not None:
                    status = self._create_resource_status(resource_type, usage, threshold, current_time)
                    self._handle_resource_status(status)
    
    def _should_check_resource(self, resource_type: ResourceType, current_time: datetime, interval: int) -> bool:
        """判断是否应该检查资源"""
        if resource_type not in self.status_history:
            return True
        
        history = self.status_history[resource_type]
        if not history:
            return True
        
        last_check = history[-1].last_check
        return (current_time - last_check).total_seconds() >= interval
    
    def _get_resource_usage(self, resource_type: ResourceType) -> Optional[float]:
        """获取资源使用率"""
        try:
            if resource_type == ResourceType.MEMORY:
                return psutil.virtual_memory().percent
            elif resource_type == ResourceType.CPU:
                return psutil.cpu_percent(interval=1)
            elif resource_type == ResourceType.DISK:
                return psutil.disk_usage('/').percent
            elif resource_type == ResourceType.DATABASE_CONNECTIONS:
                return self._get_database_connection_usage()
            elif resource_type == ResourceType.REDIS_CONNECTIONS:
                return self._get_redis_connection_usage()
            else:
                return None
        except Exception as e:
            logger.error(f"获取资源使用率失败 {resource_type.value}: {str(e)}")
            return None
    
    def _get_database_connection_usage(self) -> float:
        """获取数据库连接使用率"""
        try:
            # 这里需要根据实际的数据库连接池实现
            # 暂时返回模拟值
            from ..database import engine
            pool = engine.pool
            if hasattr(pool, 'size') and hasattr(pool, 'checked_in'):
                total = pool.size() + pool.overflow()
                used = pool.checked_in()
                return (used / total) * 100 if total > 0 else 0
            return 0
        except Exception:
            return 0
    
    def _get_redis_connection_usage(self) -> float:
        """获取Redis连接使用率"""
        try:
            # 这里需要根据实际的Redis连接池实现
            # 暂时返回模拟值
            from ..services.cache_service import cache_service
            if cache_service and cache_service.redis_client:
                info = cache_service.redis_client.info()
                connected_clients = info.get('connected_clients', 0)
                max_clients = info.get('maxclients', 10000)
                return (connected_clients / max_clients) * 100
            return 0
        except Exception:
            return 0
    
    def _create_resource_status(
        self, 
        resource_type: ResourceType, 
        usage: float, 
        threshold: ResourceThreshold, 
        current_time: datetime
    ) -> ResourceStatus:
        """创建资源状态"""
        # 确定告警级别
        if usage >= threshold.emergency_threshold:
            alert_level = AlertLevel.EMERGENCY
        elif usage >= threshold.critical_threshold:
            alert_level = AlertLevel.CRITICAL
        elif usage >= threshold.warning_threshold:
            alert_level = AlertLevel.WARNING
        else:
            alert_level = AlertLevel.INFO
        
        # 计算趋势
        trend = self._calculate_trend(resource_type, usage)
        
        return ResourceStatus(
            resource_type=resource_type,
            current_usage=usage,
            threshold_config=threshold,
            alert_level=alert_level,
            last_check=current_time,
            trend=trend
        )
    
    def _calculate_trend(self, resource_type: ResourceType, current_usage: float) -> str:
        """计算资源使用趋势"""
        if resource_type not in self.status_history:
            return "stable"
        
        history = self.status_history[resource_type]
        if len(history) < 2:
            return "stable"
        
        # 取最近5个数据点计算趋势
        recent_data = history[-5:]
        if len(recent_data) < 2:
            return "stable"
        
        # 简单的线性趋势计算
        values = [status.current_usage for status in recent_data] + [current_usage]
        if len(values) < 2:
            return "stable"
        
        # 计算平均变化率
        changes = [values[i] - values[i-1] for i in range(1, len(values))]
        avg_change = sum(changes) / len(changes)
        
        if avg_change > 2:
            return "increasing"
        elif avg_change < -2:
            return "decreasing"
        else:
            return "stable"
    
    def _handle_resource_status(self, status: ResourceStatus):
        """处理资源状态"""
        # 记录历史
        if status.resource_type not in self.status_history:
            self.status_history[status.resource_type] = []
        
        history = self.status_history[status.resource_type]
        history.append(status)
        
        # 保留最近100个记录
        if len(history) > 100:
            history.pop(0)
        
        # 记录日志
        if status.alert_level != AlertLevel.INFO:
            logger.warning(
                f"资源告警: {status.resource_type.value} "
                f"使用率 {status.current_usage:.1f}% "
                f"级别 {status.alert_level.value} "
                f"趋势 {status.trend}"
            )
        
        # 触发回调
        self._trigger_alert_callbacks(status)
        
        # 执行保护措施
        if self.protection_enabled:
            self._execute_protection_measures(status)
    
    def _trigger_alert_callbacks(self, status: ResourceStatus):
        """触发告警回调"""
        callbacks = self.alert_callbacks.get(status.alert_level, [])
        for callback in callbacks:
            try:
                callback(status)
            except Exception as e:
                logger.error(f"告警回调执行失败: {str(e)}")
    
    def _execute_protection_measures(self, status: ResourceStatus):
        """执行保护措施"""
        if status.alert_level == AlertLevel.EMERGENCY:
            self._emergency_protection(status)
        elif status.alert_level == AlertLevel.CRITICAL:
            self._critical_protection(status)
        elif status.alert_level == AlertLevel.WARNING:
            self._warning_protection(status)
    
    def _emergency_protection(self, status: ResourceStatus):
        """紧急保护措施"""
        logger.critical(f"执行紧急保护措施: {status.resource_type.value}")
        
        if status.resource_type == ResourceType.MEMORY:
            # 清理缓存
            self._clear_caches()
            # 触发垃圾回收
            import gc
            gc.collect()
        elif status.resource_type == ResourceType.DATABASE_CONNECTIONS:
            # 关闭空闲连接
            self._close_idle_database_connections()
    
    def _critical_protection(self, status: ResourceStatus):
        """严重保护措施"""
        logger.error(f"执行严重保护措施: {status.resource_type.value}")
        
        if status.resource_type == ResourceType.MEMORY:
            # 部分清理缓存
            self._partial_clear_caches()
    
    def _warning_protection(self, status: ResourceStatus):
        """警告保护措施"""
        logger.warning(f"执行警告保护措施: {status.resource_type.value}")
    
    def _clear_caches(self):
        """清理缓存"""
        try:
            from ..services.cache_service import cache_service
            if cache_service and cache_service.is_available():
                # 清理部分缓存键
                logger.info("执行缓存清理")
        except Exception as e:
            logger.error(f"缓存清理失败: {str(e)}")
    
    def _partial_clear_caches(self):
        """部分清理缓存"""
        try:
            # 只清理非关键缓存
            logger.info("执行部分缓存清理")
        except Exception as e:
            logger.error(f"部分缓存清理失败: {str(e)}")
    
    def _close_idle_database_connections(self):
        """关闭空闲数据库连接"""
        try:
            from ..database import engine
            engine.dispose()
            logger.info("已关闭空闲数据库连接")
        except Exception as e:
            logger.error(f"关闭数据库连接失败: {str(e)}")
    
    def get_current_status(self) -> Dict[str, Any]:
        """获取当前资源状态"""
        status = {}
        for resource_type in self.thresholds.keys():
            usage = self._get_resource_usage(resource_type)
            if usage is not None:
                status[resource_type.value] = {
                    'usage': usage,
                    'status': 'normal' if usage < self.thresholds[resource_type].warning_threshold else 'warning'
                }
        return status


# 全局实例
resource_monitor = ResourceMonitor()


def get_resource_monitor() -> ResourceMonitor:
    """获取资源监控器实例"""
    return resource_monitor
