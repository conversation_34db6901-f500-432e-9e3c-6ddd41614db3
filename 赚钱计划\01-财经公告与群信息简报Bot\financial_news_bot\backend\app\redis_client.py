"""
Redis客户端配置和连接池管理
提供缓存、会话存储、任务队列等功能
"""
import redis
import json
import logging
import time
from typing import Any, Optional, Dict, List, Union
from datetime import datetime, timedelta
import os
from dataclasses import dataclass, asdict

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class RedisStats:
    """Redis统计信息"""
    connections_created: int = 0
    commands_processed: int = 0
    cache_hits: int = 0
    cache_misses: int = 0
    errors: int = 0
    start_time: float = 0
    
    def __post_init__(self):
        if self.start_time == 0:
            self.start_time = time.time()
    
    def get_hit_rate(self) -> float:
        """计算缓存命中率"""
        total = self.cache_hits + self.cache_misses
        return (self.cache_hits / total * 100) if total > 0 else 0.0
    
    def get_uptime(self) -> float:
        """获取运行时间"""
        return time.time() - self.start_time


class RedisClient:
    """Redis客户端管理器"""
    
    def __init__(self):
        # Redis配置 - 统一使用服务名作为默认值
        self.host = os.getenv("REDIS_HOST", "redis")
        self.port = int(os.getenv("REDIS_PORT", "6379"))
        self.password = os.getenv("REDIS_PASSWORD", None)
        self.db = int(os.getenv("REDIS_DB", "0"))
        
        # 连接池配置
        self.max_connections = int(os.getenv("REDIS_MAX_CONNECTIONS", "50"))
        self.socket_timeout = int(os.getenv("REDIS_SOCKET_TIMEOUT", "5"))
        self.socket_connect_timeout = int(os.getenv("REDIS_CONNECT_TIMEOUT", "5"))
        self.socket_keepalive = os.getenv("REDIS_KEEPALIVE", "true").lower() == "true"
        self.socket_keepalive_options = {}
        
        # 重试配置
        self.retry_on_timeout = True
        self.retry_on_error = [redis.ConnectionError, redis.TimeoutError]
        self.health_check_interval = int(os.getenv("REDIS_HEALTH_CHECK_INTERVAL", "30"))
        
        # 统计信息
        self.stats = RedisStats()
        
        # 创建连接池
        self.pool = self._create_connection_pool()
        self.client = redis.Redis(connection_pool=self.pool)
        
        # 测试连接（非阻塞）
        try:
            self._test_connection()
        except Exception as e:
            logger.warning(f"Redis初始连接失败，将在使用时重试: {e}")
        
        logger.info(f"Redis client initialized: {self.host}:{self.port}, DB: {self.db}")
    
    def _create_connection_pool(self) -> redis.ConnectionPool:
        """创建Redis连接池"""
        try:
            pool = redis.ConnectionPool(
                host=self.host,
                port=self.port,
                password=self.password,
                db=self.db,
                max_connections=self.max_connections,
                socket_timeout=self.socket_timeout,
                socket_connect_timeout=self.socket_connect_timeout,
                socket_keepalive=self.socket_keepalive,
                socket_keepalive_options=self.socket_keepalive_options,
                retry_on_timeout=self.retry_on_timeout,
                retry_on_error=self.retry_on_error,
                health_check_interval=self.health_check_interval,
                encoding='utf-8',
                decode_responses=True
            )
            
            self.stats.connections_created += 1
            return pool
            
        except Exception as e:
            logger.error(f"Failed to create Redis connection pool: {str(e)}")
            raise
    
    def _test_connection(self):
        """测试Redis连接"""
        try:
            self.client.ping()
            logger.info("Redis connection test successful")
        except Exception as e:
            logger.error(f"Redis connection test failed: {str(e)}")
            raise
    
    def _record_command(self, success: bool = True):
        """记录命令统计"""
        self.stats.commands_processed += 1
        if not success:
            self.stats.errors += 1
    
    def _record_cache_hit(self, hit: bool):
        """记录缓存命中统计"""
        if hit:
            self.stats.cache_hits += 1
        else:
            self.stats.cache_misses += 1
    
    # 基础操作
    def get(self, key: str) -> Optional[str]:
        """获取字符串值"""
        try:
            result = self.client.get(key)
            self._record_command(True)
            self._record_cache_hit(result is not None)
            return result
        except Exception as e:
            self._record_command(False)
            logger.error(f"Redis GET error for key {key}: {str(e)}")
            return None
    
    def set(self, key: str, value: str, ex: Optional[int] = None, px: Optional[int] = None) -> bool:
        """设置字符串值"""
        try:
            result = self.client.set(key, value, ex=ex, px=px)
            self._record_command(True)
            return result
        except Exception as e:
            self._record_command(False)
            logger.error(f"Redis SET error for key {key}: {str(e)}")
            return False
    
    def delete(self, *keys: str) -> int:
        """删除键"""
        try:
            result = self.client.delete(*keys)
            self._record_command(True)
            return result
        except Exception as e:
            self._record_command(False)
            logger.error(f"Redis DELETE error for keys {keys}: {str(e)}")
            return 0
    
    def exists(self, *keys: str) -> int:
        """检查键是否存在"""
        try:
            result = self.client.exists(*keys)
            self._record_command(True)
            return result
        except Exception as e:
            self._record_command(False)
            logger.error(f"Redis EXISTS error for keys {keys}: {str(e)}")
            return 0
    
    def expire(self, key: str, time: int) -> bool:
        """设置键过期时间"""
        try:
            result = self.client.expire(key, time)
            self._record_command(True)
            return result
        except Exception as e:
            self._record_command(False)
            logger.error(f"Redis EXPIRE error for key {key}: {str(e)}")
            return False
    
    def ttl(self, key: str) -> int:
        """获取键剩余生存时间"""
        try:
            result = self.client.ttl(key)
            self._record_command(True)
            return result
        except Exception as e:
            self._record_command(False)
            logger.error(f"Redis TTL error for key {key}: {str(e)}")
            return -1
    
    # JSON操作
    def get_json(self, key: str) -> Optional[Any]:
        """获取JSON值"""
        try:
            value = self.get(key)
            if value is not None:
                return json.loads(value)
            return None
        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error for key {key}: {str(e)}")
            return None
    
    def set_json(self, key: str, value: Any, ex: Optional[int] = None) -> bool:
        """设置JSON值"""
        try:
            json_str = json.dumps(value, ensure_ascii=False)
            return self.set(key, json_str, ex=ex)
        except (TypeError, ValueError) as e:
            logger.error(f"JSON encode error for key {key}: {str(e)}")
            return False
    
    # 哈希操作
    def hget(self, name: str, key: str) -> Optional[str]:
        """获取哈希字段值"""
        try:
            result = self.client.hget(name, key)
            self._record_command(True)
            self._record_cache_hit(result is not None)
            return result
        except Exception as e:
            self._record_command(False)
            logger.error(f"Redis HGET error for {name}.{key}: {str(e)}")
            return None
    
    def hset(self, name: str, key: str, value: str) -> int:
        """设置哈希字段值"""
        try:
            result = self.client.hset(name, key, value)
            self._record_command(True)
            return result
        except Exception as e:
            self._record_command(False)
            logger.error(f"Redis HSET error for {name}.{key}: {str(e)}")
            return 0
    
    def hgetall(self, name: str) -> Dict[str, str]:
        """获取哈希所有字段"""
        try:
            result = self.client.hgetall(name)
            self._record_command(True)
            return result
        except Exception as e:
            self._record_command(False)
            logger.error(f"Redis HGETALL error for {name}: {str(e)}")
            return {}
    
    def hdel(self, name: str, *keys: str) -> int:
        """删除哈希字段"""
        try:
            result = self.client.hdel(name, *keys)
            self._record_command(True)
            return result
        except Exception as e:
            self._record_command(False)
            logger.error(f"Redis HDEL error for {name}: {str(e)}")
            return 0
    
    # 列表操作
    def lpush(self, name: str, *values: str) -> int:
        """从左侧推入列表"""
        try:
            result = self.client.lpush(name, *values)
            self._record_command(True)
            return result
        except Exception as e:
            self._record_command(False)
            logger.error(f"Redis LPUSH error for {name}: {str(e)}")
            return 0
    
    def rpop(self, name: str) -> Optional[str]:
        """从右侧弹出列表元素"""
        try:
            result = self.client.rpop(name)
            self._record_command(True)
            return result
        except Exception as e:
            self._record_command(False)
            logger.error(f"Redis RPOP error for {name}: {str(e)}")
            return None
    
    def llen(self, name: str) -> int:
        """获取列表长度"""
        try:
            result = self.client.llen(name)
            self._record_command(True)
            return result
        except Exception as e:
            self._record_command(False)
            logger.error(f"Redis LLEN error for {name}: {str(e)}")
            return 0
    
    # 集合操作
    def sadd(self, name: str, *values: str) -> int:
        """添加集合成员"""
        try:
            result = self.client.sadd(name, *values)
            self._record_command(True)
            return result
        except Exception as e:
            self._record_command(False)
            logger.error(f"Redis SADD error for {name}: {str(e)}")
            return 0
    
    def srem(self, name: str, *values: str) -> int:
        """移除集合成员"""
        try:
            result = self.client.srem(name, *values)
            self._record_command(True)
            return result
        except Exception as e:
            self._record_command(False)
            logger.error(f"Redis SREM error for {name}: {str(e)}")
            return 0
    
    def smembers(self, name: str) -> set:
        """获取集合所有成员"""
        try:
            result = self.client.smembers(name)
            self._record_command(True)
            return result
        except Exception as e:
            self._record_command(False)
            logger.error(f"Redis SMEMBERS error for {name}: {str(e)}")
            return set()
    
    # 缓存辅助方法
    def cache_get_or_set(self, key: str, func, ex: int = 3600) -> Any:
        """获取缓存或设置缓存"""
        # 尝试从缓存获取
        cached_value = self.get_json(key)
        if cached_value is not None:
            return cached_value
        
        # 缓存未命中，执行函数获取值
        try:
            value = func()
            if value is not None:
                self.set_json(key, value, ex=ex)
            return value
        except Exception as e:
            logger.error(f"Cache function execution error for key {key}: {str(e)}")
            return None
    
    # 健康检查
    def health_check(self) -> Dict[str, Any]:
        """Redis健康检查"""
        try:
            # 基础连接测试
            ping_result = self.client.ping()
            
            # 获取Redis信息
            info = self.client.info()
            
            # 连接池状态
            pool_stats = {
                "created_connections": self.pool.created_connections,
                "available_connections": len(self.pool._available_connections),
                "in_use_connections": len(self.pool._in_use_connections)
            }
            
            # 客户端统计
            client_stats = asdict(self.stats)
            client_stats['hit_rate'] = self.stats.get_hit_rate()
            client_stats['uptime'] = self.stats.get_uptime()
            
            return {
                "status": "healthy",
                "ping": ping_result,
                "redis_info": {
                    "version": info.get("redis_version"),
                    "uptime_in_seconds": info.get("uptime_in_seconds"),
                    "connected_clients": info.get("connected_clients"),
                    "used_memory_human": info.get("used_memory_human"),
                    "keyspace_hits": info.get("keyspace_hits"),
                    "keyspace_misses": info.get("keyspace_misses")
                },
                "pool_stats": pool_stats,
                "client_stats": client_stats,
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Redis health check failed: {str(e)}")
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
    
    # 连接池管理
    def reset_connection_pool(self):
        """重置连接池"""
        try:
            self.pool.disconnect()
            self.pool = self._create_connection_pool()
            self.client = redis.Redis(connection_pool=self.pool)
            logger.info("Redis connection pool reset successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to reset Redis connection pool: {str(e)}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = asdict(self.stats)
        stats['hit_rate'] = self.stats.get_hit_rate()
        stats['uptime'] = self.stats.get_uptime()
        return stats


# 全局Redis客户端实例
redis_client = RedisClient()
