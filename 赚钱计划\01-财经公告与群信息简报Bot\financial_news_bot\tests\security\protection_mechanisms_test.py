#!/usr/bin/env python3
"""
防护机制测试
验证API限流和防刷机制、防止暴力破解登录、输入数据验证和过滤、文件上传安全限制、错误信息不泄露敏感数据
"""
import asyncio
import aiohttp
import requests
import time
import json
import logging
import random
import string
from datetime import datetime
from typing import Dict, List, Any, Optional
import argparse
from concurrent.futures import ThreadPoolExecutor, as_completed

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProtectionMechanismsTester:
    """防护机制测试器"""
    
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.test_results = []
    
    def test_rate_limiting(self) -> Dict[str, Any]:
        """测试API限流机制"""
        logger.info("测试API限流机制...")
        
        results = {
            'test_name': 'API限流机制测试',
            'timestamp': datetime.now().isoformat(),
            'tests': []
        }
        
        # 测试端点
        test_endpoints = [
            '/api/v1/news/',
            '/api/v1/news/search',
            '/api/v1/auth/login'
        ]
        
        for endpoint in test_endpoints:
            logger.info(f"测试端点限流: {endpoint}")
            
            # 快速发送大量请求
            request_count = 100
            success_count = 0
            rate_limited_count = 0
            error_count = 0
            
            start_time = time.time()
            
            for i in range(request_count):
                try:
                    if endpoint == '/api/v1/auth/login':
                        # 登录端点使用POST
                        response = requests.post(
                            f"{self.base_url}{endpoint}",
                            json={'email': '<EMAIL>', 'password': 'test123'},
                            timeout=5
                        )
                    else:
                        # 其他端点使用GET
                        response = requests.get(
                            f"{self.base_url}{endpoint}",
                            timeout=5
                        )
                    
                    if response.status_code == 200:
                        success_count += 1
                    elif response.status_code == 429:  # Too Many Requests
                        rate_limited_count += 1
                    else:
                        error_count += 1
                        
                except Exception as e:
                    error_count += 1
                
                # 短暂延迟避免过快请求
                time.sleep(0.01)
            
            total_time = time.time() - start_time
            
            # 分析结果
            if rate_limited_count > 0:
                results['tests'].append({
                    'endpoint': endpoint,
                    'status': 'PASS',
                    'details': f'检测到限流: {rate_limited_count}/{request_count} 请求被限制',
                    'metrics': {
                        'total_requests': request_count,
                        'success_requests': success_count,
                        'rate_limited_requests': rate_limited_count,
                        'error_requests': error_count,
                        'total_time': total_time,
                        'requests_per_second': request_count / total_time
                    }
                })
            else:
                results['tests'].append({
                    'endpoint': endpoint,
                    'status': 'FAIL',
                    'details': f'未检测到限流: {success_count}/{request_count} 请求成功',
                    'metrics': {
                        'total_requests': request_count,
                        'success_requests': success_count,
                        'rate_limited_requests': rate_limited_count,
                        'error_requests': error_count,
                        'total_time': total_time,
                        'requests_per_second': request_count / total_time
                    }
                })
        
        return results
    
    def test_brute_force_protection(self) -> Dict[str, Any]:
        """测试暴力破解防护"""
        logger.info("测试暴力破解防护...")
        
        results = {
            'test_name': '暴力破解防护测试',
            'timestamp': datetime.now().isoformat(),
            'tests': []
        }
        
        # 测试登录暴力破解防护
        test_email = '<EMAIL>'
        wrong_passwords = ['wrong1', 'wrong2', 'wrong3', 'wrong4', 'wrong5']
        
        attempt_count = 0
        locked_out = False
        
        for password in wrong_passwords:
            try:
                response = requests.post(
                    f"{self.base_url}/api/v1/auth/login",
                    json={'email': test_email, 'password': password},
                    timeout=10
                )
                
                attempt_count += 1
                
                if response.status_code == 429:  # Too Many Requests
                    locked_out = True
                    break
                elif response.status_code == 423:  # Locked
                    locked_out = True
                    break
                
                # 短暂延迟
                time.sleep(1)
                
            except Exception as e:
                logger.warning(f"暴力破解测试请求失败: {e}")
        
        if locked_out:
            results['tests'].append({
                'test_type': '登录暴力破解防护',
                'status': 'PASS',
                'details': f'在{attempt_count}次尝试后触发防护机制'
            })
        else:
            results['tests'].append({
                'test_type': '登录暴力破解防护',
                'status': 'FAIL',
                'details': f'进行了{attempt_count}次错误登录尝试，未触发防护机制'
            })
        
        # 测试密码重置暴力破解防护
        reset_attempt_count = 0
        reset_limited = False
        
        for i in range(10):
            try:
                response = requests.post(
                    f"{self.base_url}/api/v1/auth/reset-password",
                    json={'email': test_email},
                    timeout=10
                )
                
                reset_attempt_count += 1
                
                if response.status_code == 429:
                    reset_limited = True
                    break
                
                time.sleep(0.5)
                
            except Exception as e:
                logger.warning(f"密码重置测试请求失败: {e}")
        
        if reset_limited:
            results['tests'].append({
                'test_type': '密码重置暴力破解防护',
                'status': 'PASS',
                'details': f'在{reset_attempt_count}次尝试后触发限流'
            })
        else:
            results['tests'].append({
                'test_type': '密码重置暴力破解防护',
                'status': 'WARN',
                'details': f'进行了{reset_attempt_count}次密码重置请求，未明确触发限流'
            })
        
        return results
    
    def test_input_validation(self) -> Dict[str, Any]:
        """测试输入数据验证和过滤"""
        logger.info("测试输入数据验证和过滤...")
        
        results = {
            'test_name': '输入数据验证测试',
            'timestamp': datetime.now().isoformat(),
            'tests': []
        }
        
        # 恶意输入测试载荷
        malicious_payloads = [
            # SQL注入
            "'; DROP TABLE users; --",
            "' OR '1'='1",
            "1' UNION SELECT * FROM users--",
            
            # XSS
            "<script>alert('XSS')</script>",
            "<img src=x onerror=alert('XSS')>",
            "javascript:alert('XSS')",
            
            # 命令注入
            "; ls -la; echo",
            "| cat /etc/passwd",
            "&& rm -rf /",
            
            # 路径遍历
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\config\\sam",
            
            # 长字符串
            "A" * 10000,
            
            # 特殊字符
            "null\x00byte",
            "\r\n\r\nHTTP/1.1 200 OK\r\n\r\n",
        ]
        
        # 测试端点和参数
        test_cases = [
            {
                'endpoint': '/api/v1/auth/register',
                'method': 'POST',
                'params': ['email', 'username', 'password']
            },
            {
                'endpoint': '/api/v1/news/search',
                'method': 'GET',
                'params': ['q', 'category']
            },
            {
                'endpoint': '/api/v1/subscriptions/',
                'method': 'POST',
                'params': ['name', 'description']
            }
        ]
        
        for test_case in test_cases:
            endpoint = test_case['endpoint']
            method = test_case['method']
            params = test_case['params']
            
            for param in params:
                for payload in malicious_payloads[:5]:  # 只测试前5个载荷
                    try:
                        if method == 'GET':
                            response = requests.get(
                                f"{self.base_url}{endpoint}",
                                params={param: payload},
                                timeout=10
                            )
                        else:  # POST
                            data = {param: payload}
                            response = requests.post(
                                f"{self.base_url}{endpoint}",
                                json=data,
                                timeout=10
                            )
                        
                        # 检查响应
                        if response.status_code == 400:
                            # 输入验证拒绝了恶意输入
                            results['tests'].append({
                                'endpoint': endpoint,
                                'parameter': param,
                                'payload_type': self._classify_payload(payload),
                                'status': 'PASS',
                                'details': '恶意输入被正确拒绝'
                            })
                        elif response.status_code == 200:
                            # 检查响应中是否包含原始载荷（可能存在XSS）
                            if payload in response.text:
                                results['tests'].append({
                                    'endpoint': endpoint,
                                    'parameter': param,
                                    'payload_type': self._classify_payload(payload),
                                    'status': 'FAIL',
                                    'details': '恶意输入被反射到响应中'
                                })
                            else:
                                results['tests'].append({
                                    'endpoint': endpoint,
                                    'parameter': param,
                                    'payload_type': self._classify_payload(payload),
                                    'status': 'PASS',
                                    'details': '输入被接受但已过滤'
                                })
                        else:
                            results['tests'].append({
                                'endpoint': endpoint,
                                'parameter': param,
                                'payload_type': self._classify_payload(payload),
                                'status': 'WARN',
                                'details': f'意外的响应状态码: {response.status_code}'
                            })
                            
                    except Exception as e:
                        results['tests'].append({
                            'endpoint': endpoint,
                            'parameter': param,
                            'payload_type': self._classify_payload(payload),
                            'status': 'ERROR',
                            'details': f'测试失败: {str(e)}'
                        })
                    
                    # 避免过于频繁的请求
                    time.sleep(0.1)
        
        return results
    
    def _classify_payload(self, payload: str) -> str:
        """分类恶意载荷类型"""
        if 'DROP TABLE' in payload or 'UNION SELECT' in payload:
            return 'SQL注入'
        elif '<script>' in payload or 'javascript:' in payload:
            return 'XSS'
        elif '; ls' in payload or '&& rm' in payload:
            return '命令注入'
        elif '../' in payload or '..\\' in payload:
            return '路径遍历'
        elif len(payload) > 1000:
            return '长字符串'
        else:
            return '特殊字符'
    
    def test_file_upload_security(self) -> Dict[str, Any]:
        """测试文件上传安全限制"""
        logger.info("测试文件上传安全限制...")
        
        results = {
            'test_name': '文件上传安全测试',
            'timestamp': datetime.now().isoformat(),
            'tests': []
        }
        
        # 恶意文件测试
        malicious_files = [
            {
                'name': 'test.php',
                'content': b'<?php system($_GET["cmd"]); ?>',
                'type': 'PHP脚本'
            },
            {
                'name': 'test.jsp',
                'content': b'<%@ page import="java.io.*" %><% Runtime.getRuntime().exec(request.getParameter("cmd")); %>',
                'type': 'JSP脚本'
            },
            {
                'name': 'test.exe',
                'content': b'MZ\x90\x00' + b'\x00' * 100,  # PE文件头
                'type': '可执行文件'
            },
            {
                'name': 'test.html',
                'content': b'<script>alert("XSS")</script>',
                'type': 'HTML文件'
            },
            {
                'name': 'huge_file.txt',
                'content': b'A' * (10 * 1024 * 1024),  # 10MB文件
                'type': '大文件'
            }
        ]
        
        upload_endpoints = [
            '/api/v1/upload/avatar',
            '/api/v1/upload/document',
            '/api/v1/admin/upload'
        ]
        
        for endpoint in upload_endpoints:
            for malicious_file in malicious_files:
                try:
                    files = {
                        'file': (malicious_file['name'], malicious_file['content'], 'application/octet-stream')
                    }
                    
                    response = requests.post(
                        f"{self.base_url}{endpoint}",
                        files=files,
                        timeout=30
                    )
                    
                    if response.status_code == 400:
                        results['tests'].append({
                            'endpoint': endpoint,
                            'file_type': malicious_file['type'],
                            'status': 'PASS',
                            'details': '恶意文件上传被正确拒绝'
                        })
                    elif response.status_code == 413:  # Payload Too Large
                        results['tests'].append({
                            'endpoint': endpoint,
                            'file_type': malicious_file['type'],
                            'status': 'PASS',
                            'details': '大文件上传被正确限制'
                        })
                    elif response.status_code == 404:
                        results['tests'].append({
                            'endpoint': endpoint,
                            'file_type': malicious_file['type'],
                            'status': 'SKIP',
                            'details': '上传端点不存在'
                        })
                        break  # 如果端点不存在，跳过该端点的其他测试
                    elif response.status_code == 200:
                        results['tests'].append({
                            'endpoint': endpoint,
                            'file_type': malicious_file['type'],
                            'status': 'FAIL',
                            'details': '恶意文件上传成功，存在安全风险'
                        })
                    else:
                        results['tests'].append({
                            'endpoint': endpoint,
                            'file_type': malicious_file['type'],
                            'status': 'WARN',
                            'details': f'意外的响应状态码: {response.status_code}'
                        })
                        
                except Exception as e:
                    results['tests'].append({
                        'endpoint': endpoint,
                        'file_type': malicious_file['type'],
                        'status': 'ERROR',
                        'details': f'测试失败: {str(e)}'
                    })
        
        return results
    
    def test_error_information_disclosure(self) -> Dict[str, Any]:
        """测试错误信息泄露"""
        logger.info("测试错误信息泄露...")
        
        results = {
            'test_name': '错误信息泄露测试',
            'timestamp': datetime.now().isoformat(),
            'tests': []
        }
        
        # 触发错误的测试用例
        error_test_cases = [
            {
                'endpoint': '/api/v1/nonexistent',
                'method': 'GET',
                'expected_status': 404,
                'description': '不存在的端点'
            },
            {
                'endpoint': '/api/v1/auth/login',
                'method': 'POST',
                'data': {'invalid': 'data'},
                'expected_status': 400,
                'description': '无效的请求数据'
            },
            {
                'endpoint': '/api/v1/auth/me',
                'method': 'GET',
                'expected_status': 401,
                'description': '未认证访问'
            },
            {
                'endpoint': '/api/v1/admin/dashboard',
                'method': 'GET',
                'expected_status': 401,
                'description': '未授权访问'
            }
        ]
        
        # 敏感信息模式
        sensitive_patterns = [
            'password',
            'secret',
            'key',
            'token',
            'database',
            'mysql',
            'postgresql',
            'redis',
            'stack trace',
            'traceback',
            'exception',
            'error at line',
            'file not found',
            'access denied',
            'internal server error',
            'debug',
            'localhost',
            '127.0.0.1',
            'root',
            'admin'
        ]
        
        for test_case in error_test_cases:
            try:
                if test_case['method'] == 'GET':
                    response = requests.get(
                        f"{self.base_url}{test_case['endpoint']}",
                        timeout=10
                    )
                else:  # POST
                    response = requests.post(
                        f"{self.base_url}{test_case['endpoint']}",
                        json=test_case.get('data', {}),
                        timeout=10
                    )
                
                # 检查响应内容
                response_text = response.text.lower()
                
                # 查找敏感信息
                found_sensitive = []
                for pattern in sensitive_patterns:
                    if pattern in response_text:
                        found_sensitive.append(pattern)
                
                if found_sensitive:
                    results['tests'].append({
                        'test_case': test_case['description'],
                        'endpoint': test_case['endpoint'],
                        'status': 'FAIL',
                        'details': f'错误响应中发现敏感信息: {", ".join(found_sensitive)}',
                        'response_preview': response.text[:200] + '...' if len(response.text) > 200 else response.text
                    })
                else:
                    results['tests'].append({
                        'test_case': test_case['description'],
                        'endpoint': test_case['endpoint'],
                        'status': 'PASS',
                        'details': '错误响应未泄露敏感信息'
                    })
                    
            except Exception as e:
                results['tests'].append({
                    'test_case': test_case['description'],
                    'endpoint': test_case['endpoint'],
                    'status': 'ERROR',
                    'details': f'测试失败: {str(e)}'
                })
        
        return results
    
    def run_all_protection_tests(self) -> Dict[str, Any]:
        """运行所有防护机制测试"""
        logger.info("开始运行所有防护机制测试...")
        
        all_results = {
            'timestamp': datetime.now().isoformat(),
            'test_suite': '防护机制测试',
            'tests': []
        }
        
        # 运行各项测试
        all_results['tests'].append(self.test_rate_limiting())
        all_results['tests'].append(self.test_brute_force_protection())
        all_results['tests'].append(self.test_input_validation())
        all_results['tests'].append(self.test_file_upload_security())
        all_results['tests'].append(self.test_error_information_disclosure())
        
        # 计算总体结果
        total_tests = 0
        passed_tests = 0
        failed_tests = 0
        
        for test_group in all_results['tests']:
            for test in test_group.get('tests', []):
                total_tests += 1
                if test['status'] == 'PASS':
                    passed_tests += 1
                elif test['status'] == 'FAIL':
                    failed_tests += 1
        
        all_results['summary'] = {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': failed_tests,
            'success_rate': (passed_tests / total_tests * 100) if total_tests > 0 else 0
        }
        
        return all_results
    
    def save_results(self, results: Dict[str, Any], output_file: str):
        """保存测试结果"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            logger.info(f"防护机制测试结果已保存: {output_file}")
        except Exception as e:
            logger.error(f"保存结果失败: {e}")


def main():
    parser = argparse.ArgumentParser(description='防护机制测试')
    parser.add_argument('--base-url', default='http://localhost:8000', help='目标服务URL')
    parser.add_argument('--output', default='protection_mechanisms_test_results.json', help='输出文件')
    
    args = parser.parse_args()
    
    tester = ProtectionMechanismsTester(args.base_url)
    
    try:
        results = tester.run_all_protection_tests()
        tester.save_results(results, args.output)
        
        # 输出摘要
        summary = results['summary']
        print(f"\n=== 防护机制测试摘要 ===")
        print(f"总测试数: {summary['total_tests']}")
        print(f"通过测试: {summary['passed_tests']}")
        print(f"失败测试: {summary['failed_tests']}")
        print(f"成功率: {summary['success_rate']:.1f}%")
        
        # 显示失败的测试
        failed_tests = []
        for test_group in results['tests']:
            for test in test_group.get('tests', []):
                if test['status'] == 'FAIL':
                    test_name = f"{test_group['test_name']} - "
                    if 'endpoint' in test:
                        test_name += test['endpoint']
                    elif 'test_type' in test:
                        test_name += test['test_type']
                    elif 'test_case' in test:
                        test_name += test['test_case']
                    failed_tests.append(test_name)
        
        if failed_tests:
            print(f"\n❌ 失败的测试:")
            for i, test in enumerate(failed_tests, 1):
                print(f"{i}. {test}")
        else:
            print(f"\n✅ 所有测试通过!")
        
        print(f"\n📋 详细结果: {args.output}")
        
    except Exception as e:
        logger.error(f"防护机制测试失败: {e}")
        return 1
    
    return 0


if __name__ == '__main__':
    exit(main())
