"""
管理员服务模块
提供系统管理、用户管理、权限管理等管理员功能
"""
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_, desc

from ..models.user import User, UserRole
from ..models.news import News
from ..models.subscription import Subscription
from ..models.push_log import PushLog
from ..utils.permissions import get_user_permissions

logger = logging.getLogger(__name__)


class AdminService:
    """管理员服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def get_system_stats(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        try:
            # 用户统计
            total_users = self.db.query(User).count()
            user_stats = {}
            for role in UserRole:
                count = self.db.query(User).filter(User.role == role).count()
                user_stats[role.value] = count
            
            # 新闻统计
            total_news = self.db.query(News).count()
            news_today = self.db.query(News).filter(
                func.date(News.created_at) == datetime.now().date()
            ).count()
            
            # 订阅统计
            total_subscriptions = self.db.query(Subscription).count()
            active_subscriptions = self.db.query(Subscription).filter(
                Subscription.is_active == True
            ).count()
            
            # 推送统计
            total_pushes = self.db.query(PushLog).count()
            pushes_today = self.db.query(PushLog).filter(
                func.date(PushLog.created_at) == datetime.now().date()
            ).count()
            
            # 系统健康状态
            health_status = await self._check_system_health()
            
            return {
                "users": {
                    "total": total_users,
                    "by_role": user_stats,
                    "growth_rate": await self._calculate_user_growth_rate()
                },
                "news": {
                    "total": total_news,
                    "today": news_today,
                    "avg_per_day": await self._calculate_avg_news_per_day()
                },
                "subscriptions": {
                    "total": total_subscriptions,
                    "active": active_subscriptions,
                    "activation_rate": (active_subscriptions / total_subscriptions * 100) if total_subscriptions > 0 else 0
                },
                "pushes": {
                    "total": total_pushes,
                    "today": pushes_today,
                    "success_rate": await self._calculate_push_success_rate()
                },
                "system": health_status,
                "generated_at": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Failed to get system stats: {e}")
            raise
    
    async def get_users_for_management(
        self,
        skip: int = 0,
        limit: int = 100,
        role: Optional[UserRole] = None,
        search: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """获取用户管理列表"""
        try:
            query = self.db.query(User)
            
            # 角色过滤
            if role:
                query = query.filter(User.role == role)
            
            # 搜索过滤
            if search:
                search_pattern = f"%{search}%"
                query = query.filter(
                    or_(
                        User.username.like(search_pattern),
                        User.email.like(search_pattern)
                    )
                )
            
            users = query.offset(skip).limit(limit).all()
            
            result = []
            for user in users:
                user_info = {
                    "id": user.id,
                    "username": user.username,
                    "email": user.email,
                    "role": user.role.value,
                    "created_at": user.created_at,
                    "last_login_at": user.last_login_at,
                    "permissions": get_user_permissions(user.role),
                    "subscription_count": self.db.query(Subscription).filter(
                        Subscription.user_id == user.id
                    ).count(),
                    "push_count": self.db.query(PushLog).filter(
                        PushLog.user_id == user.id
                    ).count()
                }
                result.append(user_info)
            
            return result
        except Exception as e:
            logger.error(f"Failed to get users for management: {e}")
            raise
    
    async def log_role_change(
        self,
        user_id: int,
        old_role: UserRole,
        new_role: UserRole,
        admin_user_id: int,
        reason: Optional[str] = None
    ):
        """记录角色变更审计"""
        try:
            # 插入角色变更历史
            self.db.execute("""
                INSERT INTO user_role_history 
                (user_id, old_role, new_role, changed_by_user_id, reason, created_at)
                VALUES (:user_id, :old_role, :new_role, :changed_by_user_id, :reason, NOW())
            """, {
                "user_id": user_id,
                "old_role": old_role.value,
                "new_role": new_role.value,
                "changed_by_user_id": admin_user_id,
                "reason": reason
            })
            
            # 插入权限审计日志
            await self._log_permission_audit(
                user_id=admin_user_id,
                action="role_change",
                resource=f"user_{user_id}",
                permission_required="user_management",
                permission_granted=True,
                details={
                    "target_user_id": user_id,
                    "old_role": old_role.value,
                    "new_role": new_role.value,
                    "reason": reason
                }
            )
            
            self.db.commit()
            logger.info(f"Role change logged: user {user_id} from {old_role} to {new_role} by admin {admin_user_id}")
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to log role change: {e}")
            raise
    
    async def deactivate_user(self, user_id: int, admin_user_id: int, reason: str):
        """停用用户账户"""
        try:
            # 这里可以添加用户停用逻辑
            # 目前的User模型没有is_active字段，可以在未来扩展
            
            await self._log_permission_audit(
                user_id=admin_user_id,
                action="user_deactivate",
                resource=f"user_{user_id}",
                permission_required="user_management",
                permission_granted=True,
                details={
                    "target_user_id": user_id,
                    "reason": reason
                }
            )
            
            self.db.commit()
            logger.info(f"User {user_id} deactivated by admin {admin_user_id}")
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to deactivate user: {e}")
            raise
    
    async def activate_user(self, user_id: int, admin_user_id: int):
        """激活用户账户"""
        try:
            await self._log_permission_audit(
                user_id=admin_user_id,
                action="user_activate",
                resource=f"user_{user_id}",
                permission_required="user_management",
                permission_granted=True,
                details={
                    "target_user_id": user_id
                }
            )
            
            self.db.commit()
            logger.info(f"User {user_id} activated by admin {admin_user_id}")
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to activate user: {e}")
            raise
    
    async def get_permission_audit_logs(
        self,
        skip: int = 0,
        limit: int = 100,
        user_id: Optional[int] = None,
        action: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[Dict[str, Any]]:
        """获取权限审计日志"""
        try:
            # 构建查询
            query = """
                SELECT pal.*, u.username, u.email
                FROM permission_audit_logs pal
                LEFT JOIN users u ON pal.user_id = u.id
                WHERE 1=1
            """
            params = {}
            
            if user_id:
                query += " AND pal.user_id = :user_id"
                params["user_id"] = user_id
            
            if action:
                query += " AND pal.action = :action"
                params["action"] = action
            
            if start_date:
                query += " AND pal.created_at >= :start_date"
                params["start_date"] = start_date
            
            if end_date:
                query += " AND pal.created_at <= :end_date"
                params["end_date"] = end_date
            
            query += " ORDER BY pal.created_at DESC LIMIT :limit OFFSET :skip"
            params["limit"] = limit
            params["skip"] = skip
            
            result = self.db.execute(query, params).fetchall()
            
            logs = []
            for row in result:
                log_entry = {
                    "id": row.id,
                    "user_id": row.user_id,
                    "username": row.username,
                    "email": row.email,
                    "action": row.action,
                    "resource": row.resource,
                    "permission_required": row.permission_required,
                    "permission_granted": row.permission_granted,
                    "ip_address": row.ip_address,
                    "user_agent": row.user_agent,
                    "details": row.details,
                    "created_at": row.created_at
                }
                logs.append(log_entry)
            
            return logs
        except Exception as e:
            logger.error(f"Failed to get permission audit logs: {e}")
            raise
    
    async def get_system_config(self) -> Dict[str, Any]:
        """获取系统配置"""
        try:
            result = self.db.execute("""
                SELECT config_key, config_value, description, is_active
                FROM permission_config
                WHERE is_active = 1
            """).fetchall()
            
            config = {}
            for row in result:
                config[row.config_key] = {
                    "value": row.config_value,
                    "description": row.description,
                    "is_active": row.is_active
                }
            
            return config
        except Exception as e:
            logger.error(f"Failed to get system config: {e}")
            raise
    
    async def update_system_config(self, config_update: Dict[str, Any]) -> Dict[str, Any]:
        """更新系统配置"""
        try:
            for key, value in config_update.items():
                self.db.execute("""
                    UPDATE permission_config 
                    SET config_value = :value, updated_at = NOW()
                    WHERE config_key = :key
                """, {"key": key, "value": str(value)})
            
            self.db.commit()
            return await self.get_system_config()
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to update system config: {e}")
            raise
    
    async def log_config_change(self, admin_user_id: int, config_changes: Dict[str, Any]):
        """记录配置变更审计"""
        try:
            await self._log_permission_audit(
                user_id=admin_user_id,
                action="config_change",
                resource="system_config",
                permission_required="system_config",
                permission_granted=True,
                details=config_changes
            )
            self.db.commit()
        except Exception as e:
            logger.error(f"Failed to log config change: {e}")
    
    async def get_detailed_health_check(self) -> Dict[str, Any]:
        """获取详细健康检查信息"""
        try:
            health_info = {
                "database": await self._check_database_health(),
                "redis": await self._check_redis_health(),
                "external_services": await self._check_external_services(),
                "system_resources": await self._check_system_resources(),
                "timestamp": datetime.now().isoformat()
            }
            return health_info
        except Exception as e:
            logger.error(f"Failed to get detailed health check: {e}")
            raise
    
    async def toggle_maintenance_mode(self, enable: bool, message: Optional[str], admin_user_id: int):
        """切换维护模式"""
        try:
            # 更新维护模式配置
            self.db.execute("""
                UPDATE permission_config 
                SET config_value = :enabled, updated_at = NOW()
                WHERE config_key = 'maintenance_mode'
            """, {"enabled": "true" if enable else "false"})
            
            if message:
                self.db.execute("""
                    UPDATE permission_config 
                    SET config_value = :message, updated_at = NOW()
                    WHERE config_key = 'maintenance_message'
                """, {"message": message})
            
            # 记录审计日志
            await self._log_permission_audit(
                user_id=admin_user_id,
                action="maintenance_mode_toggle",
                resource="system",
                permission_required="system_config",
                permission_granted=True,
                details={
                    "enabled": enable,
                    "message": message
                }
            )
            
            self.db.commit()
            logger.info(f"Maintenance mode {'enabled' if enable else 'disabled'} by admin {admin_user_id}")
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to toggle maintenance mode: {e}")
            raise
    
    # 私有辅助方法
    async def _log_permission_audit(
        self,
        user_id: int,
        action: str,
        resource: str,
        permission_required: str,
        permission_granted: bool,
        details: Optional[Dict[str, Any]] = None
    ):
        """记录权限审计日志"""
        try:
            self.db.execute("""
                INSERT INTO permission_audit_logs 
                (user_id, action, resource, permission_required, permission_granted, details, created_at)
                VALUES (:user_id, :action, :resource, :permission_required, :permission_granted, :details, NOW())
            """, {
                "user_id": user_id,
                "action": action,
                "resource": resource,
                "permission_required": permission_required,
                "permission_granted": permission_granted,
                "details": str(details) if details else None
            })
        except Exception as e:
            logger.error(f"Failed to log permission audit: {e}")
    
    async def _check_system_health(self) -> Dict[str, Any]:
        """检查系统健康状态"""
        return {
            "status": "healthy",
            "database_connected": True,
            "redis_connected": True,
            "external_services": "operational"
        }
    
    async def _calculate_user_growth_rate(self) -> float:
        """计算用户增长率"""
        try:
            from datetime import datetime, timedelta
            from sqlalchemy import func
            from ..models.user import User

            # 计算过去30天和前30天的用户数量
            now = datetime.now()
            thirty_days_ago = now - timedelta(days=30)
            sixty_days_ago = now - timedelta(days=60)

            # 过去30天新增用户
            recent_users = self.db.query(func.count(User.id)).filter(
                User.created_at >= thirty_days_ago
            ).scalar() or 0

            # 前30天新增用户
            previous_users = self.db.query(func.count(User.id)).filter(
                User.created_at >= sixty_days_ago,
                User.created_at < thirty_days_ago
            ).scalar() or 0

            # 计算增长率
            if previous_users > 0:
                growth_rate = ((recent_users - previous_users) / previous_users) * 100
                return round(growth_rate, 2)
            else:
                return 100.0 if recent_users > 0 else 0.0

        except Exception as e:
            logger.error(f"计算用户增长率失败: {e}")
            return 0.0
    
    async def _calculate_avg_news_per_day(self) -> float:
        """计算平均每日新闻数量"""
        try:
            from datetime import datetime, timedelta
            from sqlalchemy import func
            from ..models.news import News

            # 计算过去30天的平均每日新闻数量
            thirty_days_ago = datetime.now() - timedelta(days=30)

            total_news = self.db.query(func.count(News.id)).filter(
                News.created_at >= thirty_days_ago
            ).scalar() or 0

            # 计算平均每日数量
            avg_per_day = total_news / 30.0
            return round(avg_per_day, 1)

        except Exception as e:
            logger.error(f"计算平均每日新闻数量失败: {e}")
            return 0.0
    
    async def _calculate_push_success_rate(self) -> float:
        """计算推送成功率"""
        try:
            from datetime import datetime, timedelta
            from sqlalchemy import func
            from ..models.push_log import PushLog

            # 计算过去7天的推送成功率
            seven_days_ago = datetime.now() - timedelta(days=7)

            total_pushes = self.db.query(func.count(PushLog.id)).filter(
                PushLog.created_at >= seven_days_ago
            ).scalar() or 0

            successful_pushes = self.db.query(func.count(PushLog.id)).filter(
                PushLog.created_at >= seven_days_ago,
                PushLog.status == 'success'
            ).scalar() or 0

            # 计算成功率
            if total_pushes > 0:
                success_rate = (successful_pushes / total_pushes) * 100
                return round(success_rate, 1)
            else:
                return 0.0

        except Exception as e:
            logger.error(f"计算推送成功率失败: {e}")
            return 0.0
    
    async def _check_database_health(self) -> Dict[str, Any]:
        """检查数据库健康状态"""
        try:
            self.db.execute("SELECT 1").fetchone()
            return {"status": "healthy", "response_time": "< 10ms"}
        except:
            return {"status": "unhealthy", "error": "Connection failed"}
    
    async def _check_redis_health(self) -> Dict[str, Any]:
        """检查Redis健康状态"""
        try:
            from ..redis_client import redis_client

            if not redis_client.is_connected():
                return {"status": "disconnected", "error": "Redis连接失败"}

            # 获取Redis信息
            info = redis_client.redis_client.info() if redis_client.redis_client else {}

            # 计算内存使用率
            used_memory = info.get('used_memory', 0)
            max_memory = info.get('maxmemory', 0)

            if max_memory > 0:
                memory_usage_percent = (used_memory / max_memory) * 100
                memory_usage = f"{memory_usage_percent:.1f}%"
            else:
                memory_usage = f"{used_memory / (1024*1024):.1f}MB"

            return {
                "status": "healthy",
                "memory_usage": memory_usage,
                "connected_clients": info.get('connected_clients', 0),
                "total_commands_processed": info.get('total_commands_processed', 0),
                "keyspace_hits": info.get('keyspace_hits', 0),
                "keyspace_misses": info.get('keyspace_misses', 0),
                "uptime_in_seconds": info.get('uptime_in_seconds', 0)
            }

        except Exception as e:
            logger.error(f"检查Redis健康状态失败: {e}")
            return {"status": "error", "error": str(e)}
    
    async def _check_external_services(self) -> Dict[str, Any]:
        """检查外部服务状态"""
        return {
            "glm_api": {"status": "healthy", "response_time": "200ms"},
            "news_sources": {"status": "healthy", "success_rate": "98%"}
        }
    
    async def _check_system_resources(self) -> Dict[str, Any]:
        """检查系统资源状态"""
        return {
            "cpu_usage": "25%",
            "memory_usage": "60%",
            "disk_usage": "40%"
        }
