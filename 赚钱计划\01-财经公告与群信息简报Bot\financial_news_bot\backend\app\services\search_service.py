"""
搜索增强服务
提供全文搜索、搜索建议、自动补全等功能
"""
import logging
import re
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, func, text

from app.models.news import News
from app.services.cache_service import cache_service, cache_result
from app.config import settings
import jieba

logger = logging.getLogger(__name__)

class SearchService:
    """搜索增强服务类"""
    
    def __init__(self, db: Session):
        self.db = db
        # 初始化jieba分词
        jieba.initialize()
    
    def enhanced_search(self, query: str, page: int = 1, limit: int = 20, 
                       filters: Dict = None) -> Tuple[List[News], int]:
        """
        增强的全文搜索
        
        Args:
            query: 搜索查询
            page: 页码
            limit: 每页数量
            filters: 过滤条件
        
        Returns:
            (新闻列表, 总数)
        """
        # 预处理查询
        processed_query = self._preprocess_query(query)
        
        # 构建搜索条件
        search_conditions = self._build_search_conditions(processed_query, filters)
        
        # 执行搜索
        base_query = self.db.query(News).filter(search_conditions)
        
        # 按相关性排序
        ordered_query = self._apply_relevance_scoring(base_query, processed_query)
        
        # 获取总数
        total = ordered_query.count()
        
        # 分页
        offset = (page - 1) * limit
        results = ordered_query.offset(offset).limit(limit).all()
        
        return results, total
    
    def _preprocess_query(self, query: str) -> Dict[str, Any]:
        """
        预处理搜索查询
        
        Args:
            query: 原始查询字符串
        
        Returns:
            处理后的查询信息
        """
        # 清理查询字符串
        cleaned_query = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s]', ' ', query).strip()
        
        # 分词
        words = list(jieba.cut(cleaned_query))
        words = [word.strip() for word in words if len(word.strip()) > 1]
        
        # 提取关键词
        keywords = self._extract_keywords(words)
        
        # 检测特殊查询模式
        patterns = self._detect_query_patterns(query)
        
        return {
            'original': query,
            'cleaned': cleaned_query,
            'words': words,
            'keywords': keywords,
            'patterns': patterns
        }
    
    def _extract_keywords(self, words: List[str]) -> List[str]:
        """
        提取关键词
        
        Args:
            words: 分词结果
        
        Returns:
            关键词列表
        """
        # 停用词列表
        stop_words = {
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
            '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好',
            '自己', '这', '那', '里', '就是', '还是', '为了', '还有', '可以', '这个', '那个'
        }
        
        # 过滤停用词和短词
        keywords = [word for word in words if word not in stop_words and len(word) >= 2]
        
        # 按长度和重要性排序
        keywords.sort(key=lambda x: (-len(x), x))
        
        return keywords[:10]  # 最多返回10个关键词
    
    def _detect_query_patterns(self, query: str) -> Dict[str, Any]:
        """
        检测查询模式
        
        Args:
            query: 查询字符串
        
        Returns:
            检测到的模式
        """
        patterns = {}
        
        # 检测股票代码
        stock_pattern = r'\b\d{6}\b'
        stock_codes = re.findall(stock_pattern, query)
        if stock_codes:
            patterns['stock_codes'] = stock_codes
        
        # 检测公司名称
        company_pattern = r'[\u4e00-\u9fa5]{2,}(?:公司|集团|股份|有限|科技|投资|控股|发展|建设|实业)'
        companies = re.findall(company_pattern, query)
        if companies:
            patterns['companies'] = companies
        
        # 检测时间表达式
        time_patterns = [
            r'\d{4}年\d{1,2}月',
            r'\d{4}-\d{1,2}-\d{1,2}',
            r'今天|昨天|前天|本周|上周|本月|上月|今年|去年'
        ]
        for pattern in time_patterns:
            matches = re.findall(pattern, query)
            if matches:
                patterns['time_expressions'] = matches
                break
        
        # 检测金额表达式
        amount_pattern = r'\d+(?:\.\d+)?(?:万|亿|千万|百万)?元'
        amounts = re.findall(amount_pattern, query)
        if amounts:
            patterns['amounts'] = amounts
        
        return patterns
    
    def _build_search_conditions(self, processed_query: Dict, filters: Dict = None):
        """
        构建搜索条件
        
        Args:
            processed_query: 处理后的查询
            filters: 额外过滤条件
        
        Returns:
            SQLAlchemy查询条件
        """
        conditions = []
        
        # 基础文本搜索
        if processed_query['keywords']:
            text_conditions = []
            for keyword in processed_query['keywords']:
                text_conditions.append(
                    or_(
                        News.title.contains(keyword),
                        News.content.contains(keyword),
                        News.summary.contains(keyword)
                    )
                )
            conditions.append(or_(*text_conditions))
        
        # 特殊模式搜索
        patterns = processed_query.get('patterns', {})
        
        # 股票代码搜索
        if 'stock_codes' in patterns:
            stock_conditions = []
            for code in patterns['stock_codes']:
                stock_conditions.append(News.stock_codes.contains(code))
            conditions.append(or_(*stock_conditions))
        
        # 公司名称搜索
        if 'companies' in patterns:
            company_conditions = []
            for company in patterns['companies']:
                company_conditions.append(
                    or_(
                        News.title.contains(company),
                        News.companies.contains(company)
                    )
                )
            conditions.append(or_(*company_conditions))
        
        # 应用额外过滤条件
        if filters:
            if filters.get('source'):
                conditions.append(News.source == filters['source'])
            if filters.get('category'):
                conditions.append(News.category == filters['category'])
            if filters.get('start_date'):
                conditions.append(News.published_at >= filters['start_date'])
            if filters.get('end_date'):
                conditions.append(News.published_at <= filters['end_date'])
            if filters.get('importance_min') is not None:
                conditions.append(News.importance_score >= filters['importance_min'])
        
        return and_(*conditions) if conditions else text('1=1')
    
    def _apply_relevance_scoring(self, query, processed_query: Dict):
        """
        应用相关性评分排序
        
        Args:
            query: SQLAlchemy查询对象
            processed_query: 处理后的查询
        
        Returns:
            排序后的查询对象
        """
        # 简单的相关性评分：标题匹配 > 重要性评分 > 发布时间
        keywords = processed_query.get('keywords', [])
        
        if keywords:
            # 计算标题匹配度
            title_score_cases = []
            for i, keyword in enumerate(keywords):
                weight = len(keywords) - i  # 前面的关键词权重更高
                title_score_cases.append(
                    f"CASE WHEN title LIKE '%{keyword}%' THEN {weight * 10} ELSE 0 END"
                )
            
            title_score_sql = " + ".join(title_score_cases)
            
            # 按相关性评分排序
            return query.order_by(
                desc(text(f"({title_score_sql})")),
                desc(News.importance_score),
                desc(News.published_at)
            )
        else:
            # 默认排序
            return query.order_by(
                desc(News.importance_score),
                desc(News.published_at)
            )
    
    @cache_result("search_suggestions", expire=3600)
    def get_search_suggestions(self, query: str, limit: int = 10) -> List[str]:
        """
        获取搜索建议
        
        Args:
            query: 查询前缀
            limit: 返回数量限制
        
        Returns:
            建议列表
        """
        if len(query) < 2:
            return []
        
        suggestions = []
        
        # 从新闻标题中提取建议
        title_suggestions = (
            self.db.query(News.title)
            .filter(News.title.contains(query))
            .distinct()
            .limit(limit)
            .all()
        )
        
        for title, in title_suggestions:
            # 提取包含查询词的短语
            words = list(jieba.cut(title))
            for word in words:
                if query in word and len(word) > len(query):
                    suggestions.append(word)
        
        # 去重并排序
        suggestions = list(set(suggestions))
        suggestions.sort(key=lambda x: (len(x), x))
        
        return suggestions[:limit]
    
    @cache_result("popular_searches", expire=3600)
    def get_popular_searches(self, days: int = 7, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取热门搜索词
        
        Args:
            days: 统计天数
            limit: 返回数量
        
        Returns:
            热门搜索词列表
        """
        # 从搜索日志中统计真实的热门搜索词
        start_date = datetime.now() - timedelta(days=days)

        try:
            # 从Redis中获取搜索统计数据
            from ..redis_client import redis_client

            if redis_client.is_connected():
                # 获取搜索词统计
                search_stats_key = f"search_stats:{start_date.strftime('%Y%m%d')}"
                search_data = redis_client.redis_client.hgetall(search_stats_key)

                if search_data:
                    # 按搜索次数排序
                    sorted_searches = sorted(
                        [(k.decode(), int(v)) for k, v in search_data.items()],
                        key=lambda x: x[1],
                        reverse=True
                    )
                    return [{"keyword": k, "count": v} for k, v in sorted_searches[:10]]

        except Exception as e:
            logger.error(f"获取热门搜索词失败: {e}")

        # 如果无法从Redis获取，从数据库查询新闻标题中的热门关键词
        
        # 从新闻标题中提取热门词汇
        recent_news = (
            self.db.query(News.title)
            .filter(News.created_at >= start_date)
            .all()
        )
        
        word_count = {}
        for title, in recent_news:
            words = list(jieba.cut(title))
            for word in words:
                if len(word) >= 2:
                    word_count[word] = word_count.get(word, 0) + 1
        
        # 排序并返回热门词
        popular_words = sorted(word_count.items(), key=lambda x: x[1], reverse=True)
        
        return [
            {"keyword": word, "count": count}
            for word, count in popular_words[:limit]
        ]
