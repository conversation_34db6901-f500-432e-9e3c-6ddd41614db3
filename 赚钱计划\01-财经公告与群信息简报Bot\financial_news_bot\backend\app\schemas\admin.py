"""
管理员相关数据模式
定义管理员API的请求和响应数据结构
"""
from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional
from datetime import datetime
from enum import Enum

from ..models.user import UserRole


class SystemStatsResponse(BaseModel):
    """系统统计响应模式"""
    users: Dict[str, Any] = Field(..., description="用户统计信息")
    news: Dict[str, Any] = Field(..., description="新闻统计信息")
    subscriptions: Dict[str, Any] = Field(..., description="订阅统计信息")
    pushes: Dict[str, Any] = Field(..., description="推送统计信息")
    system: Dict[str, Any] = Field(..., description="系统健康状态")
    generated_at: str = Field(..., description="生成时间")

    class Config:
        json_schema_extra = {
            "example": {
                "users": {
                    "total": 1250,
                    "by_role": {
                        "FREE": 800,
                        "PRO": 350,
                        "ENTERPRISE": 90,
                        "ADMIN": 10
                    },
                    "growth_rate": 5.2
                },
                "news": {
                    "total": 45000,
                    "today": 150,
                    "avg_per_day": 180.5
                },
                "subscriptions": {
                    "total": 2800,
                    "active": 2650,
                    "activation_rate": 94.6
                },
                "pushes": {
                    "total": 125000,
                    "today": 450,
                    "success_rate": 95.8
                },
                "system": {
                    "status": "healthy",
                    "database_connected": True,
                    "redis_connected": True,
                    "external_services": "operational"
                },
                "generated_at": "2025-08-18T12:00:00"
            }
        }


class UserManagementResponse(BaseModel):
    """用户管理响应模式"""
    id: int = Field(..., description="用户ID")
    username: str = Field(..., description="用户名")
    email: str = Field(..., description="邮箱")
    role: str = Field(..., description="用户角色")
    created_at: datetime = Field(..., description="创建时间")
    last_login_at: Optional[datetime] = Field(None, description="最后登录时间")
    permissions: List[str] = Field(..., description="用户权限列表")
    subscription_count: int = Field(..., description="订阅数量")
    push_count: int = Field(..., description="推送数量")

    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": 123,
                "username": "john_doe",
                "email": "<EMAIL>",
                "role": "PRO",
                "created_at": "2025-01-15T10:30:00",
                "last_login_at": "2025-08-18T09:15:00",
                "permissions": [
                    "read_news",
                    "basic_subscription",
                    "advanced_subscription",
                    "export_data",
                    "view_analytics"
                ],
                "subscription_count": 5,
                "push_count": 120
            }
        }


class UserRoleUpdate(BaseModel):
    """用户角色更新请求模式"""
    new_role: UserRole = Field(..., description="新角色")

    class Config:
        json_schema_extra = {
            "example": {
                "new_role": "PRO"
            }
        }


class PermissionAuditResponse(BaseModel):
    """权限审计日志响应模式"""
    id: int = Field(..., description="日志ID")
    user_id: int = Field(..., description="用户ID")
    username: Optional[str] = Field(None, description="用户名")
    email: Optional[str] = Field(None, description="用户邮箱")
    action: str = Field(..., description="操作类型")
    resource: Optional[str] = Field(None, description="资源")
    permission_required: Optional[str] = Field(None, description="所需权限")
    permission_granted: bool = Field(..., description="权限是否授予")
    ip_address: Optional[str] = Field(None, description="IP地址")
    user_agent: Optional[str] = Field(None, description="用户代理")
    details: Optional[str] = Field(None, description="详细信息")
    created_at: datetime = Field(..., description="创建时间")

    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": 1001,
                "user_id": 123,
                "username": "admin_user",
                "email": "<EMAIL>",
                "action": "role_change",
                "resource": "user_456",
                "permission_required": "user_management",
                "permission_granted": True,
                "ip_address": "*************",
                "user_agent": "Mozilla/5.0...",
                "details": "{\"target_user_id\": 456, \"old_role\": \"FREE\", \"new_role\": \"PRO\"}",
                "created_at": "2025-08-18T11:30:00"
            }
        }


class SystemConfigResponse(BaseModel):
    """系统配置响应模式"""
    role_permissions: Dict[str, Any] = Field(..., description="角色权限配置")
    permission_cache_ttl: Dict[str, Any] = Field(..., description="权限缓存TTL")
    max_failed_attempts: Dict[str, Any] = Field(..., description="最大失败尝试次数")
    session_timeout: Dict[str, Any] = Field(..., description="会话超时时间")
    maintenance_mode: Optional[Dict[str, Any]] = Field(None, description="维护模式")
    maintenance_message: Optional[Dict[str, Any]] = Field(None, description="维护消息")

    class Config:
        json_schema_extra = {
            "example": {
                "role_permissions": {
                    "value": "{\"FREE\": [\"read_news\"], \"PRO\": [\"read_news\", \"export_data\"]}",
                    "description": "角色权限矩阵配置",
                    "is_active": True
                },
                "permission_cache_ttl": {
                    "value": "3600",
                    "description": "权限缓存过期时间（秒）",
                    "is_active": True
                },
                "max_failed_attempts": {
                    "value": "5",
                    "description": "最大失败尝试次数",
                    "is_active": True
                },
                "session_timeout": {
                    "value": "86400",
                    "description": "会话超时时间（秒）",
                    "is_active": True
                }
            }
        }


class SystemConfigUpdate(BaseModel):
    """系统配置更新请求模式"""
    permission_cache_ttl: Optional[int] = Field(None, ge=300, le=86400, description="权限缓存TTL（秒）")
    max_failed_attempts: Optional[int] = Field(None, ge=1, le=20, description="最大失败尝试次数")
    session_timeout: Optional[int] = Field(None, ge=3600, le=604800, description="会话超时时间（秒）")
    maintenance_mode: Optional[bool] = Field(None, description="维护模式开关")
    maintenance_message: Optional[str] = Field(None, max_length=500, description="维护消息")

    @validator('maintenance_message')
    def validate_maintenance_message(cls, v):
        if v is not None and len(v.strip()) == 0:
            raise ValueError('维护消息不能为空')
        return v

    class Config:
        json_schema_extra = {
            "example": {
                "permission_cache_ttl": 7200,
                "max_failed_attempts": 3,
                "session_timeout": 43200,
                "maintenance_mode": False,
                "maintenance_message": "系统维护中，预计30分钟后恢复"
            }
        }


class HealthCheckResponse(BaseModel):
    """健康检查响应模式"""
    database: Dict[str, Any] = Field(..., description="数据库状态")
    redis: Dict[str, Any] = Field(..., description="Redis状态")
    external_services: Dict[str, Any] = Field(..., description="外部服务状态")
    system_resources: Dict[str, Any] = Field(..., description="系统资源状态")

class SystemHealthResponse(BaseModel):
    """系统健康状态响应模式"""
    status: str = Field(..., description="整体状态")
    timestamp: str = Field(..., description="检查时间")
    components: Dict[str, Any] = Field(..., description="组件状态")
    version: str = Field(..., description="系统版本")
    environment: str = Field(..., description="运行环境")
    timestamp: str = Field(..., description="检查时间")

    class Config:
        json_schema_extra = {
            "example": {
                "database": {
                    "status": "healthy",
                    "response_time": "< 10ms"
                },
                "redis": {
                    "status": "healthy",
                    "memory_usage": "45%"
                },
                "external_services": {
                    "glm_api": {
                        "status": "healthy",
                        "response_time": "200ms"
                    },
                    "news_sources": {
                        "status": "healthy",
                        "success_rate": "98%"
                    }
                },
                "system_resources": {
                    "cpu_usage": "25%",
                    "memory_usage": "60%",
                    "disk_usage": "40%"
                },
                "timestamp": "2025-08-18T12:00:00"
            }
        }


class MaintenanceModeRequest(BaseModel):
    """维护模式请求模式"""
    enable: bool = Field(..., description="是否启用维护模式")
    message: Optional[str] = Field(None, max_length=500, description="维护消息")

    @validator('message')
    def validate_message_when_enabled(cls, v, values):
        if values.get('enable') and (not v or len(v.strip()) == 0):
            raise ValueError('启用维护模式时必须提供维护消息')
        return v

    class Config:
        json_schema_extra = {
            "example": {
                "enable": True,
                "message": "系统正在进行重要更新，预计1小时后恢复服务"
            }
        }


class AuditLogFilter(BaseModel):
    """审计日志过滤器"""
    user_id: Optional[int] = Field(None, description="用户ID")
    action: Optional[str] = Field(None, description="操作类型")
    start_date: Optional[datetime] = Field(None, description="开始时间")
    end_date: Optional[datetime] = Field(None, description="结束时间")
    permission_granted: Optional[bool] = Field(None, description="权限是否授予")

    @validator('end_date')
    def validate_date_range(cls, v, values):
        if v and values.get('start_date') and v < values['start_date']:
            raise ValueError('结束时间不能早于开始时间')
        return v

    class Config:
        json_schema_extra = {
            "example": {
                "user_id": 123,
                "action": "role_change",
                "start_date": "2025-08-01T00:00:00",
                "end_date": "2025-08-18T23:59:59",
                "permission_granted": True
            }
        }


class BulkUserOperation(BaseModel):
    """批量用户操作请求模式"""
    user_ids: List[int] = Field(..., min_items=1, max_items=100, description="用户ID列表")
    operation: str = Field(..., description="操作类型")
    parameters: Optional[Dict[str, Any]] = Field(None, description="操作参数")

    @validator('operation')
    def validate_operation(cls, v):
        allowed_operations = ['role_change', 'deactivate', 'activate', 'reset_password']
        if v not in allowed_operations:
            raise ValueError(f'不支持的操作类型: {v}')
        return v

    class Config:
        json_schema_extra = {
            "example": {
                "user_ids": [123, 456, 789],
                "operation": "role_change",
                "parameters": {
                    "new_role": "PRO",
                    "reason": "批量升级用户"
                }
            }
        }
