# 数据库分区和索引策略

## 数据分区策略

### 新闻表分区方案
```sql
-- 按月分区新闻表
ALTER TABLE news PARTITION BY RANGE (YEAR(published_at) * 100 + MONTH(published_at)) (
    PARTITION p202501 VALUES LESS THAN (202502),
    PARTITION p202502 VALUES LESS THAN (202503),
    PARTITION p202503 VALUES LESS THAN (202504),
    PARTITION p202504 VALUES LESS THAN (202505),
    PARTITION p202505 VALUES LESS THAN (202506),
    PARTITION p202506 VALUES LESS THAN (202507),
    PARTITION p202507 VALUES LESS THAN (202508),
    PARTITION p202508 VALUES LESS THAN (202509),
    PARTITION p202509 VALUES LESS THAN (202510),
    PARTITION p202510 VALUES LESS THAN (202511),
    PARTITION p202511 VALUES LESS THAN (202512),
    PARTITION p202512 VALUES LESS THAN (202601),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### 推送日志表分区方案
```sql
-- 按月分区推送日志表
ALTER TABLE push_logs PARTITION BY RANGE (YEAR(sent_at) * 100 + MONTH(sent_at)) (
    PARTITION p202501 VALUES LESS THAN (202502),
    PARTITION p202502 VALUES LESS THAN (202503),
    PARTITION p202503 VALUES LESS THAN (202504),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

## 索引优化策略

### 高频查询字段索引
```sql
-- 用户表索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_last_login ON users(last_login_at);

-- 订阅表索引
CREATE INDEX idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX idx_subscriptions_active ON subscriptions(is_active);
CREATE INDEX idx_subscriptions_created ON subscriptions(created_at);

-- 新闻表索引
CREATE INDEX idx_news_published_at ON news(published_at);
CREATE INDEX idx_news_category ON news(category);
CREATE INDEX idx_news_source ON news(source);
CREATE INDEX idx_news_importance ON news(importance_score);
CREATE INDEX idx_news_sentiment ON news(sentiment);

-- 推送日志表索引
CREATE INDEX idx_push_logs_user_id ON push_logs(user_id);
CREATE INDEX idx_push_logs_sent_at ON push_logs(sent_at);
CREATE INDEX idx_push_logs_status ON push_logs(status);
CREATE INDEX idx_push_logs_channel ON push_logs(channel);

-- 敏感词表索引
CREATE INDEX idx_sensitive_words_category ON sensitive_words(category);
CREATE INDEX idx_sensitive_words_severity ON sensitive_words(severity);

-- 实体表索引
CREATE INDEX idx_entities_type ON entities(entity_type);
CREATE INDEX idx_entities_stock_code ON entities(stock_code);
```

### 复合索引优化
```sql
-- 新闻表复合索引
CREATE INDEX idx_news_category_published ON news(category, published_at);
CREATE INDEX idx_news_source_published ON news(source, published_at);
CREATE INDEX idx_news_importance_published ON news(importance_score, published_at);

-- 推送日志复合索引
CREATE INDEX idx_push_logs_user_status ON push_logs(user_id, status);
CREATE INDEX idx_push_logs_user_sent ON push_logs(user_id, sent_at);

-- 订阅表复合索引
CREATE INDEX idx_subscriptions_user_active ON subscriptions(user_id, is_active);
```

### 全文搜索索引
```sql
-- 新闻内容全文搜索
ALTER TABLE news ADD FULLTEXT(title, content);
ALTER TABLE news ADD FULLTEXT(title);
ALTER TABLE news ADD FULLTEXT(content);
```

## 查询优化建议

### 1. 时间范围查询优化
```sql
-- 优化前
SELECT * FROM news WHERE published_at >= '2025-01-01' AND published_at < '2025-02-01';

-- 优化后（利用分区）
SELECT * FROM news PARTITION(p202501) WHERE published_at >= '2025-01-01' AND published_at < '2025-02-01';
```

### 2. 分页查询优化
```sql
-- 优化前
SELECT * FROM news ORDER BY published_at DESC LIMIT 1000, 20;

-- 优化后（使用游标分页）
SELECT * FROM news WHERE published_at < '2025-01-15 10:00:00' ORDER BY published_at DESC LIMIT 20;
```

### 3. 聚合查询优化
```sql
-- 按分区进行聚合
SELECT 
    DATE_FORMAT(published_at, '%Y-%m') as month,
    COUNT(*) as news_count,
    AVG(importance_score) as avg_importance
FROM news 
WHERE published_at >= '2025-01-01'
GROUP BY DATE_FORMAT(published_at, '%Y-%m');
```

## 性能监控指标

### 1. 查询性能指标
- P95 查询响应时间 < 100ms
- P99 查询响应时间 < 500ms
- 慢查询比例 < 1%
- 索引命中率 > 95%

### 2. 分区效果监控
- 分区剪枝效果
- 分区间数据分布均匀性
- 分区维护开销

### 3. 索引效果监控
- 索引使用率
- 索引选择性
- 索引维护开销

## 维护策略

### 1. 分区维护
```sql
-- 自动添加新分区（每月执行）
ALTER TABLE news ADD PARTITION (
    PARTITION p202602 VALUES LESS THAN (202603)
);

-- 删除旧分区（保留12个月数据）
ALTER TABLE news DROP PARTITION p202401;
```

### 2. 索引维护
```sql
-- 定期重建索引
ALTER TABLE news ENGINE=InnoDB;

-- 分析表统计信息
ANALYZE TABLE news;
```

### 3. 数据清理
```sql
-- 清理过期推送日志（保留3个月）
DELETE FROM push_logs WHERE sent_at < DATE_SUB(NOW(), INTERVAL 3 MONTH);

-- 清理过期新闻数据（保留12个月）
DELETE FROM news WHERE published_at < DATE_SUB(NOW(), INTERVAL 12 MONTH);
```

## 备份策略

### 1. 分区备份
```bash
# 按分区备份
mysqldump --single-transaction financial_news_bot news --where="published_at >= '2025-01-01' AND published_at < '2025-02-01'" > news_202501.sql
```

### 2. 增量备份
```bash
# 基于binlog的增量备份
mysqlbinlog --start-datetime="2025-01-01 00:00:00" --stop-datetime="2025-01-01 23:59:59" mysql-bin.000001 > incremental_backup.sql
```

## 扩展规划

### 1. 读写分离
- 主库：写操作
- 从库：读操作和报表查询
- 读写分离中间件：ProxySQL

### 2. 分库分表
- 按用户ID分库
- 按时间分表
- 使用ShardingSphere

### 3. 缓存策略
- Redis缓存热点数据
- 查询结果缓存
- 分布式缓存一致性
