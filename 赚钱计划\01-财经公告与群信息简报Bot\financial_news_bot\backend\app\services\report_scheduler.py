"""
简报调度服务
负责简报的定时生成和推送
"""
import logging
import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime, time, timedelta
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.date import DateTrigger
from sqlalchemy.orm import Session

from ..database import get_db
from ..models.report import Report, ReportSubscription, ReportType
from ..models.user import User
from ..services.report_service import ReportService

logger = logging.getLogger(__name__)


class ReportScheduler:
    """简报调度器类"""
    
    def __init__(self):
        self.scheduler = AsyncIOScheduler()
        self.is_running = False
        
        # 默认任务配置
        self.default_schedules = {
            ReportType.MORNING: {
                "hour": 7,
                "minute": 30,
                "timezone": "Asia/Shanghai"
            },
            ReportType.EVENING: {
                "hour": 18,
                "minute": 0,
                "timezone": "Asia/Shanghai"
            }
        }
    
    async def start(self):
        """启动简报调度服务"""
        try:
            if self.is_running:
                logger.warning("Report scheduler is already running")
                return
            
            # 添加默认任务
            await self._setup_default_jobs()
            
            # 启动调度器
            self.scheduler.start()
            self.is_running = True
            
            logger.info("Report scheduler started successfully")
            
        except Exception as e:
            logger.error(f"Failed to start report scheduler: {e}")
            raise
    
    async def stop(self):
        """停止简报调度服务"""
        try:
            if not self.is_running:
                return
            
            self.scheduler.shutdown(wait=True)
            self.is_running = False
            
            logger.info("Report scheduler stopped")
            
        except Exception as e:
            logger.error(f"Failed to stop report scheduler: {e}")
    
    async def _setup_default_jobs(self):
        """设置默认任务"""
        try:
            # 晨报生成任务
            self.scheduler.add_job(
                func=self._generate_morning_report,
                trigger=CronTrigger(
                    hour=self.default_schedules[ReportType.MORNING]["hour"],
                    minute=self.default_schedules[ReportType.MORNING]["minute"],
                    timezone=self.default_schedules[ReportType.MORNING]["timezone"]
                ),
                id="morning_report_generation",
                name="晨报生成任务",
                replace_existing=True
            )
            
            # 晚报生成任务
            self.scheduler.add_job(
                func=self._generate_evening_report,
                trigger=CronTrigger(
                    hour=self.default_schedules[ReportType.EVENING]["hour"],
                    minute=self.default_schedules[ReportType.EVENING]["minute"],
                    timezone=self.default_schedules[ReportType.EVENING]["timezone"]
                ),
                id="evening_report_generation",
                name="晚报生成任务",
                replace_existing=True
            )
            
            # 清理过期简报任务（每天凌晨2点）
            self.scheduler.add_job(
                func=self._cleanup_expired_reports,
                trigger=CronTrigger(hour=2, minute=0, timezone="Asia/Shanghai"),
                id="cleanup_expired_reports",
                name="清理过期简报任务",
                replace_existing=True
            )
            
            logger.info("Default report jobs setup completed")
            
        except Exception as e:
            logger.error(f"Failed to setup default report jobs: {e}")
            raise
    
    async def _generate_morning_report(self):
        """生成晨报任务"""
        try:
            logger.info("Starting morning report generation")
            
            db = next(get_db())
            try:
                report_service = ReportService(db)
                
                # 生成晨报
                report = await report_service.generate_report(
                    report_type=ReportType.MORNING,
                    report_date=datetime.now()
                )
                
                logger.info(f"Morning report generated: {report.id}")
                
                # 推送给订阅用户
                await self._push_report_to_subscribers(report, db)
                
            finally:
                db.close()
            
        except Exception as e:
            logger.error(f"Failed to generate morning report: {e}")
    
    async def _generate_evening_report(self):
        """生成晚报任务"""
        try:
            logger.info("Starting evening report generation")
            
            db = next(get_db())
            try:
                report_service = ReportService(db)
                
                # 生成晚报
                report = await report_service.generate_report(
                    report_type=ReportType.EVENING,
                    report_date=datetime.now()
                )
                
                logger.info(f"Evening report generated: {report.id}")
                
                # 推送给订阅用户
                await self._push_report_to_subscribers(report, db)
                
            finally:
                db.close()
            
        except Exception as e:
            logger.error(f"Failed to generate evening report: {e}")
    
    async def _push_report_to_subscribers(self, report: Report, db: Session):
        """推送简报给订阅用户"""
        try:
            # 获取订阅该类型简报的用户
            subscriptions = db.query(ReportSubscription).filter(
                ReportSubscription.is_active == True
            ).all()
            
            push_count = 0
            
            for subscription in subscriptions:
                try:
                    # 检查用户是否订阅了该类型简报
                    if report.type.value not in subscription.report_types:
                        continue
                    
                    # 检查推送时间是否匹配
                    if not self._should_push_now(subscription, report.type):
                        continue
                    
                    # 调用推送服务发送简报
                    try:
                        from ..services.layered_push_service import layered_push_service

                        push_result = await layered_push_service.push_report_to_subscription(
                            report, subscription
                        )

                        if push_result.get('success', False):
                            logger.info(f"成功推送简报 {report.id} 给用户 {subscription.user_id}")
                            push_count += 1
                        else:
                            logger.warning(f"推送简报失败 {report.id} 给用户 {subscription.user_id}: {push_result.get('error', '未知错误')}")

                    except Exception as e:
                        logger.error(f"推送简报异常 {report.id} 给用户 {subscription.user_id}: {e}")
                    
                    # 更新最后推送时间
                    subscription.last_delivered_at = datetime.now()
                    
                except Exception as e:
                    logger.error(f"Failed to push report to user {subscription.user_id}: {e}")
                    continue
            
            db.commit()
            logger.info(f"Report {report.id} pushed to {push_count} users")
            
        except Exception as e:
            logger.error(f"Failed to push report to subscribers: {e}")
    
    def _should_push_now(self, subscription: ReportSubscription, report_type: ReportType) -> bool:
        """检查是否应该现在推送"""
        try:
            if not subscription.delivery_time:
                return True
            
            # 获取配置的推送时间
            delivery_time_config = subscription.delivery_time.get(report_type.value)
            if not delivery_time_config:
                return True
            
            # 解析时间配置
            if isinstance(delivery_time_config, str):
                try:
                    hour, minute = map(int, delivery_time_config.split(':'))
                    configured_time = time(hour, minute)
                    current_time = datetime.now().time()
                    
                    # 允许5分钟的误差
                    time_diff = abs(
                        (current_time.hour * 60 + current_time.minute) -
                        (configured_time.hour * 60 + configured_time.minute)
                    )
                    
                    return time_diff <= 5
                    
                except ValueError:
                    logger.warning(f"Invalid delivery time format: {delivery_time_config}")
                    return True
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to check push time: {e}")
            return True
    
    async def _cleanup_expired_reports(self):
        """清理过期简报任务"""
        try:
            logger.info("Starting expired reports cleanup")
            
            db = next(get_db())
            try:
                # 清理30天前的简报
                cutoff_date = datetime.now() - timedelta(days=30)
                
                expired_reports = db.query(Report).filter(
                    Report.created_at < cutoff_date
                ).all()
                
                for report in expired_reports:
                    db.delete(report)
                
                db.commit()
                
                logger.info(f"Cleaned up {len(expired_reports)} expired reports")
                
            finally:
                db.close()
            
        except Exception as e:
            logger.error(f"Failed to cleanup expired reports: {e}")
    
    async def schedule_custom_report(
        self,
        report_type: ReportType,
        scheduled_time: datetime,
        user_id: Optional[int] = None,
        personalization_config: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        安排自定义简报生成
        
        Args:
            report_type: 简报类型
            scheduled_time: 计划时间
            user_id: 用户ID
            personalization_config: 个性化配置
            
        Returns:
            str: 任务ID
        """
        try:
            job_id = f"custom_report_{report_type.value}_{int(scheduled_time.timestamp())}"
            
            self.scheduler.add_job(
                func=self._generate_custom_report,
                trigger=DateTrigger(run_date=scheduled_time),
                args=[report_type, user_id, personalization_config],
                id=job_id,
                name=f"自定义{report_type.value}简报",
                replace_existing=True
            )
            
            logger.info(f"Custom report scheduled: {job_id} at {scheduled_time}")
            
            return job_id
            
        except Exception as e:
            logger.error(f"Failed to schedule custom report: {e}")
            raise
    
    async def _generate_custom_report(
        self,
        report_type: ReportType,
        user_id: Optional[int],
        personalization_config: Optional[Dict[str, Any]]
    ):
        """生成自定义简报"""
        try:
            db = next(get_db())
            try:
                report_service = ReportService(db)
                
                # 获取用户信息
                user = None
                if user_id:
                    user = db.query(User).filter(User.id == user_id).first()
                
                # 生成简报
                report = await report_service.generate_report(
                    report_type=report_type,
                    user=user,
                    personalization_config=personalization_config
                )
                
                logger.info(f"Custom report generated: {report.id}")
                
                # 如果是为特定用户生成，记录日志（实际应该推送）
                if user_id:
                    logger.info(f"Would push custom report {report.id} to user {user_id}")
                
            finally:
                db.close()
            
        except Exception as e:
            logger.error(f"Failed to generate custom report: {e}")
    
    def cancel_job(self, job_id: str) -> bool:
        """
        取消任务
        
        Args:
            job_id: 任务ID
            
        Returns:
            bool: 是否成功取消
        """
        try:
            self.scheduler.remove_job(job_id)
            logger.info(f"Job cancelled: {job_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to cancel job {job_id}: {e}")
            return False
    
    def get_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务状态
        
        Args:
            job_id: 任务ID
            
        Returns:
            Optional[Dict[str, Any]]: 任务状态信息
        """
        try:
            job = self.scheduler.get_job(job_id)
            if job:
                return {
                    "id": job.id,
                    "name": job.name,
                    "next_run_time": job.next_run_time.isoformat() if job.next_run_time else None,
                    "trigger": str(job.trigger)
                }
            return None
            
        except Exception as e:
            logger.error(f"Failed to get job status {job_id}: {e}")
            return None
    
    def list_jobs(self) -> List[Dict[str, Any]]:
        """
        列出所有任务
        
        Returns:
            List[Dict[str, Any]]: 任务列表
        """
        try:
            jobs = []
            for job in self.scheduler.get_jobs():
                jobs.append({
                    "id": job.id,
                    "name": job.name,
                    "next_run_time": job.next_run_time.isoformat() if job.next_run_time else None,
                    "trigger": str(job.trigger)
                })
            
            return jobs
            
        except Exception as e:
            logger.error(f"Failed to list jobs: {e}")
            return []


# 全局简报调度器实例
report_scheduler = None

def get_report_scheduler() -> ReportScheduler:
    """获取简报调度器实例"""
    global report_scheduler
    if report_scheduler is None:
        report_scheduler = ReportScheduler()
    return report_scheduler
