#!/usr/bin/env python3
"""
安全配置检查器
检查应用程序的安全配置是否符合最佳实践
"""
import os
import json
import yaml
import configparser
import re
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SecurityConfigChecker:
    """安全配置检查器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.issues = []
        self.recommendations = []
    
    def check_environment_variables(self) -> List[Dict[str, Any]]:
        """检查环境变量配置"""
        logger.info("检查环境变量配置...")
        
        issues = []
        
        # 检查.env文件
        env_files = ['.env', '.env.example', '.env.local', '.env.production']
        
        for env_file in env_files:
            env_path = self.project_root / env_file
            if env_path.exists():
                issues.extend(self._check_env_file(env_path))
        
        return issues
    
    def _check_env_file(self, env_path: Path) -> List[Dict[str, Any]]:
        """检查单个.env文件"""
        issues = []
        
        try:
            with open(env_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = content.split('\n')
            
            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                if not line or line.startswith('#'):
                    continue
                
                if '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip().strip('"\'')
                    
                    # 检查弱密码
                    if any(keyword in key.lower() for keyword in ['password', 'secret', 'key', 'token']):
                        if self._is_weak_credential(value):
                            issues.append({
                                'type': 'Weak Credential',
                                'severity': 'High',
                                'file': str(env_path),
                                'line': line_num,
                                'description': f'Weak credential detected: {key}',
                                'recommendation': 'Use strong, randomly generated credentials'
                            })
                    
                    # 检查硬编码的生产环境配置
                    if 'localhost' in value or '127.0.0.1' in value:
                        if 'production' in str(env_path).lower():
                            issues.append({
                                'type': 'Production Configuration Issue',
                                'severity': 'Medium',
                                'file': str(env_path),
                                'line': line_num,
                                'description': f'Localhost configuration in production file: {key}',
                                'recommendation': 'Use production-appropriate hostnames'
                            })
                    
                    # 检查调试模式
                    if key.lower() in ['debug', 'development'] and value.lower() in ['true', '1', 'yes']:
                        if 'production' in str(env_path).lower():
                            issues.append({
                                'type': 'Debug Mode Enabled',
                                'severity': 'High',
                                'file': str(env_path),
                                'line': line_num,
                                'description': 'Debug mode enabled in production',
                                'recommendation': 'Disable debug mode in production'
                            })
        
        except Exception as e:
            logger.error(f"检查环境文件失败 {env_path}: {e}")
        
        return issues
    
    def _is_weak_credential(self, value: str) -> bool:
        """检查是否为弱凭据"""
        if not value or len(value) < 8:
            return True
        
        weak_patterns = [
            'password', 'admin', 'root', 'test', 'demo',
            '123456', 'qwerty', 'abc123', 'password123'
        ]
        
        return value.lower() in weak_patterns
    
    def check_database_configuration(self) -> List[Dict[str, Any]]:
        """检查数据库配置"""
        logger.info("检查数据库配置...")
        
        issues = []
        
        # 检查数据库配置文件
        config_files = [
            'backend/app/core/config.py',
            'backend/config.py',
            'config/database.py'
        ]
        
        for config_file in config_files:
            config_path = self.project_root / config_file
            if config_path.exists():
                issues.extend(self._check_database_config_file(config_path))
        
        return issues
    
    def _check_database_config_file(self, config_path: Path) -> List[Dict[str, Any]]:
        """检查数据库配置文件"""
        issues = []
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查硬编码的数据库凭据
            db_patterns = [
                r'mysql://[^:]+:[^@]+@[^/]+',
                r'postgresql://[^:]+:[^@]+@[^/]+',
                r'mongodb://[^:]+:[^@]+@[^/]+'
            ]
            
            for pattern in db_patterns:
                matches = re.finditer(pattern, content, re.IGNORECASE)
                for match in matches:
                    issues.append({
                        'type': 'Hardcoded Database Credentials',
                        'severity': 'Critical',
                        'file': str(config_path),
                        'description': 'Database credentials hardcoded in source code',
                        'evidence': match.group()[:50] + '...',
                        'recommendation': 'Use environment variables for database credentials'
                    })
            
            # 检查SSL配置
            if 'sslmode=disable' in content.lower():
                issues.append({
                    'type': 'Insecure Database Connection',
                    'severity': 'Medium',
                    'file': str(config_path),
                    'description': 'Database SSL disabled',
                    'recommendation': 'Enable SSL for database connections'
                })
        
        except Exception as e:
            logger.error(f"检查数据库配置文件失败 {config_path}: {e}")
        
        return issues
    
    def check_web_security_headers(self) -> List[Dict[str, Any]]:
        """检查Web安全头配置"""
        logger.info("检查Web安全头配置...")
        
        issues = []
        
        # 检查Nginx配置
        nginx_configs = [
            'nginx/nginx.conf',
            'nginx/default.conf',
            'docker/nginx.conf'
        ]
        
        for nginx_config in nginx_configs:
            nginx_path = self.project_root / nginx_config
            if nginx_path.exists():
                issues.extend(self._check_nginx_security_headers(nginx_path))
        
        return issues
    
    def _check_nginx_security_headers(self, nginx_path: Path) -> List[Dict[str, Any]]:
        """检查Nginx安全头配置"""
        issues = []
        
        try:
            with open(nginx_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 必需的安全头
            required_headers = {
                'X-Frame-Options': 'Prevents clickjacking attacks',
                'X-Content-Type-Options': 'Prevents MIME type sniffing',
                'X-XSS-Protection': 'Enables XSS filtering',
                'Strict-Transport-Security': 'Enforces HTTPS',
                'Content-Security-Policy': 'Prevents XSS and data injection',
                'Referrer-Policy': 'Controls referrer information'
            }
            
            for header, description in required_headers.items():
                if header.lower() not in content.lower():
                    issues.append({
                        'type': 'Missing Security Header',
                        'severity': 'Medium',
                        'file': str(nginx_path),
                        'description': f'Missing {header} header',
                        'recommendation': f'Add {header} header: {description}'
                    })
            
            # 检查不安全的配置
            if 'server_tokens on' in content:
                issues.append({
                    'type': 'Information Disclosure',
                    'severity': 'Low',
                    'file': str(nginx_path),
                    'description': 'Server tokens enabled',
                    'recommendation': 'Disable server tokens to hide Nginx version'
                })
        
        except Exception as e:
            logger.error(f"检查Nginx配置失败 {nginx_path}: {e}")
        
        return issues
    
    def check_cors_configuration(self) -> List[Dict[str, Any]]:
        """检查CORS配置"""
        logger.info("检查CORS配置...")
        
        issues = []
        
        # 检查FastAPI CORS配置
        backend_files = [
            'backend/app/main.py',
            'backend/main.py',
            'backend/app/core/config.py'
        ]
        
        for backend_file in backend_files:
            backend_path = self.project_root / backend_file
            if backend_path.exists():
                issues.extend(self._check_cors_config_file(backend_path))
        
        return issues
    
    def _check_cors_config_file(self, config_path: Path) -> List[Dict[str, Any]]:
        """检查CORS配置文件"""
        issues = []
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查过于宽松的CORS配置
            if 'allow_origins=["*"]' in content or "allow_origins=['*']" in content:
                issues.append({
                    'type': 'Insecure CORS Configuration',
                    'severity': 'High',
                    'file': str(config_path),
                    'description': 'CORS allows all origins (*)',
                    'recommendation': 'Restrict CORS to specific trusted origins'
                })
            
            if 'allow_credentials=True' in content and ('allow_origins=["*"]' in content or "allow_origins=['*']" in content):
                issues.append({
                    'type': 'Critical CORS Misconfiguration',
                    'severity': 'Critical',
                    'file': str(config_path),
                    'description': 'CORS allows credentials with wildcard origin',
                    'recommendation': 'Never use wildcard origin with credentials enabled'
                })
        
        except Exception as e:
            logger.error(f"检查CORS配置失败 {config_path}: {e}")
        
        return issues
    
    def check_jwt_configuration(self) -> List[Dict[str, Any]]:
        """检查JWT配置"""
        logger.info("检查JWT配置...")
        
        issues = []
        
        # 检查JWT相关文件
        jwt_files = [
            'backend/app/core/security.py',
            'backend/app/core/config.py',
            'backend/security.py'
        ]
        
        for jwt_file in jwt_files:
            jwt_path = self.project_root / jwt_file
            if jwt_path.exists():
                issues.extend(self._check_jwt_config_file(jwt_path))
        
        return issues
    
    def _check_jwt_config_file(self, config_path: Path) -> List[Dict[str, Any]]:
        """检查JWT配置文件"""
        issues = []
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查弱JWT密钥
            jwt_secret_patterns = [
                r'SECRET_KEY\s*=\s*["\']([^"\']+)["\']',
                r'JWT_SECRET\s*=\s*["\']([^"\']+)["\']',
                r'jwt_secret\s*=\s*["\']([^"\']+)["\']'
            ]
            
            for pattern in jwt_secret_patterns:
                matches = re.finditer(pattern, content, re.IGNORECASE)
                for match in matches:
                    secret = match.group(1)
                    if len(secret) < 32:
                        issues.append({
                            'type': 'Weak JWT Secret',
                            'severity': 'High',
                            'file': str(config_path),
                            'description': 'JWT secret key is too short',
                            'recommendation': 'Use a strong, randomly generated secret key (at least 32 characters)'
                        })
            
            # 检查JWT过期时间
            if 'timedelta(days=' in content:
                days_match = re.search(r'timedelta\(days=(\d+)\)', content)
                if days_match:
                    days = int(days_match.group(1))
                    if days > 30:
                        issues.append({
                            'type': 'Long JWT Expiration',
                            'severity': 'Medium',
                            'file': str(config_path),
                            'description': f'JWT expiration time is too long ({days} days)',
                            'recommendation': 'Use shorter JWT expiration times (1-7 days)'
                        })
        
        except Exception as e:
            logger.error(f"检查JWT配置失败 {config_path}: {e}")
        
        return issues
    
    def check_docker_security(self) -> List[Dict[str, Any]]:
        """检查Docker安全配置"""
        logger.info("检查Docker安全配置...")
        
        issues = []
        
        # 检查Dockerfile
        dockerfile_paths = ['Dockerfile', 'docker/Dockerfile', 'backend/Dockerfile']
        
        for dockerfile in dockerfile_paths:
            dockerfile_path = self.project_root / dockerfile
            if dockerfile_path.exists():
                issues.extend(self._check_dockerfile_security(dockerfile_path))
        
        # 检查docker-compose.yml
        compose_files = ['docker-compose.yml', 'docker-compose.yaml', 'docker-compose.prod.yml']
        
        for compose_file in compose_files:
            compose_path = self.project_root / compose_file
            if compose_path.exists():
                issues.extend(self._check_docker_compose_security(compose_path))
        
        return issues
    
    def _check_dockerfile_security(self, dockerfile_path: Path) -> List[Dict[str, Any]]:
        """检查Dockerfile安全性"""
        issues = []
        
        try:
            with open(dockerfile_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = content.split('\n')
            
            # 检查是否使用root用户
            has_user_directive = any('USER ' in line for line in lines if not line.strip().startswith('#'))
            if not has_user_directive:
                issues.append({
                    'type': 'Docker Security Issue',
                    'severity': 'High',
                    'file': str(dockerfile_path),
                    'description': 'Container runs as root user',
                    'recommendation': 'Add USER directive to run as non-root user'
                })
            
            # 检查是否使用latest标签
            for line_num, line in enumerate(lines, 1):
                if line.strip().startswith('FROM') and ':latest' in line:
                    issues.append({
                        'type': 'Docker Best Practice Violation',
                        'severity': 'Medium',
                        'file': str(dockerfile_path),
                        'line': line_num,
                        'description': 'Using :latest tag in FROM instruction',
                        'recommendation': 'Use specific version tags instead of :latest'
                    })
        
        except Exception as e:
            logger.error(f"检查Dockerfile失败 {dockerfile_path}: {e}")
        
        return issues
    
    def _check_docker_compose_security(self, compose_path: Path) -> List[Dict[str, Any]]:
        """检查docker-compose安全性"""
        issues = []
        
        try:
            with open(compose_path, 'r', encoding='utf-8') as f:
                compose_data = yaml.safe_load(f)
            
            if 'services' in compose_data:
                for service_name, service_config in compose_data['services'].items():
                    # 检查特权模式
                    if service_config.get('privileged'):
                        issues.append({
                            'type': 'Docker Security Issue',
                            'severity': 'High',
                            'file': str(compose_path),
                            'description': f'Service {service_name} runs in privileged mode',
                            'recommendation': 'Avoid privileged mode unless absolutely necessary'
                        })
                    
                    # 检查网络模式
                    if service_config.get('network_mode') == 'host':
                        issues.append({
                            'type': 'Docker Security Issue',
                            'severity': 'Medium',
                            'file': str(compose_path),
                            'description': f'Service {service_name} uses host network mode',
                            'recommendation': 'Use bridge network mode for better isolation'
                        })
                    
                    # 检查环境变量中的敏感信息
                    if 'environment' in service_config:
                        for env_var in service_config['environment']:
                            if isinstance(env_var, str) and '=' in env_var:
                                key, value = env_var.split('=', 1)
                                if any(keyword in key.lower() for keyword in ['password', 'secret', 'key']):
                                    if not value.startswith('${'):  # 不是环境变量引用
                                        issues.append({
                                            'type': 'Hardcoded Credentials',
                                            'severity': 'High',
                                            'file': str(compose_path),
                                            'description': f'Hardcoded credential in service {service_name}',
                                            'recommendation': 'Use environment variable references or secrets'
                                        })
        
        except Exception as e:
            logger.error(f"检查docker-compose失败 {compose_path}: {e}")
        
        return issues
    
    def run_full_check(self) -> Dict[str, Any]:
        """运行完整的安全配置检查"""
        logger.info("开始安全配置检查...")
        
        all_issues = []
        
        # 运行各项检查
        all_issues.extend(self.check_environment_variables())
        all_issues.extend(self.check_database_configuration())
        all_issues.extend(self.check_web_security_headers())
        all_issues.extend(self.check_cors_configuration())
        all_issues.extend(self.check_jwt_configuration())
        all_issues.extend(self.check_docker_security())
        
        # 统计结果
        severity_counts = {'Critical': 0, 'High': 0, 'Medium': 0, 'Low': 0}
        for issue in all_issues:
            severity = issue.get('severity', 'Low')
            severity_counts[severity] += 1
        
        results = {
            'timestamp': '2024-01-01T00:00:00',  # 实际应用中使用 datetime.now().isoformat()
            'total_issues': len(all_issues),
            'severity_breakdown': severity_counts,
            'issues': all_issues,
            'recommendations': self._generate_recommendations(all_issues)
        }
        
        logger.info(f"安全配置检查完成，发现 {len(all_issues)} 个问题")
        return results
    
    def _generate_recommendations(self, issues: List[Dict[str, Any]]) -> List[str]:
        """生成安全建议"""
        recommendations = [
            "定期更新所有依赖库和框架",
            "使用强密码和多因素认证",
            "启用所有必要的安全头",
            "配置适当的CORS策略",
            "使用HTTPS和HSTS",
            "定期进行安全审计",
            "实施最小权限原则",
            "建立安全事件响应计划"
        ]
        
        # 根据发现的问题添加特定建议
        issue_types = [issue.get('type', '') for issue in issues]
        
        if any('JWT' in issue_type for issue_type in issue_types):
            recommendations.append("加强JWT令牌管理和验证")
        
        if any('Docker' in issue_type for issue_type in issue_types):
            recommendations.append("改进Docker容器安全配置")
        
        if any('CORS' in issue_type for issue_type in issue_types):
            recommendations.append("重新配置CORS策略")
        
        return recommendations
    
    def save_report(self, output_file: str, results: Dict[str, Any]):
        """保存检查报告"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            logger.info(f"安全配置检查报告已保存: {output_file}")
        except Exception as e:
            logger.error(f"保存报告失败: {e}")


def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='安全配置检查器')
    parser.add_argument('--project-root', default='.', help='项目根目录')
    parser.add_argument('--output', default='security_config_report.json', help='输出文件')
    
    args = parser.parse_args()
    
    checker = SecurityConfigChecker(args.project_root)
    results = checker.run_full_check()
    checker.save_report(args.output, results)
    
    # 输出摘要
    print(f"\n=== 安全配置检查摘要 ===")
    print(f"总问题数: {results['total_issues']}")
    print(f"严重程度分布:")
    for severity, count in results['severity_breakdown'].items():
        if count > 0:
            print(f"  {severity}: {count}")
    
    if results['total_issues'] > 0:
        print(f"\n⚠️  发现配置问题，请查看详细报告: {args.output}")
    else:
        print(f"\n✅ 配置检查通过")


if __name__ == '__main__':
    main()
