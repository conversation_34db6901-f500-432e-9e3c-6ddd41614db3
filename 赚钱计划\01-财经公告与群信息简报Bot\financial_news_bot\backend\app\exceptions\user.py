"""
用户相关异常
使用统一的异常处理基类
"""
from .base import (
    BaseAPIException,
    UserNotFoundError,
    DuplicateUserError,
    InvalidCredentialsError,
    ValidationError,
    AuthenticationError
)

# 重新导出统一的异常类
__all__ = [
    "UserNotFoundError",
    "UserAlreadyExistsError",
    "InvalidCredentialsError",
    "WeakPasswordError",
    "UnauthorizedError"
]

# 为了保持向后兼容，创建别名
UserAlreadyExistsError = DuplicateUserError
UnauthorizedError = AuthenticationError


class WeakPasswordError(ValidationError):
    """密码强度不足异常"""

    def __init__(self, detail: str = "密码强度不足"):
        super().__init__(detail, "password")
