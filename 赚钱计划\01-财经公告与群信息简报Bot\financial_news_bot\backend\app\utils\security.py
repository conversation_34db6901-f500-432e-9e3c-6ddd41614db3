from datetime import datetime, timed<PERSON>ta
from typing import Optional, Union, Dict, Any
from jose import JWTError, jwt
from passlib.context import CryptContext
import os
import secrets
import hashlib
import time
import logging
from dataclasses import dataclass

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT配置 - 增强安全性，强制使用环境变量
SECRET_KEY = os.getenv("SECRET_KEY")
if not SECRET_KEY:
    raise ValueError("SECRET_KEY environment variable must be set")

REFRESH_SECRET_KEY = os.getenv("REFRESH_SECRET_KEY")
if not REFRESH_SECRET_KEY:
    raise ValueError("REFRESH_SECRET_KEY environment variable must be set")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 15  # 缩短为15分钟
REFRESH_TOKEN_EXPIRE_DAYS = 7     # 刷新令牌7天有效

# 安全配置
MAX_LOGIN_ATTEMPTS = 3
LOCKOUT_DURATION_MINUTES = 5
PASSWORD_MIN_LENGTH = 8
PASSWORD_REQUIRE_SPECIAL = True

@dataclass
class LoginAttempt:
    """登录尝试记录"""
    ip_address: str
    user_id: Optional[str]
    timestamp: datetime
    success: bool
    user_agent: Optional[str] = None

# 登录尝试跟踪
login_attempts: Dict[str, list] = {}

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """生成密码哈希"""
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """创建JWT访问令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)

    # 添加额外的安全信息
    to_encode.update({
        "exp": expire,
        "iat": datetime.utcnow(),
        "type": "access",
        "jti": secrets.token_urlsafe(16)  # JWT ID for revocation
    })

    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def create_refresh_token(data: dict) -> str:
    """创建刷新令牌"""
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)

    to_encode.update({
        "exp": expire,
        "iat": datetime.utcnow(),
        "type": "refresh",
        "jti": secrets.token_urlsafe(16)
    })

    encoded_jwt = jwt.encode(to_encode, REFRESH_SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(token: str, token_type: str = "access") -> Optional[dict]:
    """验证JWT令牌"""
    try:
        secret_key = SECRET_KEY if token_type == "access" else REFRESH_SECRET_KEY
        payload = jwt.decode(token, secret_key, algorithms=[ALGORITHM])

        # 验证令牌类型
        if payload.get("type") != token_type:
            logger.warning(f"Token type mismatch: expected {token_type}, got {payload.get('type')}")
            return None

        return payload
    except JWTError as e:
        logger.warning(f"JWT verification failed: {str(e)}")
        return None

def refresh_access_token(refresh_token: str) -> Optional[tuple[str, str]]:
    """使用刷新令牌获取新的访问令牌"""
    try:
        payload = verify_token(refresh_token, "refresh")
        if not payload:
            return None

        # 创建新的访问令牌和刷新令牌
        user_data = {
            "sub": payload.get("sub"),
            "role": payload.get("role"),
            "user_id": payload.get("user_id")
        }

        new_access_token = create_access_token(user_data)
        new_refresh_token = create_refresh_token(user_data)

        return new_access_token, new_refresh_token

    except Exception as e:
        logger.error(f"Token refresh failed: {str(e)}")
        return None

def validate_password_strength(password: str) -> tuple[bool, str]:
    """验证密码强度 - 增强版"""
    if len(password) < PASSWORD_MIN_LENGTH:
        return False, f"密码长度至少{PASSWORD_MIN_LENGTH}位"

    if not any(c.isupper() for c in password):
        return False, "密码必须包含至少一个大写字母"

    if not any(c.islower() for c in password):
        return False, "密码必须包含至少一个小写字母"

    if not any(c.isdigit() for c in password):
        return False, "密码必须包含至少一个数字"

    if PASSWORD_REQUIRE_SPECIAL and not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
        return False, "密码必须包含至少一个特殊字符"

    # 检查常见弱密码
    weak_passwords = [
        "password", "123456", "12345678", "qwerty", "abc123",
        "password123", "admin", "root", "user", "test"
    ]
    if password.lower() in weak_passwords:
        return False, "密码过于简单，请使用更复杂的密码"

    return True, "密码强度符合要求"

def check_login_attempts(ip_address: str, user_id: Optional[str] = None) -> bool:
    """检查登录尝试次数是否超限"""
    current_time = datetime.utcnow()
    cutoff_time = current_time - timedelta(minutes=LOCKOUT_DURATION_MINUTES)

    # 清理过期的登录尝试记录
    if ip_address in login_attempts:
        login_attempts[ip_address] = [
            attempt for attempt in login_attempts[ip_address]
            if attempt.timestamp > cutoff_time
        ]

    # 检查失败次数
    if ip_address in login_attempts:
        failed_attempts = [
            attempt for attempt in login_attempts[ip_address]
            if not attempt.success and attempt.timestamp > cutoff_time
        ]

        if len(failed_attempts) >= MAX_LOGIN_ATTEMPTS:
            logger.warning(f"IP {ip_address} exceeded max login attempts")
            return False

    return True

def record_login_attempt(ip_address: str, user_id: Optional[str], success: bool, user_agent: Optional[str] = None):
    """记录登录尝试"""
    attempt = LoginAttempt(
        ip_address=ip_address,
        user_id=user_id,
        timestamp=datetime.utcnow(),
        success=success,
        user_agent=user_agent
    )

    if ip_address not in login_attempts:
        login_attempts[ip_address] = []

    login_attempts[ip_address].append(attempt)

    # 保留最近100次尝试记录
    if len(login_attempts[ip_address]) > 100:
        login_attempts[ip_address] = login_attempts[ip_address][-100:]

    # 记录日志
    status = "SUCCESS" if success else "FAILED"
    logger.info(f"Login attempt {status}: IP={ip_address}, User={user_id}, UA={user_agent}")

def generate_secure_token() -> str:
    """生成安全令牌"""
    return secrets.token_urlsafe(32)

def hash_sensitive_data(data: str) -> str:
    """对敏感数据进行哈希处理"""
    return hashlib.sha256(data.encode()).hexdigest()

def validate_ip_whitelist(ip_address: str, whitelist: list = None) -> bool:
    """验证IP白名单"""
    if not whitelist:
        return True

    return ip_address in whitelist

def get_client_ip(request) -> str:
    """获取客户端真实IP地址"""
    # 检查代理头
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        return forwarded_for.split(",")[0].strip()

    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip

    return request.client.host if hasattr(request, 'client') else "unknown"
