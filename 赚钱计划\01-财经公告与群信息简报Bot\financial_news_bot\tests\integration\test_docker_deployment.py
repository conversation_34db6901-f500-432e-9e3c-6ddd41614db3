#!/usr/bin/env python3
"""
Docker部署综合测试脚本
测试所有微服务的启动、健康检查和API功能
"""
import asyncio
import aiohttp
import json
import time
import sys
from typing import Dict, List, Any
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 服务配置
SERVICES = {
    "backend": {
        "url": "http://localhost:8000",
        "health_endpoint": "/health",
        "test_endpoints": [
            "/api/v1/news",
            "/api/v1/security/compliance/check"
        ]
    },
    "security-monitor": {
        "url": "http://localhost:8001", 
        "health_endpoint": "/health",
        "test_endpoints": [
            "/api/v1/security/status",
            "/api/v1/monitoring/system"
        ]
    },
    "compliance-monitor": {
        "url": "http://localhost:8002",
        "health_endpoint": "/health", 
        "test_endpoints": [
            "/api/v1/compliance/status",
            "/api/v1/privacy/check"
        ]
    },
    "nginx": {
        "url": "http://localhost:8080",
        "health_endpoint": "/",
        "test_endpoints": []
    }
}

class DockerTestSuite:
    """Docker部署测试套件"""
    
    def __init__(self):
        self.session = None
        self.test_results = {}
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30))
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def wait_for_service(self, service_name: str, max_wait: int = 120) -> bool:
        """等待服务启动"""
        service_config = SERVICES[service_name]
        url = f"{service_config['url']}{service_config['health_endpoint']}"
        
        logger.info(f"等待服务 {service_name} 启动...")
        
        for attempt in range(max_wait):
            try:
                async with self.session.get(url) as response:
                    if response.status in [200, 404]:  # 404也表示服务在运行
                        logger.info(f"✅ 服务 {service_name} 已启动")
                        return True
            except Exception as e:
                if attempt % 10 == 0:  # 每10秒打印一次
                    logger.info(f"等待 {service_name} 启动... ({attempt}/{max_wait})")
                await asyncio.sleep(1)
        
        logger.error(f"❌ 服务 {service_name} 启动超时")
        return False
    
    async def test_health_check(self, service_name: str) -> Dict[str, Any]:
        """测试健康检查"""
        service_config = SERVICES[service_name]
        url = f"{service_config['url']}{service_config['health_endpoint']}"
        
        try:
            async with self.session.get(url) as response:
                result = {
                    "service": service_name,
                    "status_code": response.status,
                    "success": response.status in [200, 404],
                    "response_time": 0
                }
                
                if response.status == 200:
                    try:
                        result["response"] = await response.json()
                    except:
                        result["response"] = await response.text()
                
                return result
                
        except Exception as e:
            return {
                "service": service_name,
                "status_code": 0,
                "success": False,
                "error": str(e)
            }
    
    async def test_api_endpoint(self, service_name: str, endpoint: str) -> Dict[str, Any]:
        """测试API端点"""
        service_config = SERVICES[service_name]
        url = f"{service_config['url']}{endpoint}"
        
        try:
            start_time = time.time()
            async with self.session.get(url) as response:
                response_time = time.time() - start_time
                
                result = {
                    "service": service_name,
                    "endpoint": endpoint,
                    "status_code": response.status,
                    "success": response.status < 500,
                    "response_time": round(response_time, 3)
                }
                
                if response.status == 200:
                    try:
                        result["response"] = await response.json()
                    except:
                        result["response"] = await response.text()
                
                return result
                
        except Exception as e:
            return {
                "service": service_name,
                "endpoint": endpoint,
                "status_code": 0,
                "success": False,
                "error": str(e)
            }
    
    async def test_microservice_integration(self) -> Dict[str, Any]:
        """测试微服务集成"""
        logger.info("🔗 测试微服务集成...")
        
        # 测试合规检查
        compliance_test = await self.test_compliance_check()
        
        # 测试隐私检查
        privacy_test = await self.test_privacy_check()
        
        # 测试系统监控
        monitoring_test = await self.test_system_monitoring()
        
        return {
            "compliance_check": compliance_test,
            "privacy_check": privacy_test,
            "system_monitoring": monitoring_test
        }
    
    async def test_compliance_check(self) -> Dict[str, Any]:
        """测试合规检查功能"""
        url = "http://localhost:8002/api/v1/compliance/check/enhanced"
        data = {
            "content": "这是一条测试内容，包含投资建议：建议买入某股票",
            "content_type": "text",
            "strict_mode": True,
            "check_sensitive_words": True,
            "check_investment_advice": True,
            "check_financial_promise": True,
            "filter_content": True
        }
        
        try:
            async with self.session.post(url, json=data) as response:
                result = {
                    "status_code": response.status,
                    "success": response.status == 200
                }
                
                if response.status == 200:
                    result["response"] = await response.json()
                else:
                    result["error"] = await response.text()
                
                return result
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def test_privacy_check(self) -> Dict[str, Any]:
        """测试隐私检查功能"""
        url = "http://localhost:8002/api/v1/privacy/check"
        data = {
            "content": "用户手机号：13812345678，邮箱：<EMAIL>",
            "data_type": "user_data",
            "mask_pii": True
        }
        
        try:
            async with self.session.post(url, json=data) as response:
                result = {
                    "status_code": response.status,
                    "success": response.status == 200
                }
                
                if response.status == 200:
                    result["response"] = await response.json()
                else:
                    result["error"] = await response.text()
                
                return result
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def test_system_monitoring(self) -> Dict[str, Any]:
        """测试系统监控功能"""
        url = "http://localhost:8001/api/v1/monitoring/system"
        data = {
            "include_cpu": True,
            "include_memory": True,
            "include_disk": True,
            "include_network": True
        }
        
        try:
            async with self.session.post(url, json=data) as response:
                result = {
                    "status_code": response.status,
                    "success": response.status == 200
                }
                
                if response.status == 200:
                    result["response"] = await response.json()
                else:
                    result["error"] = await response.text()
                
                return result
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def run_full_test(self) -> Dict[str, Any]:
        """运行完整测试"""
        logger.info("🚀 开始Docker部署综合测试")
        
        # 1. 等待所有服务启动
        logger.info("📋 第一阶段：等待服务启动")
        startup_results = {}
        for service_name in SERVICES.keys():
            startup_results[service_name] = await self.wait_for_service(service_name)
        
        # 2. 健康检查
        logger.info("🏥 第二阶段：健康检查")
        health_results = {}
        for service_name in SERVICES.keys():
            health_results[service_name] = await self.test_health_check(service_name)
        
        # 3. API端点测试
        logger.info("🔌 第三阶段：API端点测试")
        api_results = {}
        for service_name, config in SERVICES.items():
            api_results[service_name] = []
            for endpoint in config["test_endpoints"]:
                result = await self.test_api_endpoint(service_name, endpoint)
                api_results[service_name].append(result)
        
        # 4. 微服务集成测试
        logger.info("🔗 第四阶段：微服务集成测试")
        integration_results = await self.test_microservice_integration()
        
        return {
            "startup": startup_results,
            "health": health_results,
            "api": api_results,
            "integration": integration_results
        }

async def main():
    """主函数"""
    async with DockerTestSuite() as test_suite:
        results = await test_suite.run_full_test()
        
        # 打印测试结果
        logger.info("📊 测试结果汇总:")
        logger.info("=" * 50)
        
        # 启动结果
        logger.info("🚀 服务启动:")
        for service, success in results["startup"].items():
            status = "✅" if success else "❌"
            logger.info(f"  {status} {service}")
        
        # 健康检查结果
        logger.info("🏥 健康检查:")
        for service, result in results["health"].items():
            status = "✅" if result["success"] else "❌"
            logger.info(f"  {status} {service} (状态码: {result['status_code']})")
        
        # API测试结果
        logger.info("🔌 API测试:")
        for service, endpoints in results["api"].items():
            for endpoint_result in endpoints:
                status = "✅" if endpoint_result["success"] else "❌"
                logger.info(f"  {status} {service}{endpoint_result['endpoint']} ({endpoint_result['status_code']})")
        
        # 集成测试结果
        logger.info("🔗 集成测试:")
        for test_name, result in results["integration"].items():
            status = "✅" if result["success"] else "❌"
            logger.info(f"  {status} {test_name}")
        
        # 保存详细结果
        with open("test_results.json", "w", encoding="utf-8") as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        logger.info("📄 详细结果已保存到 test_results.json")
        
        # 检查是否有失败的测试
        all_success = (
            all(results["startup"].values()) and
            all(r["success"] for r in results["health"].values()) and
            all(r["success"] for endpoints in results["api"].values() for r in endpoints) and
            all(r["success"] for r in results["integration"].values())
        )
        
        if all_success:
            logger.info("🎉 所有测试通过！")
            return 0
        else:
            logger.error("❌ 部分测试失败，请检查详细结果")
            return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
