# 开发环境专用依赖

# 测试框架
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0
pytest-xdist==3.5.0
httpx==0.25.2

# 代码质量工具
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
bandit==1.7.5
pre-commit==3.6.0

# 开发工具
ipython==8.17.2
ipdb==0.13.13
rich==13.7.0
typer==0.9.0

# 性能分析
py-spy==0.3.14
memory-profiler==0.61.0
line-profiler==4.1.1

# 文档生成
mkdocs==1.5.3
mkdocs-material==9.4.8
mkdocs-mermaid2-plugin==1.1.1

# API 测试
locust==2.17.0
faker==20.1.0

# 数据库工具
alembic==1.13.1
sqlalchemy-utils==0.41.1

# 监控和调试
watchdog==3.0.0
python-dotenv==1.0.0

# 类型检查支持
types-redis==********
types-requests==*********
types-python-dateutil==*********

# 开发服务器增强
uvicorn[standard]==0.24.0
