import { ApiClient } from './api';
import { User, LoginRequest, RegisterRequest, AuthResponse } from '@/types';

export class AuthService {
  // 用户登录
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    const formData = new FormData();
    formData.append('email', credentials.email);
    formData.append('password', credentials.password);

    const response = await ApiClient.post<AuthResponse>('/users/login', formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });

    return response;
  }

  // 用户注册
  async register(userData: RegisterRequest): Promise<{ message: string }> {
    return ApiClient.post('/users/register', userData);
  }

  // 获取当前用户信息
  async getCurrentUser(): Promise<User> {
    return ApiClient.get('/users/me');
  }

  // 更新用户资料
  async updateProfile(userData: Partial<User>): Promise<User> {
    return ApiClient.put('/auth/profile', userData);
  }

  // 修改密码
  async changePassword(passwordData: {
    current_password: string;
    new_password: string;
  }): Promise<{ message: string }> {
    return ApiClient.post('/auth/change-password', passwordData);
  }

  // 忘记密码
  async forgotPassword(email: string): Promise<{ message: string }> {
    return ApiClient.post('/auth/forgot-password', { email });
  }

  // 重置密码
  async resetPassword(data: {
    token: string;
    new_password: string;
  }): Promise<{ message: string }> {
    return ApiClient.post('/auth/reset-password', data);
  }

  // 发送邮箱验证码
  async sendEmailVerification(): Promise<{ message: string }> {
    return ApiClient.post('/auth/send-verification-email');
  }

  // 验证邮箱
  async verifyEmail(token: string): Promise<{ message: string }> {
    return ApiClient.post('/auth/verify-email', { token });
  }

  // 发送手机验证码
  async sendSmsVerification(phone: string): Promise<{ message: string }> {
    return ApiClient.post('/auth/send-sms-verification', { phone });
  }

  // 验证手机号
  async verifyPhone(data: {
    phone: string;
    code: string;
  }): Promise<{ message: string }> {
    return ApiClient.post('/auth/verify-phone', data);
  }

  // 刷新token
  async refreshToken(): Promise<AuthResponse> {
    return ApiClient.post('/auth/refresh');
  }

  // 登出
  async logout(): Promise<{ message: string }> {
    return ApiClient.post('/auth/logout');
  }

  // 获取用户会话列表
  async getSessions(): Promise<Array<{
    id: string;
    device: string;
    ip_address: string;
    location: string;
    last_active: string;
    is_current: boolean;
  }>> {
    return ApiClient.get('/auth/sessions');
  }

  // 终止指定会话
  async terminateSession(sessionId: string): Promise<{ message: string }> {
    return ApiClient.delete(`/auth/sessions/${sessionId}`);
  }

  // 终止所有其他会话
  async terminateAllOtherSessions(): Promise<{ message: string }> {
    return ApiClient.post('/auth/terminate-all-sessions');
  }

  // 启用两步验证
  async enableTwoFactor(): Promise<{
    secret: string;
    qr_code: string;
    backup_codes: string[];
  }> {
    return ApiClient.post('/auth/2fa/enable');
  }

  // 确认两步验证
  async confirmTwoFactor(code: string): Promise<{ message: string }> {
    return ApiClient.post('/auth/2fa/confirm', { code });
  }

  // 禁用两步验证
  async disableTwoFactor(code: string): Promise<{ message: string }> {
    return ApiClient.post('/auth/2fa/disable', { code });
  }

  // 生成新的备份码
  async generateBackupCodes(): Promise<{ backup_codes: string[] }> {
    return ApiClient.post('/auth/2fa/backup-codes');
  }

  // 第三方登录 - 微信
  async loginWithWechat(code: string): Promise<AuthResponse> {
    return ApiClient.post('/auth/oauth/wechat', { code });
  }

  // 第三方登录 - QQ
  async loginWithQQ(code: string): Promise<AuthResponse> {
    return ApiClient.post('/auth/oauth/qq', { code });
  }

  // 绑定第三方账户
  async bindOAuthAccount(provider: string, code: string): Promise<{ message: string }> {
    return ApiClient.post(`/auth/oauth/bind/${provider}`, { code });
  }

  // 解绑第三方账户
  async unbindOAuthAccount(provider: string): Promise<{ message: string }> {
    return ApiClient.delete(`/auth/oauth/unbind/${provider}`);
  }

  // 获取绑定的第三方账户列表
  async getOAuthAccounts(): Promise<Array<{
    provider: string;
    provider_user_id: string;
    nickname: string;
    avatar: string;
    bound_at: string;
  }>> {
    return ApiClient.get('/auth/oauth/accounts');
  }

  // 上传头像
  async uploadAvatar(file: File): Promise<{ avatar_url: string }> {
    return ApiClient.upload('/auth/upload-avatar', file);
  }

  // 删除账户
  async deleteAccount(password: string): Promise<{ message: string }> {
    return ApiClient.post('/auth/delete-account', { password });
  }

  // 导出用户数据
  async exportUserData(): Promise<{ download_url: string }> {
    return ApiClient.post('/auth/export-data');
  }

  // 检查用户名是否可用
  async checkUsernameAvailability(username: string): Promise<{ available: boolean }> {
    return ApiClient.get('/auth/check-username', { username });
  }

  // 检查邮箱是否可用
  async checkEmailAvailability(email: string): Promise<{ available: boolean }> {
    return ApiClient.get('/auth/check-email', { email });
  }

  // 获取登录历史
  async getLoginHistory(page = 1, limit = 20): Promise<{
    items: Array<{
      id: string;
      ip_address: string;
      user_agent: string;
      location: string;
      success: boolean;
      created_at: string;
    }>;
    total: number;
    page: number;
    limit: number;
  }> {
    return ApiClient.get('/auth/login-history', { page, limit });
  }

  // 第三方登录
  async socialLogin(provider: string, code: string): Promise<AuthResponse> {
    return ApiClient.post(`/users/oauth/${provider}`, { code });
  }


}

export const authService = new AuthService();
