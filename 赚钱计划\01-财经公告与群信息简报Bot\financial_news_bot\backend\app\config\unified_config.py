"""
统一配置管理
解决配置重复定义和冲突问题
"""
import os
from typing import Optional, List
from pydantic import BaseSettings, Field, validator
import logging

logger = logging.getLogger(__name__)


class DatabaseConfig(BaseSettings):
    """数据库配置"""
    url: str = Field(..., env="DATABASE_URL", description="数据库连接URL")
    pool_size: int = Field(10, env="DB_POOL_SIZE", description="连接池大小")
    max_overflow: int = Field(20, env="DB_MAX_OVERFLOW", description="最大溢出连接数")
    timeout: int = Field(30, env="DB_POOL_TIMEOUT", description="连接超时时间（秒）")
    recycle: int = Field(3600, env="DB_POOL_RECYCLE", description="连接回收时间（秒）")
    echo: bool = Field(False, env="DB_ECHO", description="是否打印SQL语句")
    
    @validator('url')
    def validate_database_url(cls, v):
        if not v:
            raise ValueError("DATABASE_URL 环境变量必须设置")
        return v
    
    class Config:
        env_prefix = "DB_"


class RedisConfig(BaseSettings):
    """Redis配置"""
    host: str = Field("redis", env="REDIS_HOST", description="Redis主机地址")
    port: int = Field(6379, env="REDIS_PORT", description="Redis端口")
    db: int = Field(0, env="REDIS_DB", description="Redis数据库编号")
    password: Optional[str] = Field(None, env="REDIS_PASSWORD", description="Redis密码")
    max_connections: int = Field(50, env="REDIS_MAX_CONNECTIONS", description="最大连接数")
    socket_timeout: int = Field(5, env="REDIS_SOCKET_TIMEOUT", description="Socket超时时间")
    socket_connect_timeout: int = Field(5, env="REDIS_SOCKET_CONNECT_TIMEOUT", description="连接超时时间")
    retry_on_timeout: bool = Field(True, env="REDIS_RETRY_ON_TIMEOUT", description="超时重试")
    
    @property
    def url(self) -> str:
        """生成Redis连接URL"""
        if self.password:
            return f"redis://:{self.password}@{self.host}:{self.port}/{self.db}"
        return f"redis://{self.host}:{self.port}/{self.db}"
    
    class Config:
        env_prefix = "REDIS_"


class CacheConfig(BaseSettings):
    """缓存配置"""
    news_list_expire: int = Field(300, env="CACHE_NEWS_LIST_EXPIRE", description="新闻列表缓存过期时间（秒）")
    news_stats_expire: int = Field(600, env="CACHE_NEWS_STATS_EXPIRE", description="新闻统计缓存过期时间（秒）")
    search_expire: int = Field(300, env="CACHE_SEARCH_EXPIRE", description="搜索结果缓存过期时间（秒）")
    user_expire: int = Field(1800, env="CACHE_USER_EXPIRE", description="用户信息缓存过期时间（秒）")
    default_expire: int = Field(3600, env="CACHE_DEFAULT_EXPIRE", description="默认缓存过期时间（秒）")
    
    class Config:
        env_prefix = "CACHE_"


class JWTConfig(BaseSettings):
    """JWT配置"""
    secret_key: str = Field(..., env="SECRET_KEY", description="JWT密钥")
    algorithm: str = Field("HS256", env="JWT_ALGORITHM", description="JWT算法")
    access_token_expire_minutes: int = Field(30, env="ACCESS_TOKEN_EXPIRE_MINUTES", description="访问令牌过期时间（分钟）")
    refresh_token_expire_days: int = Field(7, env="REFRESH_TOKEN_EXPIRE_DAYS", description="刷新令牌过期时间（天）")
    
    @validator('secret_key')
    def validate_secret_key(cls, v):
        if not v:
            raise ValueError("SECRET_KEY 环境变量必须设置")
        if len(v) < 32:
            raise ValueError("SECRET_KEY 长度至少32个字符")
        return v
    
    class Config:
        env_prefix = "JWT_"


class AIConfig(BaseSettings):
    """AI服务配置"""
    glm_api_key: str = Field("", env="GLM_API_KEY", description="GLM API密钥")
    glm_base_url: str = Field("https://open.bigmodel.cn/api/paas/v4/", env="GLM_BASE_URL", description="GLM API基础URL")
    max_tokens: int = Field(1000, env="AI_MAX_TOKENS", description="AI响应最大令牌数")
    temperature: float = Field(0.7, env="AI_TEMPERATURE", description="AI响应温度")
    timeout: int = Field(30, env="AI_TIMEOUT", description="AI请求超时时间（秒）")
    
    class Config:
        env_prefix = "AI_"


class CrawlerConfig(BaseSettings):
    """爬虫配置"""
    user_agents: List[str] = Field(
        default=[
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36"
        ],
        env="CRAWLER_USER_AGENTS",
        description="爬虫User-Agent列表"
    )
    request_delay: float = Field(1.0, env="CRAWLER_REQUEST_DELAY", description="请求间隔（秒）")
    max_retries: int = Field(3, env="CRAWLER_MAX_RETRIES", description="最大重试次数")
    timeout: int = Field(30, env="CRAWLER_TIMEOUT", description="请求超时时间（秒）")
    concurrent_limit: int = Field(2, env="CRAWLER_CONCURRENT_LIMIT", description="并发限制")
    
    class Config:
        env_prefix = "CRAWLER_"


class LoggingConfig(BaseSettings):
    """日志配置"""
    level: str = Field("INFO", env="LOG_LEVEL", description="日志级别")
    format: str = Field("structured", env="LOG_FORMAT", description="日志格式")
    file_path: Optional[str] = Field(None, env="LOG_FILE_PATH", description="日志文件路径")
    max_file_size: int = Field(10485760, env="LOG_MAX_FILE_SIZE", description="日志文件最大大小（字节）")
    backup_count: int = Field(5, env="LOG_BACKUP_COUNT", description="日志文件备份数量")
    
    class Config:
        env_prefix = "LOG_"


class SecurityConfig(BaseSettings):
    """安全配置"""
    cors_origins: List[str] = Field(
        default=["http://localhost:3000"],
        env="CORS_ORIGINS",
        description="允许的CORS源"
    )
    cors_methods: List[str] = Field(
        default=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        env="CORS_METHODS",
        description="允许的CORS方法"
    )
    rate_limit_requests: int = Field(100, env="RATE_LIMIT_REQUESTS", description="速率限制请求数")
    rate_limit_window: int = Field(60, env="RATE_LIMIT_WINDOW", description="速率限制时间窗口（秒）")
    
    class Config:
        env_prefix = "SECURITY_"


class UnifiedConfig(BaseSettings):
    """统一配置类"""
    # 应用基本配置
    app_name: str = Field("财经新闻Bot", env="APP_NAME", description="应用名称")
    app_version: str = Field("1.0.0", env="APP_VERSION", description="应用版本")
    debug: bool = Field(False, env="DEBUG", description="调试模式")
    environment: str = Field("production", env="ENVIRONMENT", description="运行环境")
    
    # 子配置
    database: DatabaseConfig = DatabaseConfig()
    redis: RedisConfig = RedisConfig()
    cache: CacheConfig = CacheConfig()
    jwt: JWTConfig = JWTConfig()
    ai: AIConfig = AIConfig()
    crawler: CrawlerConfig = CrawlerConfig()
    logging: LoggingConfig = LoggingConfig()
    security: SecurityConfig = SecurityConfig()
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        logger.info(f"配置加载完成 - 环境: {self.environment}, 调试模式: {self.debug}")


# 全局配置实例
try:
    config = UnifiedConfig()
except Exception as e:
    logger.error(f"配置加载失败: {str(e)}")
    raise


def get_config() -> UnifiedConfig:
    """获取配置实例"""
    return config


# 向后兼容的配置访问方式
def get_database_url() -> str:
    """获取数据库URL"""
    return config.database.url


def get_redis_config() -> dict:
    """获取Redis配置"""
    return {
        "host": config.redis.host,
        "port": config.redis.port,
        "db": config.redis.db,
        "password": config.redis.password,
        "max_connections": config.redis.max_connections,
        "socket_timeout": config.redis.socket_timeout,
        "socket_connect_timeout": config.redis.socket_connect_timeout,
        "retry_on_timeout": config.redis.retry_on_timeout
    }


def get_jwt_config() -> dict:
    """获取JWT配置"""
    return {
        "secret_key": config.jwt.secret_key,
        "algorithm": config.jwt.algorithm,
        "access_token_expire_minutes": config.jwt.access_token_expire_minutes,
        "refresh_token_expire_days": config.jwt.refresh_token_expire_days
    }
