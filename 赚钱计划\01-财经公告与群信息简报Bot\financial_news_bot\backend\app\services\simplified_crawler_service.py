"""
简化爬虫服务
重构原有的复杂爬虫系统，保留核心功能，移除过度设计
"""
import asyncio
import hashlib
import logging
import random
import time
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from urllib.parse import urlparse

import httpx

logger = logging.getLogger(__name__)

class SimpleCrawlerConfig:
    """简化的爬虫配置"""
    
    # 精简的User-Agent池（保留主流浏览器）
    USER_AGENTS = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    ]
    
    # 默认请求头
    DEFAULT_HEADERS = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Cache-Control': 'max-age=0'
    }
    
    # 请求配置
    DEFAULT_TIMEOUT = 30
    DEFAULT_RATE_LIMIT = 1.0
    MAX_RETRIES = 3
    BACKOFF_FACTOR = 2.0

class CrawlerStats:
    """爬虫统计信息"""
    
    def __init__(self):
        self.total_requests = 0
        self.successful_requests = 0
        self.failed_requests = 0
        self.total_items = 0
        self.start_time = None
        self.end_time = None
    
    def start(self):
        """开始统计"""
        self.start_time = datetime.now()
    
    def end(self):
        """结束统计"""
        self.end_time = datetime.now()
    
    def record_request(self, success: bool):
        """记录请求结果"""
        self.total_requests += 1
        if success:
            self.successful_requests += 1
        else:
            self.failed_requests += 1
    
    def record_items(self, count: int):
        """记录获取的项目数量"""
        self.total_items += count
    
    def get_summary(self) -> Dict[str, Any]:
        """获取统计摘要"""
        duration = None
        if self.start_time and self.end_time:
            duration = (self.end_time - self.start_time).total_seconds()
        
        return {
            'total_requests': self.total_requests,
            'successful_requests': self.successful_requests,
            'failed_requests': self.failed_requests,
            'success_rate': self.successful_requests / self.total_requests if self.total_requests > 0 else 0,
            'total_items': self.total_items,
            'duration_seconds': duration,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None
        }

class SimpleCrawler(ABC):
    """简化的基础爬虫类"""
    
    def __init__(self, name: str, base_url: str, rate_limit: float = None):
        """
        初始化爬虫
        
        Args:
            name: 爬虫名称
            base_url: 基础URL
            rate_limit: 请求间隔（秒）
        """
        self.name = name
        self.base_url = base_url
        self.rate_limit = rate_limit or SimpleCrawlerConfig.DEFAULT_RATE_LIMIT
        self.domain = urlparse(base_url).netloc
        
        # HTTP会话
        self.session = None
        self.last_request_time = 0
        
        # 统计信息
        self.stats = CrawlerStats()
        
        logger.info(f"初始化爬虫: {self.name} - {self.base_url}")
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close_session()
    
    async def start_session(self):
        """启动HTTP会话"""
        if self.session is None:
            # 随机选择User-Agent
            user_agent = random.choice(SimpleCrawlerConfig.USER_AGENTS)
            
            # 构建请求头
            headers = SimpleCrawlerConfig.DEFAULT_HEADERS.copy()
            headers['User-Agent'] = user_agent
            
            # 创建HTTP客户端
            self.session = httpx.AsyncClient(
                headers=headers,
                timeout=SimpleCrawlerConfig.DEFAULT_TIMEOUT,
                follow_redirects=True,
                verify=False  # 忽略SSL证书验证
            )
            
            logger.info(f"[{self.name}] HTTP会话已启动")
    
    async def close_session(self):
        """关闭HTTP会话"""
        if self.session:
            await self.session.aclose()
            self.session = None
            logger.info(f"[{self.name}] HTTP会话已关闭")
    
    async def _wait_rate_limit(self):
        """等待速率限制"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.rate_limit:
            wait_time = self.rate_limit - time_since_last
            await asyncio.sleep(wait_time)
        
        self.last_request_time = time.time()
    
    async def make_request(self, url: str, method: str = 'GET', **kwargs) -> Optional[httpx.Response]:
        """
        发起HTTP请求
        
        Args:
            url: 请求URL
            method: 请求方法
            **kwargs: 其他请求参数
        
        Returns:
            响应对象或None
        """
        if not self.session:
            await self.start_session()
        
        # 等待速率限制
        await self._wait_rate_limit()
        
        for attempt in range(SimpleCrawlerConfig.MAX_RETRIES):
            try:
                logger.debug(f"[{self.name}] 请求: {url} (尝试 {attempt + 1})")
                
                response = await self.session.request(method, url, **kwargs)
                response.raise_for_status()
                
                self.stats.record_request(True)
                logger.debug(f"[{self.name}] 请求成功: {url}")
                return response
                
            except httpx.HTTPStatusError as e:
                logger.warning(f"[{self.name}] HTTP错误 {e.response.status_code}: {url}")
                if e.response.status_code in [429, 503]:  # 速率限制或服务不可用
                    if attempt < SimpleCrawlerConfig.MAX_RETRIES - 1:
                        wait_time = SimpleCrawlerConfig.BACKOFF_FACTOR ** attempt
                        logger.info(f"[{self.name}] 等待 {wait_time} 秒后重试")
                        await asyncio.sleep(wait_time)
                        continue
                break
                
            except httpx.RequestError as e:
                logger.warning(f"[{self.name}] 请求错误: {url} - {str(e)}")
                if attempt < SimpleCrawlerConfig.MAX_RETRIES - 1:
                    wait_time = SimpleCrawlerConfig.BACKOFF_FACTOR ** attempt
                    logger.info(f"[{self.name}] 等待 {wait_time} 秒后重试")
                    await asyncio.sleep(wait_time)
                    continue
                break
                
            except Exception as e:
                logger.error(f"[{self.name}] 未知错误: {url} - {str(e)}")
                break
        
        self.stats.record_request(False)
        return None
    
    def generate_content_hash(self, content: str) -> str:
        """生成内容哈希值用于去重"""
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    def clean_text(self, text: str) -> str:
        """清理文本内容"""
        if not text:
            return ""
        
        # 移除多余的空白字符
        import re
        text = re.sub(r'\s+', ' ', text.strip())
        
        # 移除HTML标签（如果有）
        text = re.sub(r'<[^>]+>', '', text)
        
        return text
    
    def calculate_importance_score(self, item: Dict[str, Any]) -> int:
        """
        计算新闻重要性评分（简化版）
        
        Args:
            item: 新闻项目
        
        Returns:
            重要性评分 (0-100)
        """
        score = 0
        title = item.get('title', '').lower()
        
        # 高重要性关键词
        high_importance_keywords = [
            '重大', '紧急', '停牌', '复牌', '重组', '并购', '收购',
            '业绩', '财报', '分红', '违规', '处罚', '调查'
        ]
        
        # 中等重要性关键词
        medium_importance_keywords = [
            '公告', '通知', '决议', '变更', '增持', '减持', '回购'
        ]
        
        # 计算关键词匹配分数
        for keyword in high_importance_keywords:
            if keyword in title:
                score += 20
        
        for keyword in medium_importance_keywords:
            if keyword in title:
                score += 10
        
        # 根据标题长度调整
        if len(title) > 20:
            score += 5
        
        return min(score, 100)  # 限制在100以内
    
    @abstractmethod
    async def fetch_news_list(self, start_date: Optional[datetime] = None, 
                             end_date: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """
        获取新闻列表（抽象方法）
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
        
        Returns:
            新闻项目列表
        """
        pass
    
    async def fetch_news_detail(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取新闻详情（可选实现）
        
        Args:
            item: 新闻项目
        
        Returns:
            详细的新闻项目
        """
        # 默认实现：直接返回原项目
        return item
    
    async def crawl(self, start_date: Optional[datetime] = None, 
                   end_date: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """
        执行爬取任务
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
        
        Returns:
            爬取的新闻列表
        """
        self.stats.start()
        
        try:
            logger.info(f"[{self.name}] 开始爬取任务")
            
            # 获取新闻列表
            news_list = await self.fetch_news_list(start_date, end_date)
            self.stats.record_items(len(news_list))
            
            # 处理每个新闻项目
            processed_news = []
            for item in news_list:
                try:
                    # 获取详情（如果需要）
                    detailed_item = await self.fetch_news_detail(item)
                    
                    # 计算重要性评分
                    detailed_item['importance_score'] = self.calculate_importance_score(detailed_item)
                    
                    # 生成内容哈希
                    content = detailed_item.get('content', detailed_item.get('title', ''))
                    detailed_item['content_hash'] = self.generate_content_hash(content)
                    
                    # 添加爬虫信息
                    detailed_item['crawler_name'] = self.name
                    detailed_item['crawl_time'] = datetime.now().isoformat()
                    
                    processed_news.append(detailed_item)
                    
                except Exception as e:
                    logger.error(f"[{self.name}] 处理新闻项目失败: {str(e)}")
                    continue
            
            logger.info(f"[{self.name}] 爬取完成，获取 {len(processed_news)} 条新闻")
            return processed_news
            
        except Exception as e:
            logger.error(f"[{self.name}] 爬取任务失败: {str(e)}")
            return []
        
        finally:
            self.stats.end()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self.stats.get_summary()

class CrawlerManager:
    """爬虫管理器"""
    
    def __init__(self):
        self.crawlers: Dict[str, SimpleCrawler] = {}
        logger.info("爬虫管理器初始化完成")
    
    def register_crawler(self, crawler: SimpleCrawler):
        """注册爬虫"""
        self.crawlers[crawler.name] = crawler
        logger.info(f"注册爬虫: {crawler.name}")
    
    def get_crawler(self, name: str) -> Optional[SimpleCrawler]:
        """获取爬虫"""
        return self.crawlers.get(name)
    
    def list_crawlers(self) -> List[str]:
        """列出所有爬虫"""
        return list(self.crawlers.keys())
    
    async def crawl_all(self, start_date: Optional[datetime] = None, 
                       end_date: Optional[datetime] = None) -> Dict[str, List[Dict[str, Any]]]:
        """
        执行所有爬虫的爬取任务
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
        
        Returns:
            各爬虫的爬取结果
        """
        results = {}
        
        for name, crawler in self.crawlers.items():
            try:
                async with crawler:
                    news_list = await crawler.crawl(start_date, end_date)
                    results[name] = news_list
                    logger.info(f"爬虫 {name} 完成，获取 {len(news_list)} 条新闻")
            except Exception as e:
                logger.error(f"爬虫 {name} 执行失败: {str(e)}")
                results[name] = []
        
        return results
    
    def get_all_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取所有爬虫的统计信息"""
        stats = {}
        for name, crawler in self.crawlers.items():
            stats[name] = crawler.get_stats()
        return stats

# 全局爬虫管理器实例
crawler_manager = CrawlerManager()
