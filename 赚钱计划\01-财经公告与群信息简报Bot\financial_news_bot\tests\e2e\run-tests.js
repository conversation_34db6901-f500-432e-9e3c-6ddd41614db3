#!/usr/bin/env node

/**
 * 端到端测试运行脚本
 * 提供多种测试运行选项和环境配置
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// 命令行参数解析
const args = process.argv.slice(2);
const options = {
  browser: 'chromium',
  headed: false,
  debug: false,
  workers: 1,
  retries: 0,
  timeout: 60000,
  grep: '',
  project: '',
  reporter: 'html',
  baseURL: 'http://localhost:3000',
  env: 'development'
};

// 解析命令行参数
for (let i = 0; i < args.length; i++) {
  const arg = args[i];
  
  switch (arg) {
    case '--browser':
      options.browser = args[++i];
      break;
    case '--headed':
      options.headed = true;
      break;
    case '--debug':
      options.debug = true;
      break;
    case '--workers':
      options.workers = parseInt(args[++i]);
      break;
    case '--retries':
      options.retries = parseInt(args[++i]);
      break;
    case '--timeout':
      options.timeout = parseInt(args[++i]);
      break;
    case '--grep':
      options.grep = args[++i];
      break;
    case '--project':
      options.project = args[++i];
      break;
    case '--reporter':
      options.reporter = args[++i];
      break;
    case '--base-url':
      options.baseURL = args[++i];
      break;
    case '--env':
      options.env = args[++i];
      break;
    case '--help':
      showHelp();
      process.exit(0);
      break;
  }
}

function showHelp() {
  console.log(`
端到端测试运行器

用法: node run-tests.js [选项]

选项:
  --browser <name>     指定浏览器 (chromium, firefox, webkit)
  --headed             有头模式运行
  --debug              调试模式
  --workers <num>      并行工作进程数
  --retries <num>      失败重试次数
  --timeout <ms>       测试超时时间
  --grep <pattern>     测试名称过滤
  --project <name>     运行特定项目
  --reporter <type>    报告格式 (html, json, junit)
  --base-url <url>     应用基础URL
  --env <env>          环境 (development, staging, production)
  --help               显示帮助信息

示例:
  node run-tests.js --browser chromium --headed
  node run-tests.js --grep "用户旅程" --workers 2
  node run-tests.js --env staging --base-url https://staging.example.com
  `);
}

// 环境配置
const envConfigs = {
  development: {
    baseURL: 'http://localhost:3000',
    timeout: 60000,
    retries: 0,
  },
  staging: {
    baseURL: 'https://staging.financial-news-bot.com',
    timeout: 120000,
    retries: 2,
  },
  production: {
    baseURL: 'https://financial-news-bot.com',
    timeout: 180000,
    retries: 3,
  }
};

// 应用环境配置
if (envConfigs[options.env]) {
  Object.assign(options, envConfigs[options.env]);
}

// 构建Playwright命令
function buildPlaywrightCommand() {
  const cmd = ['npx', 'playwright', 'test'];
  
  // 基本选项
  if (options.headed) {
    cmd.push('--headed');
  }
  
  if (options.debug) {
    cmd.push('--debug');
  }
  
  if (options.workers) {
    cmd.push('--workers', options.workers.toString());
  }
  
  if (options.retries) {
    cmd.push('--retries', options.retries.toString());
  }
  
  if (options.timeout) {
    cmd.push('--timeout', options.timeout.toString());
  }
  
  if (options.grep) {
    cmd.push('--grep', options.grep);
  }
  
  if (options.project) {
    cmd.push('--project', options.project);
  }
  
  if (options.reporter) {
    cmd.push('--reporter', options.reporter);
  }
  
  return cmd;
}

// 设置环境变量
function setupEnvironment() {
  process.env.BASE_URL = options.baseURL;
  process.env.TEST_ENV = options.env;
  process.env.PWTEST_SKIP_TEST_OUTPUT = options.debug ? '0' : '1';
  
  console.log(`🌍 环境: ${options.env}`);
  console.log(`🔗 基础URL: ${options.baseURL}`);
  console.log(`🌐 浏览器: ${options.browser}`);
  console.log(`👥 工作进程: ${options.workers}`);
  console.log(`🔄 重试次数: ${options.retries}`);
  console.log(`⏱️ 超时时间: ${options.timeout}ms`);
}

// 检查依赖
async function checkDependencies() {
  console.log('🔍 检查依赖...');
  
  // 检查Playwright是否安装
  try {
    const { execSync } = require('child_process');
    execSync('npx playwright --version', { stdio: 'pipe' });
    console.log('✅ Playwright已安装');
  } catch (error) {
    console.error('❌ Playwright未安装，请运行: npm install @playwright/test');
    process.exit(1);
  }
  
  // 检查浏览器是否安装
  try {
    const { execSync } = require('child_process');
    execSync(`npx playwright install ${options.browser}`, { stdio: 'pipe' });
    console.log(`✅ ${options.browser}浏览器已准备就绪`);
  } catch (error) {
    console.warn(`⚠️ ${options.browser}浏览器可能需要安装`);
  }
}

// 等待服务启动
async function waitForService() {
  console.log(`⏳ 等待服务启动 (${options.baseURL})...`);
  
  const http = require('http');
  const https = require('https');
  const url = require('url');
  
  const parsedUrl = url.parse(options.baseURL);
  const client = parsedUrl.protocol === 'https:' ? https : http;
  
  let attempts = 0;
  const maxAttempts = 30;
  
  while (attempts < maxAttempts) {
    try {
      await new Promise((resolve, reject) => {
        const req = client.request({
          hostname: parsedUrl.hostname,
          port: parsedUrl.port || (parsedUrl.protocol === 'https:' ? 443 : 80),
          path: '/',
          method: 'GET',
          timeout: 5000,
        }, (res) => {
          resolve(res);
        });
        
        req.on('error', reject);
        req.on('timeout', () => reject(new Error('Timeout')));
        req.end();
      });
      
      console.log('✅ 服务已启动');
      return;
    } catch (error) {
      attempts++;
      console.log(`⏳ 等待服务启动... (${attempts}/${maxAttempts})`);
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  console.error('❌ 服务启动超时');
  process.exit(1);
}

// 运行测试
async function runTests() {
  console.log('🚀 开始运行端到端测试...');
  
  const cmd = buildPlaywrightCommand();
  console.log(`📝 执行命令: ${cmd.join(' ')}`);
  
  const child = spawn(cmd[0], cmd.slice(1), {
    stdio: 'inherit',
    env: { ...process.env }
  });
  
  child.on('close', (code) => {
    if (code === 0) {
      console.log('✅ 所有测试通过!');
      generateSummaryReport();
    } else {
      console.log('❌ 测试失败!');
      process.exit(code);
    }
  });
  
  child.on('error', (error) => {
    console.error('❌ 测试运行错误:', error);
    process.exit(1);
  });
}

// 生成汇总报告
function generateSummaryReport() {
  console.log('📊 生成测试汇总报告...');
  
  const reportData = {
    timestamp: new Date().toISOString(),
    environment: options.env,
    baseURL: options.baseURL,
    browser: options.browser,
    options: options,
    summary: '测试执行完成'
  };
  
  const reportDir = path.join(__dirname, '../../test-reports');
  if (!fs.existsSync(reportDir)) {
    fs.mkdirSync(reportDir, { recursive: true });
  }
  
  const reportPath = path.join(reportDir, 'e2e-summary.json');
  fs.writeFileSync(reportPath, JSON.stringify(reportData, null, 2));
  
  console.log(`📋 汇总报告已生成: ${reportPath}`);
}

// 主函数
async function main() {
  try {
    console.log('🎭 财经新闻Bot - 端到端测试');
    console.log('================================');
    
    setupEnvironment();
    await checkDependencies();
    
    if (options.env === 'development') {
      await waitForService();
    }
    
    await runTests();
  } catch (error) {
    console.error('❌ 测试执行失败:', error);
    process.exit(1);
  }
}

// 处理进程信号
process.on('SIGINT', () => {
  console.log('\n⏹️ 测试被中断');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n⏹️ 测试被终止');
  process.exit(0);
});

// 运行主函数
main();
