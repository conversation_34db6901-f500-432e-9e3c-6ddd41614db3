"""
基础设施管理API路由
提供数据库、Redis连接池管理和监控功能
"""
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel, Field
from datetime import datetime

from app.dependencies.auth import get_current_active_user
from app.database import check_database_health, get_database_stats, reset_connection_pool
from app.redis_client import redis_client
from app.models.user import User

# 创建路由器
router = APIRouter(prefix="/infrastructure", tags=["基础设施管理"])


class ConnectionPoolRequest(BaseModel):
    """连接池操作请求模型"""
    action: str = Field(..., description="操作类型: reset/stats/health")
    service: str = Field(..., description="服务类型: database/redis")


class CacheOperationRequest(BaseModel):
    """缓存操作请求模型"""
    operation: str = Field(..., description="操作类型: get/set/delete/clear")
    key: Optional[str] = Field(None, description="缓存键")
    value: Optional[Any] = Field(None, description="缓存值")
    ttl: Optional[int] = Field(None, description="过期时间（秒）")


@router.get("/database/health", response_model=Dict[str, Any])
async def get_database_health(current_user: User = Depends(get_current_active_user)):
    """
    获取数据库健康状态
    """
    try:
        # 检查用户权限
        if current_user.role not in ["admin", "enterprise"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，需要管理员或企业版权限"
            )
        
        # 获取数据库健康状态
        health_info = check_database_health()
        
        return {
            "success": True,
            "message": "获取数据库健康状态成功",
            "data": health_info
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取数据库健康状态失败: {str(e)}"
        )


@router.get("/database/stats", response_model=Dict[str, Any])
async def get_database_statistics(current_user: User = Depends(get_current_active_user)):
    """
    获取数据库统计信息
    """
    try:
        # 检查用户权限
        if current_user.role not in ["admin", "enterprise"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，需要管理员或企业版权限"
            )
        
        # 获取数据库统计信息
        stats = get_database_stats()
        
        return {
            "success": True,
            "message": "获取数据库统计信息成功",
            "data": stats
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取数据库统计信息失败: {str(e)}"
        )


@router.post("/database/pool/reset", response_model=Dict[str, Any])
async def reset_database_pool(current_user: User = Depends(get_current_active_user)):
    """
    重置数据库连接池
    """
    try:
        # 检查用户权限
        if current_user.role != "admin":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，需要管理员权限"
            )
        
        # 重置连接池
        success = reset_connection_pool()
        
        if success:
            return {
                "success": True,
                "message": "数据库连接池重置成功"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="数据库连接池重置失败"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"重置数据库连接池失败: {str(e)}"
        )


@router.get("/redis/health", response_model=Dict[str, Any])
async def get_redis_health(current_user: User = Depends(get_current_active_user)):
    """
    获取Redis健康状态
    """
    try:
        # 检查用户权限
        if current_user.role not in ["admin", "enterprise"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，需要管理员或企业版权限"
            )
        
        # 获取Redis健康状态
        health_info = redis_client.health_check()
        
        return {
            "success": True,
            "message": "获取Redis健康状态成功",
            "data": health_info
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取Redis健康状态失败: {str(e)}"
        )


@router.get("/redis/stats", response_model=Dict[str, Any])
async def get_redis_statistics(current_user: User = Depends(get_current_active_user)):
    """
    获取Redis统计信息
    """
    try:
        # 检查用户权限
        if current_user.role not in ["admin", "enterprise"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，需要管理员或企业版权限"
            )
        
        # 获取Redis统计信息
        stats = redis_client.get_stats()
        
        return {
            "success": True,
            "message": "获取Redis统计信息成功",
            "data": stats
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取Redis统计信息失败: {str(e)}"
        )


@router.post("/redis/pool/reset", response_model=Dict[str, Any])
async def reset_redis_pool(current_user: User = Depends(get_current_active_user)):
    """
    重置Redis连接池
    """
    try:
        # 检查用户权限
        if current_user.role != "admin":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，需要管理员权限"
            )
        
        # 重置连接池
        success = redis_client.reset_connection_pool()
        
        if success:
            return {
                "success": True,
                "message": "Redis连接池重置成功"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Redis连接池重置失败"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"重置Redis连接池失败: {str(e)}"
        )


@router.post("/cache/operation", response_model=Dict[str, Any])
async def cache_operation(
    request: CacheOperationRequest,
    current_user: User = Depends(get_current_active_user)
):
    """
    缓存操作
    """
    try:
        # 检查用户权限
        if current_user.role not in ["admin", "enterprise"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，需要管理员或企业版权限"
            )
        
        result = None
        
        if request.operation == "get":
            if not request.key:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="GET操作需要提供key参数"
                )
            result = redis_client.get_json(request.key)
            
        elif request.operation == "set":
            if not request.key or request.value is None:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="SET操作需要提供key和value参数"
                )
            success = redis_client.set_json(request.key, request.value, ex=request.ttl)
            result = {"success": success}
            
        elif request.operation == "delete":
            if not request.key:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="DELETE操作需要提供key参数"
                )
            deleted_count = redis_client.delete(request.key)
            result = {"deleted_count": deleted_count}
            
        elif request.operation == "clear":
            # 清空当前数据库（谨慎操作）
            if current_user.role != "admin":
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="清空缓存需要管理员权限"
                )
            # 这里可以实现清空逻辑，但要非常小心
            result = {"message": "清空操作需要额外确认"}
            
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的操作类型: {request.operation}"
            )
        
        return {
            "success": True,
            "message": f"缓存{request.operation}操作完成",
            "data": result
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"缓存操作失败: {str(e)}"
        )


@router.get("/overview", response_model=Dict[str, Any])
async def get_infrastructure_overview(current_user: User = Depends(get_current_active_user)):
    """
    获取基础设施概览
    """
    try:
        # 检查用户权限
        if current_user.role not in ["admin", "enterprise"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，需要管理员或企业版权限"
            )
        
        # 获取数据库状态
        db_health = check_database_health()
        db_stats = get_database_stats()
        
        # 获取Redis状态
        redis_health = redis_client.health_check()
        redis_stats = redis_client.get_stats()
        
        # 汇总状态
        overall_status = "healthy"
        if db_health.get("status") != "healthy" or redis_health.get("status") != "healthy":
            overall_status = "unhealthy"
        
        return {
            "success": True,
            "message": "获取基础设施概览成功",
            "data": {
                "overall_status": overall_status,
                "database": {
                    "status": db_health.get("status"),
                    "pool_status": db_health.get("pool_status"),
                    "statistics": db_stats
                },
                "redis": {
                    "status": redis_health.get("status"),
                    "pool_stats": redis_health.get("pool_stats"),
                    "statistics": redis_stats
                },
                "timestamp": datetime.utcnow().isoformat()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取基础设施概览失败: {str(e)}"
        )


@router.get("/health", response_model=Dict[str, Any])
async def infrastructure_health_check():
    """
    基础设施健康检查（公开接口）
    """
    try:
        # 快速健康检查
        db_healthy = True
        redis_healthy = True
        
        try:
            db_health = check_database_health()
            db_healthy = db_health.get("status") == "healthy"
        except Exception:
            db_healthy = False
        
        try:
            redis_health = redis_client.health_check()
            redis_healthy = redis_health.get("status") == "healthy"
        except Exception:
            redis_healthy = False
        
        overall_healthy = db_healthy and redis_healthy
        
        return {
            "success": True,
            "message": "基础设施健康检查完成",
            "data": {
                "overall_health": overall_healthy,
                "database_health": db_healthy,
                "redis_health": redis_healthy,
                "timestamp": datetime.utcnow().isoformat()
            }
        }
        
    except Exception as e:
        return {
            "success": False,
            "message": f"基础设施健康检查失败: {str(e)}",
            "data": {
                "overall_health": False,
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
        }
