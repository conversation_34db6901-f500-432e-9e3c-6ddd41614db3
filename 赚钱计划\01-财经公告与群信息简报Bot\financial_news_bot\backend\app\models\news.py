from sqlalchemy import Column, Integer, String, Text, DateTime, Enum, JSON, Index
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum
from ..database import Base


class NewsCategory(str, PyEnum):
    """新闻分类枚举"""
    ANNOUNCEMENT = "announcement"  # 公告
    REGULATION = "regulation"      # 监管
    MARKET = "market"             # 市场动态
    FINANCE = "finance"           # 财务报告
    GOVERNANCE = "governance"     # 公司治理
    POLICY = "policy"             # 政策
    OTHER = "other"              # 其他


class NewsSource(str, PyEnum):
    """新闻来源枚举"""
    SSE = "sse"           # 上海证券交易所
    SZSE = "szse"         # 深圳证券交易所
    CSRC = "csrc"         # 证监会
    RSS = "rss"           # RSS源
    MANUAL = "manual"     # 手动添加


class NewsSentiment(str, PyEnum):
    """情感分析枚举"""
    POSITIVE = "positive"   # 正面
    NEGATIVE = "negative"   # 负面
    NEUTRAL = "neutral"     # 中性
    UNKNOWN = "unknown"     # 未知


class News(Base):
    """新闻数据模型"""
    __tablename__ = "news"

    id = Column(Integer, primary_key=True, index=True)

    # 基本信息
    title = Column(String(500), nullable=False, comment="新闻标题")
    content = Column(Text, nullable=True, comment="新闻内容")
    summary = Column(String(1000), nullable=True, comment="新闻摘要")

    # 来源信息
    source = Column(Enum(NewsSource), nullable=False, comment="新闻来源")
    source_url = Column(String(500), nullable=True, comment="原文链接")
    source_id = Column(String(100), nullable=True, comment="来源系统中的ID")

    # 时间信息
    published_at = Column(DateTime, nullable=True, comment="发布时间")
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")

    # 分类和标签
    category = Column(Enum(NewsCategory), default=NewsCategory.OTHER, comment="新闻分类")
    tags = Column(JSON, nullable=True, comment="标签列表")

    # 重要性和情感
    importance_score = Column(Integer, default=50, comment="重要性评分(0-100)")
    sentiment = Column(Enum(NewsSentiment), default=NewsSentiment.UNKNOWN, comment="情感倾向")

    # 实体信息
    entities = Column(JSON, nullable=True, comment="提取的实体信息(公司、人名、地名等)")
    companies = Column(JSON, nullable=True, comment="相关公司列表")
    stock_codes = Column(JSON, nullable=True, comment="相关股票代码")

    # 内容特征
    content_hash = Column(String(64), nullable=True, comment="内容哈希值，用于去重")
    word_count = Column(Integer, default=0, comment="字数统计")

    # 处理状态
    is_processed = Column(Integer, default=0, comment="是否已处理(0:未处理, 1:已处理)")
    is_duplicate = Column(Integer, default=0, comment="是否重复(0:不重复, 1:重复)")

    # 索引
    __table_args__ = (
        Index('idx_news_source_published', 'source', 'published_at'),
        Index('idx_news_category_created', 'category', 'created_at'),
        Index('idx_news_content_hash', 'content_hash'),
        Index('idx_news_importance', 'importance_score'),
        Index('idx_news_processed', 'is_processed'),
        Index('idx_news_duplicate', 'is_duplicate'),
    )

    # 关系定义
    push_logs = relationship("PushLog", back_populates="news")

    def __repr__(self):
        return f"<News(id={self.id}, title='{self.title[:50]}...', source='{self.source}')>"
