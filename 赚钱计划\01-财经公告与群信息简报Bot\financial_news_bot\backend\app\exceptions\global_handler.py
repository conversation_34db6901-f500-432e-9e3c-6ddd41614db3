"""
全局异常处理器
统一处理所有未捕获的异常，提供一致的错误响应格式
"""
import logging
import traceback
import uuid
from typing import Dict, Any, Optional
from datetime import datetime

from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from pydantic import ValidationError
import redis.exceptions

from .base import BaseAPIException, handle_database_error, handle_external_service_error
from ..constants import StatusCodes, EnvironmentConstants

logger = logging.getLogger(__name__)


class GlobalExceptionHandler:
    """全局异常处理器"""
    
    def __init__(self):
        self.error_tracking = {}  # 简单的错误跟踪
    
    async def handle_base_api_exception(self, request: Request, exc: BaseAPIException) -> JSONResponse:
        """处理自定义API异常"""
        request_id = self._get_request_id(request)
        
        # 记录异常
        logger.error(
            f"API异常 [{request_id}]: {exc.error_code} - {exc.detail}",
            extra={
                'request_id': request_id,
                'error_code': exc.error_code,
                'status_code': exc.status_code,
                'path': request.url.path,
                'method': request.method
            }
        )
        
        return JSONResponse(
            status_code=exc.status_code,
            content={
                'error': {
                    'code': exc.error_code or 'API_ERROR',
                    'message': exc.detail,
                    'request_id': request_id,
                    'timestamp': datetime.now().isoformat(),
                    'path': str(request.url.path)
                }
            },
            headers=exc.headers
        )
    
    async def handle_http_exception(self, request: Request, exc: HTTPException) -> JSONResponse:
        """处理FastAPI HTTP异常"""
        request_id = self._get_request_id(request)
        
        logger.warning(
            f"HTTP异常 [{request_id}]: {exc.status_code} - {exc.detail}",
            extra={
                'request_id': request_id,
                'status_code': exc.status_code,
                'path': request.url.path,
                'method': request.method
            }
        )
        
        return JSONResponse(
            status_code=exc.status_code,
            content={
                'error': {
                    'code': self._get_error_code_from_status(exc.status_code),
                    'message': exc.detail,
                    'request_id': request_id,
                    'timestamp': datetime.now().isoformat(),
                    'path': str(request.url.path)
                }
            },
            headers=getattr(exc, 'headers', None)
        )
    
    async def handle_validation_error(self, request: Request, exc: RequestValidationError) -> JSONResponse:
        """处理请求验证错误"""
        request_id = self._get_request_id(request)
        
        # 格式化验证错误信息
        errors = []
        for error in exc.errors():
            field = '.'.join(str(loc) for loc in error['loc'][1:])  # 跳过'body'
            errors.append({
                'field': field,
                'message': error['msg'],
                'type': error['type'],
                'input': error.get('input')
            })
        
        logger.warning(
            f"验证错误 [{request_id}]: {len(errors)}个字段错误",
            extra={
                'request_id': request_id,
                'errors': errors,
                'path': request.url.path,
                'method': request.method
            }
        )
        
        return JSONResponse(
            status_code=StatusCodes.UNPROCESSABLE_ENTITY,
            content={
                'error': {
                    'code': 'VALIDATION_ERROR',
                    'message': '请求数据验证失败',
                    'details': errors,
                    'request_id': request_id,
                    'timestamp': datetime.now().isoformat(),
                    'path': str(request.url.path)
                }
            }
        )
    
    async def handle_database_exception(self, request: Request, exc: SQLAlchemyError) -> JSONResponse:
        """处理数据库异常"""
        request_id = self._get_request_id(request)
        
        # 使用统一的数据库异常处理
        api_exception = handle_database_error(exc)
        
        logger.error(
            f"数据库异常 [{request_id}]: {type(exc).__name__} - {str(exc)}",
            extra={
                'request_id': request_id,
                'exception_type': type(exc).__name__,
                'path': request.url.path,
                'method': request.method
            },
            exc_info=True
        )
        
        return JSONResponse(
            status_code=api_exception.status_code,
            content={
                'error': {
                    'code': api_exception.error_code,
                    'message': api_exception.detail,
                    'request_id': request_id,
                    'timestamp': datetime.now().isoformat(),
                    'path': str(request.url.path)
                }
            }
        )
    
    async def handle_redis_exception(self, request: Request, exc: redis.exceptions.RedisError) -> JSONResponse:
        """处理Redis异常"""
        request_id = self._get_request_id(request)
        
        logger.error(
            f"Redis异常 [{request_id}]: {type(exc).__name__} - {str(exc)}",
            extra={
                'request_id': request_id,
                'exception_type': type(exc).__name__,
                'path': request.url.path,
                'method': request.method
            }
        )
        
        return JSONResponse(
            status_code=StatusCodes.SERVICE_UNAVAILABLE,
            content={
                'error': {
                    'code': 'CACHE_SERVICE_ERROR',
                    'message': '缓存服务暂时不可用',
                    'request_id': request_id,
                    'timestamp': datetime.now().isoformat(),
                    'path': str(request.url.path)
                }
            }
        )
    
    async def handle_generic_exception(self, request: Request, exc: Exception) -> JSONResponse:
        """处理通用异常"""
        request_id = self._get_request_id(request)
        
        # 记录完整的异常信息
        logger.error(
            f"未处理异常 [{request_id}]: {type(exc).__name__} - {str(exc)}",
            extra={
                'request_id': request_id,
                'exception_type': type(exc).__name__,
                'path': request.url.path,
                'method': request.method,
                'traceback': traceback.format_exc()
            },
            exc_info=True
        )
        
        # 在开发环境返回详细错误信息
        if EnvironmentConstants.is_development():
            error_detail = {
                'code': 'INTERNAL_SERVER_ERROR',
                'message': str(exc),
                'type': type(exc).__name__,
                'traceback': traceback.format_exc().split('\n'),
                'request_id': request_id,
                'timestamp': datetime.now().isoformat(),
                'path': str(request.url.path)
            }
        else:
            # 生产环境返回通用错误信息
            error_detail = {
                'code': 'INTERNAL_SERVER_ERROR',
                'message': '服务器内部错误，请稍后重试',
                'request_id': request_id,
                'timestamp': datetime.now().isoformat(),
                'path': str(request.url.path)
            }
        
        return JSONResponse(
            status_code=StatusCodes.INTERNAL_SERVER_ERROR,
            content={'error': error_detail}
        )
    
    def _get_request_id(self, request: Request) -> str:
        """获取或生成请求ID"""
        # 尝试从请求头获取
        request_id = request.headers.get('X-Request-ID')
        if not request_id:
            # 尝试从请求状态获取
            request_id = getattr(request.state, 'request_id', None)
        if not request_id:
            # 生成新的请求ID
            request_id = str(uuid.uuid4())
            request.state.request_id = request_id
        return request_id
    
    def _get_error_code_from_status(self, status_code: int) -> str:
        """根据状态码获取错误代码"""
        code_mapping = {
            400: 'BAD_REQUEST',
            401: 'UNAUTHORIZED',
            403: 'FORBIDDEN',
            404: 'NOT_FOUND',
            405: 'METHOD_NOT_ALLOWED',
            409: 'CONFLICT',
            422: 'VALIDATION_ERROR',
            429: 'RATE_LIMIT_EXCEEDED',
            500: 'INTERNAL_SERVER_ERROR',
            502: 'BAD_GATEWAY',
            503: 'SERVICE_UNAVAILABLE',
            504: 'GATEWAY_TIMEOUT'
        }
        return code_mapping.get(status_code, 'UNKNOWN_ERROR')
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计信息"""
        return {
            'total_errors': len(self.error_tracking),
            'error_types': dict(self.error_tracking),
            'last_updated': datetime.now().isoformat()
        }


# 全局异常处理器实例
global_exception_handler = GlobalExceptionHandler()


def setup_exception_handlers(app):
    """设置全局异常处理器"""
    
    @app.exception_handler(BaseAPIException)
    async def base_api_exception_handler(request: Request, exc: BaseAPIException):
        return await global_exception_handler.handle_base_api_exception(request, exc)
    
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        return await global_exception_handler.handle_http_exception(request, exc)
    
    @app.exception_handler(RequestValidationError)
    async def validation_exception_handler(request: Request, exc: RequestValidationError):
        return await global_exception_handler.handle_validation_error(request, exc)
    
    @app.exception_handler(SQLAlchemyError)
    async def database_exception_handler(request: Request, exc: SQLAlchemyError):
        return await global_exception_handler.handle_database_exception(request, exc)
    
    @app.exception_handler(redis.exceptions.RedisError)
    async def redis_exception_handler(request: Request, exc: redis.exceptions.RedisError):
        return await global_exception_handler.handle_redis_exception(request, exc)
    
    @app.exception_handler(Exception)
    async def generic_exception_handler(request: Request, exc: Exception):
        return await global_exception_handler.handle_generic_exception(request, exc)
    
    logger.info("全局异常处理器设置完成")


def get_global_exception_handler() -> GlobalExceptionHandler:
    """获取全局异常处理器实例"""
    return global_exception_handler
