"""
集成监控路由
提供安全监控和合规监控的统一API接口
替代原有的独立微服务
"""
from fastapi import APIRouter, Depends, HTTPException, Request
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime

from app.services.integrated_monitoring_service import integrated_monitoring
from app.dependencies.auth import get_current_user
from app.models.user import User

router = APIRouter(prefix="/api/v1/monitoring", tags=["集成监控服务"])

class ContentCheckRequest(BaseModel):
    """内容检查请求模型"""
    content: str = Field(..., description="要检查的内容", max_length=50000)
    check_type: str = Field(default="full", description="检查类型: security, compliance, full")

class ContentCheckResponse(BaseModel):
    """内容检查响应模型"""
    approved: bool
    security: Dict[str, Any] = {}
    compliance: Dict[str, Any] = {}
    timestamp: str

class RequestCheckRequest(BaseModel):
    """请求检查请求模型"""
    client_ip: str = Field(..., description="客户端IP地址")
    user_agent: str = Field(default="", description="用户代理字符串")
    params: Dict[str, Any] = Field(default={}, description="请求参数")
    headers: Dict[str, str] = Field(default={}, description="请求头")

class RequestCheckResponse(BaseModel):
    """请求检查响应模型"""
    approved: bool
    security: Dict[str, Any]
    timestamp: str

class MonitoringStatusResponse(BaseModel):
    """监控状态响应模型"""
    status: str
    security_events_count: int
    compliance_violations_count: int
    compliance_score: float
    last_check: str

@router.post("/content/check", response_model=ContentCheckResponse)
async def check_content(
    request: ContentCheckRequest,
    current_user: User = Depends(get_current_user)
):
    """
    内容安全和合规检查
    
    对提交的内容进行综合的安全和合规检查
    """
    try:
        if request.check_type == "security":
            # 仅安全检查
            security_result = integrated_monitoring.security_monitor.scan_content(request.content)
            return ContentCheckResponse(
                approved=security_result["safe"],
                security=security_result,
                timestamp=datetime.now().isoformat()
            )
        
        elif request.check_type == "compliance":
            # 仅合规检查
            compliance_result = integrated_monitoring.compliance_monitor.check_content_compliance(request.content)
            compliance_dict = {
                "status": compliance_result.status.value,
                "risk_level": compliance_result.risk_level.value,
                "violations": compliance_result.violations,
                "suggestions": compliance_result.suggestions,
                "details": compliance_result.details
            }
            return ContentCheckResponse(
                approved=compliance_result.status.value == "compliant",
                compliance=compliance_dict,
                timestamp=datetime.now().isoformat()
            )
        
        else:
            # 完整检查（默认）
            result = integrated_monitoring.check_content(request.content)
            return ContentCheckResponse(
                approved=result["approved"],
                security=result.get("security", {}),
                compliance=result.get("compliance", {}),
                timestamp=result["timestamp"]
            )
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"内容检查异常: {str(e)}")

@router.post("/request/check", response_model=RequestCheckResponse)
async def check_request_security(
    request: RequestCheckRequest,
    current_user: User = Depends(get_current_user)
):
    """
    请求安全检查
    
    检查HTTP请求的安全性，包括频率限制、可疑模式等
    """
    try:
        request_data = {
            "client_ip": request.client_ip,
            "user_agent": request.user_agent,
            "params": request.params,
            "headers": request.headers
        }
        
        result = integrated_monitoring.check_request(request_data)
        
        return RequestCheckResponse(
            approved=result["approved"],
            security=result.get("security", {}),
            timestamp=result["timestamp"]
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"请求检查异常: {str(e)}")

@router.get("/status", response_model=MonitoringStatusResponse)
async def get_monitoring_status(current_user: User = Depends(get_current_user)):
    """
    获取监控服务状态
    
    返回当前监控服务的运行状态和统计信息
    """
    try:
        status = integrated_monitoring.get_monitoring_status()
        
        return MonitoringStatusResponse(
            status=status["status"],
            security_events_count=status.get("security_events_count", 0),
            compliance_violations_count=status.get("compliance_violations_count", 0),
            compliance_score=status.get("compliance_score", 0.0),
            last_check=status["last_check"]
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取监控状态异常: {str(e)}")

@router.get("/security/events")
async def get_security_events(
    limit: int = 50,
    level: Optional[str] = None,
    current_user: User = Depends(get_current_user)
):
    """
    获取安全事件列表
    
    返回最近的安全事件记录
    """
    try:
        events = integrated_monitoring.security_monitor.security_events
        
        # 按级别过滤
        if level:
            events = [e for e in events if e.level.value == level]
        
        # 限制数量并转换为字典
        limited_events = events[-limit:] if events else []
        
        event_dicts = []
        for event in limited_events:
            event_dicts.append({
                "event_id": event.event_id,
                "event_type": event.event_type,
                "level": event.level.value,
                "message": event.message,
                "source": event.source,
                "details": event.details,
                "timestamp": event.timestamp.isoformat()
            })
        
        return {
            "events": event_dicts,
            "total_count": len(event_dicts),
            "available_levels": ["low", "medium", "high", "critical"]
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取安全事件异常: {str(e)}")

@router.get("/compliance/violations")
async def get_compliance_violations(
    limit: int = 50,
    days: int = 30,
    current_user: User = Depends(get_current_user)
):
    """
    获取合规违规记录
    
    返回指定天数内的合规违规记录
    """
    try:
        violations = integrated_monitoring.compliance_monitor.violation_history
        
        # 按时间过滤
        cutoff_date = datetime.now() - timedelta(days=days)
        recent_violations = [
            v for v in violations
            if v["timestamp"] >= cutoff_date
        ]
        
        # 限制数量
        limited_violations = recent_violations[-limit:] if recent_violations else []
        
        # 转换时间戳格式
        for violation in limited_violations:
            violation["timestamp"] = violation["timestamp"].isoformat()
        
        return {
            "violations": limited_violations,
            "total_count": len(limited_violations),
            "period_days": days
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取合规违规记录异常: {str(e)}")

@router.get("/compliance/audit")
async def get_compliance_audit(current_user: User = Depends(get_current_user)):
    """
    获取合规审计报告
    
    返回系统合规状况的综合审计报告
    """
    try:
        audit_result = integrated_monitoring.compliance_monitor.audit_system_compliance()
        return audit_result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取合规审计报告异常: {str(e)}")

@router.get("/health")
async def health_check():
    """
    健康检查接口
    
    用于检查监控服务是否正常运行
    """
    try:
        status = integrated_monitoring.get_monitoring_status()
        
        if status["status"] == "healthy":
            return {"status": "healthy", "timestamp": datetime.now().isoformat()}
        else:
            raise HTTPException(status_code=503, detail="监控服务不健康")
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"健康检查失败: {str(e)}")

@router.post("/test")
async def test_monitoring_service(
    test_content: str = "这是一个测试内容，用于验证监控服务功能。",
    current_user: User = Depends(get_current_user)
):
    """
    测试监控服务
    
    发送测试内容来验证监控服务是否正常工作
    """
    try:
        # 测试内容检查
        content_result = integrated_monitoring.check_content(test_content)
        
        # 测试请求检查
        test_request_data = {
            "client_ip": "127.0.0.1",
            "user_agent": "test-agent",
            "params": {"test": "value"},
            "headers": {"Content-Type": "application/json"}
        }
        request_result = integrated_monitoring.check_request(test_request_data)
        
        # 获取状态
        status = integrated_monitoring.get_monitoring_status()
        
        return {
            "test_results": {
                "content_check": content_result,
                "request_check": request_result,
                "service_status": status
            },
            "success": True,
            "message": "监控服务测试完成",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            "test_results": {},
            "success": False,
            "message": f"监控服务测试失败: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }
