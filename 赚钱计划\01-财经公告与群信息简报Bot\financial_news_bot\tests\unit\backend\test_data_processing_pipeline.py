"""
数据处理管道测试
测试数据处理管道的各个处理器和管道编排功能
"""
import pytest
from datetime import datetime
from unittest.mock import patch, Mock

from app.services.data_processing_pipeline import (
    ProcessingStage,
    ProcessingResult,
    TextCleaner,
    EntityExtractor,
    SentimentAnalyzer,
    ImportanceScorer,
    ContentClassifier,
    DuplicateDetector,
    DataProcessingPipeline,
    data_pipeline
)

class TestProcessingResult:
    """测试处理结果数据结构"""
    
    def test_processing_result_creation(self):
        """测试处理结果创建"""
        result = ProcessingResult(
            success=True,
            data="处理后的数据",
            metadata={"key": "value"},
            errors=["错误1"],
            stage=ProcessingStage.TEXT_CLEANING
        )
        
        assert result.success is True
        assert result.data == "处理后的数据"
        assert result.metadata == {"key": "value"}
        assert result.errors == ["错误1"]
        assert result.stage == ProcessingStage.TEXT_CLEANING
    
    def test_processing_result_defaults(self):
        """测试处理结果默认值"""
        result = ProcessingResult()
        
        assert result.success is True
        assert result.data is None
        assert result.metadata == {}
        assert result.errors == []
        assert result.stage is None

class TestTextCleaner:
    """测试文本清理处理器"""
    
    @pytest.fixture
    def cleaner(self):
        return TextCleaner()
    
    def test_clean_normal_text(self, cleaner):
        """测试清理正常文本"""
        text = "  这是一个测试文本。  包含多个  空格。  "
        result = cleaner.process(text)
        
        assert result.success is True
        assert result.data == "这是一个测试文本。 包含多个 空格。"
        assert result.stage == ProcessingStage.TEXT_CLEANING
        assert "original_length" in result.metadata
        assert "cleaned_length" in result.metadata
    
    def test_clean_text_with_special_characters(self, cleaner):
        """测试清理包含特殊字符的文本"""
        text = "测试文本@#$%^&*()包含特殊字符！"
        result = cleaner.process(text)
        
        assert result.success is True
        assert "@#$%^&*()" not in result.data
        assert "测试文本" in result.data
        assert "包含特殊字符！" in result.data
    
    def test_clean_invalid_input(self, cleaner):
        """测试清理无效输入"""
        result = cleaner.process(123)  # 非字符串输入
        
        assert result.success is False
        assert "输入数据必须是字符串类型" in result.errors[0]
        assert result.stage == ProcessingStage.TEXT_CLEANING
    
    def test_get_stage(self, cleaner):
        """测试获取处理阶段"""
        assert cleaner.get_stage() == ProcessingStage.TEXT_CLEANING

class TestEntityExtractor:
    """测试实体提取处理器"""
    
    @pytest.fixture
    def extractor(self):
        return EntityExtractor()
    
    def test_extract_companies(self, extractor):
        """测试提取公司名称"""
        text = "中国平安保险股份有限公司和腾讯控股有限公司签署合作协议"
        result = extractor.process(text)
        
        assert result.success is True
        assert "companies" in result.data
        assert "stock_codes" in result.data
        assert "organizations" in result.data
        assert "keywords" in result.data
        
        companies = result.data["companies"]
        assert any("平安" in company for company in companies)
        assert any("腾讯" in company for company in companies)
    
    def test_extract_stock_codes(self, extractor):
        """测试提取股票代码"""
        text = "股票代码000001和600036今日涨停"
        result = extractor.process(text)
        
        assert result.success is True
        stock_codes = result.data["stock_codes"]
        assert "000001" in stock_codes
        assert "600036" in stock_codes
    
    def test_extract_organizations(self, extractor):
        """测试提取机构名称"""
        text = "中国银行和招商证券发布研报"
        result = extractor.process(text)
        
        assert result.success is True
        organizations = result.data["organizations"]
        assert any("银行" in org for org in organizations)
        assert any("证券" in org for org in organizations)
    
    def test_extract_invalid_input(self, extractor):
        """测试提取无效输入"""
        result = extractor.process(None)
        
        assert result.success is False
        assert "输入数据必须是字符串类型" in result.errors[0]

class TestSentimentAnalyzer:
    """测试情感分析处理器"""
    
    @pytest.fixture
    def analyzer(self):
        return SentimentAnalyzer()
    
    def test_analyze_positive_sentiment(self, analyzer):
        """测试分析积极情感"""
        text = "公司业绩优秀，股价上涨，投资者乐观"
        result = analyzer.process(text)
        
        assert result.success is True
        assert result.data["sentiment"] == "positive"
        assert result.data["confidence"] > 0.5
        assert result.data["score"] > 0
        assert result.data["positive_words_count"] > 0
    
    def test_analyze_negative_sentiment(self, analyzer):
        """测试分析消极情感"""
        text = "公司亏损严重，股价下跌，风险巨大"
        result = analyzer.process(text)
        
        assert result.success is True
        assert result.data["sentiment"] == "negative"
        assert result.data["confidence"] > 0.5
        assert result.data["score"] < 0
        assert result.data["negative_words_count"] > 0
    
    def test_analyze_neutral_sentiment(self, analyzer):
        """测试分析中性情感"""
        text = "公司发布公告，说明相关情况"
        result = analyzer.process(text)
        
        assert result.success is True
        assert result.data["sentiment"] == "neutral"
        assert result.data["score"] == 0.0
    
    def test_analyze_dict_input(self, analyzer):
        """测试分析字典输入"""
        data = {"title": "好消息", "content": "公司业绩增长"}
        result = analyzer.process(data)
        
        assert result.success is True
        assert result.data["sentiment"] == "positive"

class TestImportanceScorer:
    """测试重要性评分处理器"""
    
    @pytest.fixture
    def scorer(self):
        return ImportanceScorer()
    
    def test_score_high_importance(self, scorer):
        """测试高重要性评分"""
        data = {"title": "重大资产重组公告", "content": "公司进行重大资产重组"}
        result = scorer.process(data)
        
        assert result.success is True
        assert result.data["score"] >= 60  # 高重要性
        assert result.data["level"] in ["high", "critical"]
        assert len(result.data["matched_keywords"]) > 0
    
    def test_score_low_importance(self, scorer):
        """测试低重要性评分"""
        data = {"title": "公司澄清公告", "content": "对相关事项进行澄清说明"}
        result = scorer.process(data)
        
        assert result.success is True
        assert result.data["score"] < 40  # 低重要性
        assert result.data["level"] in ["low", "minimal"]
    
    def test_score_string_input(self, scorer):
        """测试字符串输入评分"""
        result = scorer.process("业绩预告：公司预计盈利增长")
        
        assert result.success is True
        assert result.data["score"] > 0
        assert "业绩预告" in str(result.data["matched_keywords"])

class TestContentClassifier:
    """测试内容分类处理器"""
    
    @pytest.fixture
    def classifier(self):
        return ContentClassifier()
    
    def test_classify_policy_content(self, classifier):
        """测试政策监管内容分类"""
        text = "证监会发布新的监管政策和法规"
        result = classifier.process(text)
        
        assert result.success is True
        assert result.data["category"] == "政策监管"
        assert result.data["confidence"] > 0
    
    def test_classify_company_announcement(self, classifier):
        """测试公司公告分类"""
        text = "公司发布重要公告，披露相关风险"
        result = classifier.process(text)
        
        assert result.success is True
        assert result.data["category"] == "公司公告"
        assert result.data["confidence"] > 0
    
    def test_classify_market_dynamics(self, classifier):
        """测试市场动态分类"""
        text = "股市行情波动，指数上涨，成交量放大"
        result = classifier.process(text)
        
        assert result.success is True
        assert result.data["category"] == "市场动态"
        assert result.data["confidence"] > 0
    
    def test_classify_unknown_content(self, classifier):
        """测试未知内容分类"""
        text = "这是一段没有明确分类特征的文本"
        result = classifier.process(text)
        
        assert result.success is True
        assert result.data["category"] == "其他"
        assert result.data["confidence"] == 0.5

class TestDuplicateDetector:
    """测试重复检测处理器"""
    
    @pytest.fixture
    def detector(self):
        return DuplicateDetector()
    
    def test_detect_string_input(self, detector):
        """测试字符串输入重复检测"""
        text = "这是一段测试文本"
        result = detector.process(text)
        
        assert result.success is True
        assert "content_hash" in result.data
        assert "simplified_hash" in result.data
        assert result.data["content_length"] == len(text)
        assert result.data["is_duplicate"] is False
    
    def test_detect_dict_input(self, detector):
        """测试字典输入重复检测"""
        data = {"title": "标题", "content": "内容"}
        result = detector.process(data)
        
        assert result.success is True
        assert "content_hash" in result.data
        assert result.data["content_length"] > 0
    
    def test_detect_same_content_hash(self, detector):
        """测试相同内容生成相同哈希"""
        text1 = "相同的测试内容"
        text2 = "相同的测试内容"
        
        result1 = detector.process(text1)
        result2 = detector.process(text2)
        
        assert result1.data["content_hash"] == result2.data["content_hash"]

class TestDataProcessingPipeline:
    """测试数据处理管道"""
    
    @pytest.fixture
    def pipeline(self):
        return DataProcessingPipeline()
    
    def test_process_text_default_pipeline(self, pipeline):
        """测试默认管道处理文本"""
        text = "测试文本：某公司发布重要公告"
        result = pipeline.process_text(text)
        
        assert result["success"] is True
        assert result["input"] == text
        assert len(result["stages"]) == 6  # 默认6个阶段
        assert "metadata" in result
        assert "pipeline_start" in result["metadata"]
        assert "pipeline_end" in result["metadata"]
    
    def test_process_text_custom_stages(self, pipeline):
        """测试自定义阶段处理文本"""
        text = "测试文本"
        stages = [ProcessingStage.TEXT_CLEANING, ProcessingStage.SENTIMENT_ANALYSIS]
        result = pipeline.process_text(text, stages)
        
        assert result["success"] is True
        assert len(result["stages"]) == 2
        assert ProcessingStage.TEXT_CLEANING.value in result["stages"]
        assert ProcessingStage.SENTIMENT_ANALYSIS.value in result["stages"]
    
    def test_process_news_article(self, pipeline):
        """测试处理新闻文章"""
        title = "重要新闻标题"
        content = "这是新闻内容，包含重要信息"
        result = pipeline.process_news_article(title, content)
        
        assert result["success"] is True
        assert result["input"]["title"] == title
        assert result["input"]["content"] == content
        assert len(result["stages"]) == 6
    
    def test_process_batch(self, pipeline):
        """测试批量处理"""
        items = [
            "第一条文本",
            {"title": "新闻标题", "content": "新闻内容"},
            "第三条文本"
        ]
        results = pipeline.process_batch(items)
        
        assert len(results) == 3
        for i, result in enumerate(results):
            assert result["batch_index"] == i
            assert "success" in result
            assert "stages" in result
    
    def test_get_available_stages(self, pipeline):
        """测试获取可用阶段"""
        stages = pipeline.get_available_stages()
        
        assert len(stages) == 6
        assert "text_cleaning" in stages
        assert "entity_extraction" in stages
        assert "sentiment_analysis" in stages
        assert "importance_scoring" in stages
        assert "content_classification" in stages
        assert "duplicate_detection" in stages
    
    def test_get_processor_info(self, pipeline):
        """测试获取处理器信息"""
        info = pipeline.get_processor_info(ProcessingStage.TEXT_CLEANING)
        
        assert "stage" in info
        assert "class_name" in info
        assert "description" in info
        assert info["stage"] == "text_cleaning"
        assert info["class_name"] == "TextCleaner"

class TestGlobalPipeline:
    """测试全局管道实例"""
    
    def test_global_pipeline_exists(self):
        """测试全局管道实例存在"""
        assert data_pipeline is not None
        assert isinstance(data_pipeline, DataProcessingPipeline)
    
    def test_global_pipeline_processors(self):
        """测试全局管道处理器"""
        assert len(data_pipeline.processors) == 6
        assert ProcessingStage.TEXT_CLEANING in data_pipeline.processors
        assert ProcessingStage.ENTITY_EXTRACTION in data_pipeline.processors

if __name__ == "__main__":
    pytest.main([__file__])
