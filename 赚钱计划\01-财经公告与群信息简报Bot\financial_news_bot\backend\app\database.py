from sqlalchemy import create_engine, event
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import QueuePool
import os
import logging
import time
from typing import Optional
from .utils.database_config import get_database_url, get_database_config

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 使用统一的数据库配置管理
db_config = get_database_config()
DATABASE_URL = db_config.url
pool_config = db_config.get_pool_config()

# 创建数据库引擎（使用统一配置）
engine = create_engine(
    DATABASE_URL,
    # 连接池配置 - 使用统一配置管理
    poolclass=QueuePool,
    **pool_config,

    # MySQL特定配置
    connect_args={
        "charset": "utf8mb4",
        "connect_timeout": 10,
        "read_timeout": 30,
        "write_timeout": 30,
        "ssl_disabled": True,
        "autocommit": False,
        "sql_mode": "STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO"
    },

    # 性能配置
    echo=os.getenv("DB_ECHO", "false").lower() == "true",
    echo_pool=os.getenv("DB_ECHO_POOL", "false").lower() == "true",
    future=True
)

# 连接池统计信息
class DatabaseStats:
    def __init__(self):
        self.connection_count = 0
        self.query_count = 0
        self.error_count = 0
        self.slow_query_count = 0
        self.total_query_time = 0.0
        self.start_time = time.time()

    def record_connection(self):
        self.connection_count += 1

    def record_query(self, duration: float, is_slow: bool = False):
        self.query_count += 1
        self.total_query_time += duration
        if is_slow:
            self.slow_query_count += 1

    def record_error(self):
        self.error_count += 1

    def get_stats(self) -> dict:
        uptime = time.time() - self.start_time
        avg_query_time = self.total_query_time / max(self.query_count, 1)

        return {
            "uptime_seconds": uptime,
            "connection_count": self.connection_count,
            "query_count": self.query_count,
            "error_count": self.error_count,
            "slow_query_count": self.slow_query_count,
            "avg_query_time": avg_query_time,
            "queries_per_second": self.query_count / max(uptime, 1),
            "pool_size": engine.pool.size(),
            "checked_in_connections": engine.pool.checkedin(),
            "checked_out_connections": engine.pool.checkedout(),
            "overflow_connections": engine.pool.overflow(),
            "invalid_connections": engine.pool.invalidated()
        }

# 全局统计实例
db_stats = DatabaseStats()

# 数据库事件监听器
@event.listens_for(engine, "connect")
def receive_connect(dbapi_connection, connection_record):
    """连接建立时的回调"""
    db_stats.record_connection()
    logger.debug("Database connection established")

@event.listens_for(engine, "before_cursor_execute")
def receive_before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    """SQL执行前的回调"""
    context._query_start_time = time.time()

@event.listens_for(engine, "after_cursor_execute")
def receive_after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    """SQL执行后的回调"""
    if hasattr(context, '_query_start_time'):
        duration = time.time() - context._query_start_time
        is_slow = duration > 1.0  # 超过1秒认为是慢查询
        db_stats.record_query(duration, is_slow)

        if is_slow:
            logger.warning(f"Slow query detected: {duration:.2f}s - {statement[:100]}...")

@event.listens_for(engine, "handle_error")
def receive_handle_error(exception_context):
    """数据库错误处理"""
    db_stats.record_error()
    logger.error(f"Database error: {exception_context.original_exception}")

# 创建会话工厂（优化配置）
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine,
    expire_on_commit=False  # 避免在事务提交后对象过期
)

# 创建基础模型类
Base = declarative_base()

# 依赖注入函数，用于获取数据库会话（增强版）
def get_db():
    """获取数据库会话，带有错误处理和监控"""
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"Database session error: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()

# 数据库健康检查
def check_database_health() -> dict:
    """检查数据库连接健康状态"""
    try:
        # 测试连接
        with engine.connect() as conn:
            result = conn.execute("SELECT 1 as health_check")
            row = result.fetchone()

        # 获取连接池状态
        pool_status = {
            "pool_size": engine.pool.size(),
            "checked_in": engine.pool.checkedin(),
            "checked_out": engine.pool.checkedout(),
            "overflow": engine.pool.overflow(),
            "invalid": engine.pool.invalidated()
        }

        # 获取统计信息
        stats = db_stats.get_stats()

        return {
            "status": "healthy",
            "connection_test": "passed",
            "pool_status": pool_status,
            "statistics": stats,
            "timestamp": time.time()
        }

    except Exception as e:
        logger.error(f"Database health check failed: {str(e)}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": time.time()
        }

# 数据库连接池重置
def reset_connection_pool():
    """重置数据库连接池"""
    try:
        engine.dispose()
        logger.info("Database connection pool reset successfully")
        return True
    except Exception as e:
        logger.error(f"Failed to reset connection pool: {str(e)}")
        return False

# 获取数据库统计信息
def get_database_stats() -> dict:
    """获取数据库统计信息"""
    return db_stats.get_stats()
