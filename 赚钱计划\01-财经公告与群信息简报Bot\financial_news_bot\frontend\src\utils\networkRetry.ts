/**
 * 网络重试和离线处理机制
 * 提供自动重试、离线检测、数据缓存等功能
 */

import { message } from 'antd';
import { reportApiError } from './errorMonitoring';

// 重试配置
export interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffFactor: number;
  retryCondition?: (error: any) => boolean;
  onRetry?: (attempt: number, error: any) => void;
}

// 缓存配置
export interface CacheConfig {
  key: string;
  ttl: number; // 缓存时间（毫秒）
  version?: string;
}

// 网络状态
export interface NetworkStatus {
  online: boolean;
  effectiveType?: string;
  downlink?: number;
  rtt?: number;
}

// 默认重试配置
const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxRetries: 3,
  baseDelay: 1000,
  maxDelay: 10000,
  backoffFactor: 2,
  retryCondition: (error) => {
    // 网络错误或5xx服务器错误才重试
    return !error.response || error.response.status >= 500;
  },
};

class NetworkManager {
  private networkStatus: NetworkStatus = { online: navigator.onLine };
  private offlineQueue: Array<() => Promise<any>> = [];
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();
  private listeners: Array<(status: NetworkStatus) => void> = [];

  constructor() {
    this.initNetworkMonitoring();
    this.initOfflineHandling();
  }

  private initNetworkMonitoring() {
    // 监听网络状态变化
    window.addEventListener('online', () => {
      this.updateNetworkStatus({ online: true });
      this.processOfflineQueue();
    });

    window.addEventListener('offline', () => {
      this.updateNetworkStatus({ online: false });
      message.warning('网络连接已断开，将在恢复后自动重试');
    });

    // 监听网络质量变化（如果支持）
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      const updateConnection = () => {
        this.updateNetworkStatus({
          online: navigator.onLine,
          effectiveType: connection.effectiveType,
          downlink: connection.downlink,
          rtt: connection.rtt,
        });
      };

      connection.addEventListener('change', updateConnection);
      updateConnection();
    }
  }

  private initOfflineHandling() {
    // 页面可见性变化时检查网络状态
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden && !navigator.onLine) {
        // 页面重新可见时检查网络
        this.checkNetworkStatus();
      }
    });
  }

  private updateNetworkStatus(status: NetworkStatus) {
    this.networkStatus = { ...this.networkStatus, ...status };
    this.notifyListeners();
  }

  private notifyListeners() {
    this.listeners.forEach(listener => {
      try {
        listener(this.networkStatus);
      } catch (error) {
        console.error('网络状态监听器错误:', error);
      }
    });
  }

  private async checkNetworkStatus(): Promise<boolean> {
    try {
      const response = await fetch('/api/v1/health', {
        method: 'HEAD',
        cache: 'no-cache',
      });
      const online = response.ok;
      this.updateNetworkStatus({ online });
      return online;
    } catch (error) {
      this.updateNetworkStatus({ online: false });
      return false;
    }
  }

  private async processOfflineQueue() {
    if (!this.networkStatus.online || this.offlineQueue.length === 0) {
      return;
    }

    message.info('网络已恢复，正在处理离线期间的请求...');

    const queue = [...this.offlineQueue];
    this.offlineQueue = [];

    let successCount = 0;
    let failCount = 0;

    for (const request of queue) {
      try {
        await request();
        successCount++;
      } catch (error) {
        failCount++;
        console.error('离线队列请求失败:', error);
      }
    }

    if (successCount > 0) {
      message.success(`成功处理 ${successCount} 个离线请求`);
    }

    if (failCount > 0) {
      message.error(`${failCount} 个请求处理失败`);
    }
  }

  // 延迟函数
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 计算重试延迟
  private calculateDelay(attempt: number, config: RetryConfig): number {
    const delay = config.baseDelay * Math.pow(config.backoffFactor, attempt - 1);
    return Math.min(delay, config.maxDelay);
  }

  // 带重试的请求
  public async requestWithRetry<T>(
    requestFn: () => Promise<T>,
    config: Partial<RetryConfig> = {}
  ): Promise<T> {
    const finalConfig = { ...DEFAULT_RETRY_CONFIG, ...config };
    let lastError: any;

    for (let attempt = 1; attempt <= finalConfig.maxRetries + 1; attempt++) {
      try {
        const result = await requestFn();
        return result;
      } catch (error) {
        lastError = error;

        // 记录API错误
        if (error.response) {
          reportApiError(
            error.config?.url || 'unknown',
            error.response.status,
            error.message,
            { attempt, maxRetries: finalConfig.maxRetries }
          );
        }

        // 检查是否应该重试
        if (attempt > finalConfig.maxRetries || !finalConfig.retryCondition?.(error)) {
          break;
        }

        // 调用重试回调
        finalConfig.onRetry?.(attempt, error);

        // 等待重试延迟
        const delayMs = this.calculateDelay(attempt, finalConfig);
        await this.delay(delayMs);

        console.log(`请求重试 ${attempt}/${finalConfig.maxRetries}, 延迟 ${delayMs}ms`);
      }
    }

    throw lastError;
  }

  // 离线队列请求
  public async requestWithOfflineQueue<T>(
    requestFn: () => Promise<T>,
    fallbackData?: T
  ): Promise<T> {
    if (this.networkStatus.online) {
      return this.requestWithRetry(requestFn);
    }

    // 离线状态
    if (fallbackData !== undefined) {
      return fallbackData;
    }

    // 添加到离线队列
    return new Promise((resolve, reject) => {
      this.offlineQueue.push(async () => {
        try {
          const result = await this.requestWithRetry(requestFn);
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });

      message.info('当前离线，请求已加入队列，网络恢复后自动处理');
    });
  }

  // 缓存请求
  public async requestWithCache<T>(
    requestFn: () => Promise<T>,
    cacheConfig: CacheConfig
  ): Promise<T> {
    const cacheKey = `${cacheConfig.key}_${cacheConfig.version || 'v1'}`;
    const cached = this.cache.get(cacheKey);

    // 检查缓存是否有效
    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      return cached.data;
    }

    try {
      const result = await this.requestWithRetry(requestFn);
      
      // 存储到缓存
      this.cache.set(cacheKey, {
        data: result,
        timestamp: Date.now(),
        ttl: cacheConfig.ttl,
      });

      return result;
    } catch (error) {
      // 如果有过期缓存，返回缓存数据
      if (cached) {
        console.warn('请求失败，返回过期缓存数据');
        return cached.data;
      }
      throw error;
    }
  }

  // 清理过期缓存
  public cleanExpiredCache() {
    const now = Date.now();
    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp >= value.ttl) {
        this.cache.delete(key);
      }
    }
  }

  // 清空所有缓存
  public clearCache() {
    this.cache.clear();
  }

  // 获取网络状态
  public getNetworkStatus(): NetworkStatus {
    return { ...this.networkStatus };
  }

  // 监听网络状态变化
  public onNetworkStatusChange(listener: (status: NetworkStatus) => void) {
    this.listeners.push(listener);
    
    // 返回取消监听的函数
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  // 手动检查网络状态
  public async refreshNetworkStatus(): Promise<NetworkStatus> {
    await this.checkNetworkStatus();
    return this.getNetworkStatus();
  }

  // 获取离线队列长度
  public getOfflineQueueLength(): number {
    return this.offlineQueue.length;
  }
}

// 全局网络管理器实例
export const networkManager = new NetworkManager();

// 便捷方法
export const retryRequest = <T>(
  requestFn: () => Promise<T>,
  config?: Partial<RetryConfig>
): Promise<T> => {
  return networkManager.requestWithRetry(requestFn, config);
};

export const offlineRequest = <T>(
  requestFn: () => Promise<T>,
  fallbackData?: T
): Promise<T> => {
  return networkManager.requestWithOfflineQueue(requestFn, fallbackData);
};

export const cachedRequest = <T>(
  requestFn: () => Promise<T>,
  cacheConfig: CacheConfig
): Promise<T> => {
  return networkManager.requestWithCache(requestFn, cacheConfig);
};

// React Hook
export const useNetworkStatus = () => {
  const [networkStatus, setNetworkStatus] = React.useState(networkManager.getNetworkStatus());

  React.useEffect(() => {
    const unsubscribe = networkManager.onNetworkStatusChange(setNetworkStatus);
    return unsubscribe;
  }, []);

  return networkStatus;
};

// 定期清理缓存
setInterval(() => {
  networkManager.cleanExpiredCache();
}, 5 * 60 * 1000); // 每5分钟清理一次
