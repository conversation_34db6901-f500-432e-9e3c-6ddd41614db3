"""
告警系统
统一的告警管理和通知机制
"""
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from enum import Enum
from dataclasses import dataclass, field
import json

from app.services.unified_push_service import unified_push_service, PushChannel, MessageType

logger = logging.getLogger(__name__)

class AlertLevel(Enum):
    """告警级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class AlertCategory(Enum):
    """告警分类"""
    SYSTEM = "system"
    SECURITY = "security"
    PERFORMANCE = "performance"
    BUSINESS = "business"
    COMPLIANCE = "compliance"

@dataclass
class Alert:
    """告警数据结构"""
    id: str
    title: str
    message: str
    level: AlertLevel
    category: AlertCategory
    source: str
    timestamp: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)
    resolved: bool = False
    resolved_at: Optional[datetime] = None
    resolved_by: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "title": self.title,
            "message": self.message,
            "level": self.level.value,
            "category": self.category.value,
            "source": self.source,
            "timestamp": self.timestamp.isoformat(),
            "metadata": self.metadata,
            "resolved": self.resolved,
            "resolved_at": self.resolved_at.isoformat() if self.resolved_at else None,
            "resolved_by": self.resolved_by
        }

class AlertRule:
    """告警规则"""
    
    def __init__(self, 
                 name: str,
                 condition: Callable[[Dict[str, Any]], bool],
                 level: AlertLevel,
                 category: AlertCategory,
                 title_template: str,
                 message_template: str,
                 cooldown_minutes: int = 30):
        self.name = name
        self.condition = condition
        self.level = level
        self.category = category
        self.title_template = title_template
        self.message_template = message_template
        self.cooldown_minutes = cooldown_minutes
        self.last_triggered = None
    
    def should_trigger(self, data: Dict[str, Any]) -> bool:
        """检查是否应该触发告警"""
        # 检查冷却时间
        if self.last_triggered:
            cooldown_end = self.last_triggered + timedelta(minutes=self.cooldown_minutes)
            if datetime.now() < cooldown_end:
                return False
        
        # 检查条件
        try:
            return self.condition(data)
        except Exception as e:
            logger.error(f"告警规则 {self.name} 条件检查异常: {str(e)}")
            return False
    
    def create_alert(self, data: Dict[str, Any], source: str) -> Alert:
        """创建告警"""
        self.last_triggered = datetime.now()
        
        # 格式化标题和消息
        try:
            title = self.title_template.format(**data)
            message = self.message_template.format(**data)
        except KeyError as e:
            logger.warning(f"告警模板格式化失败: {str(e)}")
            title = self.title_template
            message = self.message_template
        
        alert_id = f"{self.name}_{int(datetime.now().timestamp())}"
        
        return Alert(
            id=alert_id,
            title=title,
            message=message,
            level=self.level,
            category=self.category,
            source=source,
            metadata=data
        )

class AlertManager:
    """告警管理器"""
    
    def __init__(self):
        self.rules: Dict[str, AlertRule] = {}
        self.active_alerts: Dict[str, Alert] = {}
        self.alert_history: List[Alert] = []
        self.notification_channels: Dict[AlertLevel, List[PushChannel]] = {
            AlertLevel.INFO: [PushChannel.WECHAT_WORK],
            AlertLevel.WARNING: [PushChannel.WECHAT_WORK, PushChannel.EMAIL],
            AlertLevel.ERROR: [PushChannel.WECHAT_WORK, PushChannel.EMAIL, PushChannel.FEISHU],
            AlertLevel.CRITICAL: [PushChannel.WECHAT_WORK, PushChannel.EMAIL, PushChannel.FEISHU]
        }
        
        # 初始化默认规则
        self._setup_default_rules()
        
        logger.info("告警管理器初始化完成")
    
    def _setup_default_rules(self):
        """设置默认告警规则"""
        # 系统资源告警
        self.add_rule(AlertRule(
            name="high_cpu_usage",
            condition=lambda data: data.get("cpu_usage", 0) > 80,
            level=AlertLevel.WARNING,
            category=AlertCategory.PERFORMANCE,
            title_template="CPU使用率过高",
            message_template="当前CPU使用率: {cpu_usage:.1f}%，超过阈值80%",
            cooldown_minutes=15
        ))
        
        self.add_rule(AlertRule(
            name="high_memory_usage",
            condition=lambda data: data.get("memory_usage", 0) > 85,
            level=AlertLevel.WARNING,
            category=AlertCategory.PERFORMANCE,
            title_template="内存使用率过高",
            message_template="当前内存使用率: {memory_usage:.1f}%，超过阈值85%",
            cooldown_minutes=15
        ))
        
        self.add_rule(AlertRule(
            name="high_disk_usage",
            condition=lambda data: data.get("disk_usage", 0) > 90,
            level=AlertLevel.ERROR,
            category=AlertCategory.SYSTEM,
            title_template="磁盘空间不足",
            message_template="当前磁盘使用率: {disk_usage:.1f}%，超过阈值90%",
            cooldown_minutes=30
        ))
        
        # 安全告警
        self.add_rule(AlertRule(
            name="failed_login_attempts",
            condition=lambda data: data.get("failed_count", 0) > 10,
            level=AlertLevel.WARNING,
            category=AlertCategory.SECURITY,
            title_template="登录失败次数过多",
            message_template="最近1小时内登录失败次数: {failed_count}次，超过阈值10次",
            cooldown_minutes=60
        ))
        
        self.add_rule(AlertRule(
            name="suspicious_activity",
            condition=lambda data: len(data.get("activities", [])) > 0,
            level=AlertLevel.ERROR,
            category=AlertCategory.SECURITY,
            title_template="检测到可疑活动",
            message_template="发现{activity_count}个可疑活动，请及时处理",
            cooldown_minutes=30
        ))
        
        # 业务告警
        self.add_rule(AlertRule(
            name="crawler_failure",
            condition=lambda data: data.get("success_rate", 1.0) < 0.5,
            level=AlertLevel.ERROR,
            category=AlertCategory.BUSINESS,
            title_template="爬虫成功率过低",
            message_template="爬虫 {crawler_name} 成功率: {success_rate:.1%}，低于阈值50%",
            cooldown_minutes=30
        ))
        
        self.add_rule(AlertRule(
            name="api_error_rate",
            condition=lambda data: data.get("error_rate", 0) > 0.1,
            level=AlertLevel.WARNING,
            category=AlertCategory.PERFORMANCE,
            title_template="API错误率过高",
            message_template="API错误率: {error_rate:.1%}，超过阈值10%",
            cooldown_minutes=15
        ))
    
    def add_rule(self, rule: AlertRule):
        """添加告警规则"""
        self.rules[rule.name] = rule
        logger.info(f"添加告警规则: {rule.name}")
    
    def remove_rule(self, rule_name: str):
        """移除告警规则"""
        if rule_name in self.rules:
            del self.rules[rule_name]
            logger.info(f"移除告警规则: {rule_name}")
    
    async def check_alerts(self, data: Dict[str, Any], source: str = "system"):
        """检查并触发告警"""
        triggered_alerts = []
        
        for rule_name, rule in self.rules.items():
            try:
                if rule.should_trigger(data):
                    alert = rule.create_alert(data, source)
                    await self._process_alert(alert)
                    triggered_alerts.append(alert)
                    
            except Exception as e:
                logger.error(f"处理告警规则 {rule_name} 时异常: {str(e)}")
        
        return triggered_alerts
    
    async def _process_alert(self, alert: Alert):
        """处理告警"""
        # 添加到活跃告警
        self.active_alerts[alert.id] = alert
        
        # 添加到历史记录
        self.alert_history.append(alert)
        
        # 限制历史记录数量
        if len(self.alert_history) > 1000:
            self.alert_history = self.alert_history[-1000:]
        
        # 记录日志
        logger.warning(
            f"触发告警: {alert.title}",
            extra={
                "alert_id": alert.id,
                "level": alert.level.value,
                "category": alert.category.value,
                "source": alert.source,
                "metadata": alert.metadata
            }
        )
        
        # 发送通知
        await self._send_notification(alert)
    
    async def _send_notification(self, alert: Alert):
        """发送告警通知"""
        try:
            # 获取通知渠道
            channels = self.notification_channels.get(alert.level, [PushChannel.WECHAT_WORK])
            
            # 构建通知消息
            message_content = self._format_alert_message(alert)
            
            # 发送通知
            results = await unified_push_service.send_message(
                title=f"🚨 {alert.title}",
                content=message_content,
                message_type=MessageType.TEXT,
                channels=channels
            )
            
            # 记录发送结果
            success_count = sum(1 for result in results if result.success)
            logger.info(f"告警通知发送完成: {success_count}/{len(results)} 成功")
            
        except Exception as e:
            logger.error(f"发送告警通知失败: {str(e)}")
    
    def _format_alert_message(self, alert: Alert) -> str:
        """格式化告警消息"""
        level_emoji = {
            AlertLevel.INFO: "ℹ️",
            AlertLevel.WARNING: "⚠️",
            AlertLevel.ERROR: "❌",
            AlertLevel.CRITICAL: "🔥"
        }
        
        category_emoji = {
            AlertCategory.SYSTEM: "🖥️",
            AlertCategory.SECURITY: "🔒",
            AlertCategory.PERFORMANCE: "⚡",
            AlertCategory.BUSINESS: "💼",
            AlertCategory.COMPLIANCE: "📋"
        }
        
        message = f"""
{level_emoji.get(alert.level, '🔔')} **告警通知**

**级别**: {alert.level.value.upper()}
**分类**: {category_emoji.get(alert.category, '📌')} {alert.category.value}
**来源**: {alert.source}
**时间**: {alert.timestamp.strftime('%Y-%m-%d %H:%M:%S')}

**详情**: {alert.message}

**告警ID**: {alert.id}
        """.strip()
        
        return message
    
    async def resolve_alert(self, alert_id: str, resolved_by: str = "system"):
        """解决告警"""
        if alert_id in self.active_alerts:
            alert = self.active_alerts[alert_id]
            alert.resolved = True
            alert.resolved_at = datetime.now()
            alert.resolved_by = resolved_by
            
            # 从活跃告警中移除
            del self.active_alerts[alert_id]
            
            logger.info(f"告警已解决: {alert_id} by {resolved_by}")
            
            return True
        
        return False
    
    def get_active_alerts(self, level: Optional[AlertLevel] = None, 
                         category: Optional[AlertCategory] = None) -> List[Alert]:
        """获取活跃告警"""
        alerts = list(self.active_alerts.values())
        
        if level:
            alerts = [alert for alert in alerts if alert.level == level]
        
        if category:
            alerts = [alert for alert in alerts if alert.category == category]
        
        return sorted(alerts, key=lambda x: x.timestamp, reverse=True)
    
    def get_alert_history(self, hours: int = 24, 
                         level: Optional[AlertLevel] = None) -> List[Alert]:
        """获取告警历史"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        alerts = [alert for alert in self.alert_history if alert.timestamp >= cutoff_time]
        
        if level:
            alerts = [alert for alert in alerts if alert.level == level]
        
        return sorted(alerts, key=lambda x: x.timestamp, reverse=True)
    
    def get_alert_statistics(self, hours: int = 24) -> Dict[str, Any]:
        """获取告警统计"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_alerts = [alert for alert in self.alert_history if alert.timestamp >= cutoff_time]
        
        stats = {
            "total_alerts": len(recent_alerts),
            "active_alerts": len(self.active_alerts),
            "resolved_alerts": len([alert for alert in recent_alerts if alert.resolved]),
            "by_level": {},
            "by_category": {},
            "by_source": {}
        }
        
        # 按级别统计
        for level in AlertLevel:
            count = len([alert for alert in recent_alerts if alert.level == level])
            stats["by_level"][level.value] = count
        
        # 按分类统计
        for category in AlertCategory:
            count = len([alert for alert in recent_alerts if alert.category == category])
            stats["by_category"][category.value] = count
        
        # 按来源统计
        sources = {}
        for alert in recent_alerts:
            sources[alert.source] = sources.get(alert.source, 0) + 1
        stats["by_source"] = sources
        
        return stats

# 全局告警管理器实例
alert_manager = AlertManager()

async def trigger_alert(title: str, message: str, level: AlertLevel, 
                       category: AlertCategory, source: str = "manual",
                       metadata: Dict[str, Any] = None) -> Alert:
    """手动触发告警"""
    alert_id = f"manual_{int(datetime.now().timestamp())}"
    
    alert = Alert(
        id=alert_id,
        title=title,
        message=message,
        level=level,
        category=category,
        source=source,
        metadata=metadata or {}
    )
    
    await alert_manager._process_alert(alert)
    return alert
