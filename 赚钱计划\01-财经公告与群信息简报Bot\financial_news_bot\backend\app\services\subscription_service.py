from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, func
import json

from ..models.subscription import Subscription
from ..models.user import User
from ..schemas.subscription import (
    SubscriptionCreate, SubscriptionUpdate, SubscriptionResponse,
    SubscriptionListResponse, SubscriptionStats, SubscriptionStatus
)
from ..exceptions.subscription import (
    SubscriptionNotFoundError, SubscriptionAccessDeniedError,
    SubscriptionLimitExceededError, InvalidSubscriptionConfigError,
    ChannelConfigurationError
)


class SubscriptionService:
    """订阅管理服务"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_user_subscription_limit(self, user: User) -> int:
        """获取用户订阅数量限制"""
        limits = {
            "free": 3,
            "pro": 20,
            "enterprise": 100
        }
        return limits.get(user.role, 3)
    
    def validate_channel_config(self, channel: str, config: Dict[str, Any]) -> None:
        """验证推送渠道配置"""
        if channel == "wechat":
            if "webhook_url" not in config:
                raise ChannelConfigurationError(channel, "缺少 webhook_url 字段")
            if not config["webhook_url"].startswith("https://qyapi.weixin.qq.com"):
                raise ChannelConfigurationError(channel, "webhook_url 格式不正确，应以 https://qyapi.weixin.qq.com 开头")

        elif channel == "feishu":
            if "webhook_url" not in config:
                raise ChannelConfigurationError(channel, "缺少 webhook_url 字段")
            if not config["webhook_url"].startswith("https://open.feishu.cn"):
                raise ChannelConfigurationError(channel, "webhook_url 格式不正确，应以 https://open.feishu.cn 开头")

        elif channel == "email":
            if "recipients" not in config:
                raise ChannelConfigurationError(channel, "缺少 recipients 字段")
            if not isinstance(config["recipients"], list) or not config["recipients"]:
                raise ChannelConfigurationError(channel, "recipients 必须是非空列表")

        elif channel == "webhook":
            if "url" not in config:
                raise ChannelConfigurationError(channel, "缺少 url 字段")
            if not config["url"].startswith(("http://", "https://")):
                raise ChannelConfigurationError(channel, "url 格式不正确，应以 http:// 或 https:// 开头")
    
    def create_subscription(self, user_id: int, subscription_data: SubscriptionCreate) -> SubscriptionResponse:
        """创建订阅"""
        # 检查用户是否存在
        user = self.db.query(User).filter(User.id == user_id).first()
        if not user:
            raise SubscriptionNotFoundError(user_id)
        
        # 检查订阅数量限制
        current_count = self.db.query(func.count(Subscription.id)).filter(
            Subscription.user_id == user_id
        ).scalar()
        
        limit = self.get_user_subscription_limit(user)
        if current_count >= limit:
            raise SubscriptionLimitExceededError(limit)
        
        # 验证推送渠道配置
        for channel_config in subscription_data.channels:
            self.validate_channel_config(channel_config.channel, channel_config.config)
        
        # 创建订阅记录
        subscription = Subscription(
            user_id=user_id,
            name=subscription_data.name,
            keywords=json.dumps([kw.dict() for kw in subscription_data.keywords]) if subscription_data.keywords else None,
            companies=json.dumps([comp.dict() for comp in subscription_data.companies]) if subscription_data.companies else None,
            categories=json.dumps(subscription_data.categories) if subscription_data.categories else None,
            channels=json.dumps([ch.dict() for ch in subscription_data.channels]),
            schedule=json.dumps(subscription_data.schedule.dict()) if subscription_data.schedule else None,
            status=SubscriptionStatus.ACTIVE
        )
        
        self.db.add(subscription)
        self.db.commit()
        self.db.refresh(subscription)
        
        return self._to_response(subscription)
    
    def get_subscription(self, subscription_id: int, user_id: int) -> SubscriptionResponse:
        """获取单个订阅"""
        subscription = self.db.query(Subscription).filter(
            and_(Subscription.id == subscription_id, Subscription.user_id == user_id)
        ).first()
        
        if not subscription:
            raise SubscriptionNotFoundError(subscription_id)
        
        return self._to_response(subscription)
    
    def get_user_subscriptions(
        self, 
        user_id: int, 
        page: int = 1, 
        size: int = 10,
        status: Optional[SubscriptionStatus] = None
    ) -> SubscriptionListResponse:
        """获取用户订阅列表"""
        query = self.db.query(Subscription).filter(Subscription.user_id == user_id)
        
        if status:
            query = query.filter(Subscription.status == status)
        
        # 计算总数
        total = query.count()
        
        # 分页查询
        subscriptions = query.offset((page - 1) * size).limit(size).all()
        
        return SubscriptionListResponse(
            subscriptions=[self._to_response(sub) for sub in subscriptions],
            total=total,
            page=page,
            size=size
        )
    
    def update_subscription(
        self, 
        subscription_id: int, 
        user_id: int, 
        update_data: SubscriptionUpdate
    ) -> SubscriptionResponse:
        """更新订阅"""
        subscription = self.db.query(Subscription).filter(
            and_(Subscription.id == subscription_id, Subscription.user_id == user_id)
        ).first()
        
        if not subscription:
            raise SubscriptionNotFoundError(subscription_id)
        
        # 更新字段
        update_dict = update_data.dict(exclude_unset=True)
        
        for field, value in update_dict.items():
            if field == "keywords" and value is not None:
                setattr(subscription, field, json.dumps([kw.dict() for kw in value]))
            elif field == "companies" and value is not None:
                setattr(subscription, field, json.dumps([comp.dict() for comp in value]))
            elif field == "categories" and value is not None:
                setattr(subscription, field, json.dumps(value))
            elif field == "channels" and value is not None:
                # 验证推送渠道配置
                for channel_config in value:
                    self.validate_channel_config(channel_config.channel, channel_config.config)
                setattr(subscription, field, json.dumps([ch.dict() for ch in value]))
            elif field == "schedule" and value is not None:
                setattr(subscription, field, json.dumps(value.dict()))
            else:
                setattr(subscription, field, value)
        
        self.db.commit()
        self.db.refresh(subscription)
        
        return self._to_response(subscription)
    
    def delete_subscription(self, subscription_id: int, user_id: int) -> bool:
        """删除订阅"""
        subscription = self.db.query(Subscription).filter(
            and_(Subscription.id == subscription_id, Subscription.user_id == user_id)
        ).first()
        
        if not subscription:
            raise SubscriptionNotFoundError(subscription_id)
        
        self.db.delete(subscription)
        self.db.commit()
        
        return True
    
    def toggle_subscription_status(
        self, 
        subscription_id: int, 
        user_id: int
    ) -> SubscriptionResponse:
        """切换订阅状态（激活/暂停）"""
        subscription = self.db.query(Subscription).filter(
            and_(Subscription.id == subscription_id, Subscription.user_id == user_id)
        ).first()
        
        if not subscription:
            raise SubscriptionNotFoundError(subscription_id)
        
        # 切换状态
        new_status = SubscriptionStatus.PAUSED if subscription.status == SubscriptionStatus.ACTIVE else SubscriptionStatus.ACTIVE
        subscription.status = new_status
        
        self.db.commit()
        self.db.refresh(subscription)
        
        return self._to_response(subscription)
    
    def get_subscription_stats(self, subscription_id: int, user_id: int) -> SubscriptionStats:
        """获取订阅统计信息"""
        subscription = self.db.query(Subscription).filter(
            and_(Subscription.id == subscription_id, Subscription.user_id == user_id)
        ).first()
        
        if not subscription:
            raise SubscriptionNotFoundError(subscription_id)
        
        # 从推送日志表中获取真实统计数据
        try:
            from sqlalchemy import func
            from ..models.push_log import PushLog
            from datetime import datetime

            # 查询该订阅的推送统计
            push_stats = self.db.query(
                func.count(PushLog.id).label('total_pushes'),
                func.sum(func.case([(PushLog.status == 'success', 1)], else_=0)).label('successful_pushes'),
                func.sum(func.case([(PushLog.status == 'failed', 1)], else_=0)).label('failed_pushes'),
                func.max(PushLog.created_at).label('last_push_at')
            ).filter(
                PushLog.subscription_id == subscription_id
            ).first()

            # 查询匹配的新闻数量（基于订阅关键词）
            news_matched = 0
            if subscription.keywords:
                from ..models.news import News
                from sqlalchemy import or_

                # 构建关键词查询条件
                keyword_conditions = []
                for keyword_data in subscription.keywords:
                    keyword = keyword_data.get('keyword', '') if isinstance(keyword_data, dict) else str(keyword_data)
                    if keyword:
                        keyword_conditions.append(News.title.like(f"%{keyword}%"))
                        keyword_conditions.append(News.content.like(f"%{keyword}%"))

                if keyword_conditions:
                    news_matched = self.db.query(func.count(News.id)).filter(
                        or_(*keyword_conditions),
                        News.created_at >= subscription.created_at
                    ).scalar() or 0

            return SubscriptionStats(
                subscription_id=subscription_id,
                total_news_matched=news_matched,
                total_pushes_sent=push_stats.total_pushes or 0,
                successful_pushes=push_stats.successful_pushes or 0,
                failed_pushes=push_stats.failed_pushes or 0,
                last_push_at=push_stats.last_push_at
            )

        except Exception as e:
            logger.error(f"获取订阅统计失败: {e}")
            # 即使出错也不返回模拟数据，返回实际可查询的基础数据
            return SubscriptionStats(
                subscription_id=subscription_id,
                total_news_matched=0,
                total_pushes_sent=0,
                successful_pushes=0,
                failed_pushes=0,
                last_push_at=None
            )
    
    def _to_response(self, subscription: Subscription) -> SubscriptionResponse:
        """将数据库模型转换为响应模型"""
        from ..schemas.subscription import KeywordConfig, CompanyConfig, ChannelConfig, ScheduleConfig
        
        # 解析JSON字段
        keywords = []
        if subscription.keywords:
            keywords_data = json.loads(subscription.keywords)
            keywords = [KeywordConfig(**kw) for kw in keywords_data]
        
        companies = []
        if subscription.companies:
            companies_data = json.loads(subscription.companies)
            companies = [CompanyConfig(**comp) for comp in companies_data]
        
        categories = []
        if subscription.categories:
            categories = json.loads(subscription.categories)
        
        channels = []
        if subscription.channels:
            channels_data = json.loads(subscription.channels)
            channels = [ChannelConfig(**ch) for ch in channels_data]
        
        schedule = ScheduleConfig()
        if subscription.schedule:
            schedule_data = json.loads(subscription.schedule)
            schedule = ScheduleConfig(**schedule_data)
        
        return SubscriptionResponse(
            id=subscription.id,
            user_id=subscription.user_id,
            name=subscription.name,
            keywords=keywords,
            companies=companies,
            categories=categories,
            channels=channels,
            schedule=schedule,
            status=subscription.status,
            created_at=subscription.created_at,
            updated_at=subscription.updated_at
        )
