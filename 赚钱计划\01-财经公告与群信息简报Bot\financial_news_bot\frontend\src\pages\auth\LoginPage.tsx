import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import {
  Form,
  Input,
  Button,
  Checkbox,
  Typography,
  Alert,
  Space,
  Divider,
  message,
} from 'antd';
import {
  UserOutlined,
  LockOutlined,

  EyeInvisibleOutlined,
  EyeTwoTone,
} from '@ant-design/icons';

import { useAppDispatch, useAppSelector } from '@/store';
import { login, clearError, resetLoginAttempts } from '@/store/slices/authSlice';
import SocialLogin from '@/components/auth/SocialLogin';
import { LoginRequest } from '@/types';

const { Title, Text } = Typography;

const LoginPage: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();
  
  const { loading, error, loginAttempts, lastLoginAttempt } = useAppSelector(state => state.auth);
  
  const [isLocked, setIsLocked] = useState(false);
  const [lockTimeRemaining, setLockTimeRemaining] = useState(0);

  // 检查是否被锁定
  useEffect(() => {
    if (loginAttempts >= 5 && lastLoginAttempt) {
      const lockDuration = 15 * 60 * 1000; // 15分钟
      const timePassed = Date.now() - lastLoginAttempt;
      const timeRemaining = lockDuration - timePassed;

      if (timeRemaining > 0) {
        setIsLocked(true);
        setLockTimeRemaining(Math.ceil(timeRemaining / 1000));

        const timer = setInterval(() => {
          setLockTimeRemaining(prev => {
            if (prev <= 1) {
              setIsLocked(false);
              dispatch(resetLoginAttempts());
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);

        return () => clearInterval(timer);
      } else {
        dispatch(resetLoginAttempts());
      }
    }
  }, [loginAttempts, lastLoginAttempt, dispatch]);

  // 清除错误信息
  useEffect(() => {
    return () => {
      dispatch(clearError());
    };
  }, [dispatch]);

  // 处理表单提交
  const handleSubmit = async (values: LoginRequest) => {
    if (isLocked) {
      message.error('登录已被锁定，请稍后再试');
      return;
    }

    try {
      await dispatch(login(values)).unwrap();
      message.success('登录成功！');
      
      // 重定向到目标页面或仪表板
      const from = (location.state as any)?.from?.pathname || '/dashboard';
      navigate(from, { replace: true });
    } catch (error) {
      // 错误已经在Redux中处理
    }
  };



  // 格式化锁定时间
  const formatLockTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  return (
    <div style={{ width: '100%' }}>
      <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
        <Title level={2} style={{ color: '#1890ff', marginBottom: '0.5rem' }}>
          欢迎回来
        </Title>
        <Text type="secondary">
          登录您的财经新闻Bot账户
        </Text>
      </div>

      {/* 错误提示 */}
      {error && (
        <Alert
          message={error}
          type="error"
          showIcon
          closable
          onClose={() => dispatch(clearError())}
          style={{ marginBottom: '1rem' }}
        />
      )}

      {/* 锁定提示 */}
      {isLocked && (
        <Alert
          message={`登录已被锁定 ${formatLockTime(lockTimeRemaining)}`}
          description="由于多次登录失败，您的账户已被临时锁定。请稍后再试。"
          type="warning"
          showIcon
          style={{ marginBottom: '1rem' }}
        />
      )}

      {/* 登录表单 */}
      <Form
        form={form}
        name="login"
        onFinish={handleSubmit}
        autoComplete="off"
        size="large"
        disabled={isLocked}
      >
        <Form.Item
          name="username"
          rules={[
            { required: true, message: '请输入用户名或邮箱' },
            { min: 3, message: '用户名至少3个字符' },
          ]}
        >
          <Input
            prefix={<UserOutlined />}
            placeholder="用户名或邮箱"
            autoComplete="username"
          />
        </Form.Item>

        <Form.Item
          name="password"
          rules={[
            { required: true, message: '请输入密码' },
            { min: 6, message: '密码至少6个字符' },
          ]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="密码"
            autoComplete="current-password"
            iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
          />
        </Form.Item>

        <Form.Item>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Form.Item name="remember_me" valuePropName="checked" noStyle>
              <Checkbox>记住我</Checkbox>
            </Form.Item>
            <Link to="/auth/forgot-password">
              <Text type="secondary">忘记密码？</Text>
            </Link>
          </div>
        </Form.Item>

        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            disabled={isLocked}
            block
            style={{ height: '48px', fontSize: '16px' }}
          >
            {loading ? '登录中...' : '登录'}
          </Button>
        </Form.Item>
      </Form>

      {/* 第三方登录 */}
      <Divider>
        <Text type="secondary">或使用以下方式登录</Text>
      </Divider>

      <SocialLogin
        onSuccess={() => navigate('/dashboard')}
        onError={(error) => message.error(error)}
      />

      {/* 注册链接 */}
      <div style={{ textAlign: 'center', marginTop: '2rem' }}>
        <Text type="secondary">
          还没有账户？{' '}
          <Link to="/auth/register">
            <Text type="primary">立即注册</Text>
          </Link>
        </Text>
      </div>

      {/* 登录尝试提示 */}
      {loginAttempts > 0 && loginAttempts < 5 && (
        <div style={{ textAlign: 'center', marginTop: '1rem' }}>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            登录失败 {loginAttempts} 次，{5 - loginAttempts} 次后将被锁定
          </Text>
        </div>
      )}
    </div>
  );
};

export default LoginPage;
