import React, { useState, useEffect } from 'react';
import { Card, Button, Typography, Space, Tooltip, Popover, Badge } from 'antd';
import {
  BulbOutlined,
  CloseOutlined,
  QuestionCircleOutlined,
  LightbulbOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
} from '@ant-design/icons';
import { useResponsive } from '@/hooks/useResponsive';

const { Text, Title } = Typography;

interface OperationTip {
  id: string;
  title: string;
  content: string;
  type: 'info' | 'tip' | 'warning' | 'success';
  target?: string; // 目标元素选择器
  trigger?: 'hover' | 'click' | 'focus' | 'auto';
  position?: 'top' | 'bottom' | 'left' | 'right';
  showOnce?: boolean; // 是否只显示一次
  delay?: number; // 延迟显示时间
  persistent?: boolean; // 是否持久显示
}

interface OperationTipsProps {
  tips: OperationTip[];
  enabled?: boolean;
  onTipDismiss?: (tipId: string) => void;
  onAllTipsDismiss?: () => void;
}

const OperationTips: React.FC<OperationTipsProps> = ({
  tips,
  enabled = true,
  onTipDismiss,
  onAllTipsDismiss,
}) => {
  const [dismissedTips, setDismissedTips] = useState<Set<string>>(new Set());
  const [activeTips, setActiveTips] = useState<Set<string>>(new Set());
  const { isMobile } = useResponsive();

  // 从localStorage加载已忽略的提示
  useEffect(() => {
    const dismissed = localStorage.getItem('dismissedTips');
    if (dismissed) {
      try {
        setDismissedTips(new Set(JSON.parse(dismissed)));
      } catch (error) {
        console.warn('Failed to parse dismissed tips:', error);
      }
    }
  }, []);

  // 保存已忽略的提示到localStorage
  useEffect(() => {
    localStorage.setItem('dismissedTips', JSON.stringify(Array.from(dismissedTips)));
  }, [dismissedTips]);

  // 处理提示显示逻辑
  useEffect(() => {
    if (!enabled) return;

    tips.forEach(tip => {
      if (dismissedTips.has(tip.id)) return;
      if (tip.showOnce && activeTips.has(tip.id)) return;

      const showTip = () => {
        if (tip.target) {
          const element = document.querySelector(tip.target);
          if (element) {
            attachTipToElement(element as HTMLElement, tip);
          }
        } else if (tip.trigger === 'auto') {
          setActiveTips(prev => new Set([...prev, tip.id]));
        }
      };

      if (tip.delay) {
        setTimeout(showTip, tip.delay);
      } else {
        showTip();
      }
    });
  }, [tips, enabled, dismissedTips, activeTips]);

  const attachTipToElement = (element: HTMLElement, tip: OperationTip) => {
    const handleTrigger = () => {
      setActiveTips(prev => new Set([...prev, tip.id]));
    };

    switch (tip.trigger) {
      case 'hover':
        element.addEventListener('mouseenter', handleTrigger);
        element.addEventListener('mouseleave', () => {
          if (!tip.persistent) {
            setActiveTips(prev => {
              const newSet = new Set(prev);
              newSet.delete(tip.id);
              return newSet;
            });
          }
        });
        break;
      case 'click':
        element.addEventListener('click', handleTrigger);
        break;
      case 'focus':
        element.addEventListener('focus', handleTrigger);
        element.addEventListener('blur', () => {
          if (!tip.persistent) {
            setActiveTips(prev => {
              const newSet = new Set(prev);
              newSet.delete(tip.id);
              return newSet;
            });
          }
        });
        break;
      default:
        handleTrigger();
    }
  };

  const handleDismissTip = (tipId: string) => {
    setDismissedTips(prev => new Set([...prev, tipId]));
    setActiveTips(prev => {
      const newSet = new Set(prev);
      newSet.delete(tipId);
      return newSet;
    });
    onTipDismiss?.(tipId);
  };

  const handleDismissAll = () => {
    const allTipIds = tips.map(tip => tip.id);
    setDismissedTips(new Set(allTipIds));
    setActiveTips(new Set());
    onAllTipsDismiss?.();
  };

  const getIcon = (type: OperationTip['type']) => {
    switch (type) {
      case 'info':
        return <InfoCircleOutlined style={{ color: '#1890ff' }} />;
      case 'tip':
        return <LightbulbOutlined style={{ color: '#faad14' }} />;
      case 'warning':
        return <QuestionCircleOutlined style={{ color: '#fa8c16' }} />;
      case 'success':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      default:
        return <BulbOutlined style={{ color: '#1890ff' }} />;
    }
  };

  const getCardStyle = (type: OperationTip['type']) => {
    const baseStyle = {
      borderRadius: 8,
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
      border: '1px solid',
    };

    switch (type) {
      case 'info':
        return { ...baseStyle, borderColor: '#1890ff', backgroundColor: '#f6ffed' };
      case 'tip':
        return { ...baseStyle, borderColor: '#faad14', backgroundColor: '#fffbe6' };
      case 'warning':
        return { ...baseStyle, borderColor: '#fa8c16', backgroundColor: '#fff7e6' };
      case 'success':
        return { ...baseStyle, borderColor: '#52c41a', backgroundColor: '#f6ffed' };
      default:
        return baseStyle;
    }
  };

  const visibleTips = tips.filter(tip => 
    activeTips.has(tip.id) && !dismissedTips.has(tip.id)
  );

  if (!enabled || visibleTips.length === 0) {
    return null;
  }

  return (
    <>
      {/* 浮动提示卡片 */}
      {visibleTips.map(tip => (
        <Card
          key={tip.id}
          size="small"
          style={{
            position: 'fixed',
            top: isMobile ? 80 : 100,
            right: isMobile ? 16 : 24,
            width: isMobile ? 280 : 320,
            zIndex: 1000,
            ...getCardStyle(tip.type),
          }}
          bodyStyle={{ padding: 12 }}
        >
          <div style={{ display: 'flex', alignItems: 'flex-start' }}>
            <div style={{ marginRight: 8, fontSize: 16 }}>
              {getIcon(tip.type)}
            </div>
            <div style={{ flex: 1 }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 4 }}>
                <Title level={5} style={{ margin: 0, fontSize: 14 }}>
                  {tip.title}
                </Title>
                <Button
                  type="text"
                  size="small"
                  icon={<CloseOutlined />}
                  onClick={() => handleDismissTip(tip.id)}
                  style={{ padding: 0, minWidth: 'auto' }}
                />
              </div>
              <Text style={{ fontSize: 12, lineHeight: 1.4 }}>
                {tip.content}
              </Text>
            </div>
          </div>
        </Card>
      ))}

      {/* 批量操作按钮 */}
      {visibleTips.length > 1 && (
        <div
          style={{
            position: 'fixed',
            bottom: isMobile ? 80 : 24,
            right: isMobile ? 16 : 24,
            zIndex: 1000,
          }}
        >
          <Button
            size="small"
            onClick={handleDismissAll}
            style={{
              borderRadius: 16,
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
            }}
          >
            关闭所有提示 ({visibleTips.length})
          </Button>
        </div>
      )}
    </>
  );
};

// 预定义的操作提示
export const commonTips: OperationTip[] = [
  {
    id: 'welcome',
    title: '欢迎使用',
    content: '点击右上角的用户头像可以访问个人设置和帮助文档',
    type: 'info',
    trigger: 'auto',
    delay: 2000,
    showOnce: true,
  },
  {
    id: 'subscription_tip',
    title: '创建订阅',
    content: '点击"+"按钮可以创建新的新闻订阅，获取个性化推送',
    type: 'tip',
    target: '[data-tip="create-subscription"]',
    trigger: 'hover',
    position: 'bottom',
  },
  {
    id: 'search_tip',
    title: '快速搜索',
    content: '使用 Ctrl+K 快捷键可以快速打开全局搜索',
    type: 'tip',
    target: '[data-tip="search"]',
    trigger: 'hover',
    position: 'bottom',
  },
  {
    id: 'bookmark_tip',
    title: '收藏新闻',
    content: '点击心形图标可以收藏感兴趣的新闻，方便后续查看',
    type: 'info',
    target: '[data-tip="bookmark"]',
    trigger: 'hover',
    position: 'top',
  },
  {
    id: 'notification_tip',
    title: '通知设置',
    content: '建议开启浏览器通知，以便及时接收重要新闻推送',
    type: 'warning',
    trigger: 'auto',
    delay: 5000,
    persistent: true,
  },
];

// Hook：管理操作提示
export const useOperationTips = (initialTips: OperationTip[] = commonTips) => {
  const [tips, setTips] = useState<OperationTip[]>(initialTips);
  const [enabled, setEnabled] = useState(true);

  const addTip = (tip: OperationTip) => {
    setTips(prev => [...prev, tip]);
  };

  const removeTip = (tipId: string) => {
    setTips(prev => prev.filter(tip => tip.id !== tipId));
  };

  const enableTips = () => setEnabled(true);
  const disableTips = () => setEnabled(false);

  const showTip = (tipId: string) => {
    const tip = tips.find(t => t.id === tipId);
    if (tip) {
      // 触发显示逻辑
      const event = new CustomEvent('showTip', { detail: tip });
      window.dispatchEvent(event);
    }
  };

  return {
    tips,
    enabled,
    addTip,
    removeTip,
    enableTips,
    disableTips,
    showTip,
  };
};

export default OperationTips;
