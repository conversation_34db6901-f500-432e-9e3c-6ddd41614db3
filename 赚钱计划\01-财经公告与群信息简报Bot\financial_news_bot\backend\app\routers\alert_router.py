"""
告警管理路由
提供告警系统的API接口
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime

from app.utils.alert_system import (
    alert_manager, 
    trigger_alert,
    AlertLevel, 
    AlertCategory,
    Alert
)
from app.dependencies.auth import get_current_user
from app.models.user import User

router = APIRouter(prefix="/api/v1/alerts", tags=["告警管理"])

class AlertRequest(BaseModel):
    """告警请求模型"""
    title: str = Field(..., description="告警标题", max_length=200)
    message: str = Field(..., description="告警消息", max_length=1000)
    level: str = Field(..., description="告警级别", pattern="^(info|warning|error|critical)$")
    category: str = Field(..., description="告警分类", pattern="^(system|security|performance|business|compliance)$")
    source: str = Field(default="manual", description="告警来源", max_length=100)
    metadata: Dict[str, Any] = Field(default={}, description="附加元数据")

class AlertResponse(BaseModel):
    """告警响应模型"""
    id: str
    title: str
    message: str
    level: str
    category: str
    source: str
    timestamp: datetime
    resolved: bool
    resolved_at: Optional[datetime]
    resolved_by: Optional[str]
    metadata: Dict[str, Any]

class AlertStatistics(BaseModel):
    """告警统计模型"""
    total_alerts: int
    active_alerts: int
    resolved_alerts: int
    by_level: Dict[str, int]
    by_category: Dict[str, int]
    by_source: Dict[str, int]

@router.post("/trigger", response_model=AlertResponse)
async def create_alert(
    alert_request: AlertRequest,
    current_user: User = Depends(get_current_user)
):
    """
    手动触发告警
    
    创建一个新的告警并发送通知
    """
    try:
        # 转换枚举类型
        level = AlertLevel(alert_request.level)
        category = AlertCategory(alert_request.category)
        
        # 触发告警
        alert = await trigger_alert(
            title=alert_request.title,
            message=alert_request.message,
            level=level,
            category=category,
            source=alert_request.source,
            metadata=alert_request.metadata
        )
        
        return AlertResponse(**alert.to_dict())
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"无效的告警参数: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建告警失败: {str(e)}")

@router.get("/active", response_model=List[AlertResponse])
async def get_active_alerts(
    level: Optional[str] = Query(None, description="按级别过滤"),
    category: Optional[str] = Query(None, description="按分类过滤"),
    current_user: User = Depends(get_current_user)
):
    """
    获取活跃告警
    
    返回当前未解决的告警列表
    """
    try:
        # 转换过滤参数
        level_filter = AlertLevel(level) if level else None
        category_filter = AlertCategory(category) if category else None
        
        # 获取活跃告警
        alerts = alert_manager.get_active_alerts(level_filter, category_filter)
        
        return [AlertResponse(**alert.to_dict()) for alert in alerts]
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"无效的过滤参数: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取活跃告警失败: {str(e)}")

@router.get("/history", response_model=List[AlertResponse])
async def get_alert_history(
    hours: int = Query(24, description="历史时间范围（小时）", ge=1, le=168),
    level: Optional[str] = Query(None, description="按级别过滤"),
    current_user: User = Depends(get_current_user)
):
    """
    获取告警历史
    
    返回指定时间范围内的告警历史记录
    """
    try:
        # 转换过滤参数
        level_filter = AlertLevel(level) if level else None
        
        # 获取告警历史
        alerts = alert_manager.get_alert_history(hours, level_filter)
        
        return [AlertResponse(**alert.to_dict()) for alert in alerts]
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"无效的过滤参数: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取告警历史失败: {str(e)}")

@router.post("/resolve/{alert_id}")
async def resolve_alert(
    alert_id: str,
    current_user: User = Depends(get_current_user)
):
    """
    解决告警
    
    将指定的告警标记为已解决
    """
    try:
        success = await alert_manager.resolve_alert(alert_id, current_user.username)
        
        if success:
            return {"success": True, "message": f"告警 {alert_id} 已解决"}
        else:
            raise HTTPException(status_code=404, detail=f"告警 {alert_id} 不存在或已解决")
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"解决告警失败: {str(e)}")

@router.get("/statistics", response_model=AlertStatistics)
async def get_alert_statistics(
    hours: int = Query(24, description="统计时间范围（小时）", ge=1, le=168),
    current_user: User = Depends(get_current_user)
):
    """
    获取告警统计
    
    返回指定时间范围内的告警统计信息
    """
    try:
        stats = alert_manager.get_alert_statistics(hours)
        return AlertStatistics(**stats)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取告警统计失败: {str(e)}")

@router.get("/levels")
async def get_alert_levels(current_user: User = Depends(get_current_user)):
    """
    获取告警级别列表
    
    返回所有可用的告警级别
    """
    return {
        "levels": [
            {"value": level.value, "name": level.value.upper()} 
            for level in AlertLevel
        ]
    }

@router.get("/categories")
async def get_alert_categories(current_user: User = Depends(get_current_user)):
    """
    获取告警分类列表
    
    返回所有可用的告警分类
    """
    return {
        "categories": [
            {"value": category.value, "name": category.value.upper()} 
            for category in AlertCategory
        ]
    }

@router.get("/rules")
async def get_alert_rules(current_user: User = Depends(get_current_user)):
    """
    获取告警规则列表
    
    返回当前配置的所有告警规则
    """
    try:
        rules_info = []
        
        for rule_name, rule in alert_manager.rules.items():
            rules_info.append({
                "name": rule_name,
                "level": rule.level.value,
                "category": rule.category.value,
                "title_template": rule.title_template,
                "message_template": rule.message_template,
                "cooldown_minutes": rule.cooldown_minutes,
                "last_triggered": rule.last_triggered.isoformat() if rule.last_triggered else None
            })
        
        return {
            "rules": rules_info,
            "total_count": len(rules_info)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取告警规则失败: {str(e)}")

@router.post("/test")
async def test_alert_system(current_user: User = Depends(get_current_user)):
    """
    测试告警系统
    
    发送一个测试告警来验证系统功能
    """
    try:
        # 创建测试告警
        alert = await trigger_alert(
            title="告警系统测试",
            message=f"这是一个测试告警，由用户 {current_user.username} 在 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} 触发",
            level=AlertLevel.INFO,
            category=AlertCategory.SYSTEM,
            source="test",
            metadata={
                "test": True,
                "user": current_user.username,
                "timestamp": datetime.now().isoformat()
            }
        )
        
        return {
            "success": True,
            "message": "测试告警已发送",
            "alert": AlertResponse(**alert.to_dict())
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"测试告警失败: {str(e)}")

@router.get("/health")
async def alert_system_health():
    """
    告警系统健康检查
    
    检查告警系统是否正常运行
    """
    try:
        # 检查基本功能
        active_count = len(alert_manager.active_alerts)
        rules_count = len(alert_manager.rules)
        history_count = len(alert_manager.alert_history)
        
        return {
            "status": "healthy",
            "active_alerts": active_count,
            "configured_rules": rules_count,
            "history_records": history_count,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

@router.post("/check")
async def manual_alert_check(
    data: Dict[str, Any],
    source: str = "manual",
    current_user: User = Depends(get_current_user)
):
    """
    手动触发告警检查
    
    使用提供的数据检查是否应该触发告警
    """
    try:
        triggered_alerts = await alert_manager.check_alerts(data, source)
        
        return {
            "success": True,
            "triggered_count": len(triggered_alerts),
            "alerts": [AlertResponse(**alert.to_dict()) for alert in triggered_alerts]
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"告警检查失败: {str(e)}")
