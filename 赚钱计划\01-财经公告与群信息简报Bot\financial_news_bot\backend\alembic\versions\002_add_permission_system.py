"""Add permission system enhancements

Revision ID: 002_add_permission_system
Revises: 001_initial_schema
Create Date: 2025-08-18 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '002_add_permission_system'
down_revision = '001_initial_schema'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """升级数据库结构以支持权限系统"""
    
    # 1. 确保用户表包含所有必要的角色
    # 检查并更新用户角色枚举（如果需要）
    op.execute("ALTER TABLE users MODIFY COLUMN role ENUM('FREE', 'PRO', 'ENTERPRISE', 'ADMIN') DEFAULT 'FREE'")
    
    # 2. 添加用户权限相关字段（如果将来需要）
    # 目前基于角色的权限矩阵已足够，暂不需要额外字段
    
    # 3. 创建权限审计日志表
    op.create_table('permission_audit_logs',
        sa.Column('id', sa.Integer(), nullable=False, autoincrement=True),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('action', sa.String(length=100), nullable=False),
        sa.Column('resource', sa.String(length=100), nullable=True),
        sa.Column('permission_required', sa.String(length=100), nullable=True),
        sa.Column('permission_granted', sa.Boolean(), nullable=False),
        sa.Column('ip_address', sa.String(length=45), nullable=True),
        sa.Column('user_agent', sa.String(length=500), nullable=True),
        sa.Column('details', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.TIMESTAMP(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.Index('idx_permission_audit_user_id', 'user_id'),
        sa.Index('idx_permission_audit_created_at', 'created_at'),
        sa.Index('idx_permission_audit_action', 'action')
    )
    
    # 4. 创建用户会话表（用于权限缓存和会话管理）
    op.create_table('user_sessions',
        sa.Column('id', sa.String(length=128), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('role_at_login', sa.Enum('FREE', 'PRO', 'ENTERPRISE', 'ADMIN', name='session_role'), nullable=False),
        sa.Column('permissions_cache', sa.JSON(), nullable=True),
        sa.Column('ip_address', sa.String(length=45), nullable=True),
        sa.Column('user_agent', sa.String(length=500), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),
        sa.Column('last_activity', sa.TIMESTAMP(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('created_at', sa.TIMESTAMP(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('expires_at', sa.TIMESTAMP(), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.Index('idx_user_sessions_user_id', 'user_id'),
        sa.Index('idx_user_sessions_expires_at', 'expires_at'),
        sa.Index('idx_user_sessions_last_activity', 'last_activity')
    )
    
    # 5. 创建角色变更历史表
    op.create_table('user_role_history',
        sa.Column('id', sa.Integer(), nullable=False, autoincrement=True),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('old_role', sa.Enum('FREE', 'PRO', 'ENTERPRISE', 'ADMIN', name='old_role'), nullable=True),
        sa.Column('new_role', sa.Enum('FREE', 'PRO', 'ENTERPRISE', 'ADMIN', name='new_role'), nullable=False),
        sa.Column('changed_by_user_id', sa.Integer(), nullable=True),
        sa.Column('reason', sa.String(length=255), nullable=True),
        sa.Column('created_at', sa.TIMESTAMP(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['changed_by_user_id'], ['users.id'], ondelete='SET NULL'),
        sa.Index('idx_user_role_history_user_id', 'user_id'),
        sa.Index('idx_user_role_history_created_at', 'created_at')
    )
    
    # 6. 插入默认管理员用户（如果不存在）
    # 注意：这里只是创建表结构，实际的管理员用户创建应该通过单独的脚本完成
    
    # 7. 创建权限系统配置表
    op.create_table('permission_config',
        sa.Column('id', sa.Integer(), nullable=False, autoincrement=True),
        sa.Column('config_key', sa.String(length=100), nullable=False, unique=True),
        sa.Column('config_value', sa.JSON(), nullable=True),
        sa.Column('description', sa.String(length=255), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),
        sa.Column('created_at', sa.TIMESTAMP(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('updated_at', sa.TIMESTAMP(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')),
        sa.PrimaryKeyConstraint('id'),
        sa.Index('idx_permission_config_key', 'config_key')
    )
    
    # 8. 插入默认权限配置
    op.execute("""
        INSERT INTO permission_config (config_key, config_value, description) VALUES
        ('role_permissions', '{}', '角色权限矩阵配置'),
        ('permission_cache_ttl', '3600', '权限缓存过期时间（秒）'),
        ('max_failed_attempts', '5', '最大失败尝试次数'),
        ('session_timeout', '86400', '会话超时时间（秒）')
    """)


def downgrade() -> None:
    """降级数据库结构"""
    
    # 删除权限系统相关表
    op.drop_table('permission_config')
    op.drop_table('user_role_history')
    op.drop_table('user_sessions')
    op.drop_table('permission_audit_logs')
    
    # 恢复用户角色枚举（如果需要）
    op.execute("ALTER TABLE users MODIFY COLUMN role ENUM('FREE', 'PRO', 'ENTERPRISE') DEFAULT 'FREE'")
