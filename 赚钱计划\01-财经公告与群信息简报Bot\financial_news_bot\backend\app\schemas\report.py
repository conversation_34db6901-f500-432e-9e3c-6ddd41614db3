"""
简报相关数据模式
定义简报API的请求和响应数据结构
"""
from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional
from datetime import datetime
from enum import Enum

from ..models.report import ReportType, ReportStatus


class ReportGenerateRequest(BaseModel):
    """生成简报请求模式"""
    type: ReportType = Field(..., description="简报类型")
    title: Optional[str] = Field(None, max_length=200, description="自定义标题")
    date_range: Optional[Dict[str, Any]] = Field(None, description="时间范围")
    filters: Optional[Dict[str, Any]] = Field(None, description="内容过滤条件")
    template_id: Optional[int] = Field(None, description="使用的模板ID")
    custom_sections: Optional[List[Dict[str, Any]]] = Field(None, description="自定义章节")
    priority_keywords: Optional[List[str]] = Field(None, description="优先关键词")
    exclude_keywords: Optional[List[str]] = Field(None, description="排除关键词")
    max_items_per_section: Optional[int] = Field(10, ge=1, le=100, description="每章节最大条目数")
    include_charts: bool = Field(True, description="是否包含图表")
    include_summary: bool = Field(True, description="是否包含摘要")


class ReportSubscriptionCreate(BaseModel):
    """创建简报订阅请求模式"""
    report_type: ReportType = Field(..., description="简报类型")
    schedule: str = Field(..., max_length=100, description="调度表达式(cron格式)")
    is_active: bool = Field(True, description="是否启用")
    delivery_channels: List[str] = Field(..., description="推送渠道列表")
    template_id: Optional[int] = Field(None, description="使用的模板ID")
    filters: Optional[Dict[str, Any]] = Field(None, description="内容过滤条件")
    recipients: List[str] = Field(..., description="接收者列表")
    custom_settings: Optional[Dict[str, Any]] = Field(None, description="自定义设置")


class ReportSubscriptionResponse(BaseModel):
    """简报订阅响应模式"""
    id: int = Field(..., description="订阅ID")
    report_type: ReportType = Field(..., description="简报类型")
    schedule: str = Field(..., description="调度表达式")
    is_active: bool = Field(..., description="是否启用")
    delivery_channels: List[str] = Field(..., description="推送渠道列表")
    template_id: Optional[int] = Field(None, description="模板ID")
    filters: Optional[Dict[str, Any]] = Field(None, description="过滤条件")
    recipients: List[str] = Field(..., description="接收者列表")
    custom_settings: Optional[Dict[str, Any]] = Field(None, description="自定义设置")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    last_run_at: Optional[datetime] = Field(None, description="最后运行时间")
    next_run_at: Optional[datetime] = Field(None, description="下次运行时间")
    run_count: int = Field(0, description="运行次数")

    class Config:
        from_attributes = True


class ReportFeedbackCreate(BaseModel):
    """创建简报反馈请求模式"""
    report_id: int = Field(..., description="简报ID")
    rating: int = Field(..., ge=1, le=5, description="评分(1-5)")
    feedback_type: str = Field(..., max_length=50, description="反馈类型")
    content: Optional[str] = Field(None, max_length=1000, description="反馈内容")
    suggestions: Optional[str] = Field(None, max_length=1000, description="改进建议")
    is_anonymous: bool = Field(False, description="是否匿名反馈")
    tags: Optional[List[str]] = Field(None, description="反馈标签")


class ReportStatistics(BaseModel):
    """简报统计信息模式"""
    total_reports: int = Field(..., description="总简报数")
    reports_today: int = Field(..., description="今日简报数")
    reports_this_week: int = Field(..., description="本周简报数")
    reports_this_month: int = Field(..., description="本月简报数")
    avg_generation_time: float = Field(..., description="平均生成时间(秒)")
    most_popular_type: str = Field(..., description="最受欢迎的简报类型")
    total_views: int = Field(..., description="总浏览量")
    total_downloads: int = Field(..., description="总下载量")
    user_feedback_avg: float = Field(..., description="用户反馈平均分")
    active_subscriptions: int = Field(..., description="活跃订阅数")

    class Config:
        from_attributes = True


class ReportTemplateCreate(BaseModel):
    """创建简报模板请求模式"""
    name: str = Field(..., min_length=1, max_length=100, description="模板名称")
    type: ReportType = Field(..., description="简报类型")
    description: Optional[str] = Field(None, max_length=500, description="模板描述")
    template_config: Dict[str, Any] = Field(..., description="模板配置")
    sections: List[Dict[str, Any]] = Field(..., description="模板章节配置")
    style_config: Optional[Dict[str, Any]] = Field(None, description="样式配置")
    generation_rules: Optional[Dict[str, Any]] = Field(None, description="生成规则")
    content_filters: Optional[Dict[str, Any]] = Field(None, description="内容过滤规则")
    priority_rules: Optional[Dict[str, Any]] = Field(None, description="优先级规则")
    is_default: bool = Field(False, description="是否为默认模板")

    @validator('sections')
    def validate_sections(cls, v):
        if not v or len(v) == 0:
            raise ValueError('模板必须包含至少一个章节')
        
        required_fields = ['name', 'title', 'content_type']
        for i, section in enumerate(v):
            for field in required_fields:
                if field not in section:
                    raise ValueError(f'章节{i+1}缺少必需字段: {field}')
        
        return v

    class Config:
        json_schema_extra = {
            "example": {
                "name": "标准晨报模板",
                "type": "morning",
                "description": "包含市场要闻、公司公告等标准章节的晨报模板",
                "template_config": {
                    "max_news_per_section": 5,
                    "total_word_limit": 2000,
                    "include_images": True
                },
                "sections": [
                    {
                        "name": "market_news",
                        "title": "市场要闻",
                        "content_type": "market_news",
                        "max_items": 5,
                        "priority": "high"
                    },
                    {
                        "name": "company_news",
                        "title": "公司公告",
                        "content_type": "company_announcements",
                        "max_items": 3,
                        "priority": "medium"
                    }
                ],
                "generation_rules": {
                    "time_range_hours": 16,
                    "min_importance_score": 60,
                    "exclude_duplicates": True
                },
                "is_default": False
            }
        }


class ReportTemplateUpdate(BaseModel):
    """更新简报模板请求模式"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="模板名称")
    description: Optional[str] = Field(None, max_length=500, description="模板描述")
    template_config: Optional[Dict[str, Any]] = Field(None, description="模板配置")
    sections: Optional[List[Dict[str, Any]]] = Field(None, description="模板章节配置")
    style_config: Optional[Dict[str, Any]] = Field(None, description="样式配置")
    generation_rules: Optional[Dict[str, Any]] = Field(None, description="生成规则")
    content_filters: Optional[Dict[str, Any]] = Field(None, description="内容过滤规则")
    priority_rules: Optional[Dict[str, Any]] = Field(None, description="优先级规则")
    is_active: Optional[bool] = Field(None, description="是否启用")
    is_default: Optional[bool] = Field(None, description="是否为默认模板")


class ReportTemplateResponse(BaseModel):
    """简报模板响应模式"""
    id: int = Field(..., description="模板ID")
    name: str = Field(..., description="模板名称")
    type: ReportType = Field(..., description="简报类型")
    description: Optional[str] = Field(None, description="模板描述")
    template_config: Dict[str, Any] = Field(..., description="模板配置")
    sections: List[Dict[str, Any]] = Field(..., description="模板章节配置")
    style_config: Optional[Dict[str, Any]] = Field(None, description="样式配置")
    generation_rules: Optional[Dict[str, Any]] = Field(None, description="生成规则")
    is_active: bool = Field(..., description="是否启用")
    is_default: bool = Field(..., description="是否为默认模板")
    version: str = Field(..., description="模板版本")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class ReportCreate(BaseModel):
    """创建简报请求模式"""
    title: Optional[str] = Field(None, max_length=200, description="简报标题")
    type: ReportType = Field(..., description="简报类型")
    template_id: Optional[int] = Field(None, description="使用的模板ID")
    generation_config: Optional[Dict[str, Any]] = Field(None, description="生成配置")
    report_date: Optional[datetime] = Field(None, description="简报日期")
    start_time: Optional[datetime] = Field(None, description="内容起始时间")
    end_time: Optional[datetime] = Field(None, description="内容结束时间")
    scheduled_at: Optional[datetime] = Field(None, description="计划发布时间")

    @validator('end_time')
    def validate_time_range(cls, v, values):
        if v and values.get('start_time') and v <= values['start_time']:
            raise ValueError('结束时间必须晚于开始时间')
        return v

    class Config:
        json_schema_extra = {
            "example": {
                "type": "morning",
                "template_id": 1,
                "generation_config": {
                    "personalized": True,
                    "include_charts": False,
                    "max_sections": 6
                },
                "report_date": "2025-08-18T07:30:00",
                "start_time": "2025-08-17T18:00:00",
                "end_time": "2025-08-18T07:30:00"
            }
        }


class ReportSectionResponse(BaseModel):
    """简报章节响应模式"""
    id: int = Field(..., description="章节ID")
    section_name: str = Field(..., description="章节名称")
    section_title: Optional[str] = Field(None, description="章节标题")
    section_order: int = Field(..., description="章节顺序")
    content: Optional[str] = Field(None, description="章节内容")
    content_type: Optional[str] = Field(None, description="内容类型")
    news_count: int = Field(..., description="新闻数量")
    word_count: int = Field(..., description="字数")
    importance_score: int = Field(..., description="重要性评分")

    class Config:
        from_attributes = True


class ReportResponse(BaseModel):
    """简报响应模式"""
    id: int = Field(..., description="简报ID")
    title: str = Field(..., description="简报标题")
    type: ReportType = Field(..., description="简报类型")
    status: ReportStatus = Field(..., description="简报状态")
    content: Optional[str] = Field(None, description="简报内容")
    summary: Optional[str] = Field(None, description="简报摘要")
    template_id: Optional[int] = Field(None, description="模板ID")
    news_count: int = Field(..., description="包含新闻数量")
    word_count: int = Field(..., description="字数统计")
    read_time: int = Field(..., description="预估阅读时间")
    quality_score: int = Field(..., description="内容质量评分")
    report_date: datetime = Field(..., description="简报日期")
    published_at: Optional[datetime] = Field(None, description="发布时间")
    created_at: datetime = Field(..., description="创建时间")
    sections: Optional[List[ReportSectionResponse]] = Field(None, description="简报章节")

    class Config:
        from_attributes = True


class ReportListResponse(BaseModel):
    """简报列表响应模式"""
    reports: List[ReportResponse] = Field(..., description="简报列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页数量")
    pages: int = Field(..., description="总页数")


class ReportGenerationRequest(BaseModel):
    """简报生成请求模式"""
    type: ReportType = Field(..., description="简报类型")
    template_id: Optional[int] = Field(None, description="模板ID")
    force_regenerate: bool = Field(False, description="是否强制重新生成")
    custom_config: Optional[Dict[str, Any]] = Field(None, description="自定义配置")
    target_date: Optional[datetime] = Field(None, description="目标日期")

    class Config:
        json_schema_extra = {
            "example": {
                "type": "morning",
                "template_id": 1,
                "force_regenerate": False,
                "custom_config": {
                    "include_weekend_news": True,
                    "max_word_count": 1500
                },
                "target_date": "2025-08-18T07:30:00"
            }
        }


class ReportGenerationResponse(BaseModel):
    """简报生成响应模式"""
    task_id: str = Field(..., description="生成任务ID")
    report_id: Optional[int] = Field(None, description="简报ID")
    status: str = Field(..., description="生成状态")
    estimated_time: int = Field(..., description="预估完成时间（秒）")
    message: str = Field(..., description="状态消息")

    class Config:
        json_schema_extra = {
            "example": {
                "task_id": "report_gen_20250818_073000",
                "report_id": 123,
                "status": "started",
                "estimated_time": 120,
                "message": "简报生成已开始，预计2分钟完成"
            }
        }


class ReportStatsResponse(BaseModel):
    """简报统计响应模式"""
    total_reports: int = Field(..., description="总简报数")
    reports_by_type: Dict[str, int] = Field(..., description="按类型统计")
    reports_by_status: Dict[str, int] = Field(..., description="按状态统计")
    avg_generation_time: float = Field(..., description="平均生成时间")
    avg_quality_score: float = Field(..., description="平均质量评分")
    success_rate: float = Field(..., description="生成成功率")
    recent_reports: List[ReportResponse] = Field(..., description="最近的简报")

    class Config:
        json_schema_extra = {
            "example": {
                "total_reports": 150,
                "reports_by_type": {
                    "morning": 75,
                    "evening": 70,
                    "hourly": 5
                },
                "reports_by_status": {
                    "completed": 140,
                    "published": 135,
                    "failed": 10
                },
                "avg_generation_time": 85.5,
                "avg_quality_score": 87.2,
                "success_rate": 93.3,
                "recent_reports": []
            }
        }


class ReportPreviewRequest(BaseModel):
    """简报预览请求模式"""
    template_id: int = Field(..., description="模板ID")
    preview_config: Optional[Dict[str, Any]] = Field(None, description="预览配置")
    sample_date: Optional[datetime] = Field(None, description="样本日期")

    class Config:
        json_schema_extra = {
            "example": {
                "template_id": 1,
                "preview_config": {
                    "max_items_per_section": 3,
                    "include_sample_data": True
                },
                "sample_date": "2025-08-18T07:30:00"
            }
        }


class ReportPreviewResponse(BaseModel):
    """简报预览响应模式"""
    preview_content: str = Field(..., description="预览内容")
    sections_preview: List[Dict[str, Any]] = Field(..., description="章节预览")
    estimated_stats: Dict[str, Any] = Field(..., description="预估统计信息")
    generation_notes: List[str] = Field(..., description="生成说明")

    class Config:
        json_schema_extra = {
            "example": {
                "preview_content": "<h1>财经晨报</h1><h2>市场要闻</h2>...",
                "sections_preview": [
                    {
                        "name": "market_news",
                        "title": "市场要闻",
                        "item_count": 3,
                        "estimated_words": 300
                    }
                ],
                "estimated_stats": {
                    "total_words": 1200,
                    "read_time": 5,
                    "sections": 4
                },
                "generation_notes": [
                    "使用过去16小时的新闻数据",
                    "已过滤重复和低质量内容"
                ]
            }
        }
