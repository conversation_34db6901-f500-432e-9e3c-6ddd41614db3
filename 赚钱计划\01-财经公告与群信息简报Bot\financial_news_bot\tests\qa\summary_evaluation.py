#!/usr/bin/env python3
"""
财经新闻摘要质量评审脚本
按照准确性、完整性、可读性、相关性四个维度进行评分
"""

import json
import csv
import random
import requests
from datetime import datetime, timedelta
from typing import List, Dict, Any
import pandas as pd
from dataclasses import dataclass


@dataclass
class SummaryEvaluation:
    """摘要评估结果"""
    news_id: int
    original_title: str
    original_content: str
    summary: str
    accuracy_score: float  # 准确性 1-5分
    completeness_score: float  # 完整性 1-5分
    readability_score: float  # 可读性 1-5分
    relevance_score: float  # 相关性 1-5分
    overall_score: float  # 总分
    comments: str  # 评审意见
    evaluator: str  # 评审员
    evaluation_date: str


class SummaryQualityEvaluator:
    """摘要质量评估器"""
    
    def __init__(self, api_base_url: str = "http://localhost:8000"):
        self.api_base_url = api_base_url
        self.evaluation_criteria = {
            "accuracy": {
                "name": "准确性",
                "description": "摘要内容与原文事实的一致性",
                "scoring_guide": {
                    5: "完全准确，无任何事实错误",
                    4: "基本准确，有1个轻微错误",
                    3: "大部分准确，有2-3个轻微错误",
                    2: "部分准确，有明显错误但不影响主要信息",
                    1: "严重错误，误导性信息"
                }
            },
            "completeness": {
                "name": "完整性", 
                "description": "摘要是否涵盖了原文的关键信息",
                "scoring_guide": {
                    5: "涵盖所有关键信息，信息完整",
                    4: "涵盖大部分关键信息，略有遗漏",
                    3: "涵盖主要信息，有一些重要信息遗漏",
                    2: "信息不够完整，遗漏较多",
                    1: "信息严重不完整，遗漏关键信息"
                }
            },
            "readability": {
                "name": "可读性",
                "description": "摘要的语言表达和逻辑结构",
                "scoring_guide": {
                    5: "语言流畅，逻辑清晰，易于理解",
                    4: "语言较好，逻辑基本清晰",
                    3: "语言一般，逻辑可以理解",
                    2: "语言略显生硬，逻辑不够清晰",
                    1: "语言不通顺，逻辑混乱"
                }
            },
            "relevance": {
                "name": "相关性",
                "description": "摘要内容与财经新闻主题的相关程度",
                "scoring_guide": {
                    5: "高度相关，突出财经要点",
                    4: "相关性较好，财经信息明确",
                    3: "基本相关，财经信息一般",
                    2: "相关性不足，财经信息不突出",
                    1: "相关性很差，偏离财经主题"
                }
            }
        }
    
    def fetch_news_sample(self, sample_size: int = 100) -> List[Dict[str, Any]]:
        """获取新闻样本"""
        try:
            # 获取最近的新闻
            response = requests.get(
                f"{self.api_base_url}/api/v1/news",
                params={"limit": sample_size * 2, "sort": "latest"}
            )
            response.raise_for_status()
            
            news_list = response.json().get("data", [])
            
            # 随机抽样
            if len(news_list) > sample_size:
                news_list = random.sample(news_list, sample_size)
            
            return news_list
            
        except Exception as e:
            print(f"获取新闻样本失败: {e}")
            return []
    
    def evaluate_summary(self, news_item: Dict[str, Any], evaluator: str = "auto") -> SummaryEvaluation:
        """评估单个摘要"""
        
        # 自动评估逻辑（可以替换为人工评估）
        if evaluator == "auto":
            return self._auto_evaluate(news_item)
        else:
            return self._manual_evaluate(news_item, evaluator)
    
    def _auto_evaluate(self, news_item: Dict[str, Any]) -> SummaryEvaluation:
        """自动评估摘要质量"""
        title = news_item.get("title", "")
        content = news_item.get("content", "")
        summary = news_item.get("summary", "")
        
        # 简单的自动评估逻辑
        accuracy_score = self._evaluate_accuracy(content, summary)
        completeness_score = self._evaluate_completeness(content, summary)
        readability_score = self._evaluate_readability(summary)
        relevance_score = self._evaluate_relevance(title, summary)
        
        overall_score = (accuracy_score + completeness_score + readability_score + relevance_score) / 4
        
        return SummaryEvaluation(
            news_id=news_item.get("id", 0),
            original_title=title,
            original_content=content[:500] + "..." if len(content) > 500 else content,
            summary=summary,
            accuracy_score=accuracy_score,
            completeness_score=completeness_score,
            readability_score=readability_score,
            relevance_score=relevance_score,
            overall_score=overall_score,
            comments=self._generate_auto_comments(accuracy_score, completeness_score, readability_score, relevance_score),
            evaluator="auto_evaluator",
            evaluation_date=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        )
    
    def _evaluate_accuracy(self, content: str, summary: str) -> float:
        """评估准确性"""
        # 简单的关键词匹配评估
        content_words = set(content.lower().split())
        summary_words = set(summary.lower().split())
        
        # 计算摘要中的词汇在原文中的覆盖率
        if not summary_words:
            return 1.0
        
        coverage = len(summary_words.intersection(content_words)) / len(summary_words)
        
        if coverage >= 0.9:
            return 5.0
        elif coverage >= 0.8:
            return 4.0
        elif coverage >= 0.7:
            return 3.0
        elif coverage >= 0.6:
            return 2.0
        else:
            return 1.0
    
    def _evaluate_completeness(self, content: str, summary: str) -> float:
        """评估完整性"""
        # 基于长度比例的简单评估
        if not content or not summary:
            return 1.0
        
        ratio = len(summary) / len(content)
        
        # 理想的摘要长度比例在10%-30%之间
        if 0.1 <= ratio <= 0.3:
            return 5.0
        elif 0.05 <= ratio < 0.1 or 0.3 < ratio <= 0.4:
            return 4.0
        elif 0.03 <= ratio < 0.05 or 0.4 < ratio <= 0.5:
            return 3.0
        elif ratio < 0.03 or ratio > 0.5:
            return 2.0
        else:
            return 1.0
    
    def _evaluate_readability(self, summary: str) -> float:
        """评估可读性"""
        if not summary:
            return 1.0
        
        # 简单的可读性评估
        sentences = summary.split('。')
        avg_sentence_length = sum(len(s) for s in sentences) / len(sentences) if sentences else 0
        
        # 理想的句子长度在15-40字之间
        if 15 <= avg_sentence_length <= 40:
            return 5.0
        elif 10 <= avg_sentence_length < 15 or 40 < avg_sentence_length <= 60:
            return 4.0
        elif 5 <= avg_sentence_length < 10 or 60 < avg_sentence_length <= 80:
            return 3.0
        else:
            return 2.0
    
    def _evaluate_relevance(self, title: str, summary: str) -> float:
        """评估相关性"""
        # 财经关键词
        financial_keywords = [
            "经济", "金融", "投资", "股市", "银行", "货币", "政策", "市场",
            "企业", "公司", "收益", "利润", "资本", "融资", "上市", "债券",
            "基金", "保险", "房地产", "贸易", "进出口", "GDP", "CPI", "央行"
        ]
        
        title_lower = title.lower()
        summary_lower = summary.lower()
        
        # 计算财经关键词在标题和摘要中的出现次数
        title_keywords = sum(1 for keyword in financial_keywords if keyword in title_lower)
        summary_keywords = sum(1 for keyword in financial_keywords if keyword in summary_lower)
        
        total_keywords = title_keywords + summary_keywords
        
        if total_keywords >= 5:
            return 5.0
        elif total_keywords >= 3:
            return 4.0
        elif total_keywords >= 2:
            return 3.0
        elif total_keywords >= 1:
            return 2.0
        else:
            return 1.0
    
    def _generate_auto_comments(self, accuracy: float, completeness: float, readability: float, relevance: float) -> str:
        """生成自动评估意见"""
        comments = []
        
        if accuracy < 3:
            comments.append("准确性需要改进，建议检查事实信息")
        if completeness < 3:
            comments.append("完整性不足，建议补充关键信息")
        if readability < 3:
            comments.append("可读性有待提升，建议优化语言表达")
        if relevance < 3:
            comments.append("相关性不够，建议突出财经要点")
        
        if not comments:
            comments.append("整体质量良好")
        
        return "；".join(comments)
    
    def _manual_evaluate(self, news_item: Dict[str, Any], evaluator: str) -> SummaryEvaluation:
        """人工评估摘要质量"""
        print(f"\n{'='*60}")
        print(f"新闻ID: {news_item.get('id')}")
        print(f"标题: {news_item.get('title')}")
        print(f"摘要: {news_item.get('summary')}")
        print(f"{'='*60}")
        
        # 显示评分标准
        for criterion, info in self.evaluation_criteria.items():
            print(f"\n{info['name']} ({info['description']}):")
            for score, desc in info['scoring_guide'].items():
                print(f"  {score}分: {desc}")
        
        # 获取人工评分
        accuracy_score = float(input("\n请输入准确性评分 (1-5): "))
        completeness_score = float(input("请输入完整性评分 (1-5): "))
        readability_score = float(input("请输入可读性评分 (1-5): "))
        relevance_score = float(input("请输入相关性评分 (1-5): "))
        comments = input("请输入评审意见: ")
        
        overall_score = (accuracy_score + completeness_score + readability_score + relevance_score) / 4
        
        return SummaryEvaluation(
            news_id=news_item.get("id", 0),
            original_title=news_item.get("title", ""),
            original_content=news_item.get("content", "")[:500] + "...",
            summary=news_item.get("summary", ""),
            accuracy_score=accuracy_score,
            completeness_score=completeness_score,
            readability_score=readability_score,
            relevance_score=relevance_score,
            overall_score=overall_score,
            comments=comments,
            evaluator=evaluator,
            evaluation_date=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        )
    
    def batch_evaluate(self, sample_size: int = 100, evaluator: str = "auto") -> List[SummaryEvaluation]:
        """批量评估摘要质量"""
        print(f"开始批量评估，样本大小: {sample_size}")
        
        # 获取新闻样本
        news_sample = self.fetch_news_sample(sample_size)
        if not news_sample:
            print("无法获取新闻样本")
            return []
        
        print(f"成功获取 {len(news_sample)} 条新闻样本")
        
        # 批量评估
        evaluations = []
        for i, news_item in enumerate(news_sample, 1):
            print(f"评估进度: {i}/{len(news_sample)}")
            try:
                evaluation = self.evaluate_summary(news_item, evaluator)
                evaluations.append(evaluation)
            except Exception as e:
                print(f"评估新闻 {news_item.get('id')} 失败: {e}")
        
        return evaluations
    
    def export_results(self, evaluations: List[SummaryEvaluation], filename: str = None):
        """导出评估结果"""
        if not filename:
            filename = f"summary_evaluation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        # 转换为DataFrame
        data = []
        for eval_result in evaluations:
            data.append({
                "新闻ID": eval_result.news_id,
                "原文标题": eval_result.original_title,
                "摘要": eval_result.summary,
                "准确性评分": eval_result.accuracy_score,
                "完整性评分": eval_result.completeness_score,
                "可读性评分": eval_result.readability_score,
                "相关性评分": eval_result.relevance_score,
                "总分": eval_result.overall_score,
                "评审意见": eval_result.comments,
                "评审员": eval_result.evaluator,
                "评审时间": eval_result.evaluation_date
            })
        
        df = pd.DataFrame(data)
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"评估结果已导出到: {filename}")
        
        # 生成统计报告
        self.generate_report(evaluations)
    
    def generate_report(self, evaluations: List[SummaryEvaluation]):
        """生成评估报告"""
        if not evaluations:
            print("无评估数据")
            return
        
        # 计算统计指标
        accuracy_scores = [e.accuracy_score for e in evaluations]
        completeness_scores = [e.completeness_score for e in evaluations]
        readability_scores = [e.readability_score for e in evaluations]
        relevance_scores = [e.relevance_score for e in evaluations]
        overall_scores = [e.overall_score for e in evaluations]
        
        print(f"\n{'='*50}")
        print("摘要质量评估报告")
        print(f"{'='*50}")
        print(f"评估样本数量: {len(evaluations)}")
        print(f"平均准确性评分: {sum(accuracy_scores)/len(accuracy_scores):.2f}")
        print(f"平均完整性评分: {sum(completeness_scores)/len(completeness_scores):.2f}")
        print(f"平均可读性评分: {sum(readability_scores)/len(readability_scores):.2f}")
        print(f"平均相关性评分: {sum(relevance_scores)/len(relevance_scores):.2f}")
        print(f"平均总分: {sum(overall_scores)/len(overall_scores):.2f}")
        
        # 通过率统计（≥4.0分视为通过）
        pass_count = sum(1 for score in overall_scores if score >= 4.0)
        pass_rate = pass_count / len(overall_scores) * 100
        print(f"通过率 (≥4.0分): {pass_rate:.1f}% ({pass_count}/{len(evaluations)})")
        
        # 分数分布
        score_distribution = {
            "优秀 (4.5-5.0)": sum(1 for s in overall_scores if 4.5 <= s <= 5.0),
            "良好 (4.0-4.4)": sum(1 for s in overall_scores if 4.0 <= s < 4.5),
            "一般 (3.0-3.9)": sum(1 for s in overall_scores if 3.0 <= s < 4.0),
            "较差 (2.0-2.9)": sum(1 for s in overall_scores if 2.0 <= s < 3.0),
            "很差 (1.0-1.9)": sum(1 for s in overall_scores if 1.0 <= s < 2.0),
        }
        
        print(f"\n分数分布:")
        for level, count in score_distribution.items():
            percentage = count / len(evaluations) * 100
            print(f"  {level}: {count} ({percentage:.1f}%)")


def main():
    """主函数"""
    evaluator = SummaryQualityEvaluator()
    
    # 批量评估
    evaluations = evaluator.batch_evaluate(sample_size=100, evaluator="auto")
    
    # 导出结果
    if evaluations:
        evaluator.export_results(evaluations)
    else:
        print("评估失败，无结果数据")


if __name__ == "__main__":
    main()
