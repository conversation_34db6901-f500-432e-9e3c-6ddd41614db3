"""
权限管理数据模型
定义用户角色、权限等相关数据结构
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, JSON, ForeignKey, UniqueConstraint
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime

from ..database import Base


class Permission(Base):
    """权限表"""
    __tablename__ = "permissions"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    name = Column(String(100), unique=True, nullable=False, comment="权限名称")
    code = Column(String(100), unique=True, nullable=False, comment="权限代码")
    description = Column(Text, nullable=True, comment="权限描述")
    resource = Column(String(100), nullable=False, comment="资源类型")
    action = Column(String(50), nullable=False, comment="操作类型")
    
    # 权限分组
    group_id = Column(Integer, ForeignKey("permission_groups.id"), nullable=True, comment="权限组ID")
    
    # 状态信息
    is_active = Column(Boolean, default=True, comment="是否启用")
    is_system = Column(Boolean, default=False, comment="是否系统权限")
    
    # 审计字段
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment="更新时间")
    
    # 关系
    group = relationship("PermissionGroup", back_populates="permissions")
    role_permissions = relationship("RolePermission", back_populates="permission")

    def __repr__(self):
        return f"<Permission(id={self.id}, name={self.name}, code={self.code})>"


class Role(Base):
    """角色表"""
    __tablename__ = "roles"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    name = Column(String(100), unique=True, nullable=False, comment="角色名称")
    code = Column(String(100), unique=True, nullable=False, comment="角色代码")
    description = Column(Text, nullable=True, comment="角色描述")
    
    # 角色层级
    level = Column(Integer, default=1, comment="角色层级")
    parent_id = Column(Integer, ForeignKey("roles.id"), nullable=True, comment="父角色ID")
    
    # 状态信息
    is_active = Column(Boolean, default=True, comment="是否启用")
    is_system = Column(Boolean, default=False, comment="是否系统角色")
    
    # 配置信息
    config = Column(JSON, nullable=True, comment="角色配置")
    
    # 审计字段
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment="更新时间")
    
    # 关系
    parent = relationship("Role", remote_side=[id])
    children = relationship("Role")
    user_roles = relationship("UserRole", back_populates="role")
    role_permissions = relationship("RolePermission", back_populates="role")

    def __repr__(self):
        return f"<Role(id={self.id}, name={self.name}, code={self.code})>"


class UserRole(Base):
    """用户角色关联表"""
    __tablename__ = "user_roles"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, comment="用户ID")
    role_id = Column(Integer, ForeignKey("roles.id"), nullable=False, comment="角色ID")
    
    # 分配信息
    assigned_by = Column(Integer, ForeignKey("users.id"), nullable=True, comment="分配者ID")
    assigned_at = Column(DateTime, default=func.now(), nullable=False, comment="分配时间")
    expires_at = Column(DateTime, nullable=True, comment="过期时间")
    
    # 状态信息
    is_active = Column(Boolean, default=True, comment="是否启用")
    
    # 审计字段
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment="更新时间")
    
    # 唯一约束
    __table_args__ = (UniqueConstraint('user_id', 'role_id', name='uk_user_role'),)
    
    # 关系
    user = relationship("User", foreign_keys=[user_id])
    role = relationship("Role", back_populates="user_roles")
    assigner = relationship("User", foreign_keys=[assigned_by])

    def __repr__(self):
        return f"<UserRole(user_id={self.user_id}, role_id={self.role_id})>"


class RolePermission(Base):
    """角色权限关联表"""
    __tablename__ = "role_permissions"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    role_id = Column(Integer, ForeignKey("roles.id"), nullable=False, comment="角色ID")
    permission_id = Column(Integer, ForeignKey("permissions.id"), nullable=False, comment="权限ID")
    
    # 分配信息
    assigned_by = Column(Integer, ForeignKey("users.id"), nullable=True, comment="分配者ID")
    assigned_at = Column(DateTime, default=func.now(), nullable=False, comment="分配时间")
    
    # 状态信息
    is_active = Column(Boolean, default=True, comment="是否启用")
    
    # 审计字段
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment="更新时间")
    
    # 唯一约束
    __table_args__ = (UniqueConstraint('role_id', 'permission_id', name='uk_role_permission'),)
    
    # 关系
    role = relationship("Role", back_populates="role_permissions")
    permission = relationship("Permission", back_populates="role_permissions")
    assigner = relationship("User", foreign_keys=[assigned_by])

    def __repr__(self):
        return f"<RolePermission(role_id={self.role_id}, permission_id={self.permission_id})>"


class PermissionGroup(Base):
    """权限组表"""
    __tablename__ = "permission_groups"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    name = Column(String(100), unique=True, nullable=False, comment="权限组名称")
    code = Column(String(100), unique=True, nullable=False, comment="权限组代码")
    description = Column(Text, nullable=True, comment="权限组描述")
    
    # 分组信息
    parent_id = Column(Integer, ForeignKey("permission_groups.id"), nullable=True, comment="父权限组ID")
    sort_order = Column(Integer, default=0, comment="排序顺序")
    
    # 状态信息
    is_active = Column(Boolean, default=True, comment="是否启用")
    
    # 审计字段
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment="更新时间")
    
    # 关系
    parent = relationship("PermissionGroup", remote_side=[id])
    children = relationship("PermissionGroup")
    permissions = relationship("Permission", back_populates="group")

    def __repr__(self):
        return f"<PermissionGroup(id={self.id}, name={self.name}, code={self.code})>"


class AuditLog(Base):
    """审计日志表"""
    __tablename__ = "audit_logs"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    
    # 操作信息
    action = Column(String(100), nullable=False, comment="操作类型")
    resource = Column(String(100), nullable=False, comment="资源类型")
    resource_id = Column(String(100), nullable=True, comment="资源ID")
    
    # 用户信息
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True, comment="操作用户ID")
    username = Column(String(100), nullable=True, comment="用户名")
    user_ip = Column(String(45), nullable=True, comment="用户IP")
    user_agent = Column(Text, nullable=True, comment="用户代理")
    
    # 操作详情
    description = Column(Text, nullable=True, comment="操作描述")
    old_values = Column(JSON, nullable=True, comment="修改前的值")
    new_values = Column(JSON, nullable=True, comment="修改后的值")
    
    # 结果信息
    success = Column(Boolean, nullable=False, comment="操作是否成功")
    error_message = Column(Text, nullable=True, comment="错误信息")
    
    # 时间信息
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    
    # 关系
    user = relationship("User")

    def __repr__(self):
        return f"<AuditLog(id={self.id}, action={self.action}, user_id={self.user_id})>"
