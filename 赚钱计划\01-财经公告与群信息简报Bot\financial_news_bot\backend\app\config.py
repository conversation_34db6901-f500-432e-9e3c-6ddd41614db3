"""
应用配置文件
"""
import os
from .constants import TimeConstants, DatabaseConstants, RedisConstants, APIConstants, CrawlerConstants

class Settings:
    """应用设置"""
    
    # 应用基础配置
    APP_NAME: str = "财经新闻Bot"
    APP_VERSION: str = "1.0.0"
    # DEBUG模式 - 从环境变量读取，默认为False确保生产环境安全
    DEBUG: bool = os.getenv("DEBUG", "false").lower() in ("true", "1", "yes")

    # 允许的主机列表 - 从环境变量读取，默认只允许本地访问
    ALLOWED_HOSTS: list = os.getenv("ALLOWED_HOSTS", "localhost,127.0.0.1").split(",")
    
    # 数据库配置 - 必须通过环境变量配置
    DATABASE_URL: str = os.getenv("DATABASE_URL")
    if not DATABASE_URL:
        raise ValueError("DATABASE_URL environment variable must be set")

    # Redis配置
    REDIS_HOST: str = os.getenv("REDIS_HOST", "redis")
    REDIS_PORT: int = int(os.getenv("REDIS_PORT", "6379"))
    REDIS_DB: int = int(os.getenv("REDIS_DB", "0"))
    REDIS_URL: str = os.getenv("REDIS_URL", f"redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}")

    # JWT配置 - 使用环境变量，无默认值确保安全
    SECRET_KEY: str = os.getenv("SECRET_KEY")
    if not SECRET_KEY:
        raise ValueError("SECRET_KEY environment variable must be set")
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = TimeConstants.ACCESS_TOKEN_EXPIRE_MINUTES

    # 缓存配置 - 使用统一常量，支持环境变量配置
    CACHE_EXPIRE_SECONDS: int = TimeConstants.CACHE_LONG
    CACHE_NEWS_LIST_EXPIRE: int = TimeConstants.CACHE_SHORT
    CACHE_NEWS_STATS_EXPIRE: int = TimeConstants.CACHE_MEDIUM
    CACHE_SEARCH_EXPIRE: int = TimeConstants.CACHE_SHORT

    # 分页配置 - 使用统一常量
    DEFAULT_PAGE_SIZE: int = APIConstants.DEFAULT_PAGE_SIZE
    MAX_PAGE_SIZE: int = APIConstants.MAX_PAGE_SIZE
    
    # 爬虫配置 - 使用统一常量
    CRAWLER_USER_AGENTS: list = CrawlerConstants.DEFAULT_USER_AGENTS
    CRAWLER_REQUEST_DELAY: float = 1.0  # 请求间隔（秒）
    CRAWLER_TIMEOUT: int = 30  # 请求超时（秒）
    CRAWLER_MAX_RETRIES: int = 3  # 最大重试次数
    
    # AI模型配置 - GLM-4.5 Flash
    GLM_API_KEY: str = os.getenv("GLM_API_KEY")
    if not GLM_API_KEY:
        raise ValueError("GLM_API_KEY environment variable must be set")
    GLM_BASE_URL: str = os.getenv("GLM_BASE_URL", "https://open.bigmodel.cn/api/paas/v4")
    GLM_MODEL: str = os.getenv("GLM_MODEL", "glm-4.5-flash")
    GLM_MAX_TOKENS: int = int(os.getenv("GLM_MAX_TOKENS", "4096"))
    GLM_TEMPERATURE: float = float(os.getenv("GLM_TEMPERATURE", "0.7"))
    GLM_TIMEOUT: int = int(os.getenv("GLM_TIMEOUT", "60"))
    GLM_MAX_RETRIES: int = int(os.getenv("GLM_MAX_RETRIES", "3"))

    # 内容处理配置
    CONTENT_CLASSIFICATION_CATEGORIES: list = [
        "政策监管", "公司公告", "市场动态", "行业资讯", "风险提示"
    ]
    CONTENT_SUMMARY_MIN_LENGTH: int = 100
    CONTENT_SUMMARY_MAX_LENGTH: int = 200
    CONTENT_KEYWORDS_MAX_COUNT: int = 10

    # 数据质量检查配置
    DATA_QUALITY_MIN_TITLE_LENGTH: int = 5
    DATA_QUALITY_MAX_TITLE_LENGTH: int = 200
    DATA_QUALITY_MIN_CONTENT_LENGTH: int = 50
    DATA_QUALITY_REQUIRED_FIELDS: list = ["title", "content", "source", "publish_time"]

    # 日志配置
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# 全局设置实例
settings = Settings()
