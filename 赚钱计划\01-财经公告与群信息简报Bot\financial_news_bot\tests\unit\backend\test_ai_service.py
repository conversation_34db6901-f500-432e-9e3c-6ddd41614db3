"""
AI服务测试
测试AI服务的核心功能
"""
import pytest
from unittest.mock import Mock, patch, AsyncMock
import json

from app.services.ai_service import AIService, get_ai_service

class TestAIService:
    """测试AI服务"""
    
    @pytest.fixture
    def mock_client(self):
        """模拟GLM客户端"""
        mock_client = Mock()
        return mock_client
    
    @pytest.fixture
    def ai_service(self, mock_client):
        """AI服务实例"""
        with patch('app.services.ai_service.ZhipuAI', return_value=mock_client):
            service = AIService()
            service.client = mock_client
            return service
    
    @pytest.fixture
    def mock_response(self):
        """模拟API响应"""
        mock_response = Mock()
        mock_choice = Mock()
        mock_choice.message.content = "这是AI生成的响应内容"
        mock_response.choices = [mock_choice]
        return mock_response
    
    def test_ai_service_initialization(self):
        """测试AI服务初始化"""
        with patch('app.services.ai_service.ZhipuAI') as mock_zhipu:
            service = AIService()
            
            assert service.model == "glm-4-flash"
            assert service.client is not None
            mock_zhipu.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_summarize_text_success(self, ai_service, mock_response):
        """测试文本摘要成功"""
        ai_service.client.chat.completions.create.return_value = mock_response
        
        text = "这是一段很长的测试文本，需要进行摘要处理。"
        result = await ai_service.summarize_text(text)
        
        assert result is not None
        assert "content" in result
        assert result["content"] == "这是AI生成的响应内容"
        assert result["original_length"] == len(text)
        
        # 验证API调用
        ai_service.client.chat.completions.create.assert_called_once()
        call_args = ai_service.client.chat.completions.create.call_args
        assert call_args[1]["model"] == "glm-4-flash"
        assert "摘要" in call_args[1]["messages"][0]["content"]
    
    @pytest.mark.asyncio
    async def test_summarize_text_empty_response(self, ai_service):
        """测试文本摘要空响应"""
        mock_empty_response = Mock()
        mock_empty_response.choices = []
        ai_service.client.chat.completions.create.return_value = mock_empty_response
        
        result = await ai_service.summarize_text("测试文本")
        
        assert result is None
    
    @pytest.mark.asyncio
    async def test_summarize_text_exception(self, ai_service):
        """测试文本摘要异常处理"""
        ai_service.client.chat.completions.create.side_effect = Exception("API错误")
        
        result = await ai_service.summarize_text("测试文本")
        
        assert result is None
    
    @pytest.mark.asyncio
    async def test_classify_content_success(self, ai_service):
        """测试内容分类成功"""
        # 模拟分类响应
        mock_response = Mock()
        mock_choice = Mock()
        mock_choice.message.content = json.dumps({
            "category": "财经新闻",
            "confidence": 0.95,
            "subcategory": "股市动态"
        })
        mock_response.choices = [mock_choice]
        ai_service.client.chat.completions.create.return_value = mock_response
        
        text = "股市今日大涨，上证指数上涨3%"
        result = await ai_service.classify_content(text)
        
        assert result is not None
        assert result["category"] == "财经新闻"
        assert result["confidence"] == 0.95
        assert result["subcategory"] == "股市动态"
    
    @pytest.mark.asyncio
    async def test_classify_content_invalid_json(self, ai_service):
        """测试内容分类无效JSON响应"""
        mock_response = Mock()
        mock_choice = Mock()
        mock_choice.message.content = "这不是有效的JSON"
        mock_response.choices = [mock_choice]
        ai_service.client.chat.completions.create.return_value = mock_response
        
        result = await ai_service.classify_content("测试文本")
        
        assert result is None
    
    @pytest.mark.asyncio
    async def test_extract_keywords_success(self, ai_service):
        """测试关键词提取成功"""
        mock_response = Mock()
        mock_choice = Mock()
        mock_choice.message.content = json.dumps({
            "keywords": ["股市", "上涨", "指数"],
            "entities": ["上证指数"],
            "topics": ["股市动态"]
        })
        mock_response.choices = [mock_choice]
        ai_service.client.chat.completions.create.return_value = mock_response
        
        text = "股市今日大涨，上证指数上涨3%"
        result = await ai_service.extract_keywords(text)
        
        assert result is not None
        assert "keywords" in result
        assert "entities" in result
        assert "topics" in result
        assert "股市" in result["keywords"]
        assert "上证指数" in result["entities"]
    
    @pytest.mark.asyncio
    async def test_analyze_sentiment_success(self, ai_service):
        """测试情感分析成功"""
        mock_response = Mock()
        mock_choice = Mock()
        mock_choice.message.content = json.dumps({
            "sentiment": "positive",
            "confidence": 0.88,
            "score": 0.75
        })
        mock_response.choices = [mock_choice]
        ai_service.client.chat.completions.create.return_value = mock_response
        
        text = "公司业绩优秀，前景看好"
        result = await ai_service.analyze_sentiment(text)
        
        assert result is not None
        assert result["sentiment"] == "positive"
        assert result["confidence"] == 0.88
        assert result["score"] == 0.75
    
    @pytest.mark.asyncio
    async def test_check_content_safety_success(self, ai_service):
        """测试内容安全检测成功"""
        mock_response = Mock()
        mock_choice = Mock()
        mock_choice.message.content = json.dumps({
            "is_safe": True,
            "risk_level": "low",
            "risk_categories": [],
            "confidence": 0.95
        })
        mock_response.choices = [mock_choice]
        ai_service.client.chat.completions.create.return_value = mock_response
        
        text = "这是一段正常的财经新闻内容"
        result = await ai_service.check_content_safety(text)
        
        assert result is not None
        assert result["is_safe"] is True
        assert result["risk_level"] == "low"
        assert result["risk_categories"] == []
    
    @pytest.mark.asyncio
    async def test_check_content_safety_unsafe(self, ai_service):
        """测试内容安全检测不安全内容"""
        mock_response = Mock()
        mock_choice = Mock()
        mock_choice.message.content = json.dumps({
            "is_safe": False,
            "risk_level": "high",
            "risk_categories": ["违法违规"],
            "confidence": 0.92
        })
        mock_response.choices = [mock_choice]
        ai_service.client.chat.completions.create.return_value = mock_response
        
        text = "包含不当内容的文本"
        result = await ai_service.check_content_safety(text)
        
        assert result is not None
        assert result["is_safe"] is False
        assert result["risk_level"] == "high"
        assert "违法违规" in result["risk_categories"]
    
    @pytest.mark.asyncio
    async def test_search_web_success(self, ai_service):
        """测试网络搜索成功"""
        mock_response = Mock()
        mock_choice = Mock()
        mock_choice.message.content = "根据搜索结果，找到了相关信息..."
        mock_response.choices = [mock_choice]
        ai_service.client.chat.completions.create.return_value = mock_response
        
        query = "最新股市行情"
        result = await ai_service.search_web(query)
        
        assert result is not None
        assert "content" in result
        assert "search_query" in result
        assert result["search_query"] == query
        assert result["content"] == "根据搜索结果，找到了相关信息..."
        
        # 验证工具调用
        call_args = ai_service.client.chat.completions.create.call_args
        assert "tools" in call_args[1]
        assert call_args[1]["tools"][0]["type"] == "web_search"
    
    @pytest.mark.asyncio
    async def test_search_web_empty_response(self, ai_service):
        """测试网络搜索空响应"""
        mock_response = Mock()
        mock_response.choices = []
        ai_service.client.chat.completions.create.return_value = mock_response
        
        result = await ai_service.search_web("测试查询")
        
        assert result is None
    
    def test_ai_service_model_configuration(self, ai_service):
        """测试AI服务模型配置"""
        assert ai_service.model == "glm-4-flash"
        assert ai_service.client is not None
    
    @pytest.mark.asyncio
    async def test_multiple_api_calls(self, ai_service, mock_response):
        """测试多个API调用"""
        ai_service.client.chat.completions.create.return_value = mock_response
        
        # 连续调用多个方法
        text = "测试文本"
        
        summary_result = await ai_service.summarize_text(text)
        assert summary_result is not None
        
        # 重置mock以测试不同的响应
        mock_json_response = Mock()
        mock_choice = Mock()
        mock_choice.message.content = json.dumps({"sentiment": "neutral", "confidence": 0.5})
        mock_json_response.choices = [mock_choice]
        ai_service.client.chat.completions.create.return_value = mock_json_response
        
        sentiment_result = await ai_service.analyze_sentiment(text)
        assert sentiment_result is not None
        assert sentiment_result["sentiment"] == "neutral"

class TestGlobalAIService:
    """测试全局AI服务"""
    
    def test_get_ai_service(self):
        """测试获取AI服务实例"""
        with patch('app.services.ai_service.ZhipuAI'):
            service = get_ai_service()
            
            assert service is not None
            assert isinstance(service, AIService)
    
    def test_get_ai_service_singleton(self):
        """测试AI服务单例模式"""
        with patch('app.services.ai_service.ZhipuAI'):
            service1 = get_ai_service()
            service2 = get_ai_service()
            
            # 应该返回同一个实例
            assert service1 is service2

class TestAIServiceIntegration:
    """测试AI服务集成"""
    
    @pytest.mark.asyncio
    async def test_complete_workflow(self):
        """测试完整的AI处理工作流"""
        with patch('app.services.ai_service.ZhipuAI') as mock_zhipu:
            mock_client = Mock()
            mock_zhipu.return_value = mock_client
            
            # 设置不同的响应
            responses = [
                # 摘要响应
                Mock(choices=[Mock(message=Mock(content="这是摘要"))]),
                # 分类响应
                Mock(choices=[Mock(message=Mock(content=json.dumps({"category": "财经", "confidence": 0.9})))]),
                # 关键词响应
                Mock(choices=[Mock(message=Mock(content=json.dumps({"keywords": ["股市", "上涨"]})))]),
                # 情感分析响应
                Mock(choices=[Mock(message=Mock(content=json.dumps({"sentiment": "positive", "confidence": 0.8})))]),
                # 安全检测响应
                Mock(choices=[Mock(message=Mock(content=json.dumps({"is_safe": True, "risk_level": "low"})))])
            ]
            
            mock_client.chat.completions.create.side_effect = responses
            
            service = AIService()
            text = "股市今日大涨，投资者信心增强"
            
            # 执行完整工作流
            summary = await service.summarize_text(text)
            classification = await service.classify_content(text)
            keywords = await service.extract_keywords(text)
            sentiment = await service.analyze_sentiment(text)
            safety = await service.check_content_safety(text)
            
            # 验证所有结果
            assert summary is not None
            assert classification is not None
            assert keywords is not None
            assert sentiment is not None
            assert safety is not None
            
            # 验证API调用次数
            assert mock_client.chat.completions.create.call_count == 5

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
