"""
调度管理API路由
提供动态调度配置、任务管理和监控功能
"""
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel, Field
from datetime import datetime

from app.dependencies.auth import get_current_active_user
from app.models.user import User

# 创建路由器
router = APIRouter(prefix="/scheduler", tags=["调度管理"])


class ScheduleConfigRequest(BaseModel):
    """调度配置请求模型"""
    task: str = Field(..., description="任务名称")
    schedule_type: str = Field(..., description="调度类型: cron/interval")
    schedule_value: str = Field(..., description="调度表达式")
    queue: str = Field(default="default", description="队列名称")
    priority: int = Field(default=5, description="优先级 1-10")
    enabled: bool = Field(default=True, description="是否启用")
    description: Optional[str] = Field(None, description="任务描述")
    kwargs: Optional[Dict[str, Any]] = Field(default_factory=dict, description="任务参数")


class TaskControlRequest(BaseModel):
    """任务控制请求模型"""
    action: str = Field(..., description="操作类型: enable/disable/run_now")


class ScheduleResponse(BaseModel):
    """调度响应模型"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None


@router.get("/schedules", response_model=Dict[str, Any])
async def get_schedules(current_user: User = Depends(get_current_active_user)):
    """
    获取所有调度配置
    """
    try:
        schedules = scheduler_service.get_current_schedules()
        return {
            "success": True,
            "message": "获取调度配置成功",
            "data": {
                "schedules": schedules,
                "total": len(schedules),
                "timestamp": datetime.now().isoformat()
            }
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取调度配置失败: {str(e)}"
        )


@router.get("/schedules/{task_name}", response_model=Dict[str, Any])
async def get_schedule(
    task_name: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    获取指定任务的调度配置
    """
    try:
        schedules = scheduler_service.get_current_schedules()
        
        if task_name not in schedules:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"任务不存在: {task_name}"
            )
        
        return {
            "success": True,
            "message": "获取任务配置成功",
            "data": schedules[task_name]
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取任务配置失败: {str(e)}"
        )


@router.put("/schedules/{task_name}", response_model=ScheduleResponse)
async def update_schedule(
    task_name: str,
    config: ScheduleConfigRequest,
    current_user: User = Depends(get_current_active_user)
):
    """
    更新任务调度配置
    """
    try:
        # 检查用户权限
        if current_user.role not in ["admin", "enterprise"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，需要管理员或企业版权限"
            )
        
        # 构建调度配置
        from celery.schedules import crontab
        
        if config.schedule_type == "cron":
            # 解析cron表达式
            cron_parts = config.schedule_value.split()
            if len(cron_parts) != 5:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="无效的cron表达式，格式: 分 时 日 月 周"
                )
            
            minute, hour, day, month, day_of_week = cron_parts
            schedule = crontab(
                minute=minute,
                hour=hour,
                day_of_month=day,
                month_of_year=month,
                day_of_week=day_of_week
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="暂不支持的调度类型"
            )
        
        schedule_config = {
            'task': config.task,
            'schedule': schedule,
            'options': {
                'queue': config.queue,
                'priority': config.priority
            },
            'enabled': config.enabled,
            'description': config.description or ""
        }
        
        if config.kwargs:
            schedule_config['kwargs'] = config.kwargs
        
        # 更新配置
        success = scheduler_service.update_schedule(task_name, schedule_config)
        
        if success:
            return ScheduleResponse(
                success=True,
                message=f"任务调度配置更新成功: {task_name}",
                data=schedule_config
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="更新调度配置失败"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新调度配置失败: {str(e)}"
        )


@router.post("/schedules/{task_name}/control", response_model=ScheduleResponse)
async def control_task(
    task_name: str,
    control: TaskControlRequest,
    current_user: User = Depends(get_current_active_user)
):
    """
    控制任务执行
    """
    try:
        # 检查用户权限
        if current_user.role not in ["admin", "enterprise"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，需要管理员或企业版权限"
            )
        
        if control.action == "enable":
            success = scheduler_service.enable_task(task_name)
            message = f"任务已启用: {task_name}" if success else "启用任务失败"
            
        elif control.action == "disable":
            success = scheduler_service.disable_task(task_name)
            message = f"任务已禁用: {task_name}" if success else "禁用任务失败"
            
        elif control.action == "run_now":
            # 立即执行任务
            from app.celery_app import celery_app
            
            schedules = scheduler_service.get_current_schedules()
            if task_name not in schedules:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"任务不存在: {task_name}"
                )
            
            task_config = schedules[task_name]
            task_path = task_config['task']
            
            # 发送任务到队列
            result = celery_app.send_task(
                task_path,
                queue=task_config.get('options', {}).get('queue', 'default'),
                kwargs=task_config.get('kwargs', {})
            )
            
            success = True
            message = f"任务已提交执行: {task_name}, 任务ID: {result.id}"
            
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的操作: {control.action}"
            )
        
        if success:
            return ScheduleResponse(
                success=True,
                message=message
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=message
            )
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"控制任务失败: {str(e)}"
        )


@router.get("/statistics", response_model=Dict[str, Any])
async def get_statistics(current_user: User = Depends(get_current_active_user)):
    """
    获取调度统计信息
    """
    try:
        stats = scheduler_service.get_task_statistics()
        return {
            "success": True,
            "message": "获取统计信息成功",
            "data": stats
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取统计信息失败: {str(e)}"
        )


@router.post("/reset", response_model=ScheduleResponse)
async def reset_schedules(current_user: User = Depends(get_current_active_user)):
    """
    重置调度配置为默认值
    """
    try:
        # 检查用户权限
        if current_user.role != "admin":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，需要管理员权限"
            )
        
        success = scheduler_service.reset_to_defaults()
        
        if success:
            return ScheduleResponse(
                success=True,
                message="调度配置已重置为默认值"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="重置配置失败"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"重置配置失败: {str(e)}"
        )


@router.get("/health", response_model=Dict[str, Any])
async def health_check():
    """
    调度系统健康检查
    """
    try:
        from app.celery_app import celery_app
        
        # 检查Celery连接
        inspect = celery_app.control.inspect()
        active_workers = inspect.active()
        
        health_status = {
            "celery_connected": active_workers is not None,
            "active_workers": len(active_workers) if active_workers else 0,
            "worker_details": active_workers or {},
            "timestamp": datetime.now().isoformat()
        }
        
        return {
            "success": True,
            "message": "健康检查完成",
            "data": health_status
        }
        
    except Exception as e:
        return {
            "success": False,
            "message": f"健康检查失败: {str(e)}",
            "data": {
                "celery_connected": False,
                "active_workers": 0,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        }
