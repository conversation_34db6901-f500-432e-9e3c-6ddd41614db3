/**
 * Jest测试设置文件
 * 配置测试环境和全局设置
 */
import '@testing-library/jest-dom';

// 模拟环境变量
process.env.REACT_APP_API_BASE_URL = 'http://localhost:8000';
process.env.REACT_APP_VERSION = '1.0.0';
process.env.NODE_ENV = 'test';

// 模拟localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock as any;

// 模拟sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.sessionStorage = sessionStorageMock as any;

// 模拟window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// 模拟IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  observe() {
    return null;
  }
  disconnect() {
    return null;
  }
  unobserve() {
    return null;
  }
};

// 模拟ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  observe() {
    return null;
  }
  disconnect() {
    return null;
  }
  unobserve() {
    return null;
  }
};

// 模拟console方法（避免测试时输出过多日志）
const originalError = console.error;
beforeAll(() => {
  console.error = (...args: any[]) => {
    if (
      typeof args[0] === 'string' &&
      args[0].includes('Warning: ReactDOM.render is no longer supported')
    ) {
      return;
    }
    originalError.call(console, ...args);
  };
});

afterAll(() => {
  console.error = originalError;
});

// 全局测试工具函数
global.testUtils = {
  // 等待异步操作完成
  waitFor: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // 模拟API响应
  mockApiResponse: (data: any, status = 200) => ({
    data,
    status,
    statusText: 'OK',
    headers: {},
    config: {},
  }),
  
  // 模拟错误响应
  mockApiError: (message: string, status = 500) => ({
    response: {
      data: { message },
      status,
      statusText: 'Internal Server Error',
    },
  }),
};

// 类型声明
declare global {
  namespace NodeJS {
    interface Global {
      testUtils: {
        waitFor: (ms: number) => Promise<void>;
        mockApiResponse: (data: any, status?: number) => any;
        mockApiError: (message: string, status?: number) => any;
      };
    }
  }
}
