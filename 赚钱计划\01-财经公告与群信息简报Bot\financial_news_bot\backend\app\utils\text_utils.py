"""
文本处理工具模块
提供文本分析、摘要提取、阅读时间计算等功能
"""
import re
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)


def calculate_read_time(word_count: int, words_per_minute: int = 200) -> int:
    """
    计算阅读时间
    
    Args:
        word_count: 字数
        words_per_minute: 每分钟阅读字数
        
    Returns:
        int: 预计阅读时间（分钟）
    """
    if word_count <= 0:
        return 0
    
    # 中文按字符计算，英文按单词计算
    # 这里简化处理，统一按字符计算
    read_time = max(1, round(word_count / words_per_minute))
    return read_time


def extract_summary(text: str, max_length: int = 200) -> str:
    """
    提取文本摘要
    
    Args:
        text: 原始文本
        max_length: 最大长度
        
    Returns:
        str: 摘要文本
    """
    if not text:
        return ""
    
    # 清理文本
    cleaned_text = clean_text(text)
    
    # 如果文本长度小于最大长度，直接返回
    if len(cleaned_text) <= max_length:
        return cleaned_text
    
    # 尝试按句子截取
    sentences = split_sentences(cleaned_text)
    summary = ""
    
    for sentence in sentences:
        if len(summary + sentence) <= max_length:
            summary += sentence
        else:
            break
    
    # 如果没有完整句子，直接截取
    if not summary:
        summary = cleaned_text[:max_length - 3] + "..."
    
    return summary.strip()


def clean_text(text: str) -> str:
    """
    清理文本
    
    Args:
        text: 原始文本
        
    Returns:
        str: 清理后的文本
    """
    if not text:
        return ""
    
    # 移除多余的空白字符
    text = re.sub(r'\s+', ' ', text)
    
    # 移除特殊字符（保留中文、英文、数字、基本标点）
    text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s\.,;:!?()（）【】""''、。，；：！？]', '', text)
    
    # 移除多余的标点
    text = re.sub(r'[,，]{2,}', '，', text)
    text = re.sub(r'[.。]{2,}', '。', text)
    
    return text.strip()


def split_sentences(text: str) -> List[str]:
    """
    分割句子
    
    Args:
        text: 文本
        
    Returns:
        List[str]: 句子列表
    """
    if not text:
        return []
    
    # 中文句子分割符
    sentence_endings = r'[。！？；]'
    sentences = re.split(sentence_endings, text)
    
    # 过滤空句子并添加标点
    result = []
    for i, sentence in enumerate(sentences):
        sentence = sentence.strip()
        if sentence:
            # 为非最后一个句子添加句号
            if i < len(sentences) - 1:
                sentence += '。'
            result.append(sentence)
    
    return result


def extract_keywords(text: str, max_keywords: int = 10) -> List[str]:
    """
    提取关键词
    
    Args:
        text: 文本
        max_keywords: 最大关键词数量
        
    Returns:
        List[str]: 关键词列表
    """
    if not text:
        return []
    
    # 简单的关键词提取（基于词频）
    # 移除停用词
    stop_words = {
        '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
        '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好',
        '自己', '这', '那', '它', '他', '她', '们', '这个', '那个', '什么', '怎么',
        '为什么', '因为', '所以', '但是', '然后', '如果', '虽然', '虽然', '但是'
    }
    
    # 提取中文词汇（简化处理）
    chinese_words = re.findall(r'[\u4e00-\u9fa5]{2,}', text)
    
    # 统计词频
    word_freq = {}
    for word in chinese_words:
        if word not in stop_words and len(word) >= 2:
            word_freq[word] = word_freq.get(word, 0) + 1
    
    # 按频率排序
    sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
    
    # 返回前N个关键词
    keywords = [word for word, freq in sorted_words[:max_keywords]]
    
    return keywords


def calculate_text_similarity(text1: str, text2: str) -> float:
    """
    计算文本相似度
    
    Args:
        text1: 文本1
        text2: 文本2
        
    Returns:
        float: 相似度（0-1）
    """
    if not text1 or not text2:
        return 0.0
    
    # 提取关键词
    keywords1 = set(extract_keywords(text1, 20))
    keywords2 = set(extract_keywords(text2, 20))
    
    if not keywords1 or not keywords2:
        return 0.0
    
    # 计算Jaccard相似度
    intersection = len(keywords1.intersection(keywords2))
    union = len(keywords1.union(keywords2))
    
    similarity = intersection / union if union > 0 else 0.0
    
    return similarity


def format_number(number: float, decimal_places: int = 2) -> str:
    """
    格式化数字
    
    Args:
        number: 数字
        decimal_places: 小数位数
        
    Returns:
        str: 格式化后的数字字符串
    """
    if number is None:
        return "N/A"
    
    try:
        # 处理大数字
        if abs(number) >= 1e8:
            return f"{number/1e8:.{decimal_places}f}亿"
        elif abs(number) >= 1e4:
            return f"{number/1e4:.{decimal_places}f}万"
        else:
            return f"{number:.{decimal_places}f}"
    except (ValueError, TypeError):
        return str(number)


def format_percentage(value: float, decimal_places: int = 2) -> str:
    """
    格式化百分比
    
    Args:
        value: 数值（0-1或0-100）
        decimal_places: 小数位数
        
    Returns:
        str: 格式化后的百分比字符串
    """
    if value is None:
        return "N/A"
    
    try:
        # 如果值在0-1之间，转换为百分比
        if 0 <= value <= 1:
            percentage = value * 100
        else:
            percentage = value
        
        return f"{percentage:.{decimal_places}f}%"
    except (ValueError, TypeError):
        return str(value)


def truncate_text(text: str, max_length: int, suffix: str = "...") -> str:
    """
    截断文本
    
    Args:
        text: 原始文本
        max_length: 最大长度
        suffix: 后缀
        
    Returns:
        str: 截断后的文本
    """
    if not text:
        return ""
    
    if len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix


def highlight_keywords(text: str, keywords: List[str], tag: str = "**") -> str:
    """
    高亮关键词
    
    Args:
        text: 原始文本
        keywords: 关键词列表
        tag: 高亮标签
        
    Returns:
        str: 高亮后的文本
    """
    if not text or not keywords:
        return text
    
    highlighted_text = text
    
    for keyword in keywords:
        if keyword in highlighted_text:
            highlighted_text = highlighted_text.replace(
                keyword, f"{tag}{keyword}{tag}"
            )
    
    return highlighted_text


def extract_numbers(text: str) -> List[float]:
    """
    提取文本中的数字
    
    Args:
        text: 文本
        
    Returns:
        List[float]: 数字列表
    """
    if not text:
        return []
    
    # 匹配数字（包括小数、百分比等）
    number_pattern = r'-?\d+(?:\.\d+)?(?:%|万|亿|千万|百万)?'
    matches = re.findall(number_pattern, text)
    
    numbers = []
    for match in matches:
        try:
            # 处理中文数字单位
            if match.endswith('万'):
                number = float(match[:-1]) * 10000
            elif match.endswith('亿'):
                number = float(match[:-1]) * 100000000
            elif match.endswith('千万'):
                number = float(match[:-2]) * 10000000
            elif match.endswith('百万'):
                number = float(match[:-2]) * 1000000
            elif match.endswith('%'):
                number = float(match[:-1]) / 100
            else:
                number = float(match)
            
            numbers.append(number)
        except ValueError:
            continue
    
    return numbers


def validate_text_quality(text: str) -> Dict[str, Any]:
    """
    验证文本质量
    
    Args:
        text: 文本
        
    Returns:
        Dict[str, Any]: 质量评估结果
    """
    if not text:
        return {
            "score": 0,
            "issues": ["文本为空"],
            "suggestions": ["请提供有效的文本内容"]
        }
    
    issues = []
    suggestions = []
    score = 100
    
    # 检查长度
    if len(text) < 10:
        issues.append("文本过短")
        suggestions.append("增加文本内容")
        score -= 30
    elif len(text) > 5000:
        issues.append("文本过长")
        suggestions.append("考虑分段或摘要")
        score -= 10
    
    # 检查重复内容
    sentences = split_sentences(text)
    if len(set(sentences)) < len(sentences) * 0.8:
        issues.append("存在重复内容")
        suggestions.append("删除重复句子")
        score -= 20
    
    # 检查标点符号
    punctuation_count = len(re.findall(r'[。！？；，]', text))
    if punctuation_count < len(text) / 50:
        issues.append("标点符号过少")
        suggestions.append("添加适当的标点符号")
        score -= 15
    
    # 检查关键词密度
    keywords = extract_keywords(text)
    if len(keywords) < 3:
        issues.append("关键词过少")
        suggestions.append("增加相关关键词")
        score -= 10
    
    return {
        "score": max(0, score),
        "issues": issues,
        "suggestions": suggestions,
        "word_count": len(text),
        "sentence_count": len(sentences),
        "keyword_count": len(keywords)
    }
