from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc, asc
from datetime import datetime, timedelta
import json

from ..models.news import News, NewsCategory, NewsSource, NewsSentiment
from ..schemas.news import (
    NewsCreate, NewsUpdate, NewsResponse, NewsListResponse,
    NewsSearchRequest, NewsStats, NewsSearchParams, NewsStatistics
)
from ..services.cache_service import cache_service, cache_result, invalidate_cache_pattern
from ..config import settings
from ..services.data_processing_pipeline import DataProcessingPipeline
from ..utils.deduplicator import NewsDeduplicator
import logging

logger = logging.getLogger(__name__)


class NewsService:
    """新闻业务逻辑服务"""
    
    def __init__(self, db: Session):
        self.db = db
        self.data_processor = DataProcessingPipeline()
        self.deduplicator = NewsDeduplicator()
    
    def create_news(self, news_data: NewsCreate) -> NewsResponse:
        """
        创建新闻
        
        Args:
            news_data: 新闻创建数据
            
        Returns:
            创建的新闻响应
        """
        # 处理数据
        processed_data = self.data_processor.process_news_item(news_data.dict())
        
        # 检查重复
        duplicate_result = self.deduplicator.check_duplicate(processed_data)
        
        # 创建新闻记录
        news = News(
            title=processed_data['title'],
            content=processed_data.get('content'),
            summary=processed_data.get('summary'),
            source=news_data.source,
            source_url=news_data.source_url,
            source_id=news_data.source_id,
            published_at=news_data.published_at,
            category=NewsCategory(processed_data['category']),
            tags=json.dumps(processed_data['tags']) if processed_data['tags'] else None,
            importance_score=processed_data['importance_score'],
            sentiment=NewsSentiment(processed_data['sentiment']),
            entities=json.dumps(processed_data['entities']) if processed_data['entities'] else None,
            companies=json.dumps(processed_data['companies']) if processed_data['companies'] else None,
            stock_codes=json.dumps(processed_data['stock_codes']) if processed_data['stock_codes'] else None,
            content_hash=processed_data['content_hash'],
            word_count=processed_data['word_count'],
            is_processed=1,
            is_duplicate=1 if duplicate_result['is_duplicate'] else 0
        )
        
        self.db.add(news)
        self.db.commit()
        self.db.refresh(news)
        
        logger.info(f"创建新闻: {news.title[:50]}... (ID: {news.id})")
        
        return self._to_response(news)
    
    def get_news(self, news_id: int) -> Optional[NewsResponse]:
        """
        获取单个新闻
        
        Args:
            news_id: 新闻ID
            
        Returns:
            新闻响应或None
        """
        news = self.db.query(News).filter(News.id == news_id).first()
        if not news:
            return None
        
        return self._to_response(news)
    
    def update_news(self, news_id: int, update_data: NewsUpdate) -> Optional[NewsResponse]:
        """
        更新新闻
        
        Args:
            news_id: 新闻ID
            update_data: 更新数据
            
        Returns:
            更新后的新闻响应或None
        """
        news = self.db.query(News).filter(News.id == news_id).first()
        if not news:
            return None
        
        # 更新字段
        update_dict = update_data.dict(exclude_unset=True)
        
        for field, value in update_dict.items():
            if field in ['tags', 'entities', 'companies', 'stock_codes'] and value is not None:
                setattr(news, field, json.dumps(value))
            elif field == 'is_processed' and value is not None:
                setattr(news, field, 1 if value else 0)
            else:
                setattr(news, field, value)
        
        self.db.commit()
        self.db.refresh(news)
        
        logger.info(f"更新新闻: {news.title[:50]}... (ID: {news.id})")
        
        return self._to_response(news)
    
    def delete_news(self, news_id: int) -> bool:
        """
        删除新闻
        
        Args:
            news_id: 新闻ID
            
        Returns:
            是否删除成功
        """
        news = self.db.query(News).filter(News.id == news_id).first()
        if not news:
            return False
        
        self.db.delete(news)
        self.db.commit()
        
        logger.info(f"删除新闻: {news.title[:50]}... (ID: {news.id})")
        
        return True
    
    def search_news(self, search_request: NewsSearchRequest) -> NewsListResponse:
        """
        搜索新闻
        
        Args:
            search_request: 搜索请求
            
        Returns:
            新闻列表响应
        """
        query = self.db.query(News)
        
        # 关键词搜索
        if search_request.q:
            query = query.filter(
                or_(
                    News.title.contains(search_request.q),
                    News.content.contains(search_request.q)
                )
            )
        
        # 来源过滤
        if search_request.source:
            query = query.filter(News.source == search_request.source)
        
        # 分类过滤
        if search_request.category:
            query = query.filter(News.category == search_request.category)
        
        # 日期范围过滤
        if search_request.start_date:
            query = query.filter(News.published_at >= search_request.start_date)
        
        if search_request.end_date:
            query = query.filter(News.published_at <= search_request.end_date)
        
        # 重要性过滤
        if search_request.min_importance:
            query = query.filter(News.importance_score >= search_request.min_importance)
        
        # 情感过滤
        if search_request.sentiment:
            query = query.filter(News.sentiment == search_request.sentiment)
        
        # 公司过滤
        if search_request.companies:
            for company in search_request.companies:
                query = query.filter(News.companies.contains(company))
        
        # 股票代码过滤
        if search_request.stock_codes:
            for code in search_request.stock_codes:
                query = query.filter(News.stock_codes.contains(code))
        
        # 标签过滤
        if search_request.tags:
            for tag in search_request.tags:
                query = query.filter(News.tags.contains(tag))
        
        # 排序
        if search_request.sort_by == 'published_at':
            order_field = News.published_at
        elif search_request.sort_by == 'created_at':
            order_field = News.created_at
        elif search_request.sort_by == 'importance_score':
            order_field = News.importance_score
        elif search_request.sort_by == 'word_count':
            order_field = News.word_count
        else:
            order_field = News.published_at
        
        if search_request.sort_order == 'asc':
            query = query.order_by(asc(order_field))
        else:
            query = query.order_by(desc(order_field))
        
        # 计算总数
        total = query.count()
        
        # 分页
        offset = (search_request.page - 1) * search_request.size
        news_list = query.offset(offset).limit(search_request.size).all()
        
        # 判断是否有下一页
        has_next = total > search_request.page * search_request.size
        
        return NewsListResponse(
            news=[self._to_response(news) for news in news_list],
            total=total,
            page=search_request.page,
            size=search_request.size,
            has_next=has_next
        )
    
    def get_news_stats(self) -> NewsStats:
        """
        获取新闻统计信息
        
        Returns:
            新闻统计信息
        """
        # 总新闻数
        total_news = self.db.query(func.count(News.id)).scalar()
        
        # 今日新闻数
        today = datetime.now().date()
        today_news = self.db.query(func.count(News.id)).filter(
            func.date(News.created_at) == today
        ).scalar()
        
        # 已处理新闻数
        processed_news = self.db.query(func.count(News.id)).filter(
            News.is_processed == 1
        ).scalar()
        
        # 重复新闻数
        duplicate_news = self.db.query(func.count(News.id)).filter(
            News.is_duplicate == 1
        ).scalar()
        
        # 各来源统计
        source_stats = {}
        source_results = self.db.query(
            News.source, func.count(News.id)
        ).group_by(News.source).all()
        
        for source, count in source_results:
            source_stats[source.value] = count
        
        # 各分类统计
        category_stats = {}
        category_results = self.db.query(
            News.category, func.count(News.id)
        ).group_by(News.category).all()
        
        for category, count in category_results:
            category_stats[category.value] = count
        
        # 情感统计
        sentiment_stats = {}
        sentiment_results = self.db.query(
            News.sentiment, func.count(News.id)
        ).group_by(News.sentiment).all()
        
        for sentiment, count in sentiment_results:
            sentiment_stats[sentiment.value] = count
        
        return NewsStats(
            total_news=total_news or 0,
            today_news=today_news or 0,
            processed_news=processed_news or 0,
            duplicate_news=duplicate_news or 0,
            source_stats=source_stats,
            category_stats=category_stats,
            sentiment_stats=sentiment_stats
        )
    
    def batch_create_news(self, news_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        批量创建新闻
        
        Args:
            news_list: 新闻数据列表
            
        Returns:
            批量创建结果
        """
        logger.info(f"开始批量创建新闻，共 {len(news_list)} 条")
        
        # 批量处理数据
        processed_items = []
        for item in news_list:
            processed_item = self.data_processor.process_news_item(item)
            processed_items.append(processed_item)
        
        # 批量去重
        unique_items, duplicate_items = self.deduplicator.deduplicate_batch(processed_items)
        
        # 批量插入数据库
        news_objects = []
        for item in unique_items + duplicate_items:
            news = News(
                title=item['title'],
                content=item.get('content'),
                summary=item.get('summary'),
                source=NewsSource(item['source']),
                source_url=item.get('source_url'),
                source_id=item.get('source_id'),
                published_at=item.get('published_at'),
                category=NewsCategory(item['category']),
                tags=json.dumps(item['tags']) if item['tags'] else None,
                importance_score=item['importance_score'],
                sentiment=NewsSentiment(item['sentiment']),
                entities=json.dumps(item['entities']) if item['entities'] else None,
                companies=json.dumps(item['companies']) if item['companies'] else None,
                stock_codes=json.dumps(item['stock_codes']) if item['stock_codes'] else None,
                content_hash=item['content_hash'],
                word_count=item['word_count'],
                is_processed=1,
                is_duplicate=1 if item['is_duplicate'] else 0
            )
            news_objects.append(news)
        
        # 批量插入
        self.db.bulk_save_objects(news_objects)
        self.db.commit()
        
        result = {
            'total_processed': len(news_list),
            'unique_items': len(unique_items),
            'duplicate_items': len(duplicate_items),
            'success_rate': len(unique_items) / len(news_list) if news_list else 0
        }
        
        logger.info(f"批量创建完成: {result}")
        
        return result
    
    def _to_response(self, news: News) -> NewsResponse:
        """
        将数据库模型转换为响应模型
        
        Args:
            news: 新闻数据库模型
            
        Returns:
            新闻响应模型
        """
        # 解析JSON字段
        tags = json.loads(news.tags) if news.tags else []
        entities = json.loads(news.entities) if news.entities else {}
        companies = json.loads(news.companies) if news.companies else []
        stock_codes = json.loads(news.stock_codes) if news.stock_codes else []
        
        return NewsResponse(
            id=news.id,
            title=news.title,
            content=news.content,
            summary=news.summary,
            source=news.source,
            source_url=news.source_url,
            source_id=news.source_id,
            published_at=news.published_at,
            created_at=news.created_at,
            updated_at=news.updated_at,
            category=news.category,
            tags=tags,
            importance_score=news.importance_score,
            sentiment=news.sentiment,
            entities=entities,
            companies=companies,
            stock_codes=stock_codes,
            content_hash=news.content_hash,
            word_count=news.word_count,
            is_processed=bool(news.is_processed),
            is_duplicate=bool(news.is_duplicate)
        )

    @cache_result("news_list", expire=settings.CACHE_NEWS_LIST_EXPIRE)
    def get_news_list(self, page: int = 1, limit: int = 20, filters: Dict = None,
                     sort_by: str = "published_at", sort_order: str = "desc"):
        """
        获取新闻列表，支持分页、过滤和排序
        """
        query = self.db.query(News)

        # 应用过滤条件
        if filters:
            if filters.get('source'):
                query = query.filter(News.source == filters['source'])
            if filters.get('category'):
                query = query.filter(News.category == filters['category'])
            if filters.get('start_date'):
                query = query.filter(News.published_at >= filters['start_date'])
            if filters.get('end_date'):
                query = query.filter(News.published_at <= filters['end_date'])
            if filters.get('importance_min') is not None:
                query = query.filter(News.importance_score >= filters['importance_min'])

        # 排序
        if sort_order.lower() == 'desc':
            query = query.order_by(desc(getattr(News, sort_by)))
        else:
            query = query.order_by(asc(getattr(News, sort_by)))

        # 获取总数
        total = query.count()

        # 分页
        offset = (page - 1) * limit
        news_list = query.offset(offset).limit(limit).all()

        return news_list, total

    @cache_result("news_search", expire=settings.CACHE_SEARCH_EXPIRE)
    def search_news(self, search_params: NewsSearchParams):
        """
        搜索新闻，支持全文搜索
        """
        query = self.db.query(News)

        # 关键词搜索
        if search_params.query:
            if search_params.search_in == "title":
                query = query.filter(News.title.contains(search_params.query))
            elif search_params.search_in == "content":
                query = query.filter(News.content.contains(search_params.query))
            else:  # all
                query = query.filter(
                    or_(
                        News.title.contains(search_params.query),
                        News.content.contains(search_params.query),
                        News.summary.contains(search_params.query)
                    )
                )

        # 应用其他过滤条件
        if search_params.source:
            query = query.filter(News.source == search_params.source)
        if search_params.category:
            query = query.filter(News.category == search_params.category)
        if search_params.start_date:
            query = query.filter(News.published_at >= search_params.start_date)
        if search_params.end_date:
            query = query.filter(News.published_at <= search_params.end_date)

        # 排序
        if search_params.sort_by == "relevance":
            # 简单的相关性排序：按重要性评分和发布时间
            query = query.order_by(desc(News.importance_score), desc(News.published_at))
        else:
            if search_params.sort_order.lower() == 'desc':
                query = query.order_by(desc(getattr(News, search_params.sort_by)))
            else:
                query = query.order_by(asc(getattr(News, search_params.sort_by)))

        # 获取总数
        total = query.count()

        # 分页
        offset = (search_params.page - 1) * search_params.limit
        results = query.offset(offset).limit(search_params.limit).all()

        return results, total

    @cache_result("news_stats", expire=settings.CACHE_NEWS_STATS_EXPIRE)
    def get_news_statistics(self, start_date: datetime, end_date: datetime) -> Dict:
        """
        获取新闻统计数据
        """
        # 基础统计
        total_query = self.db.query(News).filter(
            News.created_at >= start_date,
            News.created_at <= end_date
        )

        total_count = total_query.count()
        processed_count = total_query.filter(News.is_processed == 1).count()
        duplicate_count = total_query.filter(News.is_duplicate == 1).count()

        # 今日新闻数
        today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        today_count = self.db.query(News).filter(News.created_at >= today_start).count()

        # 平均重要性评分
        avg_importance = self.db.query(func.avg(News.importance_score)).filter(
            News.created_at >= start_date,
            News.created_at <= end_date
        ).scalar() or 0

        # 按来源统计
        source_stats = dict(
            self.db.query(News.source, func.count(News.id))
            .filter(News.created_at >= start_date, News.created_at <= end_date)
            .group_by(News.source)
            .all()
        )

        # 按分类统计
        category_stats = dict(
            self.db.query(News.category, func.count(News.id))
            .filter(News.created_at >= start_date, News.created_at <= end_date)
            .group_by(News.category)
            .all()
        )

        # 按情感统计
        sentiment_stats = dict(
            self.db.query(News.sentiment, func.count(News.id))
            .filter(News.created_at >= start_date, News.created_at <= end_date)
            .group_by(News.sentiment)
            .all()
        )

        return {
            "total_count": total_count,
            "today_count": today_count,
            "processed_count": processed_count,
            "duplicate_count": duplicate_count,
            "avg_importance": round(float(avg_importance), 2),
            "source_distribution": source_stats,
            "category_distribution": category_stats,
            "sentiment_distribution": sentiment_stats,
            "period_start": start_date,
            "period_end": end_date
        }

    def get_statistics_by_source(self, start_date: datetime, end_date: datetime) -> List[Dict]:
        """
        按来源统计新闻数据
        """
        results = (
            self.db.query(
                News.source,
                func.count(News.id).label('count'),
                func.avg(News.importance_score).label('avg_importance'),
                func.sum(func.case([(News.is_processed == 1, 1)], else_=0)).label('processed'),
                func.sum(func.case([(News.is_duplicate == 1, 1)], else_=0)).label('duplicates')
            )
            .filter(News.created_at >= start_date, News.created_at <= end_date)
            .group_by(News.source)
            .all()
        )

        return [
            {
                "source": result.source,
                "count": result.count,
                "avg_importance": round(float(result.avg_importance or 0), 2),
                "processed": result.processed,
                "duplicates": result.duplicates
            }
            for result in results
        ]

    def get_statistics_by_category(self, start_date: datetime, end_date: datetime) -> List[Dict]:
        """
        按分类统计新闻数据
        """
        results = (
            self.db.query(
                News.category,
                func.count(News.id).label('count'),
                func.avg(News.importance_score).label('avg_importance')
            )
            .filter(News.created_at >= start_date, News.created_at <= end_date)
            .group_by(News.category)
            .all()
        )

        return [
            {
                "category": result.category,
                "count": result.count,
                "avg_importance": round(float(result.avg_importance or 0), 2)
            }
            for result in results
        ]

    def get_timeline_statistics(self, start_date: datetime, end_date: datetime, interval: str) -> List[Dict]:
        """
        获取时间线统计数据
        """
        if interval == "hour":
            date_format = "%Y-%m-%d %H:00:00"
        elif interval == "day":
            date_format = "%Y-%m-%d"
        else:  # week
            date_format = "%Y-%u"

        results = (
            self.db.query(
                func.date_format(News.created_at, date_format).label('period'),
                func.count(News.id).label('count')
            )
            .filter(News.created_at >= start_date, News.created_at <= end_date)
            .group_by(func.date_format(News.created_at, date_format))
            .order_by(func.date_format(News.created_at, date_format))
            .all()
        )

        return [
            {
                "period": result.period,
                "count": result.count
            }
            for result in results
        ]

    def get_trending_news(self, start_time: datetime, limit: int = 10) -> List[News]:
        """
        获取热门新闻（按重要性评分和时间排序）
        """
        return (
            self.db.query(News)
            .filter(News.created_at >= start_time)
            .order_by(desc(News.importance_score), desc(News.created_at))
            .limit(limit)
            .all()
        )

    def get_all_sources(self) -> List[str]:
        """
        获取所有新闻来源
        """
        results = self.db.query(News.source).distinct().all()
        return [result.source for result in results]

    def get_all_categories(self) -> List[str]:
        """
        获取所有新闻分类
        """
        results = self.db.query(News.category).distinct().all()
        return [result.category for result in results]

    def batch_operations(self, operation: str, news_ids: List[int], value: str = None) -> int:
        """
        批量操作新闻
        """
        query = self.db.query(News).filter(News.id.in_(news_ids))

        if operation == "delete":
            count = query.count()
            query.delete(synchronize_session=False)
        elif operation == "update_category" and value:
            count = query.update({"category": value}, synchronize_session=False)
        elif operation == "update_importance" and value:
            try:
                importance_score = int(value)
                count = query.update({"importance_score": importance_score}, synchronize_session=False)
            except ValueError:
                raise ValueError("重要性评分必须是整数")
        else:
            raise ValueError(f"不支持的操作: {operation}")

        self.db.commit()
        return count

    def create_news(self, news_data: Dict) -> News:
        """
        创建新闻记录
        """
        # 处理JSON字段
        if 'entities' in news_data and news_data['entities']:
            news_data['entities'] = json.dumps(news_data['entities'])
        if 'companies' in news_data and news_data['companies']:
            news_data['companies'] = json.dumps(news_data['companies'])
        if 'stock_codes' in news_data and news_data['stock_codes']:
            news_data['stock_codes'] = json.dumps(news_data['stock_codes'])
        if 'tags' in news_data and news_data['tags']:
            news_data['tags'] = json.dumps(news_data['tags'])

        # 创建新闻对象
        news = News(**news_data)

        self.db.add(news)
        self.db.commit()
        self.db.refresh(news)

        # 清除相关缓存
        self._invalidate_news_caches()

        logger.info(f"创建新闻: {news.title[:50]}... (ID: {news.id})")

        return news

    def find_duplicate(self, title: str, content: str, source_url: str) -> Optional[News]:
        """
        查找重复新闻
        """
        # 先按标题查找
        existing = self.db.query(News).filter(News.title == title).first()
        if existing:
            return existing

        # 按URL查找
        if source_url:
            existing = self.db.query(News).filter(News.source_url == source_url).first()
            if existing:
                return existing

        return None

    def _invalidate_news_caches(self):
        """清除新闻相关缓存"""
        try:
            # 清除新闻列表缓存
            invalidate_cache_pattern("news_list:*")
            # 清除搜索缓存
            invalidate_cache_pattern("news_search:*")
            # 清除统计缓存
            invalidate_cache_pattern("news_stats:*")
            logger.debug("已清除新闻相关缓存")
        except Exception as e:
            logger.error(f"清除缓存失败: {str(e)}")
