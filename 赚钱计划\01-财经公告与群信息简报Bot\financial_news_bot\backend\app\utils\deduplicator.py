import hashlib
import jieba
from typing import List, Dict, Any, Set, Tuple
from difflib import SequenceMatcher
import logging

logger = logging.getLogger(__name__)


class NewsDeduplicator:
    """新闻去重工具类"""
    
    def __init__(self, similarity_threshold: float = 0.8):
        """
        初始化去重器
        
        Args:
            similarity_threshold: 相似度阈值，超过此值认为是重复内容
        """
        self.similarity_threshold = similarity_threshold
        self.processed_hashes: Set[str] = set()
        self.processed_items: List[Dict[str, Any]] = []
        
        # 初始化jieba分词
        jieba.initialize()
    
    def generate_content_hash(self, title: str, content: str = "") -> str:
        """
        生成内容哈希值
        
        Args:
            title: 标题
            content: 内容
            
        Returns:
            SHA256哈希值
        """
        # 清理并标准化文本
        clean_title = self._normalize_text(title)
        clean_content = self._normalize_text(content)
        
        # 组合标题和内容的前500字符
        combined = clean_title + clean_content[:500]
        
        return hashlib.sha256(combined.encode('utf-8')).hexdigest()
    
    def _normalize_text(self, text: str) -> str:
        """
        标准化文本，用于比较
        
        Args:
            text: 原始文本
            
        Returns:
            标准化后的文本
        """
        if not text:
            return ""
        
        # 转换为小写
        text = text.lower()
        
        # 移除标点符号和空白字符
        import re
        text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9]', '', text)
        
        return text
    
    def _extract_keywords(self, text: str, top_k: int = 20) -> List[str]:
        """
        提取关键词
        
        Args:
            text: 文本内容
            top_k: 返回前k个关键词
            
        Returns:
            关键词列表
        """
        if not text:
            return []
        
        # 使用jieba分词
        words = jieba.lcut(text)
        
        # 过滤停用词和短词
        stopwords = {'的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这'}
        keywords = [word for word in words if len(word) > 1 and word not in stopwords]
        
        # 统计词频并返回前k个
        from collections import Counter
        word_counts = Counter(keywords)
        
        return [word for word, count in word_counts.most_common(top_k)]
    
    def calculate_text_similarity(self, text1: str, text2: str) -> float:
        """
        计算两个文本的相似度
        
        Args:
            text1: 文本1
            text2: 文本2
            
        Returns:
            相似度 (0-1)
        """
        if not text1 or not text2:
            return 0.0
        
        # 标准化文本
        norm_text1 = self._normalize_text(text1)
        norm_text2 = self._normalize_text(text2)
        
        # 使用SequenceMatcher计算相似度
        similarity = SequenceMatcher(None, norm_text1, norm_text2).ratio()
        
        return similarity
    
    def calculate_keyword_similarity(self, text1: str, text2: str) -> float:
        """
        基于关键词计算相似度
        
        Args:
            text1: 文本1
            text2: 文本2
            
        Returns:
            关键词相似度 (0-1)
        """
        keywords1 = set(self._extract_keywords(text1))
        keywords2 = set(self._extract_keywords(text2))
        
        if not keywords1 or not keywords2:
            return 0.0
        
        # 计算Jaccard相似度
        intersection = len(keywords1.intersection(keywords2))
        union = len(keywords1.union(keywords2))
        
        return intersection / union if union > 0 else 0.0
    
    def calculate_combined_similarity(self, item1: Dict[str, Any], item2: Dict[str, Any]) -> float:
        """
        计算综合相似度
        
        Args:
            item1: 新闻项目1
            item2: 新闻项目2
            
        Returns:
            综合相似度 (0-1)
        """
        title1 = item1.get('title', '')
        title2 = item2.get('title', '')
        content1 = item1.get('content', '')
        content2 = item2.get('content', '')
        
        # 标题相似度（权重0.6）
        title_similarity = self.calculate_text_similarity(title1, title2)
        
        # 内容相似度（权重0.3）
        content_similarity = self.calculate_text_similarity(content1, content2)
        
        # 关键词相似度（权重0.1）
        keyword_similarity = self.calculate_keyword_similarity(
            title1 + " " + content1, 
            title2 + " " + content2
        )
        
        # 综合相似度
        combined_similarity = (
            title_similarity * 0.6 + 
            content_similarity * 0.3 + 
            keyword_similarity * 0.1
        )
        
        return combined_similarity
    
    def is_duplicate_by_hash(self, item: Dict[str, Any]) -> bool:
        """
        基于哈希值判断是否重复
        
        Args:
            item: 新闻项目
            
        Returns:
            是否重复
        """
        content_hash = item.get('content_hash')
        if not content_hash:
            # 如果没有哈希值，生成一个
            content_hash = self.generate_content_hash(
                item.get('title', ''), 
                item.get('content', '')
            )
            item['content_hash'] = content_hash
        
        if content_hash in self.processed_hashes:
            return True
        
        self.processed_hashes.add(content_hash)
        return False
    
    def find_similar_items(self, item: Dict[str, Any]) -> List[Tuple[Dict[str, Any], float]]:
        """
        查找相似的新闻项目
        
        Args:
            item: 待检查的新闻项目
            
        Returns:
            相似项目列表，每个元素为(项目, 相似度)
        """
        similar_items = []
        
        for processed_item in self.processed_items:
            similarity = self.calculate_combined_similarity(item, processed_item)
            if similarity >= self.similarity_threshold:
                similar_items.append((processed_item, similarity))
        
        # 按相似度降序排序
        similar_items.sort(key=lambda x: x[1], reverse=True)
        
        return similar_items
    
    def is_duplicate_by_similarity(self, item: Dict[str, Any]) -> Tuple[bool, List[Tuple[Dict[str, Any], float]]]:
        """
        基于相似度判断是否重复
        
        Args:
            item: 新闻项目
            
        Returns:
            (是否重复, 相似项目列表)
        """
        similar_items = self.find_similar_items(item)
        is_duplicate = len(similar_items) > 0
        
        return is_duplicate, similar_items
    
    def check_duplicate(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """
        检查新闻项目是否重复
        
        Args:
            item: 新闻项目
            
        Returns:
            检查结果，包含是否重复和相似项目信息
        """
        result = {
            'is_duplicate': False,
            'duplicate_type': None,
            'similar_items': [],
            'similarity_score': 0.0
        }
        
        # 首先检查哈希值
        if self.is_duplicate_by_hash(item):
            result['is_duplicate'] = True
            result['duplicate_type'] = 'exact'
            logger.debug(f"发现完全重复项目: {item.get('title', '')[:50]}...")
            return result
        
        # 然后检查相似度
        is_similar, similar_items = self.is_duplicate_by_similarity(item)
        if is_similar:
            result['is_duplicate'] = True
            result['duplicate_type'] = 'similar'
            result['similar_items'] = similar_items
            result['similarity_score'] = similar_items[0][1] if similar_items else 0.0
            logger.debug(f"发现相似重复项目: {item.get('title', '')[:50]}... "
                        f"(相似度: {result['similarity_score']:.3f})")
        else:
            # 不重复，添加到已处理列表
            self.processed_items.append(item)
        
        return result
    
    def deduplicate_batch(self, items: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """
        批量去重
        
        Args:
            items: 新闻项目列表
            
        Returns:
            (去重后的项目列表, 重复项目列表)
        """
        unique_items = []
        duplicate_items = []
        
        logger.info(f"开始批量去重，共 {len(items)} 个项目")
        
        for i, item in enumerate(items):
            if i % 100 == 0:
                logger.info(f"去重进度: {i}/{len(items)}")
            
            duplicate_result = self.check_duplicate(item)
            
            if duplicate_result['is_duplicate']:
                item['is_duplicate'] = True
                item['duplicate_info'] = duplicate_result
                duplicate_items.append(item)
            else:
                item['is_duplicate'] = False
                unique_items.append(item)
        
        logger.info(f"去重完成，唯一项目: {len(unique_items)}, 重复项目: {len(duplicate_items)}")
        
        return unique_items, duplicate_items
    
    def reset(self):
        """重置去重器状态"""
        self.processed_hashes.clear()
        self.processed_items.clear()
        logger.info("去重器状态已重置")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取去重统计信息"""
        return {
            'processed_hashes_count': len(self.processed_hashes),
            'processed_items_count': len(self.processed_items),
            'similarity_threshold': self.similarity_threshold
        }
