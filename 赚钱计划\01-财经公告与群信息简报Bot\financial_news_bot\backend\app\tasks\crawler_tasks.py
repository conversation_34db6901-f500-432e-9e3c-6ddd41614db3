"""
爬虫任务模块
实现异步数据采集、处理和存储任务
"""
import logging
from typing import List, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

from app.celery_app import celery_app
from app.database import get_db
from app.models.news import News
from app.services.news_service import NewsService
from app.services.data_processing_pipeline import DataProcessingPipeline
from app.utils.deduplicator import NewsDeduplicator

# 导入爬虫
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))

from crawlers.sse_crawler import SSECrawler
from crawlers.szse_crawler import SZSECrawler
from crawlers.csrc_crawler import CSRCCrawler
from crawlers.rss_crawler import RSSCrawler

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 初始化组件
data_processor = DataProcessingPipeline()
deduplicator = NewsDeduplicator()

@celery_app.task(bind=True, max_retries=3)
def crawl_sse(self):
    """
    上交所数据采集任务
    """
    try:
        logger.info("开始执行上交所数据采集任务")
        
        # 初始化爬虫
        crawler = SSECrawler()
        
        # 执行爬取
        news_data = crawler.crawl()
        
        if not news_data:
            logger.warning("上交所爬虫未获取到数据")
            return {"status": "warning", "message": "未获取到数据", "count": 0}
        
        # 处理和存储数据
        processed_count = _process_and_store_news(news_data, "SSE")
        
        logger.info(f"上交所数据采集完成，处理了 {processed_count} 条新闻")
        return {
            "status": "success", 
            "message": "上交所数据采集完成",
            "count": processed_count,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as exc:
        logger.error(f"上交所数据采集失败: {str(exc)}")
        # 重试机制
        if self.request.retries < self.max_retries:
            logger.info(f"将在60秒后重试，当前重试次数: {self.request.retries + 1}")
            raise self.retry(countdown=60, exc=exc)
        else:
            return {
                "status": "error",
                "message": f"上交所数据采集失败: {str(exc)}",
                "count": 0
            }

@celery_app.task(bind=True, max_retries=3)
def crawl_szse(self):
    """
    深交所数据采集任务
    """
    try:
        logger.info("开始执行深交所数据采集任务")
        
        # 初始化爬虫
        crawler = SZSECrawler()
        
        # 执行爬取
        news_data = crawler.crawl()
        
        if not news_data:
            logger.warning("深交所爬虫未获取到数据")
            return {"status": "warning", "message": "未获取到数据", "count": 0}
        
        # 处理和存储数据
        processed_count = _process_and_store_news(news_data, "SZSE")
        
        logger.info(f"深交所数据采集完成，处理了 {processed_count} 条新闻")
        return {
            "status": "success",
            "message": "深交所数据采集完成", 
            "count": processed_count,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as exc:
        logger.error(f"深交所数据采集失败: {str(exc)}")
        if self.request.retries < self.max_retries:
            logger.info(f"将在60秒后重试，当前重试次数: {self.request.retries + 1}")
            raise self.retry(countdown=60, exc=exc)
        else:
            return {
                "status": "error",
                "message": f"深交所数据采集失败: {str(exc)}",
                "count": 0
            }

@celery_app.task(bind=True, max_retries=3)
def crawl_csrc(self):
    """
    证监会数据采集任务
    """
    try:
        logger.info("开始执行证监会数据采集任务")
        
        # 初始化爬虫
        crawler = CSRCCrawler()
        
        # 执行爬取
        news_data = crawler.crawl()
        
        if not news_data:
            logger.warning("证监会爬虫未获取到数据")
            return {"status": "warning", "message": "未获取到数据", "count": 0}
        
        # 处理和存储数据
        processed_count = _process_and_store_news(news_data, "CSRC")
        
        logger.info(f"证监会数据采集完成，处理了 {processed_count} 条新闻")
        return {
            "status": "success",
            "message": "证监会数据采集完成",
            "count": processed_count,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as exc:
        logger.error(f"证监会数据采集失败: {str(exc)}")
        if self.request.retries < self.max_retries:
            logger.info(f"将在60秒后重试，当前重试次数: {self.request.retries + 1}")
            raise self.retry(countdown=60, exc=exc)
        else:
            return {
                "status": "error",
                "message": f"证监会数据采集失败: {str(exc)}",
                "count": 0
            }

@celery_app.task(bind=True, max_retries=3)
def crawl_rss(self):
    """
    RSS数据采集任务
    """
    try:
        logger.info("开始执行RSS数据采集任务")

        # 初始化爬虫
        crawler = RSSCrawler()

        # 执行爬取
        news_data = crawler.crawl()

        if not news_data:
            logger.warning("RSS爬虫未获取到数据")
            return {"status": "warning", "message": "未获取到数据", "count": 0}

        # 处理和存储数据
        processed_count = _process_and_store_news(news_data, "RSS")

        logger.info(f"RSS数据采集完成，处理了 {processed_count} 条新闻")
        return {
            "status": "success",
            "message": "RSS数据采集完成",
            "count": processed_count,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as exc:
        logger.error(f"RSS数据采集失败: {str(exc)}")
        if self.request.retries < self.max_retries:
            logger.info(f"将在60秒后重试，当前重试次数: {self.request.retries + 1}")
            raise self.retry(countdown=60, exc=exc)
        else:
            return {
                "status": "error",
                "message": f"RSS数据采集失败: {str(exc)}",
                "count": 0
            }

@celery_app.task(bind=True, max_retries=3)
def process_news_batch(self):
    """
    批量处理新闻任务
    处理未处理的新闻数据，进行去重、分类、摘要等操作
    """
    try:
        logger.info("开始执行批量新闻处理任务")

        # 获取数据库会话
        db = next(get_db())
        news_service = NewsService(db)

        # 获取最近1小时内未处理的新闻
        cutoff_time = datetime.now() - timedelta(hours=1)
        unprocessed_news = db.query(News).filter(
            News.created_at >= cutoff_time,
            News.summary.is_(None)  # 未生成摘要的新闻
        ).limit(100).all()

        if not unprocessed_news:
            logger.info("没有需要处理的新闻")
            return {"status": "success", "message": "没有需要处理的新闻", "count": 0}

        processed_count = 0
        for news in unprocessed_news:
            try:
                # 生成摘要
                if not news.summary and news.content:
                    summary = data_processor.generate_summary(news.content)
                    news.summary = summary

                # 提取实体
                if not news.entities and news.content:
                    entities = data_processor.extract_entities(news.content)
                    news.entities = entities

                # 情感分析
                if not news.sentiment and news.content:
                    sentiment = data_processor.analyze_sentiment(news.content)
                    news.sentiment = sentiment

                # 重要性评分
                if news.importance_score == 0:
                    importance = data_processor.calculate_importance(news.title, news.content)
                    news.importance_score = importance

                processed_count += 1

            except Exception as e:
                logger.error(f"处理新闻 {news.id} 时出错: {str(e)}")
                continue

        # 提交更改
        db.commit()

        logger.info(f"批量新闻处理完成，处理了 {processed_count} 条新闻")
        return {
            "status": "success",
            "message": "批量新闻处理完成",
            "count": processed_count,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as exc:
        logger.error(f"批量新闻处理失败: {str(exc)}")
        if self.request.retries < self.max_retries:
            logger.info(f"将在60秒后重试，当前重试次数: {self.request.retries + 1}")
            raise self.retry(countdown=60, exc=exc)
        else:
            return {
                "status": "error",
                "message": f"批量新闻处理失败: {str(exc)}",
                "count": 0
            }
    finally:
        if 'db' in locals():
            db.close()

def _process_and_store_news(news_data: List[Dict[str, Any]], source: str) -> int:
    """
    处理和存储新闻数据的辅助函数

    Args:
        news_data: 新闻数据列表
        source: 数据源标识

    Returns:
        处理的新闻数量
    """
    if not news_data:
        return 0

    try:
        # 获取数据库会话
        db = next(get_db())
        news_service = NewsService(db)

        processed_count = 0

        for item in news_data:
            try:
                # 数据预处理
                processed_item = data_processor.process_news_item(item)

                # 检查是否已存在（去重）
                existing_news = news_service.find_duplicate(
                    title=processed_item.get('title', ''),
                    content=processed_item.get('content', ''),
                    source_url=processed_item.get('source_url', '')
                )

                if existing_news:
                    logger.debug(f"新闻已存在，跳过: {processed_item.get('title', '')[:50]}")
                    continue

                # 创建新闻记录
                news_data_dict = {
                    'title': processed_item.get('title', ''),
                    'content': processed_item.get('content', ''),
                    'source': source,
                    'source_url': processed_item.get('source_url', ''),
                    'published_at': processed_item.get('published_at'),
                    'category': processed_item.get('category', ''),
                    'importance_score': processed_item.get('importance_score', 0),
                    'entities': processed_item.get('entities', {}),
                    'sentiment': processed_item.get('sentiment', 'neutral')
                }

                # 生成摘要
                if news_data_dict['content']:
                    summary = data_processor.generate_summary(news_data_dict['content'])
                    news_data_dict['summary'] = summary

                # 保存到数据库
                news_service.create_news(news_data_dict)
                processed_count += 1

                logger.debug(f"成功处理新闻: {news_data_dict['title'][:50]}")

            except Exception as e:
                logger.error(f"处理单条新闻时出错: {str(e)}")
                continue

        # 提交事务
        db.commit()

        return processed_count

    except Exception as e:
        logger.error(f"批量处理新闻时出错: {str(e)}")
        if 'db' in locals():
            db.rollback()
        return 0
    finally:
        if 'db' in locals():
            db.close()

@celery_app.task(bind=True)
def cleanup_old_news(self, days: int = 30):
    """
    清理旧新闻数据任务

    Args:
        days: 保留天数，默认30天
    """
    try:
        logger.info(f"开始清理 {days} 天前的旧新闻数据")

        # 获取数据库会话
        db = next(get_db())

        # 计算截止时间
        cutoff_date = datetime.now() - timedelta(days=days)

        # 删除旧新闻
        deleted_count = db.query(News).filter(
            News.created_at < cutoff_date
        ).delete()

        db.commit()

        logger.info(f"清理完成，删除了 {deleted_count} 条旧新闻")
        return {
            "status": "success",
            "message": f"清理完成，删除了 {deleted_count} 条旧新闻",
            "count": deleted_count
        }

    except Exception as exc:
        logger.error(f"清理旧新闻失败: {str(exc)}")
        if 'db' in locals():
            db.rollback()
        return {
            "status": "error",
            "message": f"清理旧新闻失败: {str(exc)}",
            "count": 0
        }
    finally:
        if 'db' in locals():
            db.close()

@celery_app.task(bind=True)
def health_check_crawlers(self):
    """
    爬虫健康检查任务
    检查各个爬虫的运行状态
    """
    try:
        logger.info("开始执行爬虫健康检查")

        results = {}
        crawlers = {
            'SSE': SSECrawler(),
            'SZSE': SZSECrawler(),
            'CSRC': CSRCCrawler(),
            'RSS': RSSCrawler()
        }

        for name, crawler in crawlers.items():
            try:
                # 执行简单的连通性测试
                test_result = crawler.test_connection()
                results[name] = {
                    "status": "healthy" if test_result else "unhealthy",
                    "timestamp": datetime.now().isoformat()
                }
            except Exception as e:
                results[name] = {
                    "status": "error",
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                }

        # 统计健康状态
        healthy_count = sum(1 for r in results.values() if r["status"] == "healthy")
        total_count = len(results)

        logger.info(f"爬虫健康检查完成: {healthy_count}/{total_count} 个爬虫正常")

        return {
            "status": "success",
            "message": f"健康检查完成: {healthy_count}/{total_count} 个爬虫正常",
            "results": results,
            "healthy_count": healthy_count,
            "total_count": total_count
        }

    except Exception as exc:
        logger.error(f"爬虫健康检查失败: {str(exc)}")
        return {
            "status": "error",
            "message": f"爬虫健康检查失败: {str(exc)}"
        }
