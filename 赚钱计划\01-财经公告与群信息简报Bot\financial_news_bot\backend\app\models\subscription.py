from sqlalchemy import Column, Integer, String, JSON, Enum, TIMESTAMP, DateTime, Boolean, ForeignKey, func
from sqlalchemy.dialects.mysql import ENUM as MySQLEnum
from sqlalchemy.orm import relationship
from ..database import Base
import enum

class SubscriptionStatus(str, enum.Enum):
    ACTIVE = "active"
    PAUSED = "paused"
    INACTIVE = "inactive"

class Subscription(Base):
    __tablename__ = "subscriptions"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    name = Column(String(100), nullable=False)
    keywords = Column(JSON, nullable=True, comment="关键词列表，JSON格式")
    companies = Column(JSON, nullable=True, comment="关注公司列表，JSON格式")
    categories = Column(JSON, nullable=True, comment="新闻分类列表，JSON格式")
    sources = Column(JSON, nullable=True, comment="新闻来源列表，JSON格式")
    channels = Column(JSON, nullable=True, comment="推送渠道配置，JSON格式")
    schedule = Column(JSON, nullable=True, comment="推送时间安排，JSON格式")
    push_config = Column(JSON, nullable=True, comment="推送配置，JSON格式")
    is_active = Column(Boolean, default=True, comment="是否激活")
    last_push_at = Column(DateTime, nullable=True, comment="最后推送时间")
    status = Column(MySQLEnum(SubscriptionStatus), default=SubscriptionStatus.ACTIVE, nullable=False)
    created_at = Column(TIMESTAMP, server_default=func.now(), nullable=False)
    updated_at = Column(TIMESTAMP, server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # 关系
    user = relationship("User", back_populates="subscriptions")
    push_logs = relationship("PushLog", back_populates="subscription")
    
    def __repr__(self):
        return f"<Subscription(id={self.id}, name='{self.name}', user_id={self.user_id}, status='{self.status}')>"
