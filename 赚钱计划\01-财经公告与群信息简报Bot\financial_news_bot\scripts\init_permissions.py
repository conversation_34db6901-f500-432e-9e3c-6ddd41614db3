#!/usr/bin/env python3
"""
权限系统初始化脚本
用于初始化权限系统的基础数据和配置
"""
import sys
import os
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Optional

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'backend'))

from sqlalchemy.orm import Session
from passlib.context import CryptContext

from app.core.database import get_db, engine
from app.models.user import User, UserRole
from app.utils.permissions import ROLE_PERMISSIONS, get_permission_groups
from app.config import settings

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def hash_password(password: str) -> str:
    """加密密码"""
    return pwd_context.hash(password)


def create_default_users(db: Session):
    """创建默认用户"""
    try:
        # 检查是否已有管理员用户
        admin_user = db.query(User).filter(User.role == UserRole.ADMIN).first()
        if admin_user:
            logger.info("管理员用户已存在，跳过创建")
            return

        # 创建默认管理员
        admin_password = getattr(settings, 'DEFAULT_ADMIN_PASSWORD', 'admin123456')
        admin_user = User(
            username="admin",
            email="<EMAIL>",
            hashed_password=hash_password(admin_password),
            role=UserRole.ADMIN,
            is_active=True,
            is_verified=True,
            subscription_type="premium",
            subscription_expires_at=datetime.now() + timedelta(days=365)
        )
        
        db.add(admin_user)
        
        # 生产环境不创建测试用户 - 用户需要通过注册流程创建
        print("✅ 权限系统初始化完成，请通过注册页面创建用户账户")

        # 移除测试用户创建逻辑
        if False:  # 禁用测试用户创建
            user = User(
                username=user_data["username"],
                email=user_data["email"],
                hashed_password=hash_password("password123"),
                role=user_data["role"],
                is_active=True,
                is_verified=True,
                subscription_type=user_data["subscription_type"],
                subscription_expires_at=datetime.now() + timedelta(days=30)
            )
            db.add(user)
        
        db.commit()
        logger.info("默认用户创建成功")
        
    except Exception as e:
        logger.error(f"创建默认用户失败: {e}")
        db.rollback()
        raise


def init_permission_system(db: Session):
    """初始化权限系统"""
    try:
        logger.info("开始初始化权限系统...")
        
        # 验证权限配置
        permission_groups = get_permission_groups()
        logger.info(f"权限组配置: {list(permission_groups.keys())}")
        
        for role, permissions in ROLE_PERMISSIONS.items():
            logger.info(f"角色 {role.value} 拥有权限: {permissions}")
        
        logger.info("权限系统初始化完成")
        
    except Exception as e:
        logger.error(f"初始化权限系统失败: {e}")
        raise


def main():
    """主函数"""
    try:
        logger.info("开始初始化权限系统...")
        
        # 获取数据库连接
        db = next(get_db())
        
        try:
            # 创建默认用户
            create_default_users(db)
            
            # 初始化权限系统
            init_permission_system(db)
            
            logger.info("权限系统初始化完成！")
            
            # 输出默认用户信息
            print("\n" + "="*50)
            print("默认用户账号信息：")
            print("="*50)
            print("管理员账号:")
            print("  用户名: admin")
            print("  密码: admin123456")
            print("  角色: ADMIN")
            print()
            print("测试账号:")
            print("  高级用户: premium_user / password123")
            print("  基础用户: basic_user / password123") 
            print("  免费用户: free_user / password123")
            print("="*50)
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"初始化失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
