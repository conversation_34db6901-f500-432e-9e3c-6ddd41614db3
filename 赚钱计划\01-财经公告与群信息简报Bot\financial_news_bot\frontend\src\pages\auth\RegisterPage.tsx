import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  Form,
  Input,
  Button,
  Typography,
  Alert,
  Divider,
  Progress,
  message,
  Row,
  Col,
} from 'antd';
import {
  UserOutlined,
  MailOutlined,
  LockOutlined,
  PhoneOutlined,
  SafetyOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone,
} from '@ant-design/icons';

import { useAppDispatch, useAppSelector } from '@/store';
import { register, clearError } from '@/store/slices/authSlice';
import SocialLogin from '@/components/auth/SocialLogin';
import { RegisterRequest } from '@/types';

const { Title, Text } = Typography;

const RegisterPage: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  
  const { loading, error } = useAppSelector(state => state.auth);
  
  const [passwordStrength, setPasswordStrength] = useState(0);
  const [verificationSent, setVerificationSent] = useState(false);
  const [countdown, setCountdown] = useState(0);

  // 密码强度检查
  const checkPasswordStrength = (password: string) => {
    let strength = 0;
    if (password.length >= 8) strength += 25;
    if (/[a-z]/.test(password)) strength += 25;
    if (/[A-Z]/.test(password)) strength += 25;
    if (/[0-9]/.test(password)) strength += 25;
    if (/[^A-Za-z0-9]/.test(password)) strength += 25;
    
    setPasswordStrength(Math.min(strength, 100));
  };

  // 获取密码强度颜色
  const getPasswordStrengthColor = () => {
    if (passwordStrength < 25) return '#ff4d4f';
    if (passwordStrength < 50) return '#faad14';
    if (passwordStrength < 75) return '#1890ff';
    return '#52c41a';
  };

  // 获取密码强度文本
  const getPasswordStrengthText = () => {
    if (passwordStrength < 25) return '弱';
    if (passwordStrength < 50) return '一般';
    if (passwordStrength < 75) return '良好';
    return '强';
  };

  // 发送验证码
  const sendVerificationCode = async () => {
    const phone = form.getFieldValue('phone');
    if (!phone) {
      message.error('请先输入手机号');
      return;
    }

    try {
      // 这里调用发送验证码的API
      message.success('验证码已发送');
      setVerificationSent(true);
      setCountdown(60);

      const timer = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } catch (error) {
      message.error('验证码发送失败');
    }
  };

  // 处理表单提交
  const handleSubmit = async (values: RegisterRequest) => {
    try {
      await dispatch(register(values)).unwrap();
      message.success('注册成功！请查收邮箱验证邮件。');
      navigate('/auth/login');
    } catch (error) {
      // 错误已经在Redux中处理
    }
  };

  return (
    <div style={{ width: '100%' }}>
      <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
        <Title level={2} style={{ color: '#1890ff', marginBottom: '0.5rem' }}>
          创建账户
        </Title>
        <Text type="secondary">
          注册财经新闻Bot，开启智能财经资讯之旅
        </Text>
      </div>

      {/* 错误提示 */}
      {error && (
        <Alert
          message={error}
          type="error"
          showIcon
          closable
          onClose={() => dispatch(clearError())}
          style={{ marginBottom: '1rem' }}
        />
      )}

      {/* 注册表单 */}
      <Form
        form={form}
        name="register"
        onFinish={handleSubmit}
        autoComplete="off"
        size="large"
        layout="vertical"
      >
        <Form.Item
          name="username"
          label="用户名"
          rules={[
            { required: true, message: '请输入用户名' },
            { min: 3, max: 20, message: '用户名长度为3-20个字符' },
            { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' },
          ]}
        >
          <Input
            prefix={<UserOutlined />}
            placeholder="请输入用户名"
            autoComplete="username"
          />
        </Form.Item>

        <Form.Item
          name="email"
          label="邮箱"
          rules={[
            { required: true, message: '请输入邮箱地址' },
            { type: 'email', message: '请输入有效的邮箱地址' },
          ]}
        >
          <Input
            prefix={<MailOutlined />}
            placeholder="请输入邮箱地址"
            autoComplete="email"
          />
        </Form.Item>

        <Form.Item
          name="phone"
          label="手机号（可选）"
          rules={[
            { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号' },
          ]}
        >
          <Input
            prefix={<PhoneOutlined />}
            placeholder="请输入手机号"
            autoComplete="tel"
          />
        </Form.Item>

        <Form.Item
          name="full_name"
          label="姓名（可选）"
          rules={[
            { max: 50, message: '姓名不能超过50个字符' },
          ]}
        >
          <Input
            prefix={<UserOutlined />}
            placeholder="请输入真实姓名"
            autoComplete="name"
          />
        </Form.Item>

        <Form.Item
          name="password"
          label="密码"
          rules={[
            { required: true, message: '请输入密码' },
            { min: 8, message: '密码至少8个字符' },
            { 
              validator: (_, value) => {
                if (value && passwordStrength < 50) {
                  return Promise.reject(new Error('密码强度太弱，请使用更复杂的密码'));
                }
                return Promise.resolve();
              }
            },
          ]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="请输入密码"
            autoComplete="new-password"
            onChange={(e) => checkPasswordStrength(e.target.value)}
            iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
          />
        </Form.Item>

        {/* 密码强度指示器 */}
        {form.getFieldValue('password') && (
          <div style={{ marginBottom: '1rem' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '4px' }}>
              <Text style={{ fontSize: '12px' }}>密码强度</Text>
              <Text style={{ fontSize: '12px', color: getPasswordStrengthColor() }}>
                {getPasswordStrengthText()}
              </Text>
            </div>
            <Progress
              percent={passwordStrength}
              strokeColor={getPasswordStrengthColor()}
              showInfo={false}
              size="small"
            />
          </div>
        )}

        <Form.Item
          name="confirm_password"
          label="确认密码"
          dependencies={['password']}
          rules={[
            { required: true, message: '请确认密码' },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('password') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error('两次输入的密码不一致'));
              },
            }),
          ]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="请再次输入密码"
            autoComplete="new-password"
            iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
          />
        </Form.Item>

        {/* 验证码（如果填写了手机号） */}
        {form.getFieldValue('phone') && (
          <Form.Item
            name="verification_code"
            label="短信验证码"
            rules={[
              { required: true, message: '请输入验证码' },
              { len: 6, message: '验证码为6位数字' },
            ]}
          >
            <Row gutter={8}>
              <Col span={16}>
                <Input
                  prefix={<SafetyOutlined />}
                  placeholder="请输入验证码"
                  maxLength={6}
                />
              </Col>
              <Col span={8}>
                <Button
                  onClick={sendVerificationCode}
                  disabled={countdown > 0}
                  block
                >
                  {countdown > 0 ? `${countdown}s` : '获取验证码'}
                </Button>
              </Col>
            </Row>
          </Form.Item>
        )}

        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            block
            style={{ height: '48px', fontSize: '16px' }}
          >
            {loading ? '注册中...' : '注册账户'}
          </Button>
        </Form.Item>
      </Form>

      {/* 登录链接 */}
      <div style={{ textAlign: 'center', marginTop: '2rem' }}>
        <Text type="secondary">
          已有账户？{' '}
          <Link to="/auth/login">
            <Text type="primary">立即登录</Text>
          </Link>
        </Text>
      </div>

      {/* 第三方注册 */}
      <Divider>
        <Text type="secondary">或使用以下方式注册</Text>
      </Divider>

      <SocialLogin
        onSuccess={() => navigate('/dashboard')}
        onError={(error) => message.error(error)}
      />

      {/* 服务条款 */}
      <div style={{ textAlign: 'center', marginTop: '1rem' }}>
        <Text type="secondary" style={{ fontSize: '12px' }}>
          注册即表示您同意我们的{' '}
          <Text type="primary" style={{ cursor: 'pointer' }}>服务条款</Text>
          {' '}和{' '}
          <Text type="primary" style={{ cursor: 'pointer' }}>隐私政策</Text>
        </Text>
      </div>
    </div>
  );
};

export default RegisterPage;
