{"openapi": "3.1.0", "info": {"title": "财经新闻Bot API", "description": "智能财经新闻推送系统API", "version": "1.0.0"}, "paths": {"/api/v1/users/register": {"post": {"tags": ["用户管理"], "summary": "Register User", "description": "用户注册", "operationId": "register_user_api_v1_users_register_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCreate"}}}, "required": true}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/users/login": {"post": {"tags": ["用户管理"], "summary": "Login User", "description": "用户登录", "operationId": "login_user_api_v1_users_login_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserLogin"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Token"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/users/me": {"get": {"tags": ["用户管理"], "summary": "Get Current Active User Info", "description": "获取当前用户信息", "operationId": "get_current_active_user_info_api_v1_users_me_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}}, "security": [{"HTTPBearer": []}]}, "put": {"tags": ["用户管理"], "summary": "Update Current User", "description": "更新当前用户信息", "operationId": "update_current_user_api_v1_users_me_put", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpdate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}, "delete": {"tags": ["用户管理"], "summary": "Delete Current User", "description": "删除当前用户账户", "operationId": "delete_current_user_api_v1_users_me_delete", "responses": {"204": {"description": "Successful Response"}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/users/permissions": {"get": {"tags": ["用户管理"], "summary": "Get Current Active User Permissions", "description": "获取当前用户权限信息", "operationId": "get_current_active_user_permissions_api_v1_users_permissions_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Current Active User Permissions Api V1 Users Permissions Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/users/{user_id}": {"get": {"tags": ["用户管理"], "summary": "Get User By Id", "description": "根据ID获取用户信息（需要认证）", "operationId": "get_user_by_id_api_v1_users__user_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["用户管理"], "summary": "Delete User By Admin", "description": "删除用户（管理员权限）", "operationId": "delete_user_by_admin_api_v1_users__user_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/users/": {"get": {"tags": ["用户管理"], "summary": "List Users", "description": "获取用户列表（管理员权限）", "operationId": "list_users_api_v1_users__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserResponse"}, "title": "Response List Users Api V1 Users  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/users/{user_id}/role": {"put": {"tags": ["用户管理"], "summary": "Update User Role", "description": "更新用户角色（管理员权限）", "operationId": "update_user_role_api_v1_users__user_id__role_put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}, {"name": "new_role", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/UserRole-Input"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/subscriptions/": {"post": {"tags": ["subscriptions"], "summary": "Create Subscription", "description": "创建新订阅\n\n- **name**: 订阅名称（必填）\n- **keywords**: 关键词列表，支持权重设置\n- **companies**: 关注公司列表，支持股票代码\n- **categories**: 新闻分类列表\n- **channels**: 推送渠道配置（必填，至少一个）\n- **schedule**: 推送时间安排", "operationId": "create_subscription_api_v1_subscriptions__post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["订阅管理"], "summary": "Get User Subscriptions", "description": "获取当前用户的订阅列表（需要basic_subscription权限）", "operationId": "get_user_subscriptions_api_v1_subscriptions__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "页码", "default": 1, "title": "Page"}, "description": "页码"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "每页数量", "default": 10, "title": "Size"}, "description": "每页数量"}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/SubscriptionStatus"}, {"type": "null"}], "description": "订阅状态过滤", "title": "Status"}, "description": "订阅状态过滤"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/subscriptions/{subscription_id}": {"get": {"tags": ["订阅管理"], "summary": "Get Subscription Detail", "description": "获取订阅详情（需要basic_subscription权限）", "operationId": "get_subscription_detail_api_v1_subscriptions__subscription_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "subscription_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Subscription Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["订阅管理"], "summary": "Update Subscription", "description": "更新订阅（需要basic_subscription权限）", "operationId": "update_subscription_api_v1_subscriptions__subscription_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "subscription_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Subscription Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["订阅管理"], "summary": "Delete Subscription", "description": "删除订阅（需要basic_subscription权限）", "operationId": "delete_subscription_api_v1_subscriptions__subscription_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "subscription_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Subscription Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/subscriptions/{subscription_id}/toggle": {"post": {"tags": ["subscriptions"], "summary": "Toggle Subscription Status", "description": "切换订阅状态（激活/暂停）\n\n- **subscription_id**: 订阅ID\n- 如果当前是激活状态，则暂停；如果是暂停状态，则激活", "operationId": "toggle_subscription_status_api_v1_subscriptions__subscription_id__toggle_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "subscription_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Subscription Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/subscriptions/{subscription_id}/stats": {"get": {"tags": ["subscriptions"], "summary": "Get Subscription Stats", "description": "获取订阅统计信息\n\n- **subscription_id**: 订阅ID\n- 返回匹配新闻数、推送统计等信息", "operationId": "get_subscription_stats_api_v1_subscriptions__subscription_id__stats_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "subscription_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Subscription Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionStats"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/subscriptions/{subscription_id}/test": {"post": {"tags": ["subscriptions"], "summary": "Test Subscription Channels", "description": "测试订阅的推送渠道配置\n\n- **subscription_id**: 订阅ID\n- 发送测试消息到配置的推送渠道", "operationId": "test_subscription_channels_api_v1_subscriptions__subscription_id__test_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "subscription_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Subscription Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/tasks/status": {"get": {"tags": ["任务管理"], "summary": "获取任务系统状态", "description": "获取Celery任务系统的整体状态", "operationId": "get_task_system_status_api_v1_tasks_status_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/tasks/active": {"get": {"tags": ["任务管理"], "summary": "获取活跃任务列表", "description": "获取当前正在执行的任务列表", "operationId": "get_active_tasks_api_v1_tasks_active_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/tasks/crawlers/run": {"post": {"tags": ["任务管理"], "summary": "手动执行爬虫任务", "description": "手动触发指定的爬虫任务\n\nArgs:\n    crawler_type: 爬虫类型 (sse, szse, csrc, rss, all)", "operationId": "run_crawler_task_api_v1_tasks_crawlers_run_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "crawler_type", "in": "query", "required": true, "schema": {"type": "string", "title": "Crawler Type"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/tasks/process/news": {"post": {"tags": ["任务管理"], "summary": "手动执行新闻处理任务", "description": "手动触发新闻批量处理任务", "operationId": "run_news_processing_api_v1_tasks_process_news_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/tasks/cleanup": {"post": {"tags": ["任务管理"], "summary": "清理旧数据", "description": "清理指定天数之前的旧数据\n\nArgs:\n    days: 保留天数，默认30天", "operationId": "cleanup_old_data_api_v1_tasks_cleanup_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "days", "in": "query", "required": false, "schema": {"type": "integer", "default": 30, "title": "Days"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/tasks/health": {"get": {"tags": ["任务管理"], "summary": "爬虫健康检查", "description": "检查所有爬虫的健康状态", "operationId": "check_crawler_health_api_v1_tasks_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/tasks/result/{task_id}": {"get": {"tags": ["任务管理"], "summary": "获取任务结果", "description": "获取指定任务的执行结果\n\nArgs:\n    task_id: 任务ID", "operationId": "get_task_result_api_v1_tasks_result__task_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/tasks/cancel/{task_id}": {"delete": {"tags": ["任务管理"], "summary": "取消任务", "description": "取消指定的任务\n\nArgs:\n    task_id: 任务ID", "operationId": "cancel_task_api_v1_tasks_cancel__task_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/news/": {"get": {"tags": ["新闻管理"], "summary": "获取新闻列表", "description": "获取新闻列表，支持分页、过滤和排序", "operationId": "get_news_list_api_v1_news__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "页码", "default": 1, "title": "Page"}, "description": "页码"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "每页数量", "default": 20, "title": "Limit"}, "description": "每页数量"}, {"name": "source", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "新闻来源", "title": "Source"}, "description": "新闻来源"}, {"name": "category", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "新闻分类", "title": "Category"}, "description": "新闻分类"}, {"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "开始时间", "title": "Start Date"}, "description": "开始时间"}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "结束时间", "title": "End Date"}, "description": "结束时间"}, {"name": "importance_min", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer", "maximum": 100, "minimum": 0}, {"type": "null"}], "description": "最低重要性评分", "title": "Importance Min"}, "description": "最低重要性评分"}, {"name": "sort_by", "in": "query", "required": false, "schema": {"type": "string", "description": "排序字段", "default": "published_at", "title": "Sort By"}, "description": "排序字段"}, {"name": "sort_order", "in": "query", "required": false, "schema": {"type": "string", "pattern": "^(asc|desc)$", "description": "排序方向", "default": "desc", "title": "Sort Order"}, "description": "排序方向"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewsListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["新闻管理"], "summary": "创建新闻", "description": "创建新闻（仅管理员可用）", "operationId": "create_news_api_v1_news__post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewsCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewsResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/news/{news_id}": {"get": {"tags": ["新闻管理"], "summary": "获取新闻详情", "description": "根据ID获取新闻详情", "operationId": "get_news_detail_api_v1_news__news_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "news_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "News Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewsResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["新闻管理"], "summary": "更新新闻", "description": "更新新闻信息（仅管理员可用）", "operationId": "update_news_api_v1_news__news_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "news_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "News Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewsUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewsResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["新闻管理"], "summary": "删除新闻", "description": "删除新闻（仅管理员可用）", "operationId": "delete_news_api_v1_news__news_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "news_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "News Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/news/search/": {"get": {"tags": ["新闻管理"], "summary": "搜索新闻", "description": "全文搜索新闻，支持标题、内容搜索", "operationId": "search_news_api_v1_news_search__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "q", "in": "query", "required": true, "schema": {"type": "string", "minLength": 1, "description": "搜索关键词", "title": "Q"}, "description": "搜索关键词"}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "页码", "default": 1, "title": "Page"}, "description": "页码"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "每页数量", "default": 20, "title": "Limit"}, "description": "每页数量"}, {"name": "source", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "新闻来源", "title": "Source"}, "description": "新闻来源"}, {"name": "category", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "新闻分类", "title": "Category"}, "description": "新闻分类"}, {"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "开始时间", "title": "Start Date"}, "description": "开始时间"}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "结束时间", "title": "End Date"}, "description": "结束时间"}, {"name": "search_in", "in": "query", "required": false, "schema": {"type": "string", "pattern": "^(title|content|all)$", "description": "搜索范围", "default": "all", "title": "Search In"}, "description": "搜索范围"}, {"name": "sort_by", "in": "query", "required": false, "schema": {"type": "string", "description": "排序字段", "default": "relevance", "title": "Sort By"}, "description": "排序字段"}, {"name": "sort_order", "in": "query", "required": false, "schema": {"type": "string", "pattern": "^(asc|desc)$", "description": "排序方向", "default": "desc", "title": "Sort Order"}, "description": "排序方向"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewsSearchResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/news/statistics/overview": {"get": {"tags": ["新闻管理"], "summary": "获取新闻统计概览", "description": "获取新闻统计数据概览", "operationId": "get_news_statistics_api_v1_news_statistics_overview_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "days", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 365, "minimum": 1, "description": "统计天数", "default": 7, "title": "Days"}, "description": "统计天数"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewsStatistics"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/news/statistics/by-source": {"get": {"tags": ["新闻管理"], "summary": "按来源统计新闻", "description": "按新闻来源统计数据", "operationId": "get_news_by_source_api_v1_news_statistics_by_source_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "days", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 365, "minimum": 1, "description": "统计天数", "default": 7, "title": "Days"}, "description": "统计天数"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/news/statistics/by-category": {"get": {"tags": ["新闻管理"], "summary": "按分类统计新闻", "description": "按新闻分类统计数据", "operationId": "get_news_by_category_api_v1_news_statistics_by_category_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "days", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 365, "minimum": 1, "description": "统计天数", "default": 7, "title": "Days"}, "description": "统计天数"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/news/statistics/timeline": {"get": {"tags": ["新闻管理"], "summary": "获取新闻时间线统计", "description": "获取新闻发布时间线统计", "operationId": "get_news_timeline_api_v1_news_statistics_timeline_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "days", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 365, "minimum": 1, "description": "统计天数", "default": 7, "title": "Days"}, "description": "统计天数"}, {"name": "interval", "in": "query", "required": false, "schema": {"type": "string", "pattern": "^(hour|day|week)$", "description": "时间间隔", "default": "day", "title": "Interval"}, "description": "时间间隔"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/news/trending": {"get": {"tags": ["新闻管理"], "summary": "获取热门新闻", "description": "获取热门/趋势新闻", "operationId": "get_trending_news_api_v1_news_trending_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 50, "minimum": 1, "description": "返回数量", "default": 10, "title": "Limit"}, "description": "返回数量"}, {"name": "hours", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 168, "minimum": 1, "description": "时间范围（小时）", "default": 24, "title": "Hours"}, "description": "时间范围（小时）"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/news/sources": {"get": {"tags": ["新闻管理"], "summary": "获取新闻来源列表", "description": "获取所有新闻来源列表", "operationId": "get_news_sources_api_v1_news_sources_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/news/categories": {"get": {"tags": ["新闻管理"], "summary": "获取新闻分类列表", "description": "获取所有新闻分类列表", "operationId": "get_news_categories_api_v1_news_categories_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/news/batch": {"post": {"tags": ["新闻管理"], "summary": "批量操作新闻", "description": "批量操作新闻（仅管理员可用）", "operationId": "batch_operations_api_v1_news_batch_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "operation", "in": "query", "required": true, "schema": {"type": "string", "pattern": "^(delete|update_category|update_importance)$", "description": "操作类型", "title": "Operation"}, "description": "操作类型"}, {"name": "news_ids", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "integer"}, "description": "新闻ID列表", "title": "News Ids"}, "description": "新闻ID列表"}, {"name": "value", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "操作值", "title": "Value"}, "description": "操作值"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/news/search/enhanced": {"get": {"tags": ["新闻管理"], "summary": "增强搜索", "description": "增强的全文搜索，支持智能分词、相关性排序", "operationId": "enhanced_search_api_v1_news_search_enhanced_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "q", "in": "query", "required": true, "schema": {"type": "string", "minLength": 1, "description": "搜索关键词", "title": "Q"}, "description": "搜索关键词"}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "页码", "default": 1, "title": "Page"}, "description": "页码"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "每页数量", "default": 20, "title": "Limit"}, "description": "每页数量"}, {"name": "source", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "新闻来源", "title": "Source"}, "description": "新闻来源"}, {"name": "category", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "新闻分类", "title": "Category"}, "description": "新闻分类"}, {"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "开始时间", "title": "Start Date"}, "description": "开始时间"}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "结束时间", "title": "End Date"}, "description": "结束时间"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewsSearchResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/news/search/suggestions": {"get": {"tags": ["新闻管理"], "summary": "搜索建议", "description": "获取搜索建议和自动补全", "operationId": "get_search_suggestions_api_v1_news_search_suggestions_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "q", "in": "query", "required": true, "schema": {"type": "string", "minLength": 1, "maxLength": 50, "description": "查询前缀", "title": "Q"}, "description": "查询前缀"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 20, "minimum": 1, "description": "返回数量", "default": 10, "title": "Limit"}, "description": "返回数量"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/news/search/popular": {"get": {"tags": ["新闻管理"], "summary": "热门搜索", "description": "获取热门搜索词", "operationId": "get_popular_searches_api_v1_news_search_popular_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "days", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 30, "minimum": 1, "description": "统计天数", "default": 7, "title": "Days"}, "description": "统计天数"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 20, "minimum": 1, "description": "返回数量", "default": 10, "title": "Limit"}, "description": "返回数量"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/news/cache/stats": {"get": {"tags": ["新闻管理"], "summary": "获取缓存统计", "description": "获取缓存系统统计信息", "operationId": "get_cache_stats_api_v1_news_cache_stats_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/news/cache/clear": {"delete": {"tags": ["新闻管理"], "summary": "清除缓存", "description": "清除指定模式的缓存", "operationId": "clear_cache_api_v1_news_cache_clear_delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "pattern", "in": "query", "required": false, "schema": {"type": "string", "description": "缓存键模式", "default": "news:*", "title": "Pattern"}, "description": "缓存键模式"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/push/channels": {"get": {"tags": ["推送管理"], "summary": "获取支持的推送渠道", "description": "获取所有支持的推送渠道及其状态", "operationId": "get_push_channels_api_v1_push_channels_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/push/test": {"post": {"tags": ["推送管理"], "summary": "测试推送", "description": "测试推送功能", "operationId": "test_push_api_v1_push_test_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Body_test_push_api_v1_push_test_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/push/custom": {"post": {"tags": ["推送管理"], "summary": "发送自定义消息", "description": "发送自定义推送消息", "operationId": "send_custom_push_api_v1_push_custom_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Body_send_custom_push_api_v1_push_custom_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/push/urgent/{news_id}": {"post": {"tags": ["推送管理"], "summary": "发送紧急新闻推送", "description": "发送紧急新闻推送", "operationId": "send_urgent_push_api_v1_push_urgent__news_id__post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "news_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "News Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/push/logs": {"get": {"tags": ["推送管理"], "summary": "Get Push Logs", "description": "获取推送日志（需要basic_subscription权限）", "operationId": "get_push_logs_api_v1_push_logs_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "description": "跳过数量", "default": 0, "title": "<PERSON><PERSON>"}, "description": "跳过数量"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "每页数量", "default": 20, "title": "Limit"}, "description": "每页数量"}, {"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "开始时间", "title": "Start Date"}, "description": "开始时间"}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "结束时间", "title": "End Date"}, "description": "结束时间"}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "推送状态", "title": "Status"}, "description": "推送状态"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PushLogResponse"}, "title": "Response Get Push Logs Api V1 Push Logs Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/push/logs/{log_id}": {"get": {"tags": ["推送管理"], "summary": "获取推送日志详情", "description": "获取推送日志详情", "operationId": "get_push_log_detail_api_v1_push_logs__log_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "log_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Log Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/push/statistics": {"get": {"tags": ["推送管理"], "summary": "获取推送统计", "description": "获取推送统计数据", "operationId": "get_push_statistics_api_v1_push_statistics_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "days", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 365, "minimum": 1, "description": "统计天数", "default": 7, "title": "Days"}, "description": "统计天数"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/templates/": {"get": {"tags": ["推送模板"], "summary": "Get Templates", "description": "获取模板列表", "operationId": "get_templates_api_v1_templates__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "tier", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "推送层级过滤", "title": "Tier"}, "description": "推送层级过滤"}, {"name": "channel", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "推送渠道过滤", "title": "Channel"}, "description": "推送渠道过滤"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TemplateResponse"}, "title": "Response Get Templates Api V1 Templates  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["推送模板"], "summary": "Create Template", "description": "创建新的推送模板", "operationId": "create_template_api_v1_templates__post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TemplateCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TemplateResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/templates/{template_id}": {"get": {"tags": ["推送模板"], "summary": "Get Template", "description": "获取模板详情", "operationId": "get_template_api_v1_templates__template_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "template_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Template Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TemplateResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/templates/health": {"get": {"tags": ["推送模板"], "summary": "Health Check", "description": "健康检查接口", "operationId": "health_check_api_v1_templates_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/rules/": {"get": {"tags": ["规则管理"], "summary": "Get Rules", "description": "获取规则列表（管理员权限）", "operationId": "get_rules_api_v1_rules__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "rule_type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "规则类型过滤", "title": "Rule Type"}, "description": "规则类型过滤"}, {"name": "active_only", "in": "query", "required": false, "schema": {"type": "boolean", "description": "只显示启用的规则", "default": true, "title": "Active Only"}, "description": "只显示启用的规则"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object"}, "title": "Response Get Rules Api V1 Rules  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["规则管理"], "summary": "Create Rule", "description": "创建新规则（管理员权限）", "operationId": "create_rule_api_v1_rules__post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "title": "Rule Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Create Rule Api V1 Rules  Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/rules/{rule_id}": {"get": {"tags": ["规则管理"], "summary": "Get Rule", "description": "获取规则详情（管理员权限）", "operationId": "get_rule_api_v1_rules__rule_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "rule_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Rule Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Rule Api V1 Rules  Rule Id  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["规则管理"], "summary": "Update Rule", "description": "更新规则（管理员权限）", "operationId": "update_rule_api_v1_rules__rule_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "rule_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Rule Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "title": "Rule Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Update Rule Api V1 Rules  Rule Id  Put"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["规则管理"], "summary": "Delete Rule", "description": "删除规则（管理员权限）", "operationId": "delete_rule_api_v1_rules__rule_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "rule_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Rule Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}, "title": "Response Delete Rule Api V1 Rules  Rule Id  Delete"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/rules/{rule_id}/test": {"post": {"tags": ["规则管理"], "summary": "Test Rule", "description": "测试规则（管理员权限）", "operationId": "test_rule_api_v1_rules__rule_id__test_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "rule_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Rule Id"}}], "requestBody": {"content": {"application/json": {"schema": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Test Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Test Rule Api V1 Rules  Rule Id  Test Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/rules/{rule_id}/statistics": {"get": {"tags": ["规则管理"], "summary": "Get Rule Statistics", "description": "获取规则统计信息（管理员权限）", "operationId": "get_rule_statistics_api_v1_rules__rule_id__statistics_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "rule_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Rule Id"}}, {"name": "days", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 90, "minimum": 1, "description": "统计天数", "default": 7, "title": "Days"}, "description": "统计天数"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Rule Statistics Api V1 Rules  Rule Id  Statistics Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/analytics/overview": {"get": {"tags": ["推送分析"], "summary": "获取推送概览", "description": "获取推送效果概览统计", "operationId": "get_push_overview_api_v1_analytics_overview_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "days", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 365, "minimum": 1, "description": "统计天数", "default": 30, "title": "Days"}, "description": "统计天数"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/analytics/channels": {"get": {"tags": ["推送分析"], "summary": "获取渠道效果分析", "description": "获取各推送渠道的效果分析", "operationId": "get_channel_performance_api_v1_analytics_channels_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "days", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 365, "minimum": 1, "description": "统计天数", "default": 30, "title": "Days"}, "description": "统计天数"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/analytics/trends": {"get": {"tags": ["推送分析"], "summary": "获取时间趋势分析", "description": "获取推送效果的时间趋势分析", "operationId": "get_time_trends_api_v1_analytics_trends_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "days", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 365, "minimum": 1, "description": "统计天数", "default": 30, "title": "Days"}, "description": "统计天数"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/analytics/subscriptions": {"get": {"tags": ["推送分析"], "summary": "获取订阅效果分析", "description": "获取各订阅的推送效果分析", "operationId": "get_subscription_performance_api_v1_analytics_subscriptions_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "days", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 365, "minimum": 1, "description": "统计天数", "default": 30, "title": "Days"}, "description": "统计天数"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/analytics/track": {"post": {"tags": ["推送分析"], "summary": "追踪用户行为", "description": "追踪用户行为（打开、点击、反馈等）", "operationId": "track_user_action_api_v1_analytics_track_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserAction"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/analytics/engagement": {"get": {"tags": ["推送分析"], "summary": "获取参与度分析", "description": "获取用户参与度分析", "operationId": "get_engagement_analysis_api_v1_analytics_engagement_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "days", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 365, "minimum": 1, "description": "统计天数", "default": 30, "title": "Days"}, "description": "统计天数"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/analytics/best-practices": {"get": {"tags": ["推送分析"], "summary": "获取推送优化建议", "description": "基于数据分析提供推送优化建议", "operationId": "get_optimization_suggestions_api_v1_analytics_best_practices_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "days", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 365, "minimum": 1, "description": "统计天数", "default": 30, "title": "Days"}, "description": "统计天数"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/scheduler/schedules": {"get": {"tags": ["调度管理"], "summary": "Get Schedules", "description": "获取所有调度配置", "operationId": "get_schedules_api_v1_scheduler_schedules_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Schedules Api V1 Scheduler Schedules Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/scheduler/schedules/{task_name}": {"get": {"tags": ["调度管理"], "summary": "Get Schedule", "description": "获取指定任务的调度配置", "operationId": "get_schedule_api_v1_scheduler_schedules__task_name__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "task_name", "in": "path", "required": true, "schema": {"type": "string", "title": "Task Name"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Schedule Api V1 Scheduler Schedules  Task Name  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["调度管理"], "summary": "Update Schedule", "description": "更新任务调度配置", "operationId": "update_schedule_api_v1_scheduler_schedules__task_name__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "task_name", "in": "path", "required": true, "schema": {"type": "string", "title": "Task Name"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScheduleConfigRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScheduleResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/scheduler/schedules/{task_name}/control": {"post": {"tags": ["调度管理"], "summary": "Control Task", "description": "控制任务执行", "operationId": "control_task_api_v1_scheduler_schedules__task_name__control_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "task_name", "in": "path", "required": true, "schema": {"type": "string", "title": "Task Name"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskControlRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScheduleResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/scheduler/statistics": {"get": {"tags": ["调度管理"], "summary": "Get Statistics", "description": "获取调度统计信息", "operationId": "get_statistics_api_v1_scheduler_statistics_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Statistics Api V1 Scheduler Statistics Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/scheduler/reset": {"post": {"tags": ["调度管理"], "summary": "Reset Schedules", "description": "重置调度配置为默认值", "operationId": "reset_schedules_api_v1_scheduler_reset_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScheduleResponse"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/scheduler/health": {"get": {"tags": ["调度管理"], "summary": "Health Check", "description": "调度系统健康检查", "operationId": "health_check_api_v1_scheduler_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Health Check Api V1 Scheduler Health Get"}}}}}}}, "/api/v1/monitoring/metrics/summary": {"get": {"tags": ["监控管理"], "summary": "Get Metrics Summary", "description": "获取监控指标摘要", "operationId": "get_metrics_summary_api_v1_monitoring_metrics_summary_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Metrics Summary Api V1 Monitoring Metrics Summary Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/monitoring/metrics/crawler": {"get": {"tags": ["监控管理"], "summary": "Get Crawler Metrics", "description": "获取爬虫监控指标", "operationId": "get_crawler_metrics_api_v1_monitoring_metrics_crawler_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Crawler Metrics Api V1 Monitoring Metrics Crawler Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/monitoring/metrics/system": {"get": {"tags": ["监控管理"], "summary": "Get System Metrics", "description": "获取系统监控指标", "operationId": "get_system_metrics_api_v1_monitoring_metrics_system_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get System Metrics Api V1 Monitoring Metrics System Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/monitoring/alerts": {"get": {"tags": ["监控管理"], "summary": "<PERSON>erts", "description": "获取当前告警信息", "operationId": "get_alerts_api_v1_monitoring_alerts_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Alerts Api V1 Monitoring Alerts Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/monitoring/alerts/rules": {"get": {"tags": ["监控管理"], "summary": "Get Alert Rules", "description": "获取告警规则配置", "operationId": "get_alert_rules_api_v1_monitoring_alerts_rules_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Alert Rules Api V1 Monitoring Alerts Rules Get"}}}}}, "security": [{"HTTPBearer": []}]}, "post": {"tags": ["监控管理"], "summary": "Create <PERSON><PERSON>", "description": "创建告警规则", "operationId": "create_alert_rule_api_v1_monitoring_alerts_rules_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AlertRuleRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Create Alert Rule Api V1 Monitoring Alerts Rules Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/monitoring/alerts/rules/{rule_name}/toggle": {"put": {"tags": ["监控管理"], "summary": "Toggle Al<PERSON> Rule", "description": "切换告警规则启用状态", "operationId": "toggle_alert_rule_api_v1_monitoring_alerts_rules__rule_name__toggle_put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "rule_name", "in": "path", "required": true, "schema": {"type": "string", "title": "Rule Name"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Toggle Alert Rule Api V1 Monitoring Alerts Rules  Rule Name  Toggle Put"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/monitoring/metrics/prometheus": {"get": {"tags": ["监控管理"], "summary": "Get Prometheus Metrics", "description": "获取Prometheus格式的监控指标", "operationId": "get_prometheus_metrics_api_v1_monitoring_metrics_prometheus_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/monitoring/health": {"get": {"tags": ["监控管理"], "summary": "Monitoring Health Check", "description": "监控系统健康检查", "operationId": "monitoring_health_check_api_v1_monitoring_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Monitoring Health Check Api V1 Monitoring Health Get"}}}}}}}, "/api/v1/security/compliance/check": {"post": {"tags": ["安全管理"], "summary": "Check Compliance", "description": "内容合规检查", "operationId": "check_compliance_api_v1_security_compliance_check_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComplianceCheckRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Check Compliance Api V1 Security Compliance Check Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/security/privacy/check": {"post": {"tags": ["安全管理"], "summary": "Check Privacy", "description": "隐私信息检查", "operationId": "check_privacy_api_v1_security_privacy_check_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PrivacyCheckRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Check Privacy Api V1 Security Privacy Check Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/security/audit/logs": {"get": {"tags": ["安全管理"], "summary": "Get Audit Logs", "description": "获取审计日志", "operationId": "get_audit_logs_api_v1_security_audit_logs_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "start_time", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Start Time"}}, {"name": "end_time", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "End Time"}}, {"name": "user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User Id"}}, {"name": "event_type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Event Type"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "Offset"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Audit Logs Api V1 Security Audit Logs Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/security/audit/statistics": {"get": {"tags": ["安全管理"], "summary": "Get Audit Statistics", "description": "获取审计统计信息", "operationId": "get_audit_statistics_api_v1_security_audit_statistics_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "days", "in": "query", "required": false, "schema": {"type": "integer", "default": 7, "title": "Days"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Audit Statistics Api V1 Security Audit Statistics Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/security/rate-limit/stats": {"get": {"tags": ["安全管理"], "summary": "Get Rate Limit Stats", "description": "获取速率限制统计信息", "operationId": "get_rate_limit_stats_api_v1_security_rate_limit_stats_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Rate Limit Stats Api V1 Security Rate Limit Stats Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/security/rate-limit/blacklist": {"post": {"tags": ["安全管理"], "summary": "Add To Blacklist", "description": "添加IP到黑名单", "operationId": "add_to_blacklist_api_v1_security_rate_limit_blacklist_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "ip_address", "in": "query", "required": true, "schema": {"type": "string", "title": "Ip Address"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Add To Blacklist Api V1 Security Rate Limit Blacklist Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/security/compliance/statistics": {"get": {"tags": ["安全管理"], "summary": "Get Compliance Statistics", "description": "获取合规统计信息", "operationId": "get_compliance_statistics_api_v1_security_compliance_statistics_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Compliance Statistics Api V1 Security Compliance Statistics Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/security/privacy/policies": {"get": {"tags": ["安全管理"], "summary": "Get Privacy Policies", "description": "获取隐私保护策略", "operationId": "get_privacy_policies_api_v1_security_privacy_policies_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Privacy Policies Api V1 Security Privacy Policies Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/security/health": {"get": {"tags": ["安全管理"], "summary": "Security Health Check", "description": "安全系统健康检查", "operationId": "security_health_check_api_v1_security_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Security Health Check Api V1 Security Health Get"}}}}}}}, "/api/v1/infrastructure/database/health": {"get": {"tags": ["基础设施管理"], "summary": "Get Database Health", "description": "获取数据库健康状态", "operationId": "get_database_health_api_v1_infrastructure_database_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Database Health Api V1 Infrastructure Database Health Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/infrastructure/database/stats": {"get": {"tags": ["基础设施管理"], "summary": "Get Database Statistics", "description": "获取数据库统计信息", "operationId": "get_database_statistics_api_v1_infrastructure_database_stats_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Database Statistics Api V1 Infrastructure Database Stats Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/infrastructure/database/pool/reset": {"post": {"tags": ["基础设施管理"], "summary": "Reset Database Pool", "description": "重置数据库连接池", "operationId": "reset_database_pool_api_v1_infrastructure_database_pool_reset_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Reset Database Pool Api V1 Infrastructure Database Pool Reset Post"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/infrastructure/redis/health": {"get": {"tags": ["基础设施管理"], "summary": "Get Redis Health", "description": "获取Redis健康状态", "operationId": "get_redis_health_api_v1_infrastructure_redis_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Redis Health Api V1 Infrastructure Redis Health Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/infrastructure/redis/stats": {"get": {"tags": ["基础设施管理"], "summary": "Get Redis Statistics", "description": "获取Redis统计信息", "operationId": "get_redis_statistics_api_v1_infrastructure_redis_stats_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Redis Statistics Api V1 Infrastructure Redis Stats Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/infrastructure/redis/pool/reset": {"post": {"tags": ["基础设施管理"], "summary": "Reset Redis Pool", "description": "重置Redis连接池", "operationId": "reset_redis_pool_api_v1_infrastructure_redis_pool_reset_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Reset Redis Pool Api V1 Infrastructure Redis Pool Reset Post"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/infrastructure/cache/operation": {"post": {"tags": ["基础设施管理"], "summary": "Cache Operation", "description": "缓存操作", "operationId": "cache_operation_api_v1_infrastructure_cache_operation_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CacheOperationRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Cache Operation Api V1 Infrastructure Cache Operation Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/infrastructure/overview": {"get": {"tags": ["基础设施管理"], "summary": "Get Infrastructure Overview", "description": "获取基础设施概览", "operationId": "get_infrastructure_overview_api_v1_infrastructure_overview_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Infrastructure Overview Api V1 Infrastructure Overview Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/infrastructure/health": {"get": {"tags": ["基础设施管理"], "summary": "Infrastructure Health Check", "description": "基础设施健康检查（公开接口）", "operationId": "infrastructure_health_check_api_v1_infrastructure_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Infrastructure Health Check Api V1 Infrastructure Health Get"}}}}}}}, "/api/v1/reports/generate": {"post": {"tags": ["简报管理"], "summary": "Generate Report", "description": "生成简报（需要basic_subscription权限）", "operationId": "generate_report_api_v1_reports_generate_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReportGenerateRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReportResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/reports/": {"get": {"tags": ["简报管理"], "summary": "Get Reports", "description": "获取简报列表（需要basic_subscription权限）", "operationId": "get_reports_api_v1_reports__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "页码", "default": 1, "title": "Page"}, "description": "页码"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "每页数量", "default": 20, "title": "Size"}, "description": "每页数量"}, {"name": "report_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/ReportType"}, {"type": "null"}], "description": "简报类型过滤", "title": "Report Type"}, "description": "简报类型过滤"}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/ReportStatus"}, {"type": "null"}], "description": "状态过滤", "title": "Status"}, "description": "状态过滤"}, {"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "开始日期", "title": "Start Date"}, "description": "开始日期"}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "结束日期", "title": "End Date"}, "description": "结束日期"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReportListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/reports/{report_id}": {"get": {"tags": ["简报管理"], "summary": "Get Report", "description": "获取简报详情（需要basic_subscription权限）", "operationId": "get_report_api_v1_reports__report_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "report_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Report Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReportResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/reports/latest/{report_type}": {"get": {"tags": ["简报管理"], "summary": "Get Latest Report", "description": "获取最新简报（需要basic_subscription权限）", "operationId": "get_latest_report_api_v1_reports_latest__report_type__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "report_type", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/ReportType"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReportResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/reports/schedule": {"post": {"tags": ["简报管理"], "summary": "Schedule Custom Report", "description": "安排自定义简报生成（需要advanced_subscription权限）", "operationId": "schedule_custom_report_api_v1_reports_schedule_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "scheduled_time", "in": "query", "required": true, "schema": {"type": "string", "format": "date-time", "description": "计划生成时间", "title": "Scheduled Time"}, "description": "计划生成时间"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReportGenerateRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}, "title": "Response Schedule Custom Report Api V1 Reports Schedule Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/reports/feedback/{report_id}": {"post": {"tags": ["简报管理"], "summary": "Submit Report Feedback", "description": "提交简报反馈（需要basic_subscription权限）", "operationId": "submit_report_feedback_api_v1_reports_feedback__report_id__post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "report_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Report Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReportFeedbackCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}, "title": "Response Submit Report Feedback Api V1 Reports Feedback  Report Id  Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/reports/statistics/summary": {"get": {"tags": ["简报管理"], "summary": "Get Report Statistics", "description": "获取简报统计信息（管理员权限）", "operationId": "get_report_statistics_api_v1_reports_statistics_summary_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "days", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 365, "minimum": 1, "description": "统计天数", "default": 30, "title": "Days"}, "description": "统计天数"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReportStatistics"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/reports/admin/jobs": {"get": {"tags": ["简报管理"], "summary": "List Scheduled Jobs", "description": "列出所有计划任务（管理员权限）", "operationId": "list_scheduled_jobs_api_v1_reports_admin_jobs_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"type": "object"}, "type": "array", "title": "Response List Scheduled Jobs Api V1 Reports Admin Jobs Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/reports/admin/jobs/{job_id}": {"delete": {"tags": ["简报管理"], "summary": "Cancel Scheduled Job", "description": "取消计划任务（管理员权限）", "operationId": "cancel_scheduled_job_api_v1_reports_admin_jobs__job_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "job_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Job Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}, "title": "Response Cancel Scheduled Job Api V1 Reports Admin Jobs  Job Id  Delete"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/push/send": {"post": {"tags": ["推送管理"], "summary": "Send Push Message", "description": "发送推送消息（需要basic_subscription权限）", "operationId": "send_push_message_api_v1_push_send_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PushMessageCreate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PushMessageResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/push/quota": {"get": {"tags": ["推送管理"], "summary": "<PERSON>", "description": "获取推送配额信息（需要basic_subscription权限）", "operationId": "get_push_quota_api_v1_push_quota_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Push Quota Api V1 Push Quota Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/push/templates": {"post": {"tags": ["推送管理"], "summary": "Create <PERSON><PERSON> Te<PERSON>late", "description": "创建推送模板（需要advanced_subscription权限）\nPRO、ENTERPRISE、ADMIN角色可用", "operationId": "create_push_template_api_v1_push_templates_post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__push__PushTemplateCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__push__PushTemplateResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["推送管理"], "summary": "Get Push Templates", "description": "获取推送模板列表（需要advanced_subscription权限）", "operationId": "get_push_templates_api_v1_push_templates_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "description": "跳过数量", "default": 0, "title": "<PERSON><PERSON>"}, "description": "跳过数量"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "每页数量", "default": 20, "title": "Limit"}, "description": "每页数量"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/app__schemas__push__PushTemplateResponse"}, "title": "Response Get Push Templates Api V1 Push Templates Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/push/templates/{template_id}": {"put": {"tags": ["推送管理"], "summary": "Update <PERSON><PERSON> Template", "description": "更新推送模板（需要advanced_subscription权限）", "operationId": "update_push_template_api_v1_push_templates__template_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "template_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Template Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__push__PushTemplateCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__push__PushTemplateResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["推送管理"], "summary": "Delete Push Template", "description": "删除推送模板（需要advanced_subscription权限）", "operationId": "delete_push_template_api_v1_push_templates__template_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "template_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Template Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/push/stats": {"get": {"tags": ["推送管理"], "summary": "Get Push Stats", "description": "获取推送统计信息（需要basic_subscription权限）", "operationId": "get_push_stats_api_v1_push_stats_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "days", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 365, "minimum": 1, "description": "统计天数", "default": 30, "title": "Days"}, "description": "统计天数"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Push Stats Api V1 Push Stats Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/push/admin/logs": {"get": {"tags": ["推送管理"], "summary": "Get All Push Logs", "description": "获取所有推送日志（管理员权限）", "operationId": "get_all_push_logs_api_v1_push_admin_logs_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "description": "跳过数量", "default": 0, "title": "<PERSON><PERSON>"}, "description": "跳过数量"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "description": "每页数量", "default": 100, "title": "Limit"}, "description": "每页数量"}, {"name": "user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "用户ID过滤", "title": "User Id"}, "description": "用户ID过滤"}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "推送状态", "title": "Status"}, "description": "推送状态"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PushLogResponse"}, "title": "Response Get All Push Logs Api V1 Push Admin Logs Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/push/admin/stats": {"get": {"tags": ["推送管理"], "summary": "Get Admin Push Stats", "description": "获取推送系统统计信息（管理员权限）", "operationId": "get_admin_push_stats_api_v1_push_admin_stats_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Admin Push Stats Api V1 Push Admin Stats Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/push/admin/broadcast": {"post": {"tags": ["推送管理"], "summary": "Broadcast Message", "description": "广播消息（管理员权限）", "operationId": "broadcast_message_api_v1_push_admin_broadcast_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "target_roles", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "items": {"type": "string"}}, {"type": "null"}], "description": "目标角色列表", "title": "Target Roles"}, "description": "目标角色列表"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PushMessageCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Broadcast Message Api V1 Push Admin Broadcast Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/push/admin/config": {"put": {"tags": ["推送管理"], "summary": "Update <PERSON><PERSON> Config", "description": "更新推送系统配置（需要system_config权限）", "operationId": "update_push_config_api_v1_push_admin_config_put", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "title": "Config Data"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Update Push Config Api V1 Push Admin Config Put"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/subscriptions/basic": {"post": {"tags": ["订阅管理"], "summary": "Create Basic Subscription", "description": "创建基础订阅（所有用户角色可用）\n\n基础订阅功能：\n- 最多3个关键词\n- 最多5个公司关注\n- 基础推送渠道", "operationId": "create_basic_subscription_api_v1_subscriptions_basic_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionCreate"}}}, "required": true}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/subscriptions/advanced": {"post": {"tags": ["订阅管理"], "summary": "Create Advanced Subscription", "description": "创建高级订阅（PRO、ENTERPRISE、ADMIN角色可用）\n\n高级订阅功能：\n- 无限关键词\n- 无限公司关注\n- 高级推送渠道\n- 自定义推送规则\n- 智能推荐", "operationId": "create_advanced_subscription_api_v1_subscriptions_advanced_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionCreate"}}}, "required": true}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/subscriptions/bulk-create": {"post": {"tags": ["订阅管理"], "summary": "Bulk Create Subscriptions", "description": "批量创建订阅（需要bulk_operations权限）\nENTERPRISE、ADMIN角色可用", "operationId": "bulk_create_subscriptions_api_v1_subscriptions_bulk_create_post", "requestBody": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/SubscriptionCreate"}, "type": "array", "title": "Subscriptions Data"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/SubscriptionResponse"}, "type": "array", "title": "Response Bulk Create Subscriptions Api V1 Subscriptions Bulk Create Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/subscriptions/stats/summary": {"get": {"tags": ["订阅管理"], "summary": "Get Subscription Stats", "description": "获取订阅统计信息（需要basic_subscription权限）", "operationId": "get_subscription_stats_api_v1_subscriptions_stats_summary_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionStats"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/subscriptions/admin/all": {"get": {"tags": ["订阅管理"], "summary": "Get All Subscriptions", "description": "获取所有订阅（管理员权限）", "operationId": "get_all_subscriptions_api_v1_subscriptions_admin_all_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "description": "跳过数量", "default": 0, "title": "<PERSON><PERSON>"}, "description": "跳过数量"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "description": "每页数量", "default": 100, "title": "Limit"}, "description": "每页数量"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SubscriptionResponse"}, "title": "Response Get All Subscriptions Api V1 Subscriptions Admin All Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/subscriptions/admin/stats": {"get": {"tags": ["订阅管理"], "summary": "Get Admin Subscription Stats", "description": "获取订阅系统统计信息（管理员权限）", "operationId": "get_admin_subscription_stats_api_v1_subscriptions_admin_stats_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Admin Subscription Stats Api V1 Subscriptions Admin Stats Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/push/management/rules": {"post": {"tags": ["推送管理"], "summary": "Create <PERSON>ush Rule", "description": "创建推送规则（管理员权限）", "operationId": "create_push_rule_api_v1_push_management_rules_post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PushRuleCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PushRuleResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["推送管理"], "summary": "Get Push Rules", "description": "获取推送规则列表（管理员权限）", "operationId": "get_push_rules_api_v1_push_management_rules_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "active_only", "in": "query", "required": false, "schema": {"type": "boolean", "description": "只显示启用的规则", "default": true, "title": "Active Only"}, "description": "只显示启用的规则"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PushRuleResponse"}, "title": "Response Get Push Rules Api V1 Push Management Rules Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/push/management/rules/{rule_id}": {"put": {"tags": ["推送管理"], "summary": "Update Push Rule", "description": "更新推送规则（管理员权限）", "operationId": "update_push_rule_api_v1_push_management_rules__rule_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "rule_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Rule Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PushRuleCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PushRuleResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["推送管理"], "summary": "Delete Push Rule", "description": "删除推送规则（管理员权限）", "operationId": "delete_push_rule_api_v1_push_management_rules__rule_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "rule_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Rule Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}, "title": "Response Delete Push Rule Api V1 Push Management Rules  Rule Id  Delete"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/push/management/templates": {"post": {"tags": ["推送管理"], "summary": "Create <PERSON><PERSON> Te<PERSON>late", "description": "创建推送模板（管理员权限）", "operationId": "create_push_template_api_v1_push_management_templates_post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__push_layer__PushTemplateCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__push_layer__PushTemplateResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["推送管理"], "summary": "Get Push Templates", "description": "获取推送模板列表（管理员权限）", "operationId": "get_push_templates_api_v1_push_management_templates_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "tier", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/PushTier"}, {"type": "null"}], "description": "层级过滤", "title": "Tier"}, "description": "层级过滤"}, {"name": "channel", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/app__models__push_layer__PushChannel"}, {"type": "null"}], "description": "渠道过滤", "title": "Channel"}, "description": "渠道过滤"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/app__schemas__push_layer__PushTemplateResponse"}, "title": "Response Get Push Templates Api V1 Push Management Templates Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/push/management/groups": {"post": {"tags": ["推送管理"], "summary": "Create Push Group", "description": "创建推送群组（管理员权限）", "operationId": "create_push_group_api_v1_push_management_groups_post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PushGroupCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PushGroupResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["推送管理"], "summary": "Get Push Groups", "description": "获取推送群组列表（管理员权限）", "operationId": "get_push_groups_api_v1_push_management_groups_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "active_only", "in": "query", "required": false, "schema": {"type": "boolean", "description": "只显示启用的群组", "default": true, "title": "Active Only"}, "description": "只显示启用的群组"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PushGroupResponse"}, "title": "Response Get Push Groups Api V1 Push Management Groups Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/push/management/groups/{group_id}/members/{user_id}": {"post": {"tags": ["推送管理"], "summary": "Add Group Member", "description": "添加群组成员（管理员权限）", "operationId": "add_group_member_api_v1_push_management_groups__group_id__members__user_id__post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "group_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Group Id"}}, {"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}, "title": "Response Add Group Member Api V1 Push Management Groups  Group Id  Members  User Id  Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/push/management/tasks": {"post": {"tags": ["推送管理"], "summary": "Create Manual Push Task", "description": "创建手动推送任务（需要advanced_subscription权限）", "operationId": "create_manual_push_task_api_v1_push_management_tasks_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PushTaskCreate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PushTaskResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/push/management/test-push": {"post": {"tags": ["推送管理"], "summary": "Test Push System", "description": "测试推送系统（管理员权限）", "operationId": "test_push_system_api_v1_push_management_test_push_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "tier", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/PushTier", "description": "测试层级", "default": "T3"}, "description": "测试层级"}, {"name": "channels", "in": "query", "required": false, "schema": {"type": "array", "items": {"$ref": "#/components/schemas/app__models__push_layer__PushChannel"}, "description": "测试渠道", "default": ["wechat"], "title": "Channels"}, "description": "测试渠道"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Test Push System Api V1 Push Management Test Push Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/push/monitoring/dashboard": {"get": {"tags": ["推送监控"], "summary": "Get Push Dashboard", "description": "获取推送监控仪表板数据（需要advanced_subscription权限）", "operationId": "get_push_dashboard_api_v1_push_monitoring_dashboard_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Push Dashboard Api V1 Push Monitoring Dashboard Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/push/monitoring/tasks": {"get": {"tags": ["推送监控"], "summary": "Get Push Tasks", "description": "获取推送任务列表（需要advanced_subscription权限）", "operationId": "get_push_tasks_api_v1_push_monitoring_tasks_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "页码", "default": 1, "title": "Page"}, "description": "页码"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "每页数量", "default": 20, "title": "Size"}, "description": "每页数量"}, {"name": "tier", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/PushTier"}, {"type": "null"}], "description": "推送层级过滤", "title": "Tier"}, "description": "推送层级过滤"}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/PushStatus-Input"}, {"type": "null"}], "description": "状态过滤", "title": "Status"}, "description": "状态过滤"}, {"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "开始日期", "title": "Start Date"}, "description": "开始日期"}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "结束日期", "title": "End Date"}, "description": "结束日期"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PushTaskResponse"}, "title": "Response Get Push Tasks Api V1 Push Monitoring Tasks Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/push/monitoring/tasks/{task_id}": {"get": {"tags": ["推送监控"], "summary": "Get Push Task Detail", "description": "获取推送任务详情（需要advanced_subscription权限）", "operationId": "get_push_task_detail_api_v1_push_monitoring_tasks__task_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PushTaskResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/push/monitoring/tasks/{task_id}/delivery-logs": {"get": {"tags": ["推送监控"], "summary": "Get Task Delivery Logs", "description": "获取推送任务的投递日志（需要advanced_subscription权限）", "operationId": "get_task_delivery_logs_api_v1_push_monitoring_tasks__task_id__delivery_logs_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PushDeliveryLogResponse"}, "title": "Response Get Task Delivery Logs Api V1 Push Monitoring Tasks  Task Id  Delivery Logs Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/push/monitoring/statistics": {"get": {"tags": ["推送监控"], "summary": "Get Push Statistics", "description": "获取推送统计信息（需要advanced_subscription权限）", "operationId": "get_push_statistics_api_v1_push_monitoring_statistics_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "days", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 90, "minimum": 1, "description": "统计天数", "default": 7, "title": "Days"}, "description": "统计天数"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PushStatistics"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/push/monitoring/rules": {"get": {"tags": ["推送监控"], "summary": "Get Push Rules", "description": "获取推送规则列表（管理员权限）", "operationId": "get_push_rules_api_v1_push_monitoring_rules_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/PushRuleResponse"}, "type": "array", "title": "Response Get Push Rules Api V1 Push Monitoring Rules Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/push/monitoring/groups": {"get": {"tags": ["推送监控"], "summary": "Get Push Groups", "description": "获取推送群组列表（管理员权限）", "operationId": "get_push_groups_api_v1_push_monitoring_groups_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/PushGroupResponse"}, "type": "array", "title": "Response Get Push Groups Api V1 Push Monitoring Groups Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/push/monitoring/tasks/{task_id}/retry": {"post": {"tags": ["推送监控"], "summary": "<PERSON><PERSON> Push Task", "description": "重试推送任务（管理员权限）", "operationId": "retry_push_task_api_v1_push_monitoring_tasks__task_id__retry_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}, "title": "Response Retry Push Task Api V1 Push Monitoring Tasks  Task Id  Retry Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/push/monitoring/tasks/{task_id}/cancel": {"post": {"tags": ["推送监控"], "summary": "Cancel Push Task", "description": "取消推送任务（管理员权限）", "operationId": "cancel_push_task_api_v1_push_monitoring_tasks__task_id__cancel_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}, "title": "Response Cancel Push Task Api V1 Push Monitoring Tasks  Task Id  Cancel Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/push/monitoring/scheduler/status": {"get": {"tags": ["推送监控"], "summary": "Get Scheduler Status", "description": "获取调度器状态（管理员权限）", "operationId": "get_scheduler_status_api_v1_push_monitoring_scheduler_status_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Scheduler Status Api V1 Push Monitoring Scheduler Status Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/push/monitoring/scheduler/restart": {"post": {"tags": ["推送监控"], "summary": "Restart Scheduler", "description": "重启调度器（管理员权限）", "operationId": "restart_scheduler_api_v1_push_monitoring_scheduler_restart_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": {"type": "string"}, "type": "object", "title": "Response Restart Scheduler Api V1 Push Monitoring Scheduler Restart Post"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/push/monitoring/channels/status": {"get": {"tags": ["推送监控"], "summary": "Get Channels Status", "description": "获取推送渠道状态（管理员权限）", "operationId": "get_channels_status_api_v1_push_monitoring_channels_status_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Channels Status Api V1 Push Monitoring Channels Status Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/push/analytics/comprehensive-report": {"get": {"tags": ["推送分析"], "summary": "Get Comprehensive Analytics Report", "description": "获取综合推送分析报告（需要advanced_subscription权限）", "operationId": "get_comprehensive_analytics_report_api_v1_push_analytics_comprehensive_report_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "days", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 90, "minimum": 1, "description": "分析天数", "default": 7, "title": "Days"}, "description": "分析天数"}, {"name": "tier", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/PushTier"}, {"type": "null"}], "description": "层级过滤", "title": "Tier"}, "description": "层级过滤"}, {"name": "channel", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/app__models__push_layer__PushChannel"}, {"type": "null"}], "description": "渠道过滤", "title": "Channel"}, "description": "渠道过滤"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Comprehensive Analytics Report Api V1 Push Analytics Comprehensive Report Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/push/analytics/tier-performance": {"get": {"tags": ["推送分析"], "summary": "Get Tier Performance Analysis", "description": "获取层级性能分析（需要advanced_subscription权限）", "operationId": "get_tier_performance_analysis_api_v1_push_analytics_tier_performance_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "days", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 90, "minimum": 1, "description": "分析天数", "default": 7, "title": "Days"}, "description": "分析天数"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Tier Performance Analysis Api V1 Push Analytics Tier Performance Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/push/analytics/channel-effectiveness": {"get": {"tags": ["推送分析"], "summary": "Get Channel Effectiveness Analysis", "description": "获取渠道效果分析（需要advanced_subscription权限）", "operationId": "get_channel_effectiveness_analysis_api_v1_push_analytics_channel_effectiveness_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "days", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 90, "minimum": 1, "description": "分析天数", "default": 7, "title": "Days"}, "description": "分析天数"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Channel Effectiveness Analysis Api V1 Push Analytics Channel Effectiveness Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/push/analytics/time-patterns": {"get": {"tags": ["推送分析"], "summary": "Get Time Pattern Analysis", "description": "获取时间模式分析（需要advanced_subscription权限）", "operationId": "get_time_pattern_analysis_api_v1_push_analytics_time_patterns_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "days", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 90, "minimum": 7, "description": "分析天数", "default": 30, "title": "Days"}, "description": "分析天数"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Time Pattern Analysis Api V1 Push Analytics Time Patterns Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/push/analytics/optimization-insights": {"get": {"tags": ["推送分析"], "summary": "Get Optimization Insights", "description": "获取优化洞察和建议（管理员权限）", "operationId": "get_optimization_insights_api_v1_push_analytics_optimization_insights_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "days", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 90, "minimum": 1, "description": "分析天数", "default": 7, "title": "Days"}, "description": "分析天数"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Optimization Insights Api V1 Push Analytics Optimization Insights Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/push/analytics/real-time-metrics": {"get": {"tags": ["推送分析"], "summary": "Get Real Time Metrics", "description": "获取实时推送指标（需要advanced_subscription权限）", "operationId": "get_real_time_metrics_api_v1_push_analytics_real_time_metrics_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Real Time Metrics Api V1 Push Analytics Real Time Metrics Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/health": {"get": {"summary": "Health", "operationId": "health_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/db-check": {"get": {"summary": "Db Check", "operationId": "db_check_db_check_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"AlertRuleRequest": {"properties": {"name": {"type": "string", "title": "Name", "description": "规则名称"}, "metric": {"type": "string", "title": "Metric", "description": "监控指标"}, "operator": {"type": "string", "title": "Operator", "description": "比较操作符"}, "threshold": {"type": "number", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "阈值"}, "duration": {"type": "integer", "title": "Duration", "description": "持续时间（秒）", "default": 0}, "severity": {"type": "string", "title": "Severity", "description": "严重级别", "default": "warning"}, "enabled": {"type": "boolean", "title": "Enabled", "description": "是否启用", "default": true}, "description": {"type": "string", "title": "Description", "description": "规则描述", "default": ""}}, "type": "object", "required": ["name", "metric", "operator", "threshold"], "title": "AlertRuleRequest", "description": "告警规则请求模型"}, "Body_send_custom_push_api_v1_push_custom_post": {"properties": {"channel": {"type": "string", "title": "Channel", "description": "推送渠道"}, "target": {"type": "string", "title": "Target", "description": "推送目标"}, "title": {"type": "string", "title": "Title", "description": "消息标题"}, "content": {"type": "string", "title": "Content", "description": "消息内容"}, "message_type": {"type": "string", "title": "Message Type", "description": "消息类型", "default": "text"}}, "type": "object", "required": ["channel", "target", "title", "content"], "title": "Body_send_custom_push_api_v1_push_custom_post"}, "Body_test_push_api_v1_push_test_post": {"properties": {"channel": {"type": "string", "title": "Channel", "description": "推送渠道"}, "target": {"type": "string", "title": "Target", "description": "推送目标"}, "message": {"type": "string", "title": "Message", "description": "测试消息内容", "default": "这是一条测试消息"}}, "type": "object", "required": ["channel", "target"], "title": "Body_test_push_api_v1_push_test_post"}, "CacheOperationRequest": {"properties": {"operation": {"type": "string", "title": "Operation", "description": "操作类型: get/set/delete/clear"}, "key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Key", "description": "缓存键"}, "value": {"anyOf": [{}, {"type": "null"}], "title": "Value", "description": "缓存值"}, "ttl": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Ttl", "description": "过期时间（秒）"}}, "type": "object", "required": ["operation"], "title": "CacheOperationRequest", "description": "缓存操作请求模型"}, "ChannelConfig": {"properties": {"channel": {"$ref": "#/components/schemas/app__schemas__subscription__PushChannel", "description": "推送渠道"}, "config": {"type": "object", "title": "Config", "description": "渠道特定配置"}, "enabled": {"type": "boolean", "title": "Enabled", "description": "是否启用", "default": true}}, "type": "object", "required": ["channel", "config"], "title": "ChannelConfig", "description": "推送渠道配置"}, "CompanyConfig": {"properties": {"name": {"type": "string", "maxLength": 200, "minLength": 1, "title": "Name", "description": "公司名称"}, "stock_code": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Stock Code", "description": "股票代码"}}, "type": "object", "required": ["name"], "title": "CompanyConfig", "description": "公司配置"}, "ComplianceCheckRequest": {"properties": {"content": {"type": "string", "title": "Content", "description": "待检查内容"}, "strict_mode": {"type": "boolean", "title": "Strict Mode", "description": "是否启用严格模式", "default": false}}, "type": "object", "required": ["content"], "title": "ComplianceCheckRequest", "description": "合规检查请求模型"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "KeywordConfig": {"properties": {"keyword": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Keyword", "description": "关键词"}, "weight": {"type": "number", "maximum": 10.0, "minimum": 0.1, "title": "Weight", "description": "权重，范围0.1-10.0", "default": 1.0}}, "type": "object", "required": ["keyword"], "title": "KeywordConfig", "description": "关键词配置"}, "NewsCreate": {"properties": {"title": {"type": "string", "maxLength": 500, "minLength": 1, "title": "Title", "description": "新闻标题"}, "content": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Content", "description": "新闻内容"}, "summary": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Summary", "description": "新闻摘要"}, "source": {"$ref": "#/components/schemas/NewsSource", "description": "新闻来源"}, "source_url": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Source Url", "description": "原文链接"}, "source_id": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Source Id", "description": "来源系统中的ID"}, "published_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Published At", "description": "发布时间"}, "category": {"anyOf": [{"$ref": "#/components/schemas/app__models__news__NewsCategory"}, {"type": "null"}], "description": "新闻分类", "default": "other"}, "tags": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Tags", "description": "标签列表", "default": []}, "importance_score": {"anyOf": [{"type": "integer", "maximum": 100.0, "minimum": 0.0}, {"type": "null"}], "title": "Importance Score", "description": "重要性评分", "default": 50}, "sentiment": {"anyOf": [{"$ref": "#/components/schemas/NewsSentiment"}, {"type": "null"}], "description": "情感倾向", "default": "unknown"}, "entities": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Entities", "description": "实体信息"}, "companies": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Companies", "description": "相关公司", "default": []}, "stock_codes": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Stock Codes", "description": "相关股票代码", "default": []}}, "type": "object", "required": ["title", "source"], "title": "NewsCreate", "description": "创建新闻请求"}, "NewsListResponse": {"properties": {"items": {"items": {"$ref": "#/components/schemas/NewsResponse"}, "type": "array", "title": "Items"}, "total": {"type": "integer", "title": "Total"}, "page": {"type": "integer", "title": "Page"}, "limit": {"type": "integer", "title": "Limit"}, "pages": {"type": "integer", "title": "Pages"}}, "type": "object", "required": ["items", "total", "page", "limit", "pages"], "title": "NewsListResponse", "description": "新闻列表响应"}, "NewsResponse": {"properties": {"id": {"type": "integer", "title": "Id"}, "title": {"type": "string", "title": "Title"}, "content": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Content"}, "summary": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Summary"}, "source": {"$ref": "#/components/schemas/NewsSource"}, "source_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Source Url"}, "source_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Source Id"}, "published_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Published At"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "category": {"$ref": "#/components/schemas/app__models__news__NewsCategory"}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags"}, "importance_score": {"type": "integer", "title": "Importance Score"}, "sentiment": {"$ref": "#/components/schemas/NewsSentiment"}, "entities": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Entities"}, "companies": {"items": {"type": "string"}, "type": "array", "title": "Companies"}, "stock_codes": {"items": {"type": "string"}, "type": "array", "title": "Stock Codes"}, "content_hash": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Content Hash"}, "word_count": {"type": "integer", "title": "Word Count"}, "is_processed": {"type": "boolean", "title": "Is Processed"}, "is_duplicate": {"type": "boolean", "title": "Is Duplicate"}}, "type": "object", "required": ["id", "title", "content", "summary", "source", "source_url", "source_id", "published_at", "created_at", "updated_at", "category", "tags", "importance_score", "sentiment", "entities", "companies", "stock_codes", "content_hash", "word_count", "is_processed", "is_duplicate"], "title": "NewsResponse", "description": "新闻响应"}, "NewsSearchResponse": {"properties": {"items": {"items": {"$ref": "#/components/schemas/NewsResponse"}, "type": "array", "title": "Items"}, "total": {"type": "integer", "title": "Total"}, "page": {"type": "integer", "title": "Page"}, "limit": {"type": "integer", "title": "Limit"}, "pages": {"type": "integer", "title": "Pages"}, "query": {"type": "string", "title": "Query"}, "search_time": {"type": "string", "format": "date-time", "title": "Search Time"}}, "type": "object", "required": ["items", "total", "page", "limit", "pages", "query", "search_time"], "title": "NewsSearchResponse", "description": "新闻搜索响应"}, "NewsSentiment": {"type": "string", "enum": ["positive", "negative", "neutral", "unknown"], "title": "NewsSentiment", "description": "情感分析枚举"}, "NewsSource": {"type": "string", "enum": ["sse", "szse", "csrc", "rss", "manual"], "title": "NewsSource", "description": "新闻来源枚举"}, "NewsStatistics": {"properties": {"total_count": {"type": "integer", "title": "Total Count"}, "today_count": {"type": "integer", "title": "Today Count"}, "processed_count": {"type": "integer", "title": "Processed Count"}, "duplicate_count": {"type": "integer", "title": "Duplicate Count"}, "avg_importance": {"type": "number", "title": "Avg Importance"}, "source_distribution": {"additionalProperties": {"type": "integer"}, "type": "object", "title": "Source Distribution"}, "category_distribution": {"additionalProperties": {"type": "integer"}, "type": "object", "title": "Category Distribution"}, "sentiment_distribution": {"additionalProperties": {"type": "integer"}, "type": "object", "title": "Sentiment Distribution"}, "period_start": {"type": "string", "format": "date-time", "title": "Period Start"}, "period_end": {"type": "string", "format": "date-time", "title": "Period End"}}, "type": "object", "required": ["total_count", "today_count", "processed_count", "duplicate_count", "avg_importance", "source_distribution", "category_distribution", "sentiment_distribution", "period_start", "period_end"], "title": "NewsStatistics", "description": "新闻统计数据"}, "NewsUpdate": {"properties": {"title": {"anyOf": [{"type": "string", "maxLength": 500, "minLength": 1}, {"type": "null"}], "title": "Title", "description": "新闻标题"}, "content": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Content", "description": "新闻内容"}, "summary": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Summary", "description": "新闻摘要"}, "category": {"anyOf": [{"$ref": "#/components/schemas/app__models__news__NewsCategory"}, {"type": "null"}], "description": "新闻分类"}, "tags": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Tags", "description": "标签列表"}, "importance_score": {"anyOf": [{"type": "integer", "maximum": 100.0, "minimum": 0.0}, {"type": "null"}], "title": "Importance Score", "description": "重要性评分"}, "sentiment": {"anyOf": [{"$ref": "#/components/schemas/NewsSentiment"}, {"type": "null"}], "description": "情感倾向"}, "entities": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Entities", "description": "实体信息"}, "companies": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Companies", "description": "相关公司"}, "stock_codes": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Stock Codes", "description": "相关股票代码"}, "is_processed": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Processed", "description": "是否已处理"}}, "type": "object", "title": "NewsUpdate", "description": "更新新闻请求"}, "PrivacyCheckRequest": {"properties": {"data": {"type": "object", "title": "Data", "description": "待检查数据"}, "data_type": {"type": "string", "title": "Data Type", "description": "数据类型", "default": "unknown"}}, "type": "object", "required": ["data"], "title": "PrivacyCheckRequest", "description": "隐私检查请求模型"}, "PushDeliveryLogResponse": {"properties": {"id": {"type": "integer", "title": "Id", "description": "日志ID"}, "task_id": {"type": "integer", "title": "Task Id", "description": "推送任务ID"}, "user_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "User Id", "description": "用户ID"}, "channel": {"type": "string", "title": "Channel", "description": "推送渠道"}, "target_address": {"type": "string", "title": "Target Address", "description": "目标地址"}, "status": {"type": "string", "title": "Status", "description": "投递状态"}, "sent_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "<PERSON><PERSON>", "description": "发送时间"}, "delivered_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Delivered At", "description": "送达时间"}, "read_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Read At", "description": "阅读时间"}, "clicked_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Clicked At", "description": "点击时间"}, "response_code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Response Code", "description": "响应码"}, "error_message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error Message", "description": "错误消息"}, "retry_count": {"type": "integer", "title": "Retry Count", "description": "重试次数"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "创建时间"}}, "type": "object", "required": ["id", "task_id", "channel", "target_address", "status", "retry_count", "created_at"], "title": "PushDeliveryLogResponse", "description": "推送投递日志响应模式"}, "PushGroupCreate": {"properties": {"name": {"type": "string", "maxLength": 100, "title": "Name", "description": "群组名称"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "群组描述"}, "group_type": {"type": "string", "title": "Group Type", "description": "群组类型"}, "channels": {"items": {"$ref": "#/components/schemas/app__models__push_layer__PushChannel"}, "type": "array", "title": "Channels", "description": "支持的推送渠道"}, "tier_permissions": {"additionalProperties": {"type": "boolean"}, "type": "object", "title": "Tier Permissions", "description": "层级权限配置"}, "quiet_hours_start": {"type": "string", "title": "Quiet Hours Start", "description": "免打扰开始时间", "default": "22:00"}, "quiet_hours_end": {"type": "string", "title": "Quiet Hours End", "description": "免打扰结束时间", "default": "07:00"}, "max_daily_pushes": {"type": "integer", "maximum": 1000.0, "minimum": 1.0, "title": "Max Daily Pushes", "description": "每日最大推送数", "default": 50}}, "type": "object", "required": ["name", "group_type", "channels", "tier_permissions"], "title": "PushGroupCreate", "description": "推送群组创建请求模式", "example": {"channels": ["wechat", "email"], "description": "高净值投资者专属群组", "group_type": "vip_investors", "max_daily_pushes": 100, "name": "VIP投资者群", "quiet_hours_end": "07:00", "quiet_hours_start": "22:00", "tier_permissions": {"T1": true, "T2": true, "T3": true}}}, "PushGroupResponse": {"properties": {"id": {"type": "integer", "title": "Id", "description": "群组ID"}, "name": {"type": "string", "title": "Name", "description": "群组名称"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "群组描述"}, "group_type": {"type": "string", "title": "Group Type", "description": "群组类型"}, "channels": {"items": {"type": "string"}, "type": "array", "title": "Channels", "description": "支持的推送渠道"}, "tier_permissions": {"additionalProperties": {"type": "boolean"}, "type": "object", "title": "Tier Permissions", "description": "层级权限配置"}, "member_count": {"type": "integer", "title": "Member Count", "description": "成员数量"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "是否启用"}, "total_pushes": {"type": "integer", "title": "Total Pushes", "description": "总推送数"}, "last_push_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Push At", "description": "最后推送时间"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "更新时间"}}, "type": "object", "required": ["id", "name", "group_type", "channels", "tier_permissions", "member_count", "is_active", "total_pushes", "created_at", "updated_at"], "title": "PushGroupResponse", "description": "推送群组响应模式"}, "PushLogResponse": {"properties": {"id": {"type": "integer", "title": "Id", "description": "推送日志ID"}, "user_id": {"type": "integer", "title": "User Id", "description": "用户ID"}, "subscription_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Subscription Id", "description": "订阅ID"}, "news_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "News Id", "description": "新闻ID"}, "push_type": {"type": "string", "title": "Push Type", "description": "推送类型"}, "channel": {"$ref": "#/components/schemas/app__schemas__push__PushChannel", "description": "推送渠道"}, "recipient": {"type": "string", "title": "Recipient", "description": "接收者"}, "title": {"type": "string", "title": "Title", "description": "推送标题"}, "content": {"type": "string", "title": "Content", "description": "推送内容"}, "status": {"$ref": "#/components/schemas/PushStatus-Output", "description": "推送状态"}, "sent_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "<PERSON><PERSON>", "description": "发送时间"}, "delivered_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Delivered At", "description": "送达时间"}, "error_message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error Message", "description": "错误信息"}, "metadata": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "description": "附加元数据"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "创建时间"}}, "type": "object", "required": ["id", "user_id", "push_type", "channel", "recipient", "title", "content", "status", "created_at"], "title": "PushLogResponse", "description": "推送日志响应模式"}, "PushMessageCreate": {"properties": {"title": {"type": "string", "maxLength": 200, "title": "Title", "description": "推送标题"}, "content": {"type": "string", "maxLength": 2000, "title": "Content", "description": "推送内容"}, "channel": {"$ref": "#/components/schemas/app__schemas__push__PushChannel", "description": "推送渠道"}, "recipient": {"type": "string", "title": "Recipient", "description": "接收者"}, "priority": {"$ref": "#/components/schemas/PushPriority", "description": "推送优先级", "default": "normal"}, "scheduled_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Scheduled At", "description": "计划推送时间"}, "template_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Template Id", "description": "使用的模板ID"}, "variables": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Variables", "description": "模板变量"}, "metadata": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "description": "附加元数据"}}, "type": "object", "required": ["title", "content", "channel", "recipient"], "title": "PushMessageCreate", "description": "创建推送消息请求模式"}, "PushMessageResponse": {"properties": {"id": {"type": "integer", "title": "Id", "description": "推送消息ID"}, "title": {"type": "string", "title": "Title", "description": "推送标题"}, "content": {"type": "string", "title": "Content", "description": "推送内容"}, "channel": {"$ref": "#/components/schemas/app__schemas__push__PushChannel", "description": "推送渠道"}, "recipient": {"type": "string", "title": "Recipient", "description": "接收者"}, "status": {"$ref": "#/components/schemas/PushStatus-Output", "description": "推送状态"}, "priority": {"$ref": "#/components/schemas/PushPriority", "description": "推送优先级"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "创建时间"}, "scheduled_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Scheduled At", "description": "计划推送时间"}, "sent_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "<PERSON><PERSON>", "description": "实际发送时间"}, "delivered_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Delivered At", "description": "送达时间"}, "error_message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error Message", "description": "错误信息"}, "metadata": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "description": "附加元数据"}}, "type": "object", "required": ["id", "title", "content", "channel", "recipient", "status", "priority", "created_at"], "title": "PushMessageResponse", "description": "推送消息响应模式"}, "PushPriority": {"type": "string", "enum": ["low", "normal", "high", "urgent"], "title": "PushPriority", "description": "推送优先级枚举"}, "PushRuleCreate": {"properties": {"name": {"type": "string", "maxLength": 100, "title": "Name", "description": "规则名称"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "规则描述"}, "trigger_conditions": {"type": "object", "title": "<PERSON><PERSON> Conditions", "description": "触发条件"}, "keywords": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Keywords", "description": "关键词列表"}, "importance_threshold": {"type": "integer", "maximum": 100.0, "minimum": 0.0, "title": "Importance Threshold", "description": "重要性阈值", "default": 80}, "tier": {"$ref": "#/components/schemas/PushTier", "description": "推送层级"}, "urgency": {"$ref": "#/components/schemas/PushUrgency", "description": "紧急程度", "default": "medium"}, "channels": {"items": {"$ref": "#/components/schemas/app__models__push_layer__PushChannel"}, "type": "array", "title": "Channels", "description": "推送渠道列表"}, "cooldown_minutes": {"type": "integer", "maximum": 1440.0, "minimum": 0.0, "title": "Cooldown Minutes", "description": "冷却时间(分钟)", "default": 15}, "quiet_hours_start": {"type": "string", "title": "Quiet Hours Start", "description": "免打扰开始时间", "default": "22:00"}, "quiet_hours_end": {"type": "string", "title": "Quiet Hours End", "description": "免打扰结束时间", "default": "07:00"}, "priority": {"type": "integer", "maximum": 1000.0, "minimum": 1.0, "title": "Priority", "description": "规则优先级", "default": 100}}, "type": "object", "required": ["name", "trigger_conditions", "tier", "channels"], "title": "PushRuleCreate", "description": "推送规则创建请求模式", "example": {"channels": ["wechat", "push"], "cooldown_minutes": 30, "description": "当市场出现重大异动时立即推送", "importance_threshold": 90, "keywords": ["暴跌", "暴涨", "熔断", "停牌"], "name": "重大市场异动告警", "priority": 100, "quiet_hours_end": "07:00", "quiet_hours_start": "22:00", "tier": "T1", "trigger_conditions": {"market_change_threshold": 3.0, "volume_spike_threshold": 2.0}, "urgency": "critical"}}, "PushRuleResponse": {"properties": {"id": {"type": "integer", "title": "Id", "description": "规则ID"}, "name": {"type": "string", "title": "Name", "description": "规则名称"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "规则描述"}, "trigger_conditions": {"type": "object", "title": "<PERSON><PERSON> Conditions", "description": "触发条件"}, "keywords": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Keywords", "description": "关键词列表"}, "importance_threshold": {"type": "integer", "title": "Importance Threshold", "description": "重要性阈值"}, "tier": {"type": "string", "title": "Tier", "description": "推送层级"}, "urgency": {"type": "string", "title": "Urgency", "description": "紧急程度"}, "channels": {"items": {"type": "string"}, "type": "array", "title": "Channels", "description": "推送渠道列表"}, "cooldown_minutes": {"type": "integer", "title": "Cooldown Minutes", "description": "冷却时间"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "是否启用"}, "priority": {"type": "integer", "title": "Priority", "description": "规则优先级"}, "trigger_count": {"type": "integer", "title": "Trigger <PERSON>", "description": "触发次数"}, "last_triggered_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Triggered At", "description": "最后触发时间"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "更新时间"}}, "type": "object", "required": ["id", "name", "trigger_conditions", "importance_threshold", "tier", "urgency", "channels", "cooldown_minutes", "is_active", "priority", "trigger_count", "created_at", "updated_at"], "title": "PushRuleResponse", "description": "推送规则响应模式"}, "PushStatistics": {"properties": {"total_tasks": {"type": "integer", "title": "Total Tasks", "description": "总任务数"}, "tasks_by_tier": {"additionalProperties": {"type": "integer"}, "type": "object", "title": "Tasks By Tier", "description": "按层级统计"}, "tasks_by_status": {"additionalProperties": {"type": "integer"}, "type": "object", "title": "Tasks By Status", "description": "按状态统计"}, "tasks_by_channel": {"additionalProperties": {"type": "integer"}, "type": "object", "title": "Tasks By Channel", "description": "按渠道统计"}, "success_rate": {"type": "number", "title": "Success Rate", "description": "成功率"}, "avg_delivery_time": {"type": "number", "title": "Avg Delivery Time", "description": "平均投递时间(秒)"}, "total_sent": {"type": "integer", "title": "Total Sent", "description": "总发送数"}, "total_delivered": {"type": "integer", "title": "Total Delivered", "description": "总送达数"}, "total_read": {"type": "integer", "title": "Total Read", "description": "总阅读数"}, "total_clicked": {"type": "integer", "title": "Total Clicked", "description": "总点击数"}, "delivery_rate": {"type": "number", "title": "Delivery Rate", "description": "送达率"}, "read_rate": {"type": "number", "title": "Read Rate", "description": "阅读率"}, "click_rate": {"type": "number", "title": "Click Rate", "description": "点击率"}}, "type": "object", "required": ["total_tasks", "tasks_by_tier", "tasks_by_status", "tasks_by_channel", "success_rate", "avg_delivery_time", "total_sent", "total_delivered", "total_read", "total_clicked", "delivery_rate", "read_rate", "click_rate"], "title": "PushStatistics", "description": "推送统计信息响应模式", "example": {"avg_delivery_time": 2.5, "click_rate": 12.0, "delivery_rate": 95.0, "read_rate": 60.0, "success_rate": 95.0, "tasks_by_channel": {"email": 400, "push": 300, "wechat": 800}, "tasks_by_status": {"failed": 45, "pending": 30, "success": 1425}, "tasks_by_tier": {"T1": 50, "T2": 300, "T3": 1150}, "total_clicked": 1710, "total_delivered": 14250, "total_read": 8550, "total_sent": 15000, "total_tasks": 1500}}, "PushStatus-Input": {"type": "string", "enum": ["pending", "processing", "success", "failed", "cancelled"], "title": "<PERSON>ush<PERSON><PERSON><PERSON>", "description": "推送状态枚举"}, "PushStatus-Output": {"type": "string", "enum": ["pending", "sending", "sent", "failed", "cancelled"], "title": "<PERSON>ush<PERSON><PERSON><PERSON>", "description": "推送状态枚举"}, "PushTaskCreate": {"properties": {"title": {"type": "string", "maxLength": 255, "title": "Title", "description": "推送标题"}, "content": {"type": "string", "title": "Content", "description": "推送内容"}, "tier": {"$ref": "#/components/schemas/PushTier", "description": "推送层级"}, "urgency": {"$ref": "#/components/schemas/PushUrgency", "description": "紧急程度", "default": "medium"}, "channels": {"items": {"$ref": "#/components/schemas/app__models__push_layer__PushChannel"}, "type": "array", "title": "Channels", "description": "推送渠道列表"}, "target_users": {"anyOf": [{"items": {"type": "integer"}, "type": "array"}, {"type": "null"}], "title": "Target Users", "description": "目标用户ID列表"}, "target_groups": {"anyOf": [{"items": {"type": "integer"}, "type": "array"}, {"type": "null"}], "title": "Target Groups", "description": "目标群组ID列表"}, "news_ids": {"anyOf": [{"items": {"type": "integer"}, "type": "array"}, {"type": "null"}], "title": "News Ids", "description": "关联新闻ID列表"}, "report_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Report Id", "description": "关联简报ID"}, "scheduled_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Scheduled At", "description": "计划推送时间"}, "metadata": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "description": "元数据"}}, "type": "object", "required": ["title", "content", "tier", "channels"], "title": "PushTaskCreate", "description": "推送任务创建请求模式", "example": {"channels": ["wechat", "push"], "content": "沪深300指数暴跌超过5%，请关注风险控制", "news_ids": [123, 456], "scheduled_at": "2025-08-18T14:30:00", "target_groups": [1, 2], "tier": "T1", "title": "【T1告警】重大市场异动", "urgency": "critical"}}, "PushTaskResponse": {"properties": {"id": {"type": "integer", "title": "Id", "description": "任务ID"}, "title": {"type": "string", "title": "Title", "description": "推送标题"}, "content": {"type": "string", "title": "Content", "description": "推送内容"}, "tier": {"type": "string", "title": "Tier", "description": "推送层级"}, "urgency": {"type": "string", "title": "Urgency", "description": "紧急程度"}, "channels": {"items": {"type": "string"}, "type": "array", "title": "Channels", "description": "推送渠道列表"}, "status": {"type": "string", "title": "Status", "description": "推送状态"}, "scheduled_at": {"type": "string", "format": "date-time", "title": "Scheduled At", "description": "计划推送时间"}, "executed_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Executed At", "description": "实际执行时间"}, "completed_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Completed At", "description": "完成时间"}, "retry_count": {"type": "integer", "title": "Retry Count", "description": "重试次数"}, "sent_count": {"type": "integer", "title": "<PERSON>t Count", "description": "发送数量"}, "delivered_count": {"type": "integer", "title": "Delivered Count", "description": "送达数量"}, "read_count": {"type": "integer", "title": "Read Count", "description": "阅读数量"}, "click_count": {"type": "integer", "title": "Click Count", "description": "点击数量"}, "error_message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error Message", "description": "错误信息"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "更新时间"}}, "type": "object", "required": ["id", "title", "content", "tier", "urgency", "channels", "status", "scheduled_at", "retry_count", "sent_count", "delivered_count", "read_count", "click_count", "created_at", "updated_at"], "title": "PushTaskResponse", "description": "推送任务响应模式"}, "PushTier": {"type": "string", "enum": ["T1", "T2", "T3"], "title": "PushTier", "description": "推送层级枚举"}, "PushUrgency": {"type": "string", "enum": ["critical", "high", "medium", "low"], "title": "<PERSON>ush<PERSON>rgency", "description": "推送紧急程度枚举"}, "ReportFeedbackCreate": {"properties": {"report_id": {"type": "integer", "title": "Report Id", "description": "简报ID"}, "rating": {"type": "integer", "maximum": 5.0, "minimum": 1.0, "title": "Rating", "description": "评分(1-5)"}, "feedback_type": {"type": "string", "maxLength": 50, "title": "Feedback Type", "description": "反馈类型"}, "content": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Content", "description": "反馈内容"}, "suggestions": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Suggestions", "description": "改进建议"}, "is_anonymous": {"type": "boolean", "title": "Is Anonymous", "description": "是否匿名反馈", "default": false}, "tags": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Tags", "description": "反馈标签"}}, "type": "object", "required": ["report_id", "rating", "feedback_type"], "title": "ReportFeedbackCreate", "description": "创建简报反馈请求模式"}, "ReportGenerateRequest": {"properties": {"type": {"$ref": "#/components/schemas/ReportType", "description": "简报类型"}, "title": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Title", "description": "自定义标题"}, "date_range": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Date Range", "description": "时间范围"}, "filters": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Filters", "description": "内容过滤条件"}, "template_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Template Id", "description": "使用的模板ID"}, "custom_sections": {"anyOf": [{"items": {"type": "object"}, "type": "array"}, {"type": "null"}], "title": "Custom Sections", "description": "自定义章节"}, "priority_keywords": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Priority Keywords", "description": "优先关键词"}, "exclude_keywords": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Exclude Keywords", "description": "排除关键词"}, "max_items_per_section": {"anyOf": [{"type": "integer", "maximum": 100.0, "minimum": 1.0}, {"type": "null"}], "title": "Max Items Per Section", "description": "每章节最大条目数", "default": 10}, "include_charts": {"type": "boolean", "title": "Include Charts", "description": "是否包含图表", "default": true}, "include_summary": {"type": "boolean", "title": "Include Summary", "description": "是否包含摘要", "default": true}}, "type": "object", "required": ["type"], "title": "ReportGenerateRequest", "description": "生成简报请求模式"}, "ReportListResponse": {"properties": {"reports": {"items": {"$ref": "#/components/schemas/ReportResponse"}, "type": "array", "title": "Reports", "description": "简报列表"}, "total": {"type": "integer", "title": "Total", "description": "总数量"}, "page": {"type": "integer", "title": "Page", "description": "当前页码"}, "size": {"type": "integer", "title": "Size", "description": "每页数量"}, "pages": {"type": "integer", "title": "Pages", "description": "总页数"}}, "type": "object", "required": ["reports", "total", "page", "size", "pages"], "title": "ReportListResponse", "description": "简报列表响应模式"}, "ReportResponse": {"properties": {"id": {"type": "integer", "title": "Id", "description": "简报ID"}, "title": {"type": "string", "title": "Title", "description": "简报标题"}, "type": {"$ref": "#/components/schemas/ReportType", "description": "简报类型"}, "status": {"$ref": "#/components/schemas/ReportStatus", "description": "简报状态"}, "content": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Content", "description": "简报内容"}, "summary": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Summary", "description": "简报摘要"}, "template_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Template Id", "description": "模板ID"}, "news_count": {"type": "integer", "title": "News Count", "description": "包含新闻数量"}, "word_count": {"type": "integer", "title": "Word Count", "description": "字数统计"}, "read_time": {"type": "integer", "title": "Read Time", "description": "预估阅读时间"}, "quality_score": {"type": "integer", "title": "Quality Score", "description": "内容质量评分"}, "report_date": {"type": "string", "format": "date-time", "title": "Report Date", "description": "简报日期"}, "published_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Published At", "description": "发布时间"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "创建时间"}, "sections": {"anyOf": [{"items": {"$ref": "#/components/schemas/ReportSectionResponse"}, "type": "array"}, {"type": "null"}], "title": "Sections", "description": "简报章节"}}, "type": "object", "required": ["id", "title", "type", "status", "news_count", "word_count", "read_time", "quality_score", "report_date", "created_at"], "title": "ReportResponse", "description": "简报响应模式"}, "ReportSectionResponse": {"properties": {"id": {"type": "integer", "title": "Id", "description": "章节ID"}, "section_name": {"type": "string", "title": "Section Name", "description": "章节名称"}, "section_title": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Section Title", "description": "章节标题"}, "section_order": {"type": "integer", "title": "Section Order", "description": "章节顺序"}, "content": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Content", "description": "章节内容"}, "content_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Content Type", "description": "内容类型"}, "news_count": {"type": "integer", "title": "News Count", "description": "新闻数量"}, "word_count": {"type": "integer", "title": "Word Count", "description": "字数"}, "importance_score": {"type": "integer", "title": "Importance Score", "description": "重要性评分"}}, "type": "object", "required": ["id", "section_name", "section_order", "news_count", "word_count", "importance_score"], "title": "ReportSectionResponse", "description": "简报章节响应模式"}, "ReportStatistics": {"properties": {"total_reports": {"type": "integer", "title": "Total Reports", "description": "总简报数"}, "reports_today": {"type": "integer", "title": "Reports Today", "description": "今日简报数"}, "reports_this_week": {"type": "integer", "title": "Reports This Week", "description": "本周简报数"}, "reports_this_month": {"type": "integer", "title": "Reports This Month", "description": "本月简报数"}, "avg_generation_time": {"type": "number", "title": "Avg Generation Time", "description": "平均生成时间(秒)"}, "most_popular_type": {"type": "string", "title": "Most Popular Type", "description": "最受欢迎的简报类型"}, "total_views": {"type": "integer", "title": "Total Views", "description": "总浏览量"}, "total_downloads": {"type": "integer", "title": "Total Downloads", "description": "总下载量"}, "user_feedback_avg": {"type": "number", "title": "User Feedback Avg", "description": "用户反馈平均分"}, "active_subscriptions": {"type": "integer", "title": "Active Subscriptions", "description": "活跃订阅数"}}, "type": "object", "required": ["total_reports", "reports_today", "reports_this_week", "reports_this_month", "avg_generation_time", "most_popular_type", "total_views", "total_downloads", "user_feedback_avg", "active_subscriptions"], "title": "ReportStatistics", "description": "简报统计信息模式"}, "ReportStatus": {"type": "string", "enum": ["draft", "generating", "completed", "published", "failed"], "title": "ReportStatus", "description": "简报状态枚举"}, "ReportType": {"type": "string", "enum": ["morning", "evening", "hourly", "special"], "title": "ReportType", "description": "简报类型枚举"}, "ScheduleConfig": {"properties": {"enabled": {"type": "boolean", "title": "Enabled", "description": "是否启用定时推送", "default": true}, "weekdays": {"items": {"type": "integer"}, "type": "array", "title": "Weekdays", "description": "推送日期，1-7表示周一到周日", "default": [1, 2, 3, 4, 5]}, "hours": {"items": {"type": "integer"}, "type": "array", "title": "Hours", "description": "推送小时，0-23", "default": [9, 12, 18]}, "timezone": {"type": "string", "title": "Timezone", "description": "时区", "default": "Asia/Shanghai"}}, "type": "object", "title": "ScheduleConfig", "description": "推送时间安排配置"}, "ScheduleConfigRequest": {"properties": {"task": {"type": "string", "title": "Task", "description": "任务名称"}, "schedule_type": {"type": "string", "title": "Schedule Type", "description": "调度类型: cron/interval"}, "schedule_value": {"type": "string", "title": "Schedule Value", "description": "调度表达式"}, "queue": {"type": "string", "title": "Queue", "description": "队列名称", "default": "default"}, "priority": {"type": "integer", "title": "Priority", "description": "优先级 1-10", "default": 5}, "enabled": {"type": "boolean", "title": "Enabled", "description": "是否启用", "default": true}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "任务描述"}, "kwargs": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Kwar<PERSON>", "description": "任务参数"}}, "type": "object", "required": ["task", "schedule_type", "schedule_value"], "title": "ScheduleConfigRequest", "description": "调度配置请求模型"}, "ScheduleResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "message": {"type": "string", "title": "Message"}, "data": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Data"}}, "type": "object", "required": ["success", "message"], "title": "ScheduleResponse", "description": "调度响应模型"}, "SubscriptionCreate": {"properties": {"name": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Name", "description": "订阅名称"}, "keywords": {"anyOf": [{"items": {"$ref": "#/components/schemas/KeywordConfig"}, "type": "array"}, {"type": "null"}], "title": "Keywords", "description": "关键词列表", "default": []}, "companies": {"anyOf": [{"items": {"$ref": "#/components/schemas/CompanyConfig"}, "type": "array"}, {"type": "null"}], "title": "Companies", "description": "关注公司列表", "default": []}, "categories": {"anyOf": [{"items": {"$ref": "#/components/schemas/app__schemas__subscription__NewsCategory"}, "type": "array"}, {"type": "null"}], "title": "Categories", "description": "新闻分类列表", "default": []}, "channels": {"items": {"$ref": "#/components/schemas/ChannelConfig"}, "type": "array", "minItems": 1, "title": "Channels", "description": "推送渠道配置"}, "schedule": {"anyOf": [{"$ref": "#/components/schemas/ScheduleConfig"}, {"type": "null"}], "description": "推送时间安排"}}, "type": "object", "required": ["name", "channels"], "title": "SubscriptionCreate", "description": "创建订阅请求"}, "SubscriptionListResponse": {"properties": {"subscriptions": {"items": {"$ref": "#/components/schemas/SubscriptionResponse"}, "type": "array", "title": "Subscriptions"}, "total": {"type": "integer", "title": "Total"}, "page": {"type": "integer", "title": "Page"}, "size": {"type": "integer", "title": "Size"}}, "type": "object", "required": ["subscriptions", "total", "page", "size"], "title": "SubscriptionListResponse", "description": "订阅列表响应"}, "SubscriptionResponse": {"properties": {"id": {"type": "integer", "title": "Id"}, "user_id": {"type": "integer", "title": "User Id"}, "name": {"type": "string", "title": "Name"}, "keywords": {"items": {"$ref": "#/components/schemas/KeywordConfig"}, "type": "array", "title": "Keywords"}, "companies": {"items": {"$ref": "#/components/schemas/CompanyConfig"}, "type": "array", "title": "Companies"}, "categories": {"items": {"$ref": "#/components/schemas/app__schemas__subscription__NewsCategory"}, "type": "array", "title": "Categories"}, "channels": {"items": {"$ref": "#/components/schemas/ChannelConfig"}, "type": "array", "title": "Channels"}, "schedule": {"$ref": "#/components/schemas/ScheduleConfig"}, "status": {"$ref": "#/components/schemas/SubscriptionStatus"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["id", "user_id", "name", "keywords", "companies", "categories", "channels", "schedule", "status", "created_at", "updated_at"], "title": "SubscriptionResponse", "description": "订阅响应"}, "SubscriptionStats": {"properties": {"subscription_id": {"type": "integer", "title": "Subscription Id"}, "total_news_matched": {"type": "integer", "title": "Total News Matched", "description": "匹配的新闻总数", "default": 0}, "total_pushes_sent": {"type": "integer", "title": "Total Pushes <PERSON>", "description": "发送的推送总数", "default": 0}, "successful_pushes": {"type": "integer", "title": "Successful Pushes", "description": "成功的推送数", "default": 0}, "failed_pushes": {"type": "integer", "title": "Failed Pushes", "description": "失败的推送数", "default": 0}, "last_push_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Push At", "description": "最后推送时间"}}, "type": "object", "required": ["subscription_id"], "title": "SubscriptionStats", "description": "订阅统计信息"}, "SubscriptionStatus": {"type": "string", "enum": ["active", "paused"], "title": "SubscriptionStatus"}, "SubscriptionUpdate": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 100, "minLength": 1}, {"type": "null"}], "title": "Name", "description": "订阅名称"}, "keywords": {"anyOf": [{"items": {"$ref": "#/components/schemas/KeywordConfig"}, "type": "array"}, {"type": "null"}], "title": "Keywords", "description": "关键词列表"}, "companies": {"anyOf": [{"items": {"$ref": "#/components/schemas/CompanyConfig"}, "type": "array"}, {"type": "null"}], "title": "Companies", "description": "关注公司列表"}, "categories": {"anyOf": [{"items": {"$ref": "#/components/schemas/app__schemas__subscription__NewsCategory"}, "type": "array"}, {"type": "null"}], "title": "Categories", "description": "新闻分类列表"}, "channels": {"anyOf": [{"items": {"$ref": "#/components/schemas/ChannelConfig"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Channels", "description": "推送渠道配置"}, "schedule": {"anyOf": [{"$ref": "#/components/schemas/ScheduleConfig"}, {"type": "null"}], "description": "推送时间安排"}, "status": {"anyOf": [{"$ref": "#/components/schemas/SubscriptionStatus"}, {"type": "null"}], "description": "订阅状态"}}, "type": "object", "title": "SubscriptionUpdate", "description": "更新订阅请求"}, "TaskControlRequest": {"properties": {"action": {"type": "string", "title": "Action", "description": "操作类型: enable/disable/run_now"}}, "type": "object", "required": ["action"], "title": "TaskControlRequest", "description": "任务控制请求模型"}, "TemplateCreate": {"properties": {"name": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Name", "description": "模板名称"}, "tier": {"type": "string", "title": "Tier", "description": "推送层级", "default": "T3"}, "channel": {"type": "string", "title": "Channel", "description": "推送渠道", "default": "wechat"}, "title_template": {"type": "string", "minLength": 1, "title": "Title Template", "description": "标题模板"}, "content_template": {"type": "string", "minLength": 1, "title": "Content Template", "description": "内容模板"}, "format_config": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Format Config", "description": "格式配置"}, "is_default": {"type": "boolean", "title": "<PERSON>", "description": "是否默认模板", "default": false}}, "type": "object", "required": ["name", "title_template", "content_template"], "title": "TemplateCreate"}, "TemplateResponse": {"properties": {"id": {"type": "integer", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "tier": {"type": "string", "title": "Tier"}, "channel": {"type": "string", "title": "Channel"}, "title_template": {"type": "string", "title": "Title Template"}, "content_template": {"type": "string", "title": "Content Template"}, "format_config": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Format Config"}, "is_active": {"type": "boolean", "title": "Is Active"}, "is_default": {"type": "boolean", "title": "<PERSON>"}, "version": {"type": "string", "title": "Version"}, "usage_count": {"type": "integer", "title": "Usage Count"}, "created_at": {"type": "string", "title": "Created At"}, "updated_at": {"type": "string", "title": "Updated At"}}, "type": "object", "required": ["id", "name", "tier", "channel", "title_template", "content_template", "format_config", "is_active", "is_default", "version", "usage_count", "created_at", "updated_at"], "title": "TemplateResponse"}, "Token": {"properties": {"access_token": {"type": "string", "title": "Access Token"}, "token_type": {"type": "string", "title": "Token Type", "default": "bearer"}, "expires_in": {"type": "integer", "title": "Expires In"}}, "type": "object", "required": ["access_token", "expires_in"], "title": "Token", "example": {"access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "expires_in": 3600, "token_type": "bearer"}}, "UserAction": {"properties": {"push_log_id": {"type": "integer", "title": "Push Log Id", "description": "推送日志ID"}, "action": {"type": "string", "title": "Action", "description": "行为类型"}, "metadata": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "description": "行为元数据"}}, "type": "object", "required": ["push_log_id", "action"], "title": "UserAction"}, "UserCreate": {"properties": {"username": {"type": "string", "maxLength": 50, "minLength": 3, "title": "Username", "description": "用户名"}, "email": {"type": "string", "format": "email", "title": "Email", "description": "邮箱地址"}, "password": {"type": "string", "maxLength": 100, "minLength": 8, "title": "Password", "description": "密码"}}, "type": "object", "required": ["username", "email", "password"], "title": "UserCreate", "example": {"email": "<EMAIL>", "password": "password123", "username": "testuser"}}, "UserLogin": {"properties": {"email": {"type": "string", "format": "email", "title": "Email", "description": "邮箱地址"}, "password": {"type": "string", "title": "Password", "description": "密码"}}, "type": "object", "required": ["email", "password"], "title": "UserLogin", "example": {"email": "<EMAIL>", "password": "password123"}}, "UserResponse": {"properties": {"username": {"type": "string", "maxLength": 50, "minLength": 3, "title": "Username", "description": "用户名"}, "email": {"type": "string", "format": "email", "title": "Email", "description": "邮箱地址"}, "id": {"type": "integer", "title": "Id"}, "role": {"$ref": "#/components/schemas/UserRole-Output"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "last_login_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Login At"}}, "type": "object", "required": ["username", "email", "id", "role", "created_at", "updated_at"], "title": "UserResponse", "example": {"created_at": "2024-01-01T00:00:00", "email": "<EMAIL>", "id": 1, "last_login_at": "2024-01-01T00:00:00", "role": "FREE", "updated_at": "2024-01-01T00:00:00", "username": "testuser"}}, "UserRole-Input": {"type": "string", "enum": ["FREE", "PRO", "ENTERPRISE", "ADMIN"], "title": "UserRole"}, "UserRole-Output": {"type": "string", "enum": ["FREE", "PRO", "ENTERPRISE"], "title": "UserRole"}, "UserUpdate": {"properties": {"username": {"anyOf": [{"type": "string", "maxLength": 50, "minLength": 3}, {"type": "null"}], "title": "Username", "description": "用户名"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email", "description": "邮箱地址"}}, "type": "object", "title": "UserUpdate", "example": {"email": "<EMAIL>", "username": "newusername"}}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}, "app__models__news__NewsCategory": {"type": "string", "enum": ["announcement", "regulation", "market", "finance", "governance", "policy", "other"], "title": "NewsCategory", "description": "新闻分类枚举"}, "app__models__push_layer__PushChannel": {"type": "string", "enum": ["wechat", "email", "sms", "push", "webhook", "<PERSON><PERSON><PERSON>"], "title": "PushChannel", "description": "推送渠道枚举"}, "app__schemas__push__PushChannel": {"type": "string", "enum": ["wechat", "email", "sms", "webhook", "app_push"], "title": "PushChannel", "description": "推送渠道枚举"}, "app__schemas__push__PushTemplateCreate": {"properties": {"name": {"type": "string", "maxLength": 100, "title": "Name", "description": "模板名称"}, "title_template": {"type": "string", "maxLength": 200, "title": "Title Template", "description": "标题模板"}, "content_template": {"type": "string", "maxLength": 2000, "title": "Content Template", "description": "内容模板"}, "channels": {"items": {"$ref": "#/components/schemas/app__schemas__push__PushChannel"}, "type": "array", "title": "Channels", "description": "支持的推送渠道"}, "variables": {"items": {"type": "string"}, "type": "array", "title": "Variables", "description": "模板变量列表"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "是否启用", "default": true}}, "type": "object", "required": ["name", "title_template", "content_template", "channels", "variables"], "title": "PushTemplateCreate", "description": "创建推送模板请求模式"}, "app__schemas__push__PushTemplateResponse": {"properties": {"id": {"type": "integer", "title": "Id", "description": "模板ID"}, "name": {"type": "string", "title": "Name", "description": "模板名称"}, "title_template": {"type": "string", "title": "Title Template", "description": "标题模板"}, "content_template": {"type": "string", "title": "Content Template", "description": "内容模板"}, "channels": {"items": {"$ref": "#/components/schemas/app__schemas__push__PushChannel"}, "type": "array", "title": "Channels", "description": "支持的推送渠道"}, "variables": {"items": {"type": "string"}, "type": "array", "title": "Variables", "description": "模板变量列表"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "是否启用"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "创建时间"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At", "description": "更新时间"}, "usage_count": {"type": "integer", "title": "Usage Count", "description": "使用次数", "default": 0}}, "type": "object", "required": ["id", "name", "title_template", "content_template", "channels", "variables", "is_active", "created_at"], "title": "PushTemplateResponse", "description": "推送模板响应模式"}, "app__schemas__push_layer__PushTemplateCreate": {"properties": {"name": {"type": "string", "maxLength": 100, "title": "Name", "description": "模板名称"}, "tier": {"$ref": "#/components/schemas/PushTier", "description": "适用层级"}, "channel": {"$ref": "#/components/schemas/app__models__push_layer__PushChannel", "description": "适用渠道"}, "title_template": {"type": "string", "title": "Title Template", "description": "标题模板"}, "content_template": {"type": "string", "title": "Content Template", "description": "内容模板"}, "format_config": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Format Config", "description": "格式配置"}, "is_default": {"type": "boolean", "title": "<PERSON>", "description": "是否为默认模板", "default": false}}, "type": "object", "required": ["name", "tier", "channel", "title_template", "content_template"], "title": "PushTemplateCreate", "description": "推送模板创建请求模式", "example": {"channel": "wechat", "content_template": "{{content}}\n\n⏰ {{timestamp}}\n📊 重要性：{{importance}}/100", "format_config": {"mention_all": true, "use_markdown": true}, "is_default": true, "name": "T1微信群告警模板", "tier": "T1", "title_template": "【{{tier}}告警】{{title}}"}}, "app__schemas__push_layer__PushTemplateResponse": {"properties": {"id": {"type": "integer", "title": "Id", "description": "模板ID"}, "name": {"type": "string", "title": "Name", "description": "模板名称"}, "tier": {"type": "string", "title": "Tier", "description": "适用层级"}, "channel": {"type": "string", "title": "Channel", "description": "适用渠道"}, "title_template": {"type": "string", "title": "Title Template", "description": "标题模板"}, "content_template": {"type": "string", "title": "Content Template", "description": "内容模板"}, "format_config": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Format Config", "description": "格式配置"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "是否启用"}, "is_default": {"type": "boolean", "title": "<PERSON>", "description": "是否为默认模板"}, "usage_count": {"type": "integer", "title": "Usage Count", "description": "使用次数"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "更新时间"}}, "type": "object", "required": ["id", "name", "tier", "channel", "title_template", "content_template", "is_active", "is_default", "usage_count", "created_at", "updated_at"], "title": "PushTemplateResponse", "description": "推送模板响应模式"}, "app__schemas__subscription__NewsCategory": {"type": "string", "enum": ["announcement", "regulation", "market", "finance", "governance", "other"], "title": "NewsCategory"}, "app__schemas__subscription__PushChannel": {"type": "string", "enum": ["wechat", "feishu", "email", "webhook"], "title": "PushChannel"}}, "securitySchemes": {"HTTPBearer": {"type": "http", "scheme": "bearer"}}}}