# 财经新闻Bot - 智能财经信息推送系统

[![Python](https://img.shields.io/badge/Python-3.9+-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://docker.com)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

## 📋 项目概述

财经新闻Bot是一个智能化的财经信息推送系统，通过自动化爬取、AI分析和多渠道推送，为用户提供及时、准确的财经资讯服务。

### 🎯 核心功能

- **🕷️ 智能爬虫系统**：自动爬取多个财经网站的最新资讯
- **🤖 AI内容分析**：使用GLM-4.5模型进行内容摘要、分类和情感分析
- **📱 多渠道推送**：支持企业微信、飞书、邮件等多种推送方式
- **📊 数据处理管道**：基于管道模式的高效数据处理架构
- **🔒 安全监控**：集成安全、合规、性能监控和告警系统
- **📈 用户管理**：完整的用户认证、权限管理和行为分析

### 🏗️ 系统架构

```
财经新闻Bot
├── 🌐 API网关层 (FastAPI)
├── 🔐 认证授权层 (JWT + RBAC)
├── 🧠 业务逻辑层
│   ├── 统一推送服务 (4种推送策略)
│   ├── AI服务 (GLM-4.5集成)
│   ├── 数据处理管道 (6个处理器)
│   ├── 简化爬虫服务 (管道模式)
│   └── 集成监控服务 (4个监控器)
├── 💾 数据存储层 (MySQL + Redis)
└── 📊 监控告警层 (统一日志 + 告警系统)
```

## 🚀 快速开始

### 环境要求

- Python 3.9+
- Docker & Docker Compose
- MySQL 8.0+
- Redis 6.0+

### 安装部署

1. **克隆项目**
```bash
git clone <repository-url>
cd financial_news_bot
```

2. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库、API密钥等
```

3. **Docker部署（推荐）**
```bash
docker-compose up -d
```

4. **本地开发部署**
```bash
# 安装依赖
pip install -r requirements.txt

# 数据库迁移
alembic upgrade head

# 启动服务
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 访问服务

- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health
- **数据库检查**: http://localhost:8000/db-check

## 📚 API文档

### 核心服务接口

#### 1. 统一推送服务
```http
POST /api/v1/push/send
Content-Type: application/json

{
  "title": "推送标题",
  "content": "推送内容",
  "mode": "basic",
  "channels": ["wechat_work", "email"]
}
```

#### 2. AI服务
```http
POST /api/v1/ai/summarize
Content-Type: application/json

{
  "text": "需要摘要的文本内容",
  "max_length": 200
}
```

#### 3. 数据处理管道
```http
POST /api/v1/data-processing/news
Content-Type: application/json

{
  "title": "新闻标题",
  "content": "新闻内容",
  "stages": ["text_cleaning", "sentiment_analysis"]
}
```

#### 4. 爬虫服务
```http
POST /api/v1/crawler/crawl
Content-Type: application/json

{
  "crawler_name": "sse_crawler",
  "days_back": 7
}
```

#### 5. 告警管理
```http
GET /api/v1/alerts/active
POST /api/v1/alerts/trigger
```

### 完整API文档

访问 http://localhost:8000/docs 查看完整的交互式API文档。

## 🔧 配置说明

### 环境变量配置

```bash
# 数据库配置
DATABASE_URL=mysql+pymysql://user:password@host:port/database

# Redis配置
REDIS_URL=redis://localhost:6379/0

# AI服务配置
GLM_API_KEY=your-glm-api-key

# 推送服务配置
WECHAT_WORK_WEBHOOK=your-webhook-url
FEISHU_WEBHOOK=your-webhook-url
EMAIL_SMTP_HOST=smtp.example.com
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=your-email
EMAIL_PASSWORD=your-password

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=structured
ENABLE_FILE_LOGGING=true

# 安全配置
SECRET_KEY=your-secret-key
ACCESS_TOKEN_EXPIRE_MINUTES=30
```

### 推送渠道配置

#### 企业微信
1. 创建企业微信机器人
2. 获取Webhook URL
3. 配置 `WECHAT_WORK_WEBHOOK` 环境变量

#### 飞书
1. 创建飞书机器人
2. 获取Webhook URL
3. 配置 `FEISHU_WEBHOOK` 环境变量

#### 邮件
1. 配置SMTP服务器信息
2. 设置邮箱账号和密码
3. 配置相关环境变量

## 🧪 测试

### 运行测试

```bash
# 安装测试依赖
pip install pytest pytest-asyncio pytest-cov

# 运行所有测试
python run_all_tests.py

# 运行特定测试
pytest tests/test_unified_push_service.py -v

# 生成覆盖率报告
python run_all_tests.py --coverage
```

### 测试覆盖范围

- ✅ 统一推送服务测试
- ✅ 数据处理管道测试
- ✅ 简化爬虫服务测试
- ✅ AI服务测试
- ✅ 集成监控服务测试

目标覆盖率：≥80%

## 📊 监控和日志

### 日志系统

系统使用统一的日志配置，支持：

- **结构化日志**：便于分析和检索
- **JSON格式**：适合日志收集系统
- **自动轮转**：防止日志文件过大
- **分级存储**：错误日志单独存储

日志文件位置：
- 应用日志：`logs/financial_news_bot.log`
- 错误日志：`logs/financial_news_bot_error.log`

### 告警系统

系统内置完整的告警机制：

- **5个告警级别**：INFO, WARNING, ERROR, CRITICAL
- **5个告警分类**：系统、安全、性能、业务、合规
- **8个默认规则**：CPU、内存、磁盘、登录失败等
- **多渠道通知**：自动推送到配置的渠道

### 监控指标

- **系统资源**：CPU、内存、磁盘使用率
- **应用性能**：API响应时间、错误率
- **业务指标**：爬虫成功率、推送成功率
- **安全指标**：登录失败、可疑活动

## 🔒 安全特性

### 认证授权
- JWT Token认证
- 基于角色的访问控制(RBAC)
- 密码加密存储
- 会话管理

### 数据安全
- 敏感信息环境变量配置
- 数据库连接加密
- API请求限流
- 输入验证和过滤

### 监控安全
- 登录失败监控
- 可疑活动检测
- API安全检查
- 实时告警通知

## 📈 性能优化

### 架构优化
- 微服务架构简化（7个→5个服务）
- 统一推送服务（5个→1个服务）
- 管道模式数据处理
- 异步处理机制

### 代码优化
- 移除重复代码（6500+行）
- 单一职责原则
- 依赖注入模式
- 缓存机制

### 数据库优化
- 连接池管理
- 查询优化
- 索引优化
- 数据分页

## 🛠️ 开发指南

### 项目结构

```
financial_news_bot/
├── backend/
│   ├── app/
│   │   ├── routers/          # API路由
│   │   ├── services/         # 业务服务
│   │   ├── models/           # 数据模型
│   │   ├── utils/            # 工具类
│   │   ├── middleware/       # 中间件
│   │   └── main.py          # 应用入口
│   ├── tests/               # 测试文件
│   ├── logs/                # 日志文件
│   └── requirements.txt     # 依赖列表
├── docker-compose.yml       # Docker配置
├── .env.example            # 环境变量示例
└── README.md               # 项目文档
```

### 开发规范

1. **代码风格**：遵循PEP 8规范
2. **类型注解**：使用Python类型提示
3. **文档字符串**：使用Google风格
4. **测试覆盖**：新功能必须有测试
5. **日志记录**：关键操作记录日志

### 贡献指南

1. Fork项目
2. 创建功能分支
3. 编写测试
4. 提交代码
5. 创建Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 支持

如有问题或建议，请：

1. 查看[文档](docs/)
2. 提交[Issue](issues/)
3. 联系开发团队

---

**财经新闻Bot** - 让财经资讯触手可及 🚀
