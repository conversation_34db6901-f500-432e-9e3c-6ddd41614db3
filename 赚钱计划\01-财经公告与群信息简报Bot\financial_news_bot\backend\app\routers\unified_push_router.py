"""
统一推送路由
提供简化的推送API接口
"""
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime

from app.services.unified_push_service import (
    unified_push_service, 
    PushMode, 
    PushChannel, 
    MessageType,
    PushResult
)
from app.dependencies.auth import get_current_user
from app.models.user import User

router = APIRouter(prefix="/api/v1/push", tags=["统一推送服务"])

class PushRequest(BaseModel):
    """推送请求模型"""
    title: str = Field(..., description="消息标题", max_length=200)
    content: str = Field(..., description="消息内容", max_length=4000)
    mode: str = Field(default="basic", description="推送模式: basic, enhanced, layered, analytics")
    channels: List[str] = Field(default=["wechat_work"], description="推送渠道列表")
    targets: List[str] = Field(default=[], description="目标用户/群组列表")
    message_type: str = Field(default="text", description="消息类型: text, markdown, card, html")
    extra_data: Dict[str, Any] = Field(default={}, description="额外参数")

class PushResponse(BaseModel):
    """推送响应模型"""
    success: bool
    message: str
    results: List[Dict[str, Any]]
    total_count: int
    success_count: int
    failed_count: int
    timestamp: datetime

class BatchPushRequest(BaseModel):
    """批量推送请求模型"""
    messages: List[PushRequest] = Field(..., description="消息列表", max_items=50)
    mode: str = Field(default="basic", description="推送模式")

@router.post("/send", response_model=PushResponse)
async def send_message(
    request: PushRequest,
    current_user: User = Depends(get_current_user)
):
    """
    发送单条消息
    
    支持的推送模式:
    - basic: 基础推送
    - enhanced: 增强推送（带重试）
    - layered: 分层推送（按优先级）
    - analytics: 带分析的推送
    """
    try:
        # 验证推送模式
        try:
            push_mode = PushMode(request.mode)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"不支持的推送模式: {request.mode}")
        
        # 验证推送渠道
        push_channels = []
        for channel_str in request.channels:
            try:
                push_channels.append(PushChannel(channel_str))
            except ValueError:
                raise HTTPException(status_code=400, detail=f"不支持的推送渠道: {channel_str}")
        
        # 验证消息类型
        try:
            message_type = MessageType(request.message_type)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"不支持的消息类型: {request.message_type}")
        
        # 执行推送
        results = await unified_push_service.send_message(
            title=request.title,
            content=request.content,
            mode=push_mode,
            channels=push_channels,
            targets=request.targets,
            message_type=message_type,
            **request.extra_data
        )
        
        # 统计结果
        success_count = sum(1 for r in results if r.success)
        failed_count = len(results) - success_count
        
        # 转换结果为字典格式
        result_dicts = []
        for result in results:
            result_dicts.append({
                "success": result.success,
                "message": result.message,
                "channel": result.channel.value if result.channel else None,
                "target": result.target,
                "timestamp": result.timestamp.isoformat(),
                "response_data": result.response_data
            })
        
        return PushResponse(
            success=success_count > 0,
            message=f"推送完成，成功: {success_count}, 失败: {failed_count}",
            results=result_dicts,
            total_count=len(results),
            success_count=success_count,
            failed_count=failed_count,
            timestamp=datetime.now()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"推送服务异常: {str(e)}")

@router.post("/batch", response_model=PushResponse)
async def send_batch_messages(
    request: BatchPushRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user)
):
    """
    批量发送消息
    
    支持一次发送多条消息，最多50条
    """
    if len(request.messages) > 50:
        raise HTTPException(status_code=400, detail="批量推送最多支持50条消息")
    
    try:
        all_results = []
        
        for message_request in request.messages:
            # 验证推送模式
            try:
                push_mode = PushMode(message_request.mode)
            except ValueError:
                push_mode = PushMode(request.mode)  # 使用批量请求的默认模式
            
            # 验证推送渠道
            push_channels = []
            for channel_str in message_request.channels:
                try:
                    push_channels.append(PushChannel(channel_str))
                except ValueError:
                    continue  # 跳过不支持的渠道
            
            if not push_channels:
                push_channels = [PushChannel.WECHAT_WORK]  # 默认渠道
            
            # 验证消息类型
            try:
                message_type = MessageType(message_request.message_type)
            except ValueError:
                message_type = MessageType.TEXT  # 默认文本类型
            
            # 执行推送
            results = await unified_push_service.send_message(
                title=message_request.title,
                content=message_request.content,
                mode=push_mode,
                channels=push_channels,
                targets=message_request.targets,
                message_type=message_type,
                **message_request.extra_data
            )
            
            all_results.extend(results)
        
        # 统计总结果
        success_count = sum(1 for r in all_results if r.success)
        failed_count = len(all_results) - success_count
        
        # 转换结果为字典格式
        result_dicts = []
        for result in all_results:
            result_dicts.append({
                "success": result.success,
                "message": result.message,
                "channel": result.channel.value if result.channel else None,
                "target": result.target,
                "timestamp": result.timestamp.isoformat(),
                "response_data": result.response_data
            })
        
        return PushResponse(
            success=success_count > 0,
            message=f"批量推送完成，总计: {len(all_results)}, 成功: {success_count}, 失败: {failed_count}",
            results=result_dicts,
            total_count=len(all_results),
            success_count=success_count,
            failed_count=failed_count,
            timestamp=datetime.now()
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量推送服务异常: {str(e)}")

@router.get("/channels")
async def get_supported_channels(current_user: User = Depends(get_current_user)):
    """获取支持的推送渠道列表"""
    return {
        "channels": [
            {"value": "wechat_work", "name": "企业微信", "description": "企业微信应用推送"},
            {"value": "feishu", "name": "飞书", "description": "飞书机器人推送"},
            {"value": "email", "name": "邮件", "description": "SMTP邮件推送"},
            {"value": "wechat_group", "name": "微信群", "description": "微信群机器人推送"}
        ]
    }

@router.get("/modes")
async def get_supported_modes(current_user: User = Depends(get_current_user)):
    """获取支持的推送模式列表"""
    return {
        "modes": [
            {"value": "basic", "name": "基础推送", "description": "简单直接的推送"},
            {"value": "enhanced", "name": "增强推送", "description": "带重试和监控的推送"},
            {"value": "layered", "name": "分层推送", "description": "按优先级推送"},
            {"value": "analytics", "name": "分析推送", "description": "带统计分析的推送"},
            {"value": "batch", "name": "批量推送", "description": "批量消息推送"}
        ]
    }

@router.get("/message-types")
async def get_supported_message_types(current_user: User = Depends(get_current_user)):
    """获取支持的消息类型列表"""
    return {
        "message_types": [
            {"value": "text", "name": "纯文本", "description": "纯文本消息"},
            {"value": "markdown", "name": "Markdown", "description": "Markdown格式消息"},
            {"value": "card", "name": "卡片消息", "description": "富文本卡片消息"},
            {"value": "html", "name": "HTML", "description": "HTML格式消息"}
        ]
    }

@router.post("/test")
async def test_push_service(
    channel: str = "wechat_work",
    current_user: User = Depends(get_current_user)
):
    """
    测试推送服务
    
    发送一条测试消息来验证推送服务是否正常工作
    """
    try:
        # 验证推送渠道
        try:
            push_channel = PushChannel(channel)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"不支持的推送渠道: {channel}")
        
        # 发送测试消息
        results = await unified_push_service.send_message(
            title="推送服务测试",
            content=f"这是一条测试消息，发送时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            mode=PushMode.BASIC,
            channels=[push_channel],
            targets=[],
            message_type=MessageType.TEXT
        )
        
        if results and results[0].success:
            return {"success": True, "message": "测试推送发送成功"}
        else:
            error_msg = results[0].message if results else "未知错误"
            return {"success": False, "message": f"测试推送发送失败: {error_msg}"}
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"测试推送异常: {str(e)}")
