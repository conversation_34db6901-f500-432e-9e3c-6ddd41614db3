# 数据处理器迁移指南

## 📋 迁移概述

原有的数据处理器（`app/utils/data_processor.py`）违反单一职责原则（499行，20+个方法），已创建基于管道模式的新架构（`app/services/data_processing_pipeline.py`）。

## 🔄 迁移状态

### ✅ 已完成
- 创建了基于管道模式的数据处理架构
- 拆分为6个专门的处理器（TextCleaner、EntityExtractor等）
- 提供了新的API接口（`/api/v1/data-processing`）
- 实现了灵活的处理流程配置

### 🔄 待迁移
以下文件仍在使用原有的复杂数据处理器，需要逐步迁移：

#### 1. 任务系统
- `app/tasks/crawler_tasks.py:14,32` - 导入和使用 `DataProcessor`
- 使用方法：`data_processor.generate_summary()`, `extract_entities()`, `analyze_sentiment()`, `calculate_importance()`

#### 2. 新闻服务
- `app/services/news_service.py:26` - 初始化 `DataProcessor`
- `app/routers/news.py:26,134` - 导入和使用 `DataProcessor`

#### 3. 内容处理服务
- `app/services/content_processor.py` - 可能存在功能重叠

## 🚀 迁移计划

### 阶段1：更新任务系统
将 `crawler_tasks.py` 中的数据处理更新为使用新的管道系统：

```python
# 原有方式
from app.utils.data_processor import DataProcessor
data_processor = DataProcessor()
summary = data_processor.generate_summary(content)

# 新方式
from app.services.data_processing_pipeline import data_pipeline
result = data_pipeline.process_text(content, [ProcessingStage.TEXT_CLEANING])
```

### 阶段2：更新新闻服务
将新闻服务中的数据处理更新为使用新的管道系统：

```python
# 原有方式
processed_data = self.data_processor.process_news_item(item)

# 新方式
result = data_pipeline.process_news_article(item['title'], item['content'])
processed_data = self._convert_pipeline_result(result)
```

### 阶段3：更新路由接口
将新闻路由中的数据处理更新为使用新的管道系统。

### 阶段4：清理旧代码
完成迁移后，删除原有的复杂数据处理器：
- `app/utils/data_processor.py`
- 更新所有相关导入

## 📊 架构对比

### 原有系统（复杂）
```python
class DataProcessor:
    def clean_text(self):          # 文本清理
    def extract_entities(self):    # 实体提取
    def analyze_sentiment(self):   # 情感分析
    def calculate_importance(self): # 重要性评分
    def classify_content(self):    # 内容分类
    def extract_keywords(self):    # 关键词提取
    def detect_duplicates(self):   # 重复检测
    def format_output(self):       # 输出格式化
    # ... 总计20+个方法，499行代码
```

### 新系统（管道模式）
```python
# 6个专门的处理器
class TextCleaner(DataProcessor):      # 专门负责文本清理
class EntityExtractor(DataProcessor):  # 专门负责实体提取
class SentimentAnalyzer(DataProcessor): # 专门负责情感分析
class ImportanceScorer(DataProcessor):  # 专门负责重要性评分
class ContentClassifier(DataProcessor): # 专门负责内容分类
class DuplicateDetector(DataProcessor): # 专门负责重复检测

# 管道编排器
class DataProcessingPipeline:          # 协调各处理器执行
```

## 🔄 迁移映射表

| 原有方法 | 新管道阶段 | 新处理器 |
|---------|-----------|----------|
| `clean_text()` | `TEXT_CLEANING` | `TextCleaner` |
| `extract_entities()` | `ENTITY_EXTRACTION` | `EntityExtractor` |
| `analyze_sentiment()` | `SENTIMENT_ANALYSIS` | `SentimentAnalyzer` |
| `calculate_importance()` | `IMPORTANCE_SCORING` | `ImportanceScorer` |
| `classify_content()` | `CONTENT_CLASSIFICATION` | `ContentClassifier` |
| `detect_duplicates()` | `DUPLICATE_DETECTION` | `DuplicateDetector` |

## 📝 迁移示例

### 示例1：任务系统迁移
```python
# 原有代码
@celery_app.task
def process_news_batch():
    for news in unprocessed_news:
        summary = data_processor.generate_summary(news.content)
        entities = data_processor.extract_entities(news.content)
        sentiment = data_processor.analyze_sentiment(news.content)
        importance = data_processor.calculate_importance(news.title, news.content)

# 迁移后代码
@celery_app.task
def process_news_batch():
    for news in unprocessed_news:
        result = data_pipeline.process_news_article(news.title, news.content)
        if result['success']:
            # 从管道结果中提取各项数据
            summary = result['stages']['text_cleaning']['data']
            entities = result['stages']['entity_extraction']['data']
            sentiment = result['stages']['sentiment_analysis']['data']
            importance = result['stages']['importance_scoring']['data']
```

### 示例2：新闻服务迁移
```python
# 原有代码
class NewsService:
    def __init__(self, db: Session):
        self.data_processor = DataProcessor()
    
    def create_news(self, news_data):
        processed_item = self.data_processor.process_news_item(news_data)

# 迁移后代码
class NewsService:
    def __init__(self, db: Session):
        # 不再需要初始化数据处理器
        pass
    
    def create_news(self, news_data):
        result = data_pipeline.process_news_article(
            news_data['title'], 
            news_data['content']
        )
        processed_item = self._convert_pipeline_result(result, news_data)
```

## ⚠️ 注意事项

1. **向后兼容**：在完成迁移前，保留原有系统确保功能正常
2. **数据格式**：新管道系统的输出格式可能与原系统不同，需要适配
3. **性能测试**：确保新系统性能不低于原系统
4. **错误处理**：新系统有更完善的错误处理机制
5. **批量处理**：新系统支持批量处理，可以提高效率

## 🎯 迁移优先级

1. **高优先级**：`crawler_tasks.py`（影响数据采集）
2. **中优先级**：`news_service.py`（影响新闻创建）
3. **低优先级**：`news.py` 路由（影响API接口）

## 📝 迁移检查清单

- [ ] 更新 `crawler_tasks.py` 使用新管道
- [ ] 更新 `news_service.py` 使用新管道
- [ ] 更新 `news.py` 路由使用新管道
- [ ] 创建结果转换函数
- [ ] 验证功能完整性
- [ ] 性能对比测试
- [ ] 更新相关测试
- [ ] 删除原有复杂处理器
- [ ] 更新文档

---

*此迁移计划确保数据处理系统的稳定性，同时提供更好的可维护性和扩展性。*
