"""
推送效果分析服务
提供推送效果统计和用户行为分析
"""
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import func, desc, and_

from app.models.push_log import PushLog
from app.models.user import User
from app.models.subscription import Subscription

logger = logging.getLogger(__name__)

class AnalyticsService:
    """分析服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_push_overview(self, user_id: int, days: int = 30) -> Dict[str, Any]:
        """
        获取推送概览统计
        
        Args:
            user_id: 用户ID
            days: 统计天数
        
        Returns:
            概览统计数据
        """
        try:
            # 计算时间范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            # 基础查询
            base_query = self.db.query(PushLog).filter(
                PushLog.user_id == user_id,
                PushLog.created_at >= start_date
            )
            
            # 基础统计
            total_pushes = base_query.count()
            successful_pushes = base_query.filter(PushLog.success_count > 0).count()
            failed_pushes = total_pushes - successful_pushes
            
            # 效果统计
            analytics_query = base_query.filter(PushLog.success_count > 0)
            
            total_sent = analytics_query.with_entities(func.sum(PushLog.success_count)).scalar() or 0
            total_opens = analytics_query.with_entities(func.sum(PushLog.unique_opens)).scalar() or 0
            total_clicks = analytics_query.with_entities(func.sum(PushLog.unique_clicks)).scalar() or 0
            
            # 计算平均指标
            avg_open_rate = analytics_query.with_entities(func.avg(PushLog.open_rate)).scalar() or 0
            avg_click_rate = analytics_query.with_entities(func.avg(PushLog.click_rate)).scalar() or 0
            avg_delivery_rate = analytics_query.with_entities(func.avg(PushLog.delivery_rate)).scalar() or 0
            
            # 成功率
            success_rate = (successful_pushes / total_pushes * 100) if total_pushes > 0 else 0
            
            return {
                "period": f"{days}天",
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "total_pushes": total_pushes,
                "successful_pushes": successful_pushes,
                "failed_pushes": failed_pushes,
                "success_rate": round(success_rate, 2),
                "total_sent": total_sent,
                "total_opens": total_opens,
                "total_clicks": total_clicks,
                "avg_open_rate": round(avg_open_rate, 2),
                "avg_click_rate": round(avg_click_rate, 2),
                "avg_delivery_rate": round(avg_delivery_rate, 2),
                "overall_open_rate": round((total_opens / total_sent * 100) if total_sent > 0 else 0, 2),
                "overall_click_rate": round((total_clicks / total_sent * 100) if total_sent > 0 else 0, 2)
            }
            
        except Exception as e:
            logger.error(f"获取推送概览失败: {str(e)}")
            raise
    
    def get_channel_performance(self, user_id: int, days: int = 30) -> List[Dict[str, Any]]:
        """
        获取渠道效果分析
        
        Args:
            user_id: 用户ID
            days: 统计天数
        
        Returns:
            渠道效果数据
        """
        try:
            # 计算时间范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            # 获取推送日志
            logs = self.db.query(PushLog).filter(
                PushLog.user_id == user_id,
                PushLog.created_at >= start_date,
                PushLog.push_channels.isnot(None)
            ).all()
            
            # 按渠道统计
            channel_stats = {}
            
            for log in logs:
                if not log.push_channels:
                    continue
                
                for channel in log.push_channels:
                    if channel not in channel_stats:
                        channel_stats[channel] = {
                            "channel": channel,
                            "push_count": 0,
                            "success_count": 0,
                            "total_sent": 0,
                            "total_opens": 0,
                            "total_clicks": 0,
                            "avg_open_rate": 0,
                            "avg_click_rate": 0,
                            "avg_delivery_rate": 0
                        }
                    
                    stats = channel_stats[channel]
                    stats["push_count"] += 1
                    
                    if log.success_count > 0:
                        stats["success_count"] += 1
                        stats["total_sent"] += log.success_count
                        stats["total_opens"] += log.unique_opens or 0
                        stats["total_clicks"] += log.unique_clicks or 0
            
            # 计算平均指标
            for channel, stats in channel_stats.items():
                if stats["success_count"] > 0:
                    # 获取该渠道的平均指标
                    channel_logs = [log for log in logs 
                                  if log.push_channels and channel in log.push_channels 
                                  and log.success_count > 0]
                    
                    if channel_logs:
                        stats["avg_open_rate"] = sum(log.open_rate or 0 for log in channel_logs) / len(channel_logs)
                        stats["avg_click_rate"] = sum(log.click_rate or 0 for log in channel_logs) / len(channel_logs)
                        stats["avg_delivery_rate"] = sum(log.delivery_rate or 0 for log in channel_logs) / len(channel_logs)
                
                # 计算整体指标
                stats["overall_open_rate"] = (stats["total_opens"] / stats["total_sent"] * 100) if stats["total_sent"] > 0 else 0
                stats["overall_click_rate"] = (stats["total_clicks"] / stats["total_sent"] * 100) if stats["total_sent"] > 0 else 0
                stats["success_rate"] = (stats["success_count"] / stats["push_count"] * 100) if stats["push_count"] > 0 else 0
                
                # 四舍五入
                for key in ["avg_open_rate", "avg_click_rate", "avg_delivery_rate", "overall_open_rate", "overall_click_rate", "success_rate"]:
                    stats[key] = round(stats[key], 2)
            
            return list(channel_stats.values())
            
        except Exception as e:
            logger.error(f"获取渠道效果分析失败: {str(e)}")
            raise
    
    def get_time_trend(self, user_id: int, days: int = 30) -> Dict[str, Any]:
        """
        获取时间趋势分析
        
        Args:
            user_id: 用户ID
            days: 统计天数
        
        Returns:
            时间趋势数据
        """
        try:
            # 计算时间范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            # 按日期分组统计
            daily_stats = self.db.query(
                func.date(PushLog.created_at).label('date'),
                func.count(PushLog.id).label('push_count'),
                func.sum(func.case([(PushLog.success_count > 0, 1)], else_=0)).label('success_count'),
                func.sum(PushLog.success_count).label('total_sent'),
                func.sum(PushLog.unique_opens).label('total_opens'),
                func.sum(PushLog.unique_clicks).label('total_clicks')
            ).filter(
                PushLog.user_id == user_id,
                PushLog.created_at >= start_date
            ).group_by(func.date(PushLog.created_at)).all()
            
            # 构建趋势数据
            trend_data = []
            for stat in daily_stats:
                date_str = stat.date.isoformat()
                push_count = stat.push_count or 0
                success_count = stat.success_count or 0
                total_sent = stat.total_sent or 0
                total_opens = stat.total_opens or 0
                total_clicks = stat.total_clicks or 0
                
                trend_data.append({
                    "date": date_str,
                    "push_count": push_count,
                    "success_count": success_count,
                    "success_rate": round((success_count / push_count * 100) if push_count > 0 else 0, 2),
                    "total_sent": total_sent,
                    "total_opens": total_opens,
                    "total_clicks": total_clicks,
                    "open_rate": round((total_opens / total_sent * 100) if total_sent > 0 else 0, 2),
                    "click_rate": round((total_clicks / total_sent * 100) if total_sent > 0 else 0, 2)
                })
            
            return {
                "period": f"{days}天",
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "trend_data": sorted(trend_data, key=lambda x: x["date"])
            }
            
        except Exception as e:
            logger.error(f"获取时间趋势分析失败: {str(e)}")
            raise
    
    def get_subscription_performance(self, user_id: int, days: int = 30) -> List[Dict[str, Any]]:
        """
        获取订阅效果分析
        
        Args:
            user_id: 用户ID
            days: 统计天数
        
        Returns:
            订阅效果数据
        """
        try:
            # 计算时间范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            # 按订阅分组统计
            subscription_stats = self.db.query(
                PushLog.subscription_id,
                func.count(PushLog.id).label('push_count'),
                func.sum(func.case([(PushLog.success_count > 0, 1)], else_=0)).label('success_count'),
                func.sum(PushLog.success_count).label('total_sent'),
                func.sum(PushLog.unique_opens).label('total_opens'),
                func.sum(PushLog.unique_clicks).label('total_clicks'),
                func.avg(PushLog.open_rate).label('avg_open_rate'),
                func.avg(PushLog.click_rate).label('avg_click_rate')
            ).filter(
                PushLog.user_id == user_id,
                PushLog.created_at >= start_date,
                PushLog.subscription_id.isnot(None)
            ).group_by(PushLog.subscription_id).all()
            
            # 获取订阅信息并构建结果
            result = []
            for stat in subscription_stats:
                subscription = self.db.query(Subscription).filter(
                    Subscription.id == stat.subscription_id
                ).first()
                
                subscription_name = subscription.name if subscription else f"订阅{stat.subscription_id}"
                
                push_count = stat.push_count or 0
                success_count = stat.success_count or 0
                total_sent = stat.total_sent or 0
                total_opens = stat.total_opens or 0
                total_clicks = stat.total_clicks or 0
                
                result.append({
                    "subscription_id": stat.subscription_id,
                    "subscription_name": subscription_name,
                    "push_count": push_count,
                    "success_count": success_count,
                    "success_rate": round((success_count / push_count * 100) if push_count > 0 else 0, 2),
                    "total_sent": total_sent,
                    "total_opens": total_opens,
                    "total_clicks": total_clicks,
                    "avg_open_rate": round(stat.avg_open_rate or 0, 2),
                    "avg_click_rate": round(stat.avg_click_rate or 0, 2),
                    "overall_open_rate": round((total_opens / total_sent * 100) if total_sent > 0 else 0, 2),
                    "overall_click_rate": round((total_clicks / total_sent * 100) if total_sent > 0 else 0, 2)
                })
            
            return sorted(result, key=lambda x: x["total_sent"], reverse=True)
            
        except Exception as e:
            logger.error(f"获取订阅效果分析失败: {str(e)}")
            raise
    
    def track_user_action(self, push_log_id: int, action: str, metadata: Dict[str, Any] = None) -> bool:
        """
        追踪用户行为
        
        Args:
            push_log_id: 推送日志ID
            action: 行为类型 (open, click, feedback)
            metadata: 行为元数据
        
        Returns:
            是否成功
        """
        try:
            push_log = self.db.query(PushLog).filter(PushLog.id == push_log_id).first()
            
            if not push_log:
                logger.warning(f"推送日志不存在: {push_log_id}")
                return False
            
            # 更新行为统计
            if action == "open":
                push_log.open_count += 1
                if not push_log.opened_at:
                    push_log.opened_at = datetime.now()
                    push_log.unique_opens += 1
            elif action == "click":
                push_log.click_count += 1
                if not push_log.clicked_at:
                    push_log.clicked_at = datetime.now()
                    push_log.unique_clicks += 1
            elif action == "feedback":
                if not push_log.user_feedback:
                    push_log.user_feedback = {}
                push_log.user_feedback.update(metadata or {})
            
            # 重新计算效果指标
            if push_log.success_count > 0:
                push_log.open_rate = (push_log.unique_opens / push_log.success_count) * 100
                push_log.click_rate = (push_log.unique_clicks / push_log.success_count) * 100
            
            # 计算参与度评分
            push_log.engagement_score = self._calculate_engagement_score(push_log)
            
            self.db.commit()
            
            logger.info(f"用户行为追踪成功: {action} for push_log {push_log_id}")
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"用户行为追踪失败: {str(e)}")
            return False
    
    def _calculate_engagement_score(self, push_log: PushLog) -> int:
        """
        计算参与度评分
        
        Args:
            push_log: 推送日志
        
        Returns:
            参与度评分 (0-100)
        """
        score = 0
        
        # 基础送达得分
        if push_log.success_count > 0:
            score += 20
        
        # 打开得分
        if push_log.unique_opens > 0:
            score += 30
            # 打开率加分
            if push_log.open_rate > 50:
                score += 10
        
        # 点击得分
        if push_log.unique_clicks > 0:
            score += 40
            # 点击率加分
            if push_log.click_rate > 10:
                score += 10
        
        # 用户反馈加分
        if push_log.user_feedback:
            feedback_score = push_log.user_feedback.get('rating', 0)
            if feedback_score > 3:
                score += 10
        
        return min(score, 100)
