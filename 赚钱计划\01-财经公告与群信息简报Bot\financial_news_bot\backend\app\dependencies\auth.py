from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from typing import Optional
import logging

from ..database import get_db
from ..models.user import User, UserRole
from ..utils.security import verify_token
from ..exceptions.user import UnauthorizedError
from ..exceptions.permissions import (
    AuthenticationRequiredError,
    InvalidTokenError,
    UserNotFoundError,
    UserInactiveError
)

# HTTP Bearer认证方案
security = HTTPBearer()
logger = logging.getLogger(__name__)

def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """获取当前认证用户"""
    token = credentials.credentials

    try:
        # 验证令牌
        payload = verify_token(token)
        if payload is None:
            logger.warning("Invalid token provided")
            raise InvalidTokenError("无效的访问令牌")

        # 获取用户ID
        user_id: str = payload.get("sub")
        if user_id is None:
            logger.warning("Token payload missing user ID")
            raise InvalidTokenError("无效的令牌载荷")

        # 查询用户
        user = db.query(User).filter(User.id == int(user_id)).first()
        if user is None:
            logger.warning(f"User not found for ID: {user_id}")
            raise UserNotFoundError(user_id=int(user_id))

        logger.debug(f"User authenticated: {user.id} ({user.username})")
        return user

    except ValueError as e:
        logger.error(f"Token validation error: {e}")
        raise InvalidTokenError("令牌格式错误")
    except Exception as e:
        logger.error(f"Authentication error: {e}")
        raise AuthenticationRequiredError("认证失败")

def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    """
    获取当前活跃用户

    Args:
        current_user: 当前认证用户

    Returns:
        User: 活跃用户对象

    Raises:
        UserInactiveError: 用户未激活
    """
    # 检查用户状态（可扩展为检查是否被禁用、是否需要验证邮箱等）
    # 目前所有用户都被认为是活跃的，未来可以添加 is_active 字段

    logger.debug(f"Active user check passed for user {current_user.id}")
    return current_user


def get_optional_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False)),
    db: Session = Depends(get_db)
) -> Optional[User]:
    """
    获取可选的当前用户（不强制要求认证）

    Args:
        credentials: 可选的认证凭据
        db: 数据库会话

    Returns:
        Optional[User]: 用户对象或None
    """
    if not credentials:
        return None

    try:
        # 复用get_current_user的逻辑，但不抛出异常
        token = credentials.credentials
        payload = verify_token(token)
        if payload is None:
            return None

        user_id: str = payload.get("sub")
        if user_id is None:
            return None

        user = db.query(User).filter(User.id == int(user_id)).first()
        return user

    except Exception as e:
        logger.debug(f"Optional authentication failed: {e}")
        return None


# 兼容性保持：保留旧的角色检查函数，但标记为废弃
def require_role(required_role: str):
    """
    要求特定角色的依赖工厂函数（已废弃，请使用permissions模块）

    Args:
        required_role: 要求的角色名称

    Returns:
        依赖注入函数
    """
    def role_checker(current_user: User = Depends(get_current_active_user)) -> User:
        if current_user.role.value != required_role:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"需要{required_role}角色权限"
            )
        return current_user
    return role_checker


# 预定义的角色依赖（已废弃，请使用permissions模块）
require_pro_user = require_role("PRO")
require_enterprise_user = require_role("ENTERPRISE")
require_admin_user = require_role("ADMIN")
