"""
权限相关异常定义
"""
from fastapi import HTTPException, status
from typing import List, Optional


class PermissionError(HTTPException):
    """权限错误基类"""
    
    def __init__(
        self,
        detail: str = "Permission denied",
        status_code: int = status.HTTP_403_FORBIDDEN,
        headers: Optional[dict] = None
    ):
        super().__init__(status_code=status_code, detail=detail, headers=headers)


class InsufficientPermissionError(PermissionError):
    """权限不足错误"""
    
    def __init__(
        self,
        required_permission: str,
        user_role: str = None,
        detail: str = None
    ):
        if detail is None:
            if user_role:
                detail = f"Role '{user_role}' does not have permission '{required_permission}'"
            else:
                detail = f"Permission '{required_permission}' required"
        
        super().__init__(detail=detail)
        self.required_permission = required_permission
        self.user_role = user_role


class MultiplePermissionsRequiredError(PermissionError):
    """多权限要求错误"""
    
    def __init__(
        self,
        required_permissions: List[str],
        missing_permissions: List[str] = None,
        user_role: str = None
    ):
        if missing_permissions:
            detail = f"Missing permissions: {', '.join(missing_permissions)}"
        else:
            detail = f"Required permissions: {', '.join(required_permissions)}"
        
        if user_role:
            detail += f" (current role: {user_role})"
        
        super().__init__(detail=detail)
        self.required_permissions = required_permissions
        self.missing_permissions = missing_permissions
        self.user_role = user_role


class RoleHierarchyError(PermissionError):
    """角色层级错误"""
    
    def __init__(
        self,
        user_role: str,
        required_role: str,
        detail: str = None
    ):
        if detail is None:
            detail = f"Role '{required_role}' or higher required (current role: {user_role})"
        
        super().__init__(detail=detail)
        self.user_role = user_role
        self.required_role = required_role


class AuthenticationRequiredError(HTTPException):
    """认证要求错误"""
    
    def __init__(self, detail: str = "Authentication required"):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=detail,
            headers={"WWW-Authenticate": "Bearer"}
        )


class InvalidTokenError(AuthenticationRequiredError):
    """无效令牌错误"""
    
    def __init__(self, detail: str = "Invalid authentication token"):
        super().__init__(detail=detail)


class TokenExpiredError(AuthenticationRequiredError):
    """令牌过期错误"""
    
    def __init__(self, detail: str = "Authentication token has expired"):
        super().__init__(detail=detail)


class UserNotFoundError(HTTPException):
    """用户未找到错误"""
    
    def __init__(self, user_id: int = None, username: str = None):
        if user_id:
            detail = f"User with ID {user_id} not found"
        elif username:
            detail = f"User '{username}' not found"
        else:
            detail = "User not found"
        
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=detail
        )
        self.user_id = user_id
        self.username = username


class UserInactiveError(PermissionError):
    """用户未激活错误"""
    
    def __init__(self, detail: str = "User account is inactive"):
        super().__init__(detail=detail, status_code=status.HTTP_403_FORBIDDEN)


# 权限错误代码映射
PERMISSION_ERROR_CODES = {
    "INSUFFICIENT_PERMISSION": "E001",
    "MULTIPLE_PERMISSIONS_REQUIRED": "E002", 
    "ROLE_HIERARCHY_ERROR": "E003",
    "AUTHENTICATION_REQUIRED": "E004",
    "INVALID_TOKEN": "E005",
    "TOKEN_EXPIRED": "E006",
    "USER_NOT_FOUND": "E007",
    "USER_INACTIVE": "E008"
}


def create_permission_error_response(
    error_code: str,
    message: str,
    details: dict = None
) -> dict:
    """
    创建标准化的权限错误响应
    
    Args:
        error_code: 错误代码
        message: 错误消息
        details: 额外详情
        
    Returns:
        dict: 标准化错误响应
    """
    response = {
        "error": {
            "code": PERMISSION_ERROR_CODES.get(error_code, "E999"),
            "message": message,
            "type": "PermissionError"
        }
    }
    
    if details:
        response["error"]["details"] = details
    
    return response
