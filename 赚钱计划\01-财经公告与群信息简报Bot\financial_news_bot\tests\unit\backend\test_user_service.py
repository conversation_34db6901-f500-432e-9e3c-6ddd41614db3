"""
用户服务单元测试
测试用户注册、登录、权限管理等功能
"""
import pytest
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session
from fastapi import HTTPException

from app.services.user_service import UserService
from app.models.user import User
from app.schemas.user import UserCreate, UserUpdate
from app.core.security import verify_password, get_password_hash


class TestUserService:
    """用户服务测试类"""

    def test_create_user_success(self, db_session: Session):
        """测试成功创建用户"""
        user_service = UserService(db_session)
        user_data = UserCreate(
            username="newuser",
            email="<EMAIL>",
            password="password123"
        )
        
        user = user_service.create_user(user_data)
        
        assert user.username == "newuser"
        assert user.email == "<EMAIL>"
        assert user.is_active is True
        assert verify_password("password123", user.password_hash)

    def test_create_user_duplicate_username(self, db_session: Session, test_user: User):
        """测试创建重复用户名的用户"""
        user_service = UserService(db_session)
        user_data = UserCreate(
            username=test_user.username,
            email="<EMAIL>",
            password="password123"
        )
        
        with pytest.raises(HTTPException) as exc_info:
            user_service.create_user(user_data)
        
        assert exc_info.value.status_code == 400
        assert "already registered" in str(exc_info.value.detail)

    def test_create_user_duplicate_email(self, db_session: Session, test_user: User):
        """测试创建重复邮箱的用户"""
        user_service = UserService(db_session)
        user_data = UserCreate(
            username="differentuser",
            email=test_user.email,
            password="password123"
        )
        
        with pytest.raises(HTTPException) as exc_info:
            user_service.create_user(user_data)
        
        assert exc_info.value.status_code == 400
        assert "already registered" in str(exc_info.value.detail)

    def test_authenticate_user_success(self, db_session: Session, test_user: User):
        """测试用户认证成功"""
        user_service = UserService(db_session)
        
        authenticated_user = user_service.authenticate_user(
            test_user.username, 
            "testpassword"
        )
        
        assert authenticated_user is not None
        assert authenticated_user.id == test_user.id
        assert authenticated_user.username == test_user.username

    def test_authenticate_user_wrong_password(self, db_session: Session, test_user: User):
        """测试错误密码认证"""
        user_service = UserService(db_session)
        
        authenticated_user = user_service.authenticate_user(
            test_user.username, 
            "wrongpassword"
        )
        
        assert authenticated_user is None

    def test_authenticate_user_not_found(self, db_session: Session):
        """测试用户不存在的认证"""
        user_service = UserService(db_session)
        
        authenticated_user = user_service.authenticate_user(
            "nonexistentuser", 
            "password"
        )
        
        assert authenticated_user is None

    def test_authenticate_user_inactive(self, db_session: Session):
        """测试非活跃用户认证"""
        user_service = UserService(db_session)
        
        # 创建非活跃用户
        inactive_user = User(
            username="inactiveuser",
            email="<EMAIL>",
            password_hash=get_password_hash("password123"),
            is_active=False
        )
        db_session.add(inactive_user)
        db_session.commit()
        
        authenticated_user = user_service.authenticate_user(
            "inactiveuser", 
            "password123"
        )
        
        assert authenticated_user is None

    def test_get_user_by_id_success(self, db_session: Session, test_user: User):
        """测试通过ID获取用户成功"""
        user_service = UserService(db_session)
        
        user = user_service.get_user_by_id(test_user.id)
        
        assert user is not None
        assert user.id == test_user.id
        assert user.username == test_user.username

    def test_get_user_by_id_not_found(self, db_session: Session):
        """测试获取不存在的用户"""
        user_service = UserService(db_session)
        
        user = user_service.get_user_by_id(99999)
        
        assert user is None

    def test_get_user_by_username_success(self, db_session: Session, test_user: User):
        """测试通过用户名获取用户成功"""
        user_service = UserService(db_session)
        
        user = user_service.get_user_by_username(test_user.username)
        
        assert user is not None
        assert user.username == test_user.username

    def test_get_user_by_email_success(self, db_session: Session, test_user: User):
        """测试通过邮箱获取用户成功"""
        user_service = UserService(db_session)
        
        user = user_service.get_user_by_email(test_user.email)
        
        assert user is not None
        assert user.email == test_user.email

    def test_update_user_success(self, db_session: Session, test_user: User):
        """测试更新用户信息成功"""
        user_service = UserService(db_session)
        update_data = UserUpdate(
            email="<EMAIL>"
        )

        updated_user = user_service.update_user(test_user.id, update_data)

        assert updated_user.email == "<EMAIL>"
        assert updated_user.username == test_user.username  # 不变

    def test_update_user_not_found(self, db_session: Session):
        """测试更新不存在的用户"""
        user_service = UserService(db_session)
        update_data = UserUpdate(email="<EMAIL>")
        
        with pytest.raises(HTTPException) as exc_info:
            user_service.update_user(99999, update_data)
        
        assert exc_info.value.status_code == 404

    def test_change_password_success(self, db_session: Session, test_user: User):
        """测试修改密码成功"""
        user_service = UserService(db_session)
        
        result = user_service.change_password(
            test_user.id, 
            "testpassword", 
            "newpassword123"
        )
        
        assert result is True
        # 验证新密码
        db_session.refresh(test_user)
        assert verify_password("newpassword123", test_user.password_hash)

    def test_change_password_wrong_current(self, db_session: Session, test_user: User):
        """测试修改密码时当前密码错误"""
        user_service = UserService(db_session)
        
        result = user_service.change_password(
            test_user.id, 
            "wrongpassword", 
            "newpassword123"
        )
        
        assert result is False

    def test_deactivate_user_success(self, db_session: Session, test_user: User):
        """测试停用用户成功"""
        user_service = UserService(db_session)
        
        result = user_service.deactivate_user(test_user.id)
        
        assert result is True
        db_session.refresh(test_user)
        assert test_user.is_active is False

    def test_activate_user_success(self, db_session: Session):
        """测试激活用户成功"""
        user_service = UserService(db_session)
        
        # 创建非活跃用户
        inactive_user = User(
            username="inactiveuser",
            email="<EMAIL>",
            password_hash=get_password_hash("password123"),
            is_active=False
        )
        db_session.add(inactive_user)
        db_session.commit()
        
        result = user_service.activate_user(inactive_user.id)
        
        assert result is True
        db_session.refresh(inactive_user)
        assert inactive_user.is_active is True

    def test_get_users_list(self, db_session: Session, test_user: User, test_admin_user: User):
        """测试获取用户列表"""
        user_service = UserService(db_session)
        
        users = user_service.get_users(skip=0, limit=10)
        
        assert len(users) >= 2
        usernames = [user.username for user in users]
        assert test_user.username in usernames
        assert test_admin_user.username in usernames

    def test_get_users_pagination(self, db_session: Session):
        """测试用户列表分页"""
        user_service = UserService(db_session)
        
        # 创建多个用户
        for i in range(5):
            user = User(
                username=f"user{i}",
                email=f"user{i}@example.com",
                password_hash=get_password_hash("password123"),
                is_active=True
            )
            db_session.add(user)
        db_session.commit()
        
        # 测试分页
        page1 = user_service.get_users(skip=0, limit=3)
        page2 = user_service.get_users(skip=3, limit=3)
        
        assert len(page1) == 3
        assert len(page2) >= 2
        
        # 确保没有重复
        page1_ids = [user.id for user in page1]
        page2_ids = [user.id for user in page2]
        assert len(set(page1_ids) & set(page2_ids)) == 0

    def test_is_admin_user(self, db_session: Session, test_user: User, test_admin_user: User):
        """测试管理员用户检查"""
        user_service = UserService(db_session)

        # 检查用户角色
        assert test_admin_user.role.value == "ADMIN"
        assert test_user.role.value == "FREE"

    @patch('app.services.user_service.send_email')
    def test_send_password_reset_email(self, mock_send_email, db_session: Session, test_user: User):
        """测试发送密码重置邮件"""
        user_service = UserService(db_session)
        mock_send_email.return_value = True
        
        result = user_service.send_password_reset_email(test_user.email)
        
        assert result is True
        mock_send_email.assert_called_once()

    def test_validate_password_strength(self, db_session: Session):
        """测试密码强度验证"""
        user_service = UserService(db_session)
        
        # 强密码
        assert user_service.validate_password_strength("StrongPass123!") is True
        
        # 弱密码
        assert user_service.validate_password_strength("123") is False
        assert user_service.validate_password_strength("password") is False
        assert user_service.validate_password_strength("12345678") is False
