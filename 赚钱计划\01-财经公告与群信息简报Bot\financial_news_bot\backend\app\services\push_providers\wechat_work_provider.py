"""
企业微信推送提供商
支持企业微信群机器人消息推送
"""
import logging
import json
import re
from typing import Dict, Any
import httpx

from ..unified_push_service import BasePushProvider, PushMessage, PushResult, PushChannel, MessageType

logger = logging.getLogger(__name__)

class WeChatWorkProvider(BasePushProvider):
    """企业微信推送提供商"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.channel = PushChannel.WECHAT_WORK
        self.timeout = config.get('timeout', 30)
        self.max_content_length = config.get('max_content_length', 4096)
    
    def validate_target(self, target: str) -> bool:
        """
        验证企业微信Webhook地址是否有效
        
        Args:
            target: Webhook URL
        
        Returns:
            是否有效
        """
        if not target:
            return False
        
        # 企业微信机器人webhook格式验证
        pattern = r'https://qyapi\.weixin\.qq\.com/cgi-bin/webhook/send\?key=[a-zA-Z0-9\-_]+'
        return bool(re.match(pattern, target))
    
    async def send_message(self, target: str, message: PushMessage) -> PushResult:
        """
        发送企业微信消息
        
        Args:
            target: 企业微信Webhook URL
            message: 推送消息
        
        Returns:
            推送结果
        """
        try:
            # 根据消息类型格式化消息
            payload = self._format_message_payload(message)
            
            # 发送HTTP请求
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    target,
                    json=payload,
                    headers={'Content-Type': 'application/json'}
                )
                
                response_data = response.json()
                
                if response.status_code == 200 and response_data.get('errcode') == 0:
                    return PushResult(
                        success=True,
                        message="企业微信消息发送成功",
                        response_data=response_data
                    )
                else:
                    error_msg = response_data.get('errmsg', '未知错误')
                    return PushResult(
                        success=False,
                        message=f"企业微信消息发送失败: {error_msg}",
                        error_code=str(response_data.get('errcode', 'UNKNOWN')),
                        response_data=response_data
                    )
                    
        except httpx.TimeoutException:
            return PushResult(
                success=False,
                message="企业微信消息发送超时",
                error_code="TIMEOUT"
            )
        except Exception as e:
            return PushResult(
                success=False,
                message=f"企业微信消息发送异常: {str(e)}",
                error_code="EXCEPTION"
            )
    
    def _format_message_payload(self, message: PushMessage) -> Dict[str, Any]:
        """
        格式化企业微信消息载荷
        
        Args:
            message: 推送消息
        
        Returns:
            企业微信API格式的消息载荷
        """
        # 截断过长的内容
        content = self._truncate_content(message.content)
        
        if message.message_type == MessageType.MARKDOWN:
            return self._format_markdown_message(message.title, content)
        elif message.message_type == MessageType.CARD:
            return self._format_card_message(message.title, content, message.extra_data)
        else:
            return self._format_text_message(message.title, content)
    
    def _format_text_message(self, title: str, content: str) -> Dict[str, Any]:
        """格式化文本消息"""
        full_content = f"{title}\n\n{content}" if title else content
        
        return {
            "msgtype": "text",
            "text": {
                "content": full_content
            }
        }
    
    def _format_markdown_message(self, title: str, content: str) -> Dict[str, Any]:
        """格式化Markdown消息"""
        # 企业微信支持的Markdown格式
        markdown_content = content
        
        # 如果有标题，添加到Markdown内容中
        if title:
            markdown_content = f"# {title}\n\n{content}"
        
        return {
            "msgtype": "markdown",
            "markdown": {
                "content": markdown_content
            }
        }
    
    def _format_card_message(self, title: str, content: str, extra_data: Dict[str, Any]) -> Dict[str, Any]:
        """格式化卡片消息"""
        # 企业微信的图文消息格式
        articles = []
        
        # 主文章
        main_article = {
            "title": title,
            "description": content[:100] + "..." if len(content) > 100 else content,
            "url": extra_data.get('url', ''),
            "picurl": extra_data.get('image_url', '')
        }
        articles.append(main_article)
        
        # 额外文章（如果有）
        extra_articles = extra_data.get('articles', [])
        for article in extra_articles[:9]:  # 最多10篇文章
            articles.append({
                "title": article.get('title', ''),
                "description": article.get('description', ''),
                "url": article.get('url', ''),
                "picurl": article.get('image_url', '')
            })
        
        return {
            "msgtype": "news",
            "news": {
                "articles": articles
            }
        }
    
    def _truncate_content(self, content: str) -> str:
        """
        截断过长的内容
        
        Args:
            content: 原始内容
        
        Returns:
            截断后的内容
        """
        if len(content) <= self.max_content_length:
            return content
        
        # 截断并添加省略号
        truncated = content[:self.max_content_length - 10]
        return truncated + "\n\n... (内容过长已截断)"
    
    def format_news_list_message(self, news_list, subscription_name: str) -> PushMessage:
        """
        格式化新闻列表为企业微信消息
        
        Args:
            news_list: 新闻列表
            subscription_name: 订阅名称
        
        Returns:
            格式化后的推送消息
        """
        if not news_list:
            return PushMessage(
                title="📰 财经新闻推送",
                content="暂无新闻更新",
                message_type=MessageType.TEXT
            )
        
        # 构建Markdown格式的新闻推送
        title = f"📰 {subscription_name} - 财经新闻推送"
        
        content_lines = [
            f"## 📊 本次推送概览",
            f"- 新闻数量: **{len(news_list)}** 条",
            f"- 推送时间: {news_list[0].created_at.strftime('%Y-%m-%d %H:%M')}",
            "",
            "## 📰 新闻列表"
        ]
        
        # 添加新闻条目
        for i, news in enumerate(news_list[:10], 1):  # 最多显示10条
            importance_icon = "🔥" if news.importance_score >= 80 else "📌"
            content_lines.append(
                f"{i}. {importance_icon} **{news.title}**"
            )
            content_lines.append(f"   📅 {news.published_at.strftime('%m-%d %H:%M')} | 🏢 {news.source} | 📊 {news.importance_score}分")
            
            if news.summary:
                summary = news.summary[:80] + "..." if len(news.summary) > 80 else news.summary
                content_lines.append(f"   💡 {summary}")
            
            content_lines.append("")
        
        if len(news_list) > 10:
            content_lines.append(f"... 还有 **{len(news_list) - 10}** 条新闻未显示")
        
        content_lines.extend([
            "",
            "---",
            "💼 财经新闻Bot | 智能推送服务"
        ])
        
        return PushMessage(
            title=title,
            content="\n".join(content_lines),
            message_type=MessageType.MARKDOWN
        )
    
    def create_news_card_message(self, news_list, subscription_name: str) -> PushMessage:
        """
        创建新闻卡片消息
        
        Args:
            news_list: 新闻列表
            subscription_name: 订阅名称
        
        Returns:
            卡片格式的推送消息
        """
        if not news_list:
            return self.format_news_list_message(news_list, subscription_name)
        
        # 选择最重要的新闻作为主卡片
        main_news = max(news_list, key=lambda x: x.importance_score)
        
        title = f"📰 {subscription_name} - 重要财经新闻"
        content = f"发现 {len(news_list)} 条新闻，其中重要新闻：{main_news.title}"
        
        # 构建额外文章列表
        articles = []
        for news in news_list[:5]:  # 最多5篇文章
            if news != main_news:
                articles.append({
                    "title": news.title,
                    "description": news.summary[:50] + "..." if news.summary and len(news.summary) > 50 else (news.summary or ""),
                    "url": news.source_url or "",
                })
        
        extra_data = {
            "url": main_news.source_url or "",
            "articles": articles
        }
        
        return PushMessage(
            title=title,
            content=content,
            message_type=MessageType.CARD,
            extra_data=extra_data
        )
