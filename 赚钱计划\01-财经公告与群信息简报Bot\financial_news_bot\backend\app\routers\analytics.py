"""
推送效果分析API
提供推送效果统计和用户行为分析
"""
from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query, Body
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from app.database import get_db
from app.dependencies.auth import get_current_user
from app.models.user import User
# from app.services.analytics_service import AnalyticsService  # 临时注释，服务有问题

router = APIRouter(prefix="/analytics", tags=["推送分析"])

# Pydantic模型
class UserAction(BaseModel):
    push_log_id: int = Field(..., description="推送日志ID")
    action: str = Field(..., description="行为类型")
    metadata: Optional[Dict[str, Any]] = Field(None, description="行为元数据")

@router.get("/overview", summary="获取推送概览")
async def get_push_overview(
    days: int = Query(30, ge=1, le=365, description="统计天数"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取推送效果概览统计
    """
    try:
        analytics_service = AnalyticsService(db)
        overview = analytics_service.get_push_overview(current_user.id, days)
        
        return overview
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取推送概览失败: {str(e)}"
        )

@router.get("/channels", summary="获取渠道效果分析")
async def get_channel_performance(
    days: int = Query(30, ge=1, le=365, description="统计天数"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取各推送渠道的效果分析
    """
    try:
        analytics_service = AnalyticsService(db)
        channel_performance = analytics_service.get_channel_performance(current_user.id, days)
        
        return {
            "period": f"{days}天",
            "channels": channel_performance,
            "total_channels": len(channel_performance)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取渠道效果分析失败: {str(e)}"
        )

@router.get("/trends", summary="获取时间趋势分析")
async def get_time_trends(
    days: int = Query(30, ge=1, le=365, description="统计天数"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取推送效果的时间趋势分析
    """
    try:
        analytics_service = AnalyticsService(db)
        trends = analytics_service.get_time_trend(current_user.id, days)
        
        return trends
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取时间趋势分析失败: {str(e)}"
        )

@router.get("/subscriptions", summary="获取订阅效果分析")
async def get_subscription_performance(
    days: int = Query(30, ge=1, le=365, description="统计天数"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取各订阅的推送效果分析
    """
    try:
        analytics_service = AnalyticsService(db)
        subscription_performance = analytics_service.get_subscription_performance(current_user.id, days)
        
        return {
            "period": f"{days}天",
            "subscriptions": subscription_performance,
            "total_subscriptions": len(subscription_performance)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取订阅效果分析失败: {str(e)}"
        )

@router.post("/track", summary="追踪用户行为")
async def track_user_action(
    action_data: UserAction,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    追踪用户行为（打开、点击、反馈等）
    """
    try:
        analytics_service = AnalyticsService(db)
        
        # 验证推送日志是否属于当前用户
        from app.models.push_log import PushLog
        push_log = db.query(PushLog).filter(
            PushLog.id == action_data.push_log_id,
            PushLog.user_id == current_user.id
        ).first()
        
        if not push_log:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="推送记录不存在或无权限访问"
            )
        
        success = analytics_service.track_user_action(
            action_data.push_log_id,
            action_data.action,
            action_data.metadata
        )
        
        if success:
            return {
                "message": "用户行为追踪成功",
                "push_log_id": action_data.push_log_id,
                "action": action_data.action
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="用户行为追踪失败"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"追踪用户行为失败: {str(e)}"
        )

@router.get("/engagement", summary="获取参与度分析")
async def get_engagement_analysis(
    days: int = Query(30, ge=1, le=365, description="统计天数"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取用户参与度分析
    """
    try:
        from app.models.push_log import PushLog
        from sqlalchemy import func
        from datetime import datetime, timedelta
        
        # 计算时间范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # 参与度统计
        engagement_stats = db.query(
            func.count(PushLog.id).label('total_pushes'),
            func.avg(PushLog.engagement_score).label('avg_engagement'),
            func.sum(func.case([(PushLog.engagement_score >= 80, 1)], else_=0)).label('high_engagement'),
            func.sum(func.case([(PushLog.engagement_score >= 50, 1)], else_=0)).label('medium_engagement'),
            func.sum(func.case([(PushLog.engagement_score < 50, 1)], else_=0)).label('low_engagement')
        ).filter(
            PushLog.user_id == current_user.id,
            PushLog.created_at >= start_date,
            PushLog.success_count > 0
        ).first()
        
        total_pushes = engagement_stats.total_pushes or 0
        avg_engagement = engagement_stats.avg_engagement or 0
        high_engagement = engagement_stats.high_engagement or 0
        medium_engagement = engagement_stats.medium_engagement or 0
        low_engagement = engagement_stats.low_engagement or 0
        
        # 参与度分布
        engagement_distribution = {
            "high": {
                "count": high_engagement,
                "percentage": round((high_engagement / total_pushes * 100) if total_pushes > 0 else 0, 2)
            },
            "medium": {
                "count": medium_engagement,
                "percentage": round((medium_engagement / total_pushes * 100) if total_pushes > 0 else 0, 2)
            },
            "low": {
                "count": low_engagement,
                "percentage": round((low_engagement / total_pushes * 100) if total_pushes > 0 else 0, 2)
            }
        }
        
        return {
            "period": f"{days}天",
            "total_pushes": total_pushes,
            "avg_engagement_score": round(avg_engagement, 2),
            "engagement_distribution": engagement_distribution
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取参与度分析失败: {str(e)}"
        )

@router.get("/best-practices", summary="获取推送优化建议")
async def get_optimization_suggestions(
    days: int = Query(30, ge=1, le=365, description="统计天数"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    基于数据分析提供推送优化建议
    """
    try:
        analytics_service = AnalyticsService(db)
        
        # 获取各项分析数据
        overview = analytics_service.get_push_overview(current_user.id, days)
        channel_performance = analytics_service.get_channel_performance(current_user.id, days)
        
        suggestions = []
        
        # 基于整体表现的建议
        if overview["avg_open_rate"] < 20:
            suggestions.append({
                "type": "open_rate",
                "priority": "high",
                "title": "提升打开率",
                "description": f"当前平均打开率为{overview['avg_open_rate']}%，建议优化推送标题和发送时间",
                "actions": [
                    "使用更吸引人的标题",
                    "分析用户活跃时间，选择最佳推送时机",
                    "个性化推送内容"
                ]
            })
        
        if overview["avg_click_rate"] < 5:
            suggestions.append({
                "type": "click_rate",
                "priority": "high",
                "title": "提升点击率",
                "description": f"当前平均点击率为{overview['avg_click_rate']}%，建议优化推送内容和格式",
                "actions": [
                    "使用更清晰的行动号召",
                    "优化推送内容格式",
                    "添加相关链接和按钮"
                ]
            })
        
        # 基于渠道表现的建议
        if channel_performance:
            best_channel = max(channel_performance, key=lambda x: x["overall_open_rate"])
            worst_channel = min(channel_performance, key=lambda x: x["overall_open_rate"])
            
            if best_channel["overall_open_rate"] - worst_channel["overall_open_rate"] > 20:
                suggestions.append({
                    "type": "channel_optimization",
                    "priority": "medium",
                    "title": "优化推送渠道",
                    "description": f"{best_channel['channel']}表现最佳(打开率{best_channel['overall_open_rate']}%)，{worst_channel['channel']}表现较差",
                    "actions": [
                        f"增加{best_channel['channel']}渠道的使用频率",
                        f"优化{worst_channel['channel']}渠道的消息格式",
                        "考虑调整渠道分配策略"
                    ]
                })
        
        # 基于成功率的建议
        if overview["success_rate"] < 80:
            suggestions.append({
                "type": "delivery",
                "priority": "high",
                "title": "提升推送成功率",
                "description": f"当前推送成功率为{overview['success_rate']}%，需要检查推送配置",
                "actions": [
                    "检查推送渠道配置",
                    "验证推送目标地址",
                    "优化推送频率和时间"
                ]
            })
        
        # 如果表现良好，给出维持建议
        if not suggestions:
            suggestions.append({
                "type": "maintain",
                "priority": "low",
                "title": "保持良好表现",
                "description": "当前推送效果表现良好，建议继续保持现有策略",
                "actions": [
                    "定期监控推送效果",
                    "持续优化推送内容",
                    "关注用户反馈"
                ]
            })
        
        return {
            "period": f"{days}天",
            "suggestions": suggestions,
            "total_suggestions": len(suggestions)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取优化建议失败: {str(e)}"
        )
