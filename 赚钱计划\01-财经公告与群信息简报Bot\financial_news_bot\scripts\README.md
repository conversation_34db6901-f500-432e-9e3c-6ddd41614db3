# 脚本工具集说明

本目录包含财经新闻Bot项目的所有管理和开发工具脚本。

## 📁 脚本概览

### 🛠️ 开发工具
- **`dev-tools.py`** - 统一开发工具集
- **`test-runner.py`** - 测试运行器
- **`code_quality_check.py`** - 代码质量检查
- **`format_code.py`** - 代码格式化
- **`quality_manager.py`** - 综合质量管理
- **`run_all_tests.py`** - 完整测试套件

### 🚀 部署管理
- **`deploy-manager.py`** - 部署管理工具
- **`config-manager.py`** - 配置管理工具

### 📊 监控工具
- **`monitor-system.py`** - 系统监控
- **`check-health.py`** - 健康检查

## 🔧 使用指南

### 开发环境设置

```bash
# 设置开发环境
python scripts/dev-tools.py setup

# 格式化代码
python scripts/dev-tools.py format

# 检查代码质量
python scripts/dev-tools.py quality

# 运行测试
python scripts/dev-tools.py test

# 完整检查
python scripts/dev-tools.py full
```

### 测试管理

```bash
# 运行单元测试
python scripts/test-runner.py unit

# 运行集成测试
python scripts/test-runner.py integration

# 运行所有测试（带覆盖率）
python scripts/test-runner.py all --coverage

# 运行特定测试文件
python scripts/test-runner.py file --file test_example.py

# 列出所有测试
python scripts/test-runner.py list

# 清理测试文件
python scripts/test-runner.py clean
```

### 部署管理

```bash
# 启动服务
python scripts/deploy-manager.py start

# 停止服务
python scripts/deploy-manager.py stop

# 重启服务
python scripts/deploy-manager.py restart

# 查看状态
python scripts/deploy-manager.py status

# 查看日志
python scripts/deploy-manager.py logs

# 更新服务
python scripts/deploy-manager.py update

# 备份数据
python scripts/deploy-manager.py backup

# 健康检查
python scripts/deploy-manager.py health
```

### 配置管理

```bash
# 生成安全配置
python scripts/config-manager.py generate

# 验证配置
python scripts/config-manager.py validate

# 显示配置模板
python scripts/config-manager.py template

# 备份配置
python scripts/config-manager.py backup

# 恢复配置
python scripts/config-manager.py restore --backup-file .env.backup.20240101_120000

# 列出备份
python scripts/config-manager.py list-backups

# 检查权限
python scripts/config-manager.py check-permissions
```

### 代码质量管理

```bash
# 安装质量工具
python scripts/quality_manager.py --install

# 运行完整检查
python scripts/quality_manager.py --check

# 修复可自动修复的问题
python scripts/quality_manager.py --check --fix
```

## 📋 脚本功能对照表

| 功能 | 旧脚本（已删除） | 新脚本 | 命令 |
|------|------------------|--------|------|
| 开发环境设置 | `dev-setup.sh` | `dev-tools.py` | `setup` |
| 代码格式化 | 分散在多个文件 | `dev-tools.py` | `format` |
| 质量检查 | `code_quality_check.py` | `dev-tools.py` | `quality` |
| 测试运行 | `run_tests.py` | `test-runner.py` | `all` |
| 服务启动 | `start.sh` | `deploy-manager.py` | `start` |
| 服务停止 | `stop.sh` | `deploy-manager.py` | `stop` |
| 部署更新 | `deploy.sh`, `update.sh` | `deploy-manager.py` | `update` |
| 配置生成 | `generate_secure_config.py` | `config-manager.py` | `generate` |
| 配置验证 | `validate_config.py` | `config-manager.py` | `validate` |
| 性能监控 | `monitor-performance.py` | `monitor-system.py` | - |
| 健康检查 | 分散功能 | `deploy-manager.py` | `health` |

## 🎯 最佳实践

### 开发流程

1. **开始开发前**：
   ```bash
   python scripts/dev-tools.py setup
   python scripts/config-manager.py validate
   ```

2. **代码提交前**：
   ```bash
   python scripts/dev-tools.py full
   ```

3. **部署前**：
   ```bash
   python scripts/test-runner.py all --coverage
   python scripts/deploy-manager.py health
   ```

### 生产环境

1. **首次部署**：
   ```bash
   python scripts/config-manager.py generate
   python scripts/deploy-manager.py start --build
   ```

2. **日常维护**：
   ```bash
   python scripts/deploy-manager.py health
   python scripts/deploy-manager.py backup
   ```

3. **更新部署**：
   ```bash
   python scripts/deploy-manager.py backup
   python scripts/deploy-manager.py update
   ```

## 🔍 故障排除

### 常见问题

1. **脚本执行权限问题**：
   ```bash
   chmod +x scripts/*.py
   ```

2. **Python路径问题**：
   确保在项目根目录执行脚本

3. **依赖缺失**：
   ```bash
   python scripts/dev-tools.py setup
   ```

4. **Docker问题**：
   ```bash
   python scripts/deploy-manager.py health
   ```

### 日志查看

- 开发日志：`backend/logs/`
- 部署日志：`docker-compose logs`
- 测试日志：`pytest` 输出

## 📈 脚本优化成果

### 整合前（问题）
- ❌ 12个重复功能的脚本
- ❌ 功能分散，难以维护
- ❌ 缺乏统一的接口
- ❌ 文档不完整

### 整合后（优化）
- ✅ 6个核心工具脚本
- ✅ 功能集中，易于维护
- ✅ 统一的命令行接口
- ✅ 完整的使用文档

### 量化改进
- **脚本数量**：12个 → 6个（减少50%）
- **代码重复**：大量重复 → 零重复
- **维护成本**：高 → 低
- **使用便利性**：差 → 优

---

**注意**：所有脚本都支持 `--help` 参数查看详细使用说明。
