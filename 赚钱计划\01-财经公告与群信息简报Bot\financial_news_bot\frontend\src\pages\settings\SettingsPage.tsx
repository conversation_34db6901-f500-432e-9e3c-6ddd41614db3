import React, { useState, useEffect } from 'react';
import {
  Card,
  Tabs,
  Form,
  Switch,
  Select,
  TimePicker,
  Checkbox,
  Button,
  Typography,
  Space,
  Row,
  Col,
  Divider,
  Alert,
  message,
  Table,
  Tag,
  Modal,
  Input,
  Radio,
} from 'antd';
import {
  BellOutlined,
  CreditCardOutlined,
  QuestionCircleOutlined,
  DownloadOutlined,
  EyeOutlined,
  SettingOutlined,
  GlobalOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';

import { useAppDispatch, useAppSelector } from '@/store';
import { setPageTitle, toggleDarkMode, setPrimaryColor } from '@/store/slices/uiSlice';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

interface NotificationSettings {
  email_enabled: boolean;
  push_enabled: boolean;
  sms_enabled: boolean;
  quiet_hours: {
    enabled: boolean;
    start_time: string;
    end_time: string;
  };
  frequency: 'realtime' | 'hourly' | 'daily';
  categories: string[];
}

interface BillingRecord {
  id: string;
  date: string;
  description: string;
  amount: number;
  status: 'paid' | 'pending' | 'failed';
  invoice_url?: string;
}

const SettingsPage: React.FC = () => {
  const dispatch = useAppDispatch();
  const { theme } = useAppSelector(state => state.ui);
  const [form] = Form.useForm();
  const [feedbackForm] = Form.useForm();

  const [activeTab, setActiveTab] = useState('notifications');
  const [loading, setLoading] = useState(false);
  const [feedbackModalVisible, setFeedbackModalVisible] = useState(false);

  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings>({
    email_enabled: true,
    push_enabled: true,
    sms_enabled: false,
    quiet_hours: {
      enabled: true,
      start_time: '22:00',
      end_time: '08:00',
    },
    frequency: 'daily',
    categories: ['finance', 'technology'],
  });

  const [billingRecords] = useState<BillingRecord[]>([
    {
      id: '1',
      date: '2024-01-15',
      description: '高级订阅 - 月费',
      amount: 99.00,
      status: 'paid',
      invoice_url: '/invoices/1.pdf',
    },
    {
      id: '2',
      date: '2023-12-15',
      description: '高级订阅 - 月费',
      amount: 99.00,
      status: 'paid',
      invoice_url: '/invoices/2.pdf',
    },
  ]);

  useEffect(() => {
    dispatch(setPageTitle('系统设置'));
    form.setFieldsValue(notificationSettings);
  }, [dispatch, form, notificationSettings]);

  const handleNotificationSave = async (values: NotificationSettings) => {
    setLoading(true);
    try {
      // 真实API调用
      const response = await fetch('/api/v1/users/settings/notifications', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
        body: JSON.stringify(values),
      });

      if (response.ok) {
        setNotificationSettings(values);
        message.success('通知设置保存成功');
      } else {
        const error = await response.text();
        message.error(`保存失败: ${error}`);
      }
    } catch (error) {
      console.error('保存通知设置失败:', error);
      message.error('保存失败，请检查网络连接');
    } finally {
      setLoading(false);
    }
  };

  const handleFeedbackSubmit = async (values: any) => {
    try {
      // 真实API调用
      const response = await fetch('/api/v1/feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
        body: JSON.stringify(values),
      });

      if (response.ok) {
        message.success('反馈提交成功，我们会尽快处理');
        setFeedbackModalVisible(false);
        feedbackForm.resetFields();
      } else {
        const error = await response.text();
        message.error(`提交失败: ${error}`);
      }
    } catch (error) {
      console.error('提交反馈失败:', error);
      message.error('提交失败，请检查网络连接');
    }
  };

  const billingColumns = [
    {
      title: '日期',
      dataIndex: 'date',
      key: 'date',
      render: (date: string) => dayjs(date).format('YYYY-MM-DD'),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number) => `¥${amount.toFixed(2)}`,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap = {
          paid: { text: '已支付', color: 'success' },
          pending: { text: '待支付', color: 'warning' },
          failed: { text: '支付失败', color: 'error' },
        };
        const config = statusMap[status as keyof typeof statusMap];
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: BillingRecord) => (
        <Space>
          {record.invoice_url && (
            <Button
              type="link"
              size="small"
              icon={<DownloadOutlined />}
              onClick={() => window.open(record.invoice_url)}
            >
              下载发票
            </Button>
          )}
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
          >
            查看详情
          </Button>
        </Space>
      ),
    },
  ];

  const renderNotificationSettings = () => (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleNotificationSave}
      initialValues={notificationSettings}
    >
      <Card title="推送渠道" size="small" style={{ marginBottom: '16px' }}>
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item name="email_enabled" label="邮箱通知" valuePropName="checked">
              <Switch />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="push_enabled" label="浏览器推送" valuePropName="checked">
              <Switch />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="sms_enabled" label="短信通知" valuePropName="checked">
              <Switch />
            </Form.Item>
          </Col>
        </Row>
      </Card>

      <Card title="推送频率" size="small" style={{ marginBottom: '16px' }}>
        <Form.Item name="frequency" label="通知频率">
          <Radio.Group>
            <Radio value="realtime">实时推送</Radio>
            <Radio value="hourly">每小时汇总</Radio>
            <Radio value="daily">每日汇总</Radio>
          </Radio.Group>
        </Form.Item>
      </Card>

      <Card title="免打扰时间" size="small" style={{ marginBottom: '16px' }}>
        <Form.Item name={['quiet_hours', 'enabled']} valuePropName="checked">
          <Checkbox>启用免打扰时间</Checkbox>
        </Form.Item>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name={['quiet_hours', 'start_time']} label="开始时间">
              <TimePicker format="HH:mm" style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name={['quiet_hours', 'end_time']} label="结束时间">
              <TimePicker format="HH:mm" style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>
      </Card>

      <Card title="通知类别" size="small" style={{ marginBottom: '16px' }}>
        <Form.Item name="categories" label="接收以下类别的通知">
          <Checkbox.Group>
            <Row>
              <Col span={6}><Checkbox value="finance">财经新闻</Checkbox></Col>
              <Col span={6}><Checkbox value="technology">科技资讯</Checkbox></Col>
              <Col span={6}><Checkbox value="policy">政策法规</Checkbox></Col>
              <Col span={6}><Checkbox value="market">市场动态</Checkbox></Col>
            </Row>
          </Checkbox.Group>
        </Form.Item>
      </Card>

      <Form.Item>
        <Button type="primary" htmlType="submit" loading={loading}>
          保存设置
        </Button>
      </Form.Item>
    </Form>
  );

  const renderBillingSettings = () => (
    <div>
      <Card title="当前订阅" size="small" style={{ marginBottom: '16px' }}>
        <Row gutter={16}>
          <Col span={12}>
            <div>
              <Text strong>订阅套餐：</Text>
              <Text>高级版</Text>
            </div>
            <div style={{ marginTop: '8px' }}>
              <Text strong>月费：</Text>
              <Text>¥99.00</Text>
            </div>
          </Col>
          <Col span={12}>
            <div>
              <Text strong>到期时间：</Text>
              <Text>2024-02-15</Text>
            </div>
            <div style={{ marginTop: '8px' }}>
              <Text strong>自动续费：</Text>
              <Switch defaultChecked />
            </div>
          </Col>
        </Row>
        <Divider />
        <Space>
          <Button type="primary">升级套餐</Button>
          <Button>修改付款方式</Button>
          <Button danger>取消订阅</Button>
        </Space>
      </Card>

      <Card title="账单历史" size="small">
        <Table
          columns={billingColumns}
          dataSource={billingRecords}
          rowKey="id"
          pagination={{ pageSize: 10 }}
        />
      </Card>
    </div>
  );

  const renderAppearanceSettings = () => (
    <div>
      <Card title="主题设置" size="small" style={{ marginBottom: '16px' }}>
        <Row gutter={16}>
          <Col span={12}>
            <div style={{ marginBottom: '16px' }}>
              <Text strong>深色模式</Text>
              <br />
              <Switch
                checked={theme.darkMode}
                onChange={() => dispatch(toggleDarkMode())}
                style={{ marginTop: '8px' }}
              />
            </div>
          </Col>
          <Col span={12}>
            <div style={{ marginBottom: '16px' }}>
              <Text strong>主题色</Text>
              <br />
              <Select
                value={theme.primaryColor}
                onChange={(color) => dispatch(setPrimaryColor(color))}
                style={{ width: '100%', marginTop: '8px' }}
              >
                <Option value="#1890ff">蓝色</Option>
                <Option value="#52c41a">绿色</Option>
                <Option value="#faad14">橙色</Option>
                <Option value="#722ed1">紫色</Option>
                <Option value="#eb2f96">粉色</Option>
              </Select>
            </div>
          </Col>
        </Row>
      </Card>

      <Card title="语言设置" size="small" style={{ marginBottom: '16px' }}>
        <Select defaultValue="zh-CN" style={{ width: '200px' }}>
          <Option value="zh-CN">简体中文</Option>
          <Option value="en-US">English</Option>
        </Select>
      </Card>
    </div>
  );

  const renderHelpSettings = () => (
    <div>
      <Card title="帮助中心" size="small" style={{ marginBottom: '16px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Button type="link" icon={<QuestionCircleOutlined />}>
            常见问题
          </Button>
          <Button type="link" icon={<QuestionCircleOutlined />}>
            使用教程
          </Button>
          <Button type="link" icon={<QuestionCircleOutlined />}>
            联系客服
          </Button>
        </Space>
      </Card>

      <Card title="意见反馈" size="small">
        <Button
          type="primary"
          onClick={() => setFeedbackModalVisible(true)}
        >
          提交反馈
        </Button>
      </Card>
    </div>
  );

  return (
    <div>
      <Title level={2} style={{ marginBottom: '24px' }}>
        系统设置
      </Title>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane
          tab={
            <span>
              <BellOutlined />
              通知设置
            </span>
          }
          key="notifications"
        >
          {renderNotificationSettings()}
        </TabPane>

        <TabPane
          tab={
            <span>
              <CreditCardOutlined />
              账单管理
            </span>
          }
          key="billing"
        >
          {renderBillingSettings()}
        </TabPane>

        <TabPane
          tab={
            <span>
              <SettingOutlined />
              外观设置
            </span>
          }
          key="appearance"
        >
          {renderAppearanceSettings()}
        </TabPane>

        <TabPane
          tab={
            <span>
              <QuestionCircleOutlined />
              帮助支持
            </span>
          }
          key="help"
        >
          {renderHelpSettings()}
        </TabPane>
      </Tabs>

      {/* 反馈模态框 */}
      <Modal
        title="意见反馈"
        open={feedbackModalVisible}
        onCancel={() => setFeedbackModalVisible(false)}
        footer={null}
      >
        <Form
          form={feedbackForm}
          layout="vertical"
          onFinish={handleFeedbackSubmit}
        >
          <Form.Item
            name="type"
            label="反馈类型"
            rules={[{ required: true, message: '请选择反馈类型' }]}
          >
            <Select placeholder="请选择反馈类型">
              <Option value="bug">Bug报告</Option>
              <Option value="feature">功能建议</Option>
              <Option value="improvement">体验改进</Option>
              <Option value="other">其他</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="title"
            label="标题"
            rules={[{ required: true, message: '请输入标题' }]}
          >
            <Input placeholder="请简要描述问题或建议" />
          </Form.Item>

          <Form.Item
            name="content"
            label="详细描述"
            rules={[{ required: true, message: '请输入详细描述' }]}
          >
            <Input.TextArea
              rows={4}
              placeholder="请详细描述您遇到的问题或建议..."
            />
          </Form.Item>

          <Form.Item
            name="contact"
            label="联系方式"
          >
            <Input placeholder="请留下您的联系方式（可选）" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                提交反馈
              </Button>
              <Button onClick={() => setFeedbackModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default SettingsPage;
