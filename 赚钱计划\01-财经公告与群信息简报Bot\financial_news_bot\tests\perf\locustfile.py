"""
财经新闻Bot系统性能测试脚本
使用Locust进行负载测试和性能评估

运行命令：
locust -f tests/perf/locustfile.py --headless -u 100 -r 10 -H http://localhost:8000

参数说明：
-u 100: 模拟100个并发用户
-r 10: 每秒启动10个用户
-H: 目标主机地址
"""

import json
import random
from locust import HttpUser, task, between
from locust.exception import RescheduleTask


class FinancialNewsBotUser(HttpUser):
    """财经新闻Bot用户行为模拟"""
    
    wait_time = between(1, 3)  # 用户操作间隔1-3秒
    
    def on_start(self):
        """用户开始测试时的初始化操作"""
        self.auth_token = None
        self.user_id = None
        self.subscription_id = None
        
        # 模拟用户登录
        self.login()
    
    def login(self):
        """用户登录"""
        login_data = {
            "username": f"test_user_{random.randint(1000, 9999)}",
            "password": "test123456"
        }
        
        with self.client.post(
            "/api/v1/auth/login",
            json=login_data,
            catch_response=True,
            name="用户登录"
        ) as response:
            if response.status_code == 200:
                try:
                    data = response.json()
                    self.auth_token = data.get("access_token")
                    self.user_id = data.get("user", {}).get("id")
                    response.success()
                except (ValueError, KeyError):
                    response.failure("登录响应格式错误")
            else:
                response.failure(f"登录失败: {response.status_code}")
    
    def get_headers(self):
        """获取认证头"""
        if self.auth_token:
            return {"Authorization": f"Bearer {self.auth_token}"}
        return {}
    
    @task(10)
    def browse_news_list(self):
        """浏览新闻列表 - 最高频操作"""
        params = {
            "page": random.randint(1, 5),
            "limit": random.choice([10, 20, 50]),
            "category": random.choice(["", "财经", "科技", "政策"]),
            "sort": random.choice(["latest", "popular", "importance"])
        }
        
        with self.client.get(
            "/api/v1/news",
            params=params,
            headers=self.get_headers(),
            catch_response=True,
            name="浏览新闻列表"
        ) as response:
            if response.status_code == 200:
                try:
                    data = response.json()
                    if "data" in data and len(data["data"]) > 0:
                        response.success()
                    else:
                        response.failure("新闻列表为空")
                except ValueError:
                    response.failure("响应格式错误")
            else:
                response.failure(f"请求失败: {response.status_code}")
    
    @task(8)
    def view_news_detail(self):
        """查看新闻详情"""
        news_id = random.randint(1, 100)
        
        with self.client.get(
            f"/api/v1/news/{news_id}",
            headers=self.get_headers(),
            catch_response=True,
            name="查看新闻详情"
        ) as response:
            if response.status_code == 200:
                response.success()
            elif response.status_code == 404:
                response.success()  # 404也是正常情况
            else:
                response.failure(f"请求失败: {response.status_code}")
    
    @task(5)
    def search_news(self):
        """搜索新闻"""
        keywords = [
            "央行", "货币政策", "股市", "人工智能", "新能源",
            "经济", "投资", "科技", "创新", "政策"
        ]
        
        search_data = {
            "keyword": random.choice(keywords),
            "page": 1,
            "limit": 20
        }
        
        with self.client.get(
            "/api/v1/news/search",
            params=search_data,
            headers=self.get_headers(),
            catch_response=True,
            name="搜索新闻"
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"搜索失败: {response.status_code}")
    
    @task(3)
    def manage_subscriptions(self):
        """管理订阅"""
        with self.client.get(
            "/api/v1/subscriptions",
            headers=self.get_headers(),
            catch_response=True,
            name="获取订阅列表"
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"获取订阅失败: {response.status_code}")
    
    @task(2)
    def create_subscription(self):
        """创建订阅"""
        subscription_data = {
            "name": f"测试订阅_{random.randint(1000, 9999)}",
            "description": "性能测试创建的订阅",
            "keywords": random.sample(["财经", "科技", "政策", "投资", "创新"], 2),
            "channels": ["email"],
            "frequency": random.choice(["realtime", "daily", "weekly"])
        }
        
        with self.client.post(
            "/api/v1/subscriptions",
            json=subscription_data,
            headers=self.get_headers(),
            catch_response=True,
            name="创建订阅"
        ) as response:
            if response.status_code == 201:
                try:
                    data = response.json()
                    self.subscription_id = data.get("id")
                    response.success()
                except ValueError:
                    response.failure("创建订阅响应格式错误")
            else:
                response.failure(f"创建订阅失败: {response.status_code}")
    
    @task(2)
    def update_profile(self):
        """更新用户资料"""
        profile_data = {
            "full_name": f"测试用户_{random.randint(1000, 9999)}",
            "company": "测试公司",
            "position": "测试工程师"
        }
        
        with self.client.put(
            "/api/v1/users/profile",
            json=profile_data,
            headers=self.get_headers(),
            catch_response=True,
            name="更新用户资料"
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"更新资料失败: {response.status_code}")
    
    @task(1)
    def bookmark_news(self):
        """收藏新闻"""
        news_id = random.randint(1, 100)
        
        with self.client.post(
            f"/api/v1/news/{news_id}/bookmark",
            headers=self.get_headers(),
            catch_response=True,
            name="收藏新闻"
        ) as response:
            if response.status_code in [200, 201]:
                response.success()
            else:
                response.failure(f"收藏失败: {response.status_code}")
    
    @task(1)
    def get_notifications(self):
        """获取通知"""
        with self.client.get(
            "/api/v1/notifications",
            headers=self.get_headers(),
            catch_response=True,
            name="获取通知"
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"获取通知失败: {response.status_code}")


class AdminUser(HttpUser):
    """管理员用户行为模拟"""
    
    wait_time = between(2, 5)
    weight = 1  # 管理员用户权重较低
    
    def on_start(self):
        """管理员登录"""
        self.auth_token = None
        self.admin_login()
    
    def admin_login(self):
        """管理员登录"""
        login_data = {
            "username": "admin",
            "password": "admin123456"
        }
        
        with self.client.post(
            "/api/v1/auth/login",
            json=login_data,
            catch_response=True,
            name="管理员登录"
        ) as response:
            if response.status_code == 200:
                try:
                    data = response.json()
                    self.auth_token = data.get("access_token")
                    response.success()
                except ValueError:
                    response.failure("管理员登录响应格式错误")
            else:
                response.failure(f"管理员登录失败: {response.status_code}")
    
    def get_headers(self):
        """获取认证头"""
        if self.auth_token:
            return {"Authorization": f"Bearer {self.auth_token}"}
        return {}
    
    @task(5)
    def view_dashboard(self):
        """查看管理后台"""
        with self.client.get(
            "/api/v1/admin/dashboard",
            headers=self.get_headers(),
            catch_response=True,
            name="管理后台首页"
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"管理后台访问失败: {response.status_code}")
    
    @task(3)
    def manage_users(self):
        """管理用户"""
        with self.client.get(
            "/api/v1/admin/users",
            headers=self.get_headers(),
            catch_response=True,
            name="用户管理"
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"用户管理访问失败: {response.status_code}")
    
    @task(2)
    def view_statistics(self):
        """查看统计数据"""
        with self.client.get(
            "/api/v1/admin/statistics",
            headers=self.get_headers(),
            catch_response=True,
            name="统计数据"
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"统计数据访问失败: {response.status_code}")


# 性能测试配置
class WebsiteUser(HttpUser):
    """简化的网站用户 - 用于基础性能测试"""
    
    wait_time = between(1, 3)
    
    @task
    def news_list(self):
        """获取新闻列表"""
        self.client.get("/api/v1/news?limit=20")
    
    @task
    def health_check(self):
        """健康检查"""
        self.client.get("/api/v1/health")
