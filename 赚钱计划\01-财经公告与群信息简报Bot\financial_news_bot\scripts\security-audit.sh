#!/bin/bash

# 安全审计脚本
# 综合安全检查，包括依赖库漏洞扫描、代码安全分析等

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BACKEND_DIR="$PROJECT_ROOT/backend"
FRONTEND_DIR="$PROJECT_ROOT/frontend"
RESULTS_DIR="$PROJECT_ROOT/security-results"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# 创建结果目录
mkdir -p "$RESULTS_DIR"

# 检查工具是否安装
check_tools() {
    log_info "检查安全工具..."
    
    local missing_tools=()
    
    # Python安全工具
    if ! python3 -c "import safety" 2>/dev/null; then
        log_warning "Safety未安装，正在安装..."
        pip3 install safety
    fi
    
    if ! python3 -c "import bandit" 2>/dev/null; then
        log_warning "Bandit未安装，正在安装..."
        pip3 install bandit
    fi
    
    # Node.js安全工具
    if [ -d "$FRONTEND_DIR" ]; then
        if ! command -v npm >/dev/null 2>&1; then
            missing_tools+=("npm")
        fi
    fi
    
    # 其他工具
    if ! command -v git >/dev/null 2>&1; then
        missing_tools+=("git")
    fi
    
    if [ ${#missing_tools[@]} -gt 0 ]; then
        log_error "缺少必要工具: ${missing_tools[*]}"
        log_info "请安装缺少的工具后重新运行"
        exit 1
    fi
    
    log_success "安全工具检查完成"
}

# Python依赖安全检查
check_python_dependencies() {
    log_info "检查Python依赖库安全漏洞..."
    
    cd "$BACKEND_DIR"
    
    # 使用Safety检查已知漏洞
    local safety_report="$RESULTS_DIR/python_safety_report_$TIMESTAMP.json"
    
    if safety check --json --output "$safety_report"; then
        log_success "Python依赖安全检查通过"
    else
        log_warning "发现Python依赖安全问题，详见: $safety_report"
    fi
    
    # 生成依赖清单
    local deps_file="$RESULTS_DIR/python_dependencies_$TIMESTAMP.txt"
    pip3 freeze > "$deps_file"
    log_info "Python依赖清单已保存: $deps_file"
    
    # 检查过期依赖
    local outdated_file="$RESULTS_DIR/python_outdated_$TIMESTAMP.txt"
    pip3 list --outdated > "$outdated_file" 2>/dev/null || true
    log_info "过期依赖清单已保存: $outdated_file"
}

# Node.js依赖安全检查
check_nodejs_dependencies() {
    if [ ! -d "$FRONTEND_DIR" ]; then
        log_info "跳过Node.js依赖检查（前端目录不存在）"
        return
    fi
    
    log_info "检查Node.js依赖库安全漏洞..."
    
    cd "$FRONTEND_DIR"
    
    # 使用npm audit检查漏洞
    local audit_report="$RESULTS_DIR/nodejs_audit_report_$TIMESTAMP.json"
    
    if npm audit --json > "$audit_report" 2>/dev/null; then
        log_success "Node.js依赖安全检查通过"
    else
        log_warning "发现Node.js依赖安全问题，详见: $audit_report"
        
        # 尝试自动修复
        log_info "尝试自动修复Node.js依赖漏洞..."
        if npm audit fix --dry-run > "$RESULTS_DIR/nodejs_audit_fix_$TIMESTAMP.txt" 2>&1; then
            log_info "可修复的漏洞清单已保存"
        fi
    fi
    
    # 生成依赖清单
    local deps_file="$RESULTS_DIR/nodejs_dependencies_$TIMESTAMP.json"
    npm list --json > "$deps_file" 2>/dev/null || true
    log_info "Node.js依赖清单已保存: $deps_file"
}

# 代码安全分析
analyze_code_security() {
    log_info "进行代码安全分析..."
    
    # Python代码安全分析（Bandit）
    if [ -d "$BACKEND_DIR" ]; then
        log_info "使用Bandit分析Python代码安全..."
        
        local bandit_report="$RESULTS_DIR/bandit_report_$TIMESTAMP.json"
        
        cd "$BACKEND_DIR"
        if bandit -r app/ -f json -o "$bandit_report" 2>/dev/null; then
            log_success "Python代码安全分析完成"
        else
            log_warning "Python代码安全分析发现问题，详见: $bandit_report"
        fi
        
        # 生成HTML报告
        local bandit_html="$RESULTS_DIR/bandit_report_$TIMESTAMP.html"
        bandit -r app/ -f html -o "$bandit_html" 2>/dev/null || true
    fi
    
    # 检查敏感信息泄露
    check_sensitive_data_exposure
}

# 检查敏感信息泄露
check_sensitive_data_exposure() {
    log_info "检查敏感信息泄露..."
    
    local sensitive_report="$RESULTS_DIR/sensitive_data_report_$TIMESTAMP.txt"
    
    {
        echo "=== 敏感信息泄露检查报告 ==="
        echo "检查时间: $(date)"
        echo ""
        
        # 检查常见敏感信息模式
        echo "=== 检查密码和密钥 ==="
        find "$PROJECT_ROOT" -type f \( -name "*.py" -o -name "*.js" -o -name "*.ts" -o -name "*.json" -o -name "*.yaml" -o -name "*.yml" \) \
            -not -path "*/node_modules/*" \
            -not -path "*/.git/*" \
            -not -path "*/venv/*" \
            -not -path "*/__pycache__/*" \
            -exec grep -l -i -E "(password|passwd|pwd|secret|key|token|api_key)" {} \; 2>/dev/null || true
        
        echo ""
        echo "=== 检查硬编码凭据 ==="
        find "$PROJECT_ROOT" -type f \( -name "*.py" -o -name "*.js" -o -name "*.ts" \) \
            -not -path "*/node_modules/*" \
            -not -path "*/.git/*" \
            -not -path "*/venv/*" \
            -exec grep -n -E "(password\s*=\s*['\"][^'\"]+['\"]|api_key\s*=\s*['\"][^'\"]+['\"])" {} + 2>/dev/null || true
        
        echo ""
        echo "=== 检查数据库连接字符串 ==="
        find "$PROJECT_ROOT" -type f \( -name "*.py" -o -name "*.js" -o -name "*.ts" -o -name "*.env*" \) \
            -not -path "*/node_modules/*" \
            -not -path "*/.git/*" \
            -exec grep -n -E "(mysql://|postgresql://|mongodb://|redis://)" {} + 2>/dev/null || true
        
        echo ""
        echo "=== 检查IP地址和URL ==="
        find "$PROJECT_ROOT" -type f \( -name "*.py" -o -name "*.js" -o -name "*.ts" -o -name "*.json" \) \
            -not -path "*/node_modules/*" \
            -not -path "*/.git/*" \
            -exec grep -n -E "([0-9]{1,3}\.){3}[0-9]{1,3}" {} + 2>/dev/null || true
        
    } > "$sensitive_report"
    
    log_info "敏感信息检查报告已保存: $sensitive_report"
}

# Docker安全检查
check_docker_security() {
    log_info "检查Docker配置安全..."
    
    local docker_report="$RESULTS_DIR/docker_security_$TIMESTAMP.txt"
    
    {
        echo "=== Docker安全检查报告 ==="
        echo "检查时间: $(date)"
        echo ""
        
        # 检查Dockerfile
        if [ -f "$PROJECT_ROOT/Dockerfile" ]; then
            echo "=== Dockerfile安全检查 ==="
            
            # 检查是否使用root用户
            if grep -q "USER root" "$PROJECT_ROOT/Dockerfile" 2>/dev/null; then
                echo "⚠️  警告: Dockerfile中使用了root用户"
            fi
            
            # 检查是否暴露了不必要的端口
            echo "暴露的端口:"
            grep "EXPOSE" "$PROJECT_ROOT/Dockerfile" 2>/dev/null || echo "未找到EXPOSE指令"
            
            # 检查基础镜像
            echo "基础镜像:"
            grep "FROM" "$PROJECT_ROOT/Dockerfile" 2>/dev/null || echo "未找到FROM指令"
        fi
        
        # 检查docker-compose.yml
        if [ -f "$PROJECT_ROOT/docker-compose.yml" ]; then
            echo ""
            echo "=== docker-compose.yml安全检查 ==="
            
            # 检查是否使用了默认密码
            if grep -i -E "(password|passwd)" "$PROJECT_ROOT/docker-compose.yml" 2>/dev/null; then
                echo "⚠️  警告: docker-compose.yml中可能包含密码"
            fi
            
            # 检查网络配置
            echo "网络配置:"
            grep -A 5 "networks:" "$PROJECT_ROOT/docker-compose.yml" 2>/dev/null || echo "未找到网络配置"
        fi
        
    } > "$docker_report"
    
    log_info "Docker安全检查报告已保存: $docker_report"
}

# 运行Web应用安全扫描
run_web_security_scan() {
    log_info "运行Web应用安全扫描..."
    
    local target_url="http://localhost:8000"
    local scanner_script="$PROJECT_ROOT/tests/security/security_scanner.py"
    local scan_report="$RESULTS_DIR/web_security_scan_$TIMESTAMP.json"
    
    if [ -f "$scanner_script" ]; then
        # 检查目标服务是否运行
        if curl -s -f "$target_url/health" >/dev/null 2>&1; then
            log_info "运行自定义安全扫描器..."
            
            cd "$PROJECT_ROOT"
            if python3 "$scanner_script" --target "$target_url" --output "$scan_report"; then
                log_success "Web安全扫描完成"
            else
                log_warning "Web安全扫描发现问题，详见: $scan_report"
            fi
        else
            log_warning "目标服务未运行，跳过Web安全扫描"
        fi
    else
        log_warning "安全扫描器脚本不存在，跳过Web安全扫描"
    fi
}

# 检查SSL/TLS配置
check_ssl_configuration() {
    log_info "检查SSL/TLS配置..."
    
    local ssl_report="$RESULTS_DIR/ssl_check_$TIMESTAMP.txt"
    
    {
        echo "=== SSL/TLS配置检查报告 ==="
        echo "检查时间: $(date)"
        echo ""
        
        # 检查Nginx配置
        if [ -f "$PROJECT_ROOT/nginx/nginx.conf" ]; then
            echo "=== Nginx SSL配置 ==="
            
            if grep -q "ssl_certificate" "$PROJECT_ROOT/nginx/nginx.conf" 2>/dev/null; then
                echo "✅ 找到SSL证书配置"
                grep "ssl_" "$PROJECT_ROOT/nginx/nginx.conf" 2>/dev/null
            else
                echo "⚠️  警告: 未找到SSL证书配置"
            fi
            
            # 检查安全头
            echo ""
            echo "=== 安全头检查 ==="
            if grep -q "add_header.*X-Frame-Options" "$PROJECT_ROOT/nginx/nginx.conf" 2>/dev/null; then
                echo "✅ 配置了X-Frame-Options"
            else
                echo "⚠️  建议添加X-Frame-Options头"
            fi
            
            if grep -q "add_header.*X-Content-Type-Options" "$PROJECT_ROOT/nginx/nginx.conf" 2>/dev/null; then
                echo "✅ 配置了X-Content-Type-Options"
            else
                echo "⚠️  建议添加X-Content-Type-Options头"
            fi
        fi
        
    } > "$ssl_report"
    
    log_info "SSL/TLS检查报告已保存: $ssl_report"
}

# 生成安全审计报告
generate_security_report() {
    log_info "生成综合安全审计报告..."
    
    local report_file="$RESULTS_DIR/security_audit_report_$TIMESTAMP.html"
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>财经新闻Bot安全审计报告</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; }
        .success { color: green; }
        .warning { color: orange; }
        .error { color: red; }
        .file-list { background-color: #f9f9f9; padding: 10px; border-radius: 5px; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>财经新闻Bot安全审计报告</h1>
        <p><strong>审计时间:</strong> $(date)</p>
        <p><strong>审计范围:</strong> 依赖库漏洞、代码安全、配置安全、Web应用安全</p>
    </div>
    
    <div class="section">
        <h2>审计摘要</h2>
        <table>
            <tr>
                <th>检查项目</th>
                <th>状态</th>
                <th>报告文件</th>
            </tr>
            <tr>
                <td>Python依赖安全</td>
                <td class="success">已检查</td>
                <td>python_safety_report_$TIMESTAMP.json</td>
            </tr>
            <tr>
                <td>Node.js依赖安全</td>
                <td class="success">已检查</td>
                <td>nodejs_audit_report_$TIMESTAMP.json</td>
            </tr>
            <tr>
                <td>代码安全分析</td>
                <td class="success">已检查</td>
                <td>bandit_report_$TIMESTAMP.json</td>
            </tr>
            <tr>
                <td>敏感信息泄露</td>
                <td class="success">已检查</td>
                <td>sensitive_data_report_$TIMESTAMP.txt</td>
            </tr>
            <tr>
                <td>Docker安全</td>
                <td class="success">已检查</td>
                <td>docker_security_$TIMESTAMP.txt</td>
            </tr>
            <tr>
                <td>Web应用安全</td>
                <td class="success">已检查</td>
                <td>web_security_scan_$TIMESTAMP.json</td>
            </tr>
            <tr>
                <td>SSL/TLS配置</td>
                <td class="success">已检查</td>
                <td>ssl_check_$TIMESTAMP.txt</td>
            </tr>
        </table>
    </div>
    
    <div class="section">
        <h2>安全建议</h2>
        <ul>
            <li>定期更新依赖库，修复已知漏洞</li>
            <li>使用强密码和双因素认证</li>
            <li>配置适当的安全头</li>
            <li>启用HTTPS和HSTS</li>
            <li>实施输入验证和输出编码</li>
            <li>定期进行安全审计和渗透测试</li>
            <li>建立安全事件响应流程</li>
        </ul>
    </div>
    
    <div class="section">
        <h2>生成的报告文件</h2>
        <div class="file-list">
EOF

    # 列出所有生成的报告文件
    find "$RESULTS_DIR" -name "*$TIMESTAMP*" -type f | while read -r file; do
        echo "            <p>$(basename "$file")</p>" >> "$report_file"
    done

    cat >> "$report_file" << EOF
        </div>
    </div>
    
    <div class="section">
        <h2>下一步行动</h2>
        <ol>
            <li>审查所有发现的安全问题</li>
            <li>按优先级修复高危和中危漏洞</li>
            <li>更新安全策略和流程</li>
            <li>培训开发团队安全最佳实践</li>
            <li>建立定期安全审计计划</li>
        </ol>
    </div>
</body>
</html>
EOF

    log_success "综合安全审计报告已生成: $report_file"
}

# 主函数
main() {
    log_info "开始安全审计..."
    
    # 检查工具
    check_tools
    
    # 依赖库安全检查
    check_python_dependencies
    check_nodejs_dependencies
    
    # 代码安全分析
    analyze_code_security
    
    # Docker安全检查
    check_docker_security
    
    # Web应用安全扫描
    run_web_security_scan
    
    # SSL/TLS配置检查
    check_ssl_configuration
    
    # 生成综合报告
    generate_security_report
    
    log_success "🔒 安全审计完成！"
    log_info "📊 查看结果目录: $RESULTS_DIR"
    log_info "📋 综合报告: $RESULTS_DIR/security_audit_report_$TIMESTAMP.html"
}

# 信号处理
trap 'log_error "安全审计被中断"; exit 1' INT TERM

# 运行主函数
main "$@"
