from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum

from ..models.news import NewsCategory, NewsSource, NewsSentiment


class NewsCreate(BaseModel):
    """创建新闻请求"""
    title: str = Field(..., min_length=1, max_length=500, description="新闻标题")
    content: Optional[str] = Field(None, description="新闻内容")
    summary: Optional[str] = Field(None, max_length=1000, description="新闻摘要")
    source: NewsSource = Field(..., description="新闻来源")
    source_url: Optional[str] = Field(None, max_length=500, description="原文链接")
    source_id: Optional[str] = Field(None, max_length=100, description="来源系统中的ID")
    published_at: Optional[datetime] = Field(None, description="发布时间")
    category: Optional[NewsCategory] = Field(NewsCategory.OTHER, description="新闻分类")
    tags: Optional[List[str]] = Field(default=[], description="标签列表")
    importance_score: Optional[int] = Field(50, ge=0, le=100, description="重要性评分")
    sentiment: Optional[NewsSentiment] = Field(NewsSentiment.UNKNOWN, description="情感倾向")
    entities: Optional[Dict[str, Any]] = Field(None, description="实体信息")
    companies: Optional[List[str]] = Field(default=[], description="相关公司")
    stock_codes: Optional[List[str]] = Field(default=[], description="相关股票代码")

    class Config:
        schema_extra = {
            "example": {
                "title": "某公司发布2024年第三季度财报",
                "content": "某公司今日发布2024年第三季度财务报告...",
                "summary": "某公司Q3营收增长15%，净利润同比上升20%",
                "source": "sse",
                "source_url": "http://www.sse.com.cn/disclosure/...",
                "source_id": "2024-001",
                "published_at": "2024-01-15T10:30:00",
                "category": "finance",
                "tags": ["财报", "业绩"],
                "importance_score": 80,
                "sentiment": "positive",
                "companies": ["某公司"],
                "stock_codes": ["000001"]
            }
        }


class NewsUpdate(BaseModel):
    """更新新闻请求"""
    title: Optional[str] = Field(None, min_length=1, max_length=500, description="新闻标题")
    content: Optional[str] = Field(None, description="新闻内容")
    summary: Optional[str] = Field(None, max_length=1000, description="新闻摘要")
    category: Optional[NewsCategory] = Field(None, description="新闻分类")
    tags: Optional[List[str]] = Field(None, description="标签列表")
    importance_score: Optional[int] = Field(None, ge=0, le=100, description="重要性评分")
    sentiment: Optional[NewsSentiment] = Field(None, description="情感倾向")
    entities: Optional[Dict[str, Any]] = Field(None, description="实体信息")
    companies: Optional[List[str]] = Field(None, description="相关公司")
    stock_codes: Optional[List[str]] = Field(None, description="相关股票代码")
    is_processed: Optional[bool] = Field(None, description="是否已处理")


class NewsResponse(BaseModel):
    """新闻响应"""
    id: int
    title: str
    content: Optional[str]
    summary: Optional[str]
    source: NewsSource
    source_url: Optional[str]
    source_id: Optional[str]
    published_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime
    category: NewsCategory
    tags: List[str]
    importance_score: int
    sentiment: NewsSentiment
    entities: Optional[Dict[str, Any]]
    companies: List[str]
    stock_codes: List[str]
    content_hash: Optional[str]
    word_count: int
    is_processed: bool
    is_duplicate: bool

    class Config:
        orm_mode = True
        schema_extra = {
            "example": {
                "id": 1,
                "title": "某公司发布2024年第三季度财报",
                "content": "某公司今日发布2024年第三季度财务报告...",
                "summary": "某公司Q3营收增长15%，净利润同比上升20%",
                "source": "sse",
                "source_url": "http://www.sse.com.cn/disclosure/...",
                "source_id": "2024-001",
                "published_at": "2024-01-15T10:30:00",
                "created_at": "2024-01-15T10:30:00",
                "updated_at": "2024-01-15T10:30:00",
                "category": "finance",
                "tags": ["财报", "业绩"],
                "importance_score": 80,
                "sentiment": "positive",
                "entities": {"companies": ["某公司"], "persons": []},
                "companies": ["某公司"],
                "stock_codes": ["000001"],
                "content_hash": "abc123...",
                "word_count": 500,
                "is_processed": True,
                "is_duplicate": False
            }
        }


class NewsListResponse(BaseModel):
    """新闻列表响应"""
    items: List[NewsResponse]
    total: int
    page: int
    limit: int
    pages: int

    class Config:
        schema_extra = {
            "example": {
                "items": [],
                "total": 100,
                "page": 1,
                "limit": 20,
                "pages": 5
            }
        }

class NewsSearchParams(BaseModel):
    """新闻搜索参数"""
    query: str
    page: int = 1
    limit: int = 20
    source: Optional[str] = None
    category: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    search_in: str = "all"  # title, content, all
    sort_by: str = "relevance"
    sort_order: str = "desc"

class NewsSearchResponse(BaseModel):
    """新闻搜索响应"""
    items: List[NewsResponse]
    total: int
    page: int
    limit: int
    pages: int
    query: str
    search_time: datetime

    class Config:
        schema_extra = {
            "example": {
                "items": [],
                "total": 50,
                "page": 1,
                "limit": 20,
                "pages": 3,
                "query": "财报",
                "search_time": "2024-01-15T10:30:00"
            }
        }

class NewsStatistics(BaseModel):
    """新闻统计数据"""
    total_count: int
    today_count: int
    processed_count: int
    duplicate_count: int
    avg_importance: float
    source_distribution: Dict[str, int]
    category_distribution: Dict[str, int]
    sentiment_distribution: Dict[str, int]
    period_start: datetime
    period_end: datetime

    class Config:
        schema_extra = {
            "example": {
                "total_count": 1000,
                "today_count": 50,
                "processed_count": 950,
                "duplicate_count": 30,
                "avg_importance": 65.5,
                "source_distribution": {
                    "SSE": 400,
                    "SZSE": 350,
                    "CSRC": 150,
                    "RSS": 100
                },
                "category_distribution": {
                    "公司公告": 500,
                    "财经新闻": 200,
                    "监管动态": 150,
                    "市场资讯": 100,
                    "其他": 50
                },
                "sentiment_distribution": {
                    "positive": 300,
                    "negative": 200,
                    "neutral": 400,
                    "unknown": 100
                },
                "period_start": "2024-01-01T00:00:00",
                "period_end": "2024-01-15T23:59:59"
            }
        }


class NewsSearchRequest(BaseModel):
    """新闻搜索请求"""
    q: Optional[str] = Field(None, description="搜索关键词")
    source: Optional[NewsSource] = Field(None, description="新闻来源过滤")
    category: Optional[NewsCategory] = Field(None, description="分类过滤")
    start_date: Optional[datetime] = Field(None, description="开始日期")
    end_date: Optional[datetime] = Field(None, description="结束日期")
    min_importance: Optional[int] = Field(None, ge=0, le=100, description="最低重要性评分")
    sentiment: Optional[NewsSentiment] = Field(None, description="情感过滤")
    companies: Optional[List[str]] = Field(None, description="公司过滤")
    stock_codes: Optional[List[str]] = Field(None, description="股票代码过滤")
    tags: Optional[List[str]] = Field(None, description="标签过滤")
    page: int = Field(1, ge=1, description="页码")
    size: int = Field(10, ge=1, le=100, description="每页数量")
    sort_by: Optional[str] = Field("published_at", description="排序字段")
    sort_order: Optional[str] = Field("desc", description="排序方向(asc/desc)")

    @validator('sort_by')
    def validate_sort_by(cls, v):
        allowed_fields = ['published_at', 'created_at', 'importance_score', 'word_count']
        if v not in allowed_fields:
            raise ValueError(f'sort_by must be one of {allowed_fields}')
        return v

    @validator('sort_order')
    def validate_sort_order(cls, v):
        if v.lower() not in ['asc', 'desc']:
            raise ValueError('sort_order must be asc or desc')
        return v.lower()

    class Config:
        schema_extra = {
            "example": {
                "q": "财报",
                "source": "sse",
                "category": "finance",
                "start_date": "2024-01-01T00:00:00",
                "end_date": "2024-12-31T23:59:59",
                "min_importance": 70,
                "sentiment": "positive",
                "companies": ["某公司"],
                "stock_codes": ["000001"],
                "tags": ["财报"],
                "page": 1,
                "size": 10,
                "sort_by": "published_at",
                "sort_order": "desc"
            }
        }


class NewsStats(BaseModel):
    """新闻统计信息"""
    total_news: int = Field(description="新闻总数")
    today_news: int = Field(description="今日新闻数")
    processed_news: int = Field(description="已处理新闻数")
    duplicate_news: int = Field(description="重复新闻数")
    source_stats: Dict[str, int] = Field(description="各来源新闻统计")
    category_stats: Dict[str, int] = Field(description="各分类新闻统计")
    sentiment_stats: Dict[str, int] = Field(description="情感分析统计")

    class Config:
        schema_extra = {
            "example": {
                "total_news": 1000,
                "today_news": 50,
                "processed_news": 950,
                "duplicate_news": 30,
                "source_stats": {
                    "sse": 400,
                    "szse": 350,
                    "csrc": 150,
                    "rss": 100
                },
                "category_stats": {
                    "announcement": 500,
                    "finance": 200,
                    "regulation": 150,
                    "market": 100,
                    "other": 50
                },
                "sentiment_stats": {
                    "positive": 300,
                    "negative": 200,
                    "neutral": 400,
                    "unknown": 100
                }
            }
        }
