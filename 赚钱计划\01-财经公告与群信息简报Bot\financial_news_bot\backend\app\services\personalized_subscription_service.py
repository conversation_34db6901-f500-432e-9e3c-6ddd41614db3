"""
个性化订阅配置服务
支持多维度订阅规则和智能推荐
"""
import asyncio
import logging
import json
from typing import Dict, List, Optional, Any, Set, Union
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from enum import Enum

from app.config import settings

logger = logging.getLogger(__name__)


class SubscriptionType(Enum):
    """订阅类型枚举"""
    COMPANY = "company"           # 公司订阅
    INDUSTRY = "industry"         # 行业订阅
    KEYWORD = "keyword"           # 关键词订阅
    TOPIC = "topic"              # 主题订阅
    STOCK_CODE = "stock_code"    # 股票代码订阅
    PERSON = "person"            # 人物订阅
    ORGANIZATION = "organization" # 机构订阅


class FilterType(Enum):
    """过滤类型枚举"""
    WHITELIST = "whitelist"      # 白名单（仅推送）
    BLACKLIST = "blacklist"      # 黑名单（过滤排除）


class LogicOperator(Enum):
    """逻辑操作符枚举"""
    AND = "and"
    OR = "or"
    NOT = "not"


class Priority(Enum):
    """优先级枚举"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    URGENT = 4


@dataclass
class SubscriptionRule:
    """订阅规则"""
    rule_id: str
    subscription_type: SubscriptionType
    filter_type: FilterType
    values: List[str]              # 订阅值列表
    priority: Priority
    logic_operator: LogicOperator
    is_active: bool = True
    created_at: datetime = None
    updated_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()


@dataclass
class UserSubscription:
    """用户订阅配置"""
    user_id: str
    subscription_name: str
    rules: List[SubscriptionRule]
    is_active: bool = True
    notification_settings: Dict[str, Any] = None
    created_at: datetime = None
    updated_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()
        if self.notification_settings is None:
            self.notification_settings = {
                "email": True,
                "push": True,
                "frequency": "realtime"  # realtime, hourly, daily
            }


@dataclass
class MatchResult:
    """匹配结果"""
    is_match: bool
    matched_rules: List[SubscriptionRule]
    priority_score: float
    match_details: Dict[str, Any]


class PersonalizedSubscriptionService:
    """个性化订阅服务"""
    
    def __init__(self):
        """初始化订阅服务"""
        self.user_subscriptions: Dict[str, List[UserSubscription]] = {}
        self.predefined_topics = self._load_predefined_topics()
        self.industry_mapping = self._load_industry_mapping()
        
        logger.info("个性化订阅服务初始化完成")
    
    def _load_predefined_topics(self) -> Dict[str, List[str]]:
        """加载预定义主题"""
        return {
            "重大公告": ["业绩预告", "重大合同", "股权变动", "重组并购", "分红派息"],
            "监管动态": ["证监会", "银保监会", "央行", "政策法规", "监管处罚"],
            "市场热点": ["新能源", "人工智能", "生物医药", "半导体", "新基建"],
            "财务数据": ["年报", "中报", "季报", "业绩快报", "财务指标"],
            "风险提示": ["业绩预警", "违规处罚", "退市风险", "债务违约", "诉讼仲裁"],
            "投资机会": ["增持回购", "定增配股", "资产注入", "业务拓展", "技术突破"]
        }
    
    def _load_industry_mapping(self) -> Dict[str, List[str]]:
        """加载行业映射"""
        return {
            "金融": ["银行", "保险", "证券", "基金", "信托", "租赁"],
            "科技": ["计算机", "通信", "电子", "软件", "互联网", "人工智能"],
            "消费": ["食品饮料", "纺织服装", "家电", "汽车", "零售", "旅游"],
            "医药": ["生物医药", "医疗器械", "化学制药", "中药", "医疗服务"],
            "能源": ["石油", "煤炭", "电力", "新能源", "化工", "钢铁"],
            "地产": ["房地产", "建筑", "建材", "装饰", "园林", "物业"]
        }
    
    async def create_subscription(self, user_id: str, subscription_name: str, 
                                 rules: List[Dict[str, Any]]) -> UserSubscription:
        """
        创建用户订阅
        
        Args:
            user_id: 用户ID
            subscription_name: 订阅名称
            rules: 规则列表
            
        Returns:
            用户订阅对象
        """
        try:
            # 转换规则
            subscription_rules = []
            for i, rule_data in enumerate(rules):
                rule = SubscriptionRule(
                    rule_id=f"{user_id}_{subscription_name}_{i}_{datetime.now().timestamp()}",
                    subscription_type=SubscriptionType(rule_data["subscription_type"]),
                    filter_type=FilterType(rule_data["filter_type"]),
                    values=rule_data["values"],
                    priority=Priority(rule_data.get("priority", Priority.MEDIUM.value)),
                    logic_operator=LogicOperator(rule_data.get("logic_operator", LogicOperator.OR.value))
                )
                subscription_rules.append(rule)
            
            # 创建订阅
            subscription = UserSubscription(
                user_id=user_id,
                subscription_name=subscription_name,
                rules=subscription_rules,
                notification_settings=rules[0].get("notification_settings") if rules else None
            )
            
            # 保存订阅
            if user_id not in self.user_subscriptions:
                self.user_subscriptions[user_id] = []
            self.user_subscriptions[user_id].append(subscription)
            
            logger.info(f"用户 {user_id} 创建订阅: {subscription_name}")
            return subscription
            
        except Exception as e:
            logger.error(f"创建订阅失败: {str(e)}")
            raise
    
    async def match_content(self, user_id: str, title: str, content: str, 
                           entities: List[Dict[str, Any]] = None) -> List[MatchResult]:
        """
        匹配内容与用户订阅
        
        Args:
            user_id: 用户ID
            title: 文章标题
            content: 文章内容
            entities: 实体列表
            
        Returns:
            匹配结果列表
        """
        try:
            user_subs = self.user_subscriptions.get(user_id, [])
            match_results = []
            
            text = f"{title} {content}"
            
            for subscription in user_subs:
                if not subscription.is_active:
                    continue
                
                match_result = await self._evaluate_subscription_rules(
                    subscription, text, entities or []
                )
                
                if match_result.is_match:
                    match_results.append(match_result)
            
            # 按优先级排序
            match_results.sort(key=lambda x: x.priority_score, reverse=True)
            
            return match_results
            
        except Exception as e:
            logger.error(f"内容匹配失败: {str(e)}")
            return []
    
    async def _evaluate_subscription_rules(self, subscription: UserSubscription, 
                                          text: str, entities: List[Dict[str, Any]]) -> MatchResult:
        """
        评估订阅规则
        
        Args:
            subscription: 用户订阅
            text: 文本内容
            entities: 实体列表
            
        Returns:
            匹配结果
        """
        matched_rules = []
        total_priority_score = 0.0
        match_details = {}
        
        for rule in subscription.rules:
            if not rule.is_active:
                continue
            
            is_rule_match = await self._evaluate_single_rule(rule, text, entities)
            
            if is_rule_match:
                matched_rules.append(rule)
                total_priority_score += rule.priority.value
                
                # 记录匹配详情
                if rule.subscription_type.value not in match_details:
                    match_details[rule.subscription_type.value] = []
                match_details[rule.subscription_type.value].extend(rule.values)
        
        # 根据逻辑操作符判断整体匹配结果
        is_match = len(matched_rules) > 0
        
        return MatchResult(
            is_match=is_match,
            matched_rules=matched_rules,
            priority_score=total_priority_score,
            match_details=match_details
        )
    
    async def _evaluate_single_rule(self, rule: SubscriptionRule, text: str, 
                                   entities: List[Dict[str, Any]]) -> bool:
        """
        评估单个规则
        
        Args:
            rule: 订阅规则
            text: 文本内容
            entities: 实体列表
            
        Returns:
            是否匹配
        """
        matches = []
        
        for value in rule.values:
            if rule.subscription_type == SubscriptionType.KEYWORD:
                # 关键词匹配
                match = value.lower() in text.lower()
            elif rule.subscription_type == SubscriptionType.COMPANY:
                # 公司名称匹配
                match = any(
                    entity.get("type") == "company" and value in entity.get("text", "")
                    for entity in entities
                ) or value in text
            elif rule.subscription_type == SubscriptionType.STOCK_CODE:
                # 股票代码匹配
                match = any(
                    entity.get("type") == "stock_code" and value == entity.get("text", "")
                    for entity in entities
                ) or value in text
            elif rule.subscription_type == SubscriptionType.INDUSTRY:
                # 行业匹配
                industry_keywords = self.industry_mapping.get(value, [value])
                match = any(keyword in text for keyword in industry_keywords)
            elif rule.subscription_type == SubscriptionType.TOPIC:
                # 主题匹配
                topic_keywords = self.predefined_topics.get(value, [value])
                match = any(keyword in text for keyword in topic_keywords)
            else:
                # 默认文本匹配
                match = value in text
            
            matches.append(match)
        
        # 根据逻辑操作符计算结果
        if rule.logic_operator == LogicOperator.AND:
            result = all(matches)
        elif rule.logic_operator == LogicOperator.OR:
            result = any(matches)
        elif rule.logic_operator == LogicOperator.NOT:
            result = not any(matches)
        else:
            result = any(matches)  # 默认OR
        
        # 处理过滤类型
        if rule.filter_type == FilterType.BLACKLIST:
            result = not result  # 黑名单取反
        
        return result
    
    async def get_user_subscriptions(self, user_id: str) -> List[UserSubscription]:
        """获取用户订阅列表"""
        return self.user_subscriptions.get(user_id, [])
    
    async def get_subscription_suggestions(self, user_id: str, 
                                         user_behavior: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        基于用户行为生成订阅建议
        
        Args:
            user_id: 用户ID
            user_behavior: 用户行为数据
            
        Returns:
            订阅建议列表
        """
        suggestions = []
        
        # 基于阅读历史的建议
        read_companies = user_behavior.get("read_companies", [])
        if read_companies:
            suggestions.append({
                "type": "company_subscription",
                "title": "关注的公司订阅",
                "description": f"基于您经常阅读的公司新闻，建议订阅: {', '.join(read_companies[:5])}",
                "suggested_rules": [{
                    "subscription_type": "company",
                    "filter_type": "whitelist",
                    "values": read_companies[:5],
                    "priority": 3
                }]
            })
        
        # 基于搜索关键词的建议
        search_keywords = user_behavior.get("search_keywords", [])
        if search_keywords:
            suggestions.append({
                "type": "keyword_subscription",
                "title": "关键词订阅",
                "description": f"基于您的搜索历史，建议订阅关键词: {', '.join(search_keywords[:5])}",
                "suggested_rules": [{
                    "subscription_type": "keyword",
                    "filter_type": "whitelist",
                    "values": search_keywords[:5],
                    "priority": 2
                }]
            })
        
        # 基于行业偏好的建议
        preferred_industries = user_behavior.get("preferred_industries", [])
        if preferred_industries:
            suggestions.append({
                "type": "industry_subscription",
                "title": "行业订阅",
                "description": f"基于您的阅读偏好，建议订阅行业: {', '.join(preferred_industries[:3])}",
                "suggested_rules": [{
                    "subscription_type": "industry",
                    "filter_type": "whitelist",
                    "values": preferred_industries[:3],
                    "priority": 2
                }]
            })
        
        return suggestions


# 全局个性化订阅服务实例
personalized_subscription_service = None

def get_personalized_subscription_service() -> PersonalizedSubscriptionService:
    """获取个性化订阅服务实例"""
    global personalized_subscription_service
    if personalized_subscription_service is None:
        personalized_subscription_service = PersonalizedSubscriptionService()
    return personalized_subscription_service
