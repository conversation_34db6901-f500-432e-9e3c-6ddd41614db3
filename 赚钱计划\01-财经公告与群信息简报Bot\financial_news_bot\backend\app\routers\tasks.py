"""
任务管理API路由
提供Celery任务的监控、管理和统计功能
"""
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from datetime import datetime, timedelta

from app.database import get_db
from app.dependencies.auth import get_current_user
from app.models.user import User
from app.celery_app import celery_app
from app.tasks.crawler_tasks import (
    crawl_sse, crawl_szse, crawl_csrc, crawl_rss,
    process_news_batch, cleanup_old_news, health_check_crawlers
)

router = APIRouter(prefix="/tasks", tags=["任务管理"])

@router.get("/status", summary="获取任务系统状态")
async def get_task_system_status(
    current_user: User = Depends(get_current_user)
):
    """
    获取Celery任务系统的整体状态
    """
    try:
        # 获取Celery状态
        inspect = celery_app.control.inspect()
        
        # 获取活跃任务
        active_tasks = inspect.active()
        
        # 获取已注册任务
        registered_tasks = inspect.registered()
        
        # 获取队列状态
        stats = inspect.stats()
        
        # 统计信息
        total_workers = len(stats) if stats else 0
        total_active_tasks = sum(len(tasks) for tasks in active_tasks.values()) if active_tasks else 0
        
        return {
            "status": "running" if total_workers > 0 else "stopped",
            "workers": total_workers,
            "active_tasks": total_active_tasks,
            "registered_tasks": registered_tasks,
            "stats": stats,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取任务系统状态失败: {str(e)}"
        )

@router.get("/active", summary="获取活跃任务列表")
async def get_active_tasks(
    current_user: User = Depends(get_current_user)
):
    """
    获取当前正在执行的任务列表
    """
    try:
        inspect = celery_app.control.inspect()
        active_tasks = inspect.active()
        
        if not active_tasks:
            return {"active_tasks": [], "count": 0}
        
        # 格式化任务信息
        formatted_tasks = []
        for worker, tasks in active_tasks.items():
            for task in tasks:
                formatted_tasks.append({
                    "worker": worker,
                    "task_id": task.get("id"),
                    "task_name": task.get("name"),
                    "args": task.get("args", []),
                    "kwargs": task.get("kwargs", {}),
                    "time_start": task.get("time_start"),
                    "acknowledged": task.get("acknowledged", False)
                })
        
        return {
            "active_tasks": formatted_tasks,
            "count": len(formatted_tasks)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取活跃任务失败: {str(e)}"
        )

@router.post("/crawlers/run", summary="手动执行爬虫任务")
async def run_crawler_task(
    crawler_type: str,
    current_user: User = Depends(get_current_user)
):
    """
    手动触发指定的爬虫任务
    
    Args:
        crawler_type: 爬虫类型 (sse, szse, csrc, rss, all)
    """
    if current_user.role not in ["PRO", "ENTERPRISE"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有PRO和ENTERPRISE用户可以手动执行爬虫任务"
        )
    
    task_map = {
        "sse": crawl_sse,
        "szse": crawl_szse,
        "csrc": crawl_csrc,
        "rss": crawl_rss
    }
    
    try:
        if crawler_type == "all":
            # 执行所有爬虫任务
            task_results = {}
            for name, task_func in task_map.items():
                result = task_func.delay()
                task_results[name] = {
                    "task_id": result.id,
                    "status": "submitted"
                }
            
            return {
                "message": "所有爬虫任务已提交",
                "tasks": task_results
            }
        
        elif crawler_type in task_map:
            # 执行指定爬虫任务
            task_func = task_map[crawler_type]
            result = task_func.delay()
            
            return {
                "message": f"{crawler_type.upper()}爬虫任务已提交",
                "task_id": result.id,
                "status": "submitted"
            }
        
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的爬虫类型: {crawler_type}。支持的类型: sse, szse, csrc, rss, all"
            )
            
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"执行爬虫任务失败: {str(e)}"
        )

@router.post("/process/news", summary="手动执行新闻处理任务")
async def run_news_processing(
    current_user: User = Depends(get_current_user)
):
    """
    手动触发新闻批量处理任务
    """
    if current_user.role not in ["PRO", "ENTERPRISE"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有PRO和ENTERPRISE用户可以手动执行处理任务"
        )
    
    try:
        result = process_news_batch.delay()
        
        return {
            "message": "新闻处理任务已提交",
            "task_id": result.id,
            "status": "submitted"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"执行新闻处理任务失败: {str(e)}"
        )

@router.post("/cleanup", summary="清理旧数据")
async def cleanup_old_data(
    days: int = 30,
    current_user: User = Depends(get_current_user)
):
    """
    清理指定天数之前的旧数据
    
    Args:
        days: 保留天数，默认30天
    """
    if current_user.role != "ENTERPRISE":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有ENTERPRISE用户可以执行数据清理"
        )
    
    if days < 7:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="保留天数不能少于7天"
        )
    
    try:
        result = cleanup_old_news.delay(days)
        
        return {
            "message": f"数据清理任务已提交，将清理{days}天前的数据",
            "task_id": result.id,
            "status": "submitted"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"执行数据清理任务失败: {str(e)}"
        )

@router.get("/health", summary="爬虫健康检查")
async def check_crawler_health(
    current_user: User = Depends(get_current_user)
):
    """
    检查所有爬虫的健康状态
    """
    try:
        result = health_check_crawlers.delay()
        
        # 等待任务完成（最多30秒）
        task_result = result.get(timeout=30)
        
        return task_result
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"爬虫健康检查失败: {str(e)}"
        )

@router.get("/result/{task_id}", summary="获取任务结果")
async def get_task_result(
    task_id: str,
    current_user: User = Depends(get_current_user)
):
    """
    获取指定任务的执行结果
    
    Args:
        task_id: 任务ID
    """
    try:
        result = celery_app.AsyncResult(task_id)
        
        return {
            "task_id": task_id,
            "status": result.status,
            "result": result.result,
            "traceback": result.traceback,
            "date_done": result.date_done.isoformat() if result.date_done else None
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取任务结果失败: {str(e)}"
        )

@router.delete("/cancel/{task_id}", summary="取消任务")
async def cancel_task(
    task_id: str,
    current_user: User = Depends(get_current_user)
):
    """
    取消指定的任务
    
    Args:
        task_id: 任务ID
    """
    if current_user.role not in ["PRO", "ENTERPRISE"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有PRO和ENTERPRISE用户可以取消任务"
        )
    
    try:
        celery_app.control.revoke(task_id, terminate=True)
        
        return {
            "message": f"任务 {task_id} 已取消",
            "task_id": task_id
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"取消任务失败: {str(e)}"
        )
