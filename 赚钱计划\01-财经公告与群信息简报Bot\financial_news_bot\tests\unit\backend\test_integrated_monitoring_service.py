"""
集成监控服务测试
测试集成监控服务的核心功能
"""
import pytest
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, timedelta
import json

from app.services.integrated_monitoring_service import (
    IntegratedMonitoringService,
    SecurityMonitor,
    ComplianceMonitor,
    PerformanceMonitor,
    SystemHealthMonitor,
    monitoring_service
)

class TestSecurityMonitor:
    """测试安全监控器"""
    
    @pytest.fixture
    def security_monitor(self):
        return SecurityMonitor()
    
    @pytest.mark.asyncio
    async def test_check_failed_logins(self, security_monitor):
        """测试检查失败登录"""
        # 模拟数据库查询
        with patch('app.services.integrated_monitoring_service.get_db') as mock_get_db:
            mock_db = Mock()
            mock_get_db.return_value.__enter__.return_value = mock_db
            
            # 模拟查询结果
            mock_db.query.return_value.filter.return_value.count.return_value = 3
            
            result = await security_monitor.check_failed_logins()
            
            assert result["check_type"] == "failed_logins"
            assert result["status"] in ["normal", "warning", "critical"]
            assert "failed_count" in result
            assert "threshold" in result
    
    @pytest.mark.asyncio
    async def test_check_suspicious_activities(self, security_monitor):
        """测试检查可疑活动"""
        result = await security_monitor.check_suspicious_activities()
        
        assert result["check_type"] == "suspicious_activities"
        assert result["status"] in ["normal", "warning", "critical"]
        assert "activities" in result
        assert isinstance(result["activities"], list)
    
    @pytest.mark.asyncio
    async def test_check_api_security(self, security_monitor):
        """测试检查API安全"""
        result = await security_monitor.check_api_security()
        
        assert result["check_type"] == "api_security"
        assert result["status"] in ["normal", "warning", "critical"]
        assert "issues" in result
        assert isinstance(result["issues"], list)
    
    @pytest.mark.asyncio
    async def test_run_security_checks(self, security_monitor):
        """测试运行安全检查"""
        results = await security_monitor.run_security_checks()
        
        assert len(results) == 3  # 三个检查项
        assert all("check_type" in result for result in results)
        assert all("status" in result for result in results)
        assert all("timestamp" in result for result in results)

class TestComplianceMonitor:
    """测试合规监控器"""
    
    @pytest.fixture
    def compliance_monitor(self):
        return ComplianceMonitor()
    
    @pytest.mark.asyncio
    async def test_check_data_retention(self, compliance_monitor):
        """测试检查数据保留"""
        with patch('app.services.integrated_monitoring_service.get_db') as mock_get_db:
            mock_db = Mock()
            mock_get_db.return_value.__enter__.return_value = mock_db
            
            # 模拟查询结果
            mock_db.query.return_value.filter.return_value.count.return_value = 5
            
            result = await compliance_monitor.check_data_retention()
            
            assert result["check_type"] == "data_retention"
            assert result["status"] in ["normal", "warning", "critical"]
            assert "expired_count" in result
    
    @pytest.mark.asyncio
    async def test_check_privacy_compliance(self, compliance_monitor):
        """测试检查隐私合规"""
        result = await compliance_monitor.check_privacy_compliance()
        
        assert result["check_type"] == "privacy_compliance"
        assert result["status"] in ["normal", "warning", "critical"]
        assert "issues" in result
        assert isinstance(result["issues"], list)
    
    @pytest.mark.asyncio
    async def test_check_content_compliance(self, compliance_monitor):
        """测试检查内容合规"""
        with patch('app.services.integrated_monitoring_service.get_db') as mock_get_db:
            mock_db = Mock()
            mock_get_db.return_value.__enter__.return_value = mock_db
            
            # 模拟查询结果
            mock_db.query.return_value.filter.return_value.count.return_value = 2
            
            result = await compliance_monitor.check_content_compliance()
            
            assert result["check_type"] == "content_compliance"
            assert result["status"] in ["normal", "warning", "critical"]
            assert "flagged_count" in result
    
    @pytest.mark.asyncio
    async def test_run_compliance_checks(self, compliance_monitor):
        """测试运行合规检查"""
        results = await compliance_monitor.run_compliance_checks()
        
        assert len(results) == 3  # 三个检查项
        assert all("check_type" in result for result in results)
        assert all("status" in result for result in results)

class TestPerformanceMonitor:
    """测试性能监控器"""
    
    @pytest.fixture
    def performance_monitor(self):
        return PerformanceMonitor()
    
    @pytest.mark.asyncio
    async def test_check_database_performance(self, performance_monitor):
        """测试检查数据库性能"""
        with patch('app.services.integrated_monitoring_service.get_db') as mock_get_db:
            mock_db = Mock()
            mock_get_db.return_value.__enter__.return_value = mock_db
            
            # 模拟执行时间测量
            with patch('time.time', side_effect=[0, 0.1]):  # 100ms查询时间
                result = await performance_monitor.check_database_performance()
            
            assert result["check_type"] == "database_performance"
            assert result["status"] in ["normal", "warning", "critical"]
            assert "query_time" in result
            assert "connection_count" in result
    
    @pytest.mark.asyncio
    async def test_check_api_performance(self, performance_monitor):
        """测试检查API性能"""
        # 模拟缓存数据
        mock_cache_data = {
            "avg_response_time": 150,
            "request_count": 1000,
            "error_rate": 0.02
        }
        
        with patch('app.services.integrated_monitoring_service.cache_service') as mock_cache:
            mock_cache.get = AsyncMock(return_value=json.dumps(mock_cache_data))
            
            result = await performance_monitor.check_api_performance()
            
            assert result["check_type"] == "api_performance"
            assert result["status"] in ["normal", "warning", "critical"]
            assert "avg_response_time" in result
            assert "request_count" in result
            assert "error_rate" in result
    
    @pytest.mark.asyncio
    async def test_check_system_resources(self, performance_monitor):
        """测试检查系统资源"""
        with patch('psutil.cpu_percent', return_value=45.0), \
             patch('psutil.virtual_memory') as mock_memory, \
             patch('psutil.disk_usage') as mock_disk:
            
            # 模拟内存使用情况
            mock_memory.return_value.percent = 60.0
            
            # 模拟磁盘使用情况
            mock_disk.return_value.percent = 70.0
            
            result = await performance_monitor.check_system_resources()
            
            assert result["check_type"] == "system_resources"
            assert result["status"] in ["normal", "warning", "critical"]
            assert "cpu_usage" in result
            assert "memory_usage" in result
            assert "disk_usage" in result
    
    @pytest.mark.asyncio
    async def test_run_performance_checks(self, performance_monitor):
        """测试运行性能检查"""
        with patch('psutil.cpu_percent', return_value=45.0), \
             patch('psutil.virtual_memory') as mock_memory, \
             patch('psutil.disk_usage') as mock_disk:
            
            mock_memory.return_value.percent = 60.0
            mock_disk.return_value.percent = 70.0
            
            results = await performance_monitor.run_performance_checks()
            
            assert len(results) == 3  # 三个检查项
            assert all("check_type" in result for result in results)
            assert all("status" in result for result in results)

class TestSystemHealthMonitor:
    """测试系统健康监控器"""
    
    @pytest.fixture
    def health_monitor(self):
        return SystemHealthMonitor()
    
    @pytest.mark.asyncio
    async def test_check_service_health(self, health_monitor):
        """测试检查服务健康"""
        result = await health_monitor.check_service_health()
        
        assert result["check_type"] == "service_health"
        assert result["status"] in ["normal", "warning", "critical"]
        assert "services" in result
        assert isinstance(result["services"], dict)
    
    @pytest.mark.asyncio
    async def test_check_external_dependencies(self, health_monitor):
        """测试检查外部依赖"""
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_client.return_value.__aenter__.return_value.get = AsyncMock(return_value=mock_response)
            
            result = await health_monitor.check_external_dependencies()
            
            assert result["check_type"] == "external_dependencies"
            assert result["status"] in ["normal", "warning", "critical"]
            assert "dependencies" in result
    
    @pytest.mark.asyncio
    async def test_check_background_tasks(self, health_monitor):
        """测试检查后台任务"""
        result = await health_monitor.check_background_tasks()
        
        assert result["check_type"] == "background_tasks"
        assert result["status"] in ["normal", "warning", "critical"]
        assert "tasks" in result
        assert isinstance(result["tasks"], dict)
    
    @pytest.mark.asyncio
    async def test_run_health_checks(self, health_monitor):
        """测试运行健康检查"""
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_client.return_value.__aenter__.return_value.get = AsyncMock(return_value=mock_response)
            
            results = await health_monitor.run_health_checks()
            
            assert len(results) == 3  # 三个检查项
            assert all("check_type" in result for result in results)
            assert all("status" in result for result in results)

class TestIntegratedMonitoringService:
    """测试集成监控服务"""
    
    @pytest.fixture
    def monitoring_service_instance(self):
        return IntegratedMonitoringService()
    
    @pytest.mark.asyncio
    async def test_run_all_checks(self, monitoring_service_instance):
        """测试运行所有检查"""
        with patch('psutil.cpu_percent', return_value=45.0), \
             patch('psutil.virtual_memory') as mock_memory, \
             patch('psutil.disk_usage') as mock_disk, \
             patch('httpx.AsyncClient') as mock_client:
            
            mock_memory.return_value.percent = 60.0
            mock_disk.return_value.percent = 70.0
            
            mock_response = Mock()
            mock_response.status_code = 200
            mock_client.return_value.__aenter__.return_value.get = AsyncMock(return_value=mock_response)
            
            results = await monitoring_service_instance.run_all_checks()
            
            assert "security" in results
            assert "compliance" in results
            assert "performance" in results
            assert "health" in results
            
            # 验证每个类别都有结果
            assert len(results["security"]) > 0
            assert len(results["compliance"]) > 0
            assert len(results["performance"]) > 0
            assert len(results["health"]) > 0
    
    @pytest.mark.asyncio
    async def test_get_system_status(self, monitoring_service_instance):
        """测试获取系统状态"""
        with patch.object(monitoring_service_instance, 'run_all_checks') as mock_run_checks:
            mock_run_checks.return_value = {
                "security": [{"status": "normal"}, {"status": "warning"}],
                "compliance": [{"status": "normal"}],
                "performance": [{"status": "critical"}],
                "health": [{"status": "normal"}]
            }
            
            status = await monitoring_service_instance.get_system_status()
            
            assert status["overall_status"] == "critical"  # 因为有critical状态
            assert status["total_checks"] == 4
            assert status["normal_count"] == 3
            assert status["warning_count"] == 1
            assert status["critical_count"] == 1
    
    @pytest.mark.asyncio
    async def test_get_alerts(self, monitoring_service_instance):
        """测试获取告警"""
        with patch.object(monitoring_service_instance, 'run_all_checks') as mock_run_checks:
            mock_run_checks.return_value = {
                "security": [
                    {"status": "warning", "check_type": "failed_logins", "message": "登录失败次数过多"},
                    {"status": "normal", "check_type": "api_security"}
                ],
                "performance": [
                    {"status": "critical", "check_type": "database_performance", "message": "数据库响应缓慢"}
                ]
            }
            
            alerts = await monitoring_service_instance.get_alerts()
            
            assert len(alerts) == 2  # 一个warning，一个critical
            assert any(alert["severity"] == "warning" for alert in alerts)
            assert any(alert["severity"] == "critical" for alert in alerts)
            assert all("message" in alert for alert in alerts)
            assert all("timestamp" in alert for alert in alerts)
    
    def test_monitoring_service_initialization(self, monitoring_service_instance):
        """测试监控服务初始化"""
        assert monitoring_service_instance.security_monitor is not None
        assert monitoring_service_instance.compliance_monitor is not None
        assert monitoring_service_instance.performance_monitor is not None
        assert monitoring_service_instance.health_monitor is not None
        
        assert isinstance(monitoring_service_instance.security_monitor, SecurityMonitor)
        assert isinstance(monitoring_service_instance.compliance_monitor, ComplianceMonitor)
        assert isinstance(monitoring_service_instance.performance_monitor, PerformanceMonitor)
        assert isinstance(monitoring_service_instance.health_monitor, SystemHealthMonitor)

class TestGlobalMonitoringService:
    """测试全局监控服务"""
    
    def test_global_monitoring_service_exists(self):
        """测试全局监控服务存在"""
        assert monitoring_service is not None
        assert isinstance(monitoring_service, IntegratedMonitoringService)
    
    @pytest.mark.asyncio
    async def test_global_monitoring_service_functionality(self):
        """测试全局监控服务功能"""
        with patch('psutil.cpu_percent', return_value=45.0), \
             patch('psutil.virtual_memory') as mock_memory, \
             patch('psutil.disk_usage') as mock_disk:
            
            mock_memory.return_value.percent = 60.0
            mock_disk.return_value.percent = 70.0
            
            # 测试基本功能可用性
            status = await monitoring_service.get_system_status()
            
            assert "overall_status" in status
            assert "total_checks" in status
            assert "timestamp" in status

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
