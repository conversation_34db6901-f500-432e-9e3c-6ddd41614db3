"""
GLM-4.5 Flash AI服务模块
提供文本摘要、内容分类、情感分析等AI功能

TODO: 此服务已实现但未在路由中使用，被content_aggregator和content_processor引用
      考虑在新闻处理、内容分析等功能中集成此服务
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

import zai
from zai import ZhipuAiClient

from app.config import settings

logger = logging.getLogger(__name__)


class AIService:
    """AI服务类（兼容GLM）"""
    pass


class GLMService:
    """GLM-4.5 Flash AI服务类"""
    
    def __init__(self):
        """初始化GLM服务"""
        if not settings.GLM_API_KEY:
            raise ValueError("GLM_API_KEY is required")
        
        self.client = ZhipuAiClient(api_key=settings.GLM_API_KEY)
        self.model = settings.GLM_MODEL
        self.max_tokens = settings.GLM_MAX_TOKENS
        self.temperature = settings.GLM_TEMPERATURE
        self.timeout = settings.GLM_TIMEOUT
        self.max_retries = settings.GLM_MAX_RETRIES
        
        logger.info(f"GLM服务初始化完成，模型: {self.model}")
    
    async def _call_api(self, messages: List[Dict[str, str]], **kwargs) -> Optional[str]:
        """
        调用GLM API的基础方法
        
        Args:
            messages: 对话消息列表
            **kwargs: 其他API参数
            
        Returns:
            API响应内容
        """
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=kwargs.get('max_tokens', self.max_tokens),
                temperature=kwargs.get('temperature', self.temperature),
                thinking={
                    "type": kwargs.get('thinking_type', 'enabled')
                },
                stream=False
            )
            
            if response.choices and len(response.choices) > 0:
                return response.choices[0].message.content
            else:
                logger.warning("GLM API返回空响应")
                return None
                
        except Exception as e:
            logger.error(f"GLM API调用失败: {str(e)}")
            return None
    
    async def generate_summary(self, content: str, max_length: int = None) -> Optional[str]:
        """
        生成文本摘要
        
        Args:
            content: 原始文本内容
            max_length: 摘要最大长度
            
        Returns:
            生成的摘要文本
        """
        if not content or len(content.strip()) < 50:
            logger.warning("内容过短，无法生成摘要")
            return None
        
        max_length = max_length or settings.CONTENT_SUMMARY_MAX_LENGTH
        
        prompt = f"""
请为以下财经新闻内容生成一个简洁的摘要，要求：
1. 摘要长度控制在{settings.CONTENT_SUMMARY_MIN_LENGTH}-{max_length}字之间
2. 包含主体、事件、影响、时间四要素
3. 语言简洁明了，突出重点信息
4. 保持客观中性的语调

原文内容：
{content}

请生成摘要：
"""
        
        messages = [
            {"role": "user", "content": prompt}
        ]
        
        return await self._call_api(messages, temperature=0.3)
    
    async def classify_content(self, title: str, content: str) -> Optional[Dict[str, Any]]:
        """
        内容分类
        
        Args:
            title: 文章标题
            content: 文章内容
            
        Returns:
            分类结果，包含类别和置信度
        """
        categories = settings.CONTENT_CLASSIFICATION_CATEGORIES
        categories_str = "、".join(categories)
        
        prompt = f"""
请对以下财经新闻进行分类，从以下类别中选择最合适的一个：
{categories_str}

分类标准：
- 政策监管：涉及政府政策、监管规定、法规变化等
- 公司公告：上市公司发布的各类公告信息
- 市场动态：股市行情、交易数据、市场趋势等
- 行业资讯：特定行业的发展动态、技术进展等
- 风险提示：风险警告、业绩预警、违规处罚等

标题：{title}
内容：{content[:500]}...

请返回JSON格式的结果，包含以下字段：
- category: 分类结果（必须是上述类别之一）
- confidence: 置信度（0-1之间的数值）
- reason: 分类理由（简短说明）

示例格式：
{{"category": "公司公告", "confidence": 0.95, "reason": "内容涉及上市公司发布的业绩公告"}}
"""
        
        messages = [
            {"role": "user", "content": prompt}
        ]
        
        response = await self._call_api(messages, temperature=0.1)
        
        if response:
            try:
                import json
                import re

                # 尝试提取JSON部分
                response_clean = response.strip()

                # 如果响应包含```json标记，提取其中的JSON
                json_match = re.search(r'```json\s*(.*?)\s*```', response_clean, re.DOTALL)
                if json_match:
                    response_clean = json_match.group(1).strip()

                # 如果响应包含```标记，提取其中的内容
                elif '```' in response_clean:
                    parts = response_clean.split('```')
                    if len(parts) >= 3:
                        response_clean = parts[1].strip()

                # 尝试找到JSON对象
                json_match = re.search(r'\{.*\}', response_clean, re.DOTALL)
                if json_match:
                    response_clean = json_match.group(0)

                result = json.loads(response_clean)

                # 验证分类结果
                if result.get('category') in categories:
                    return result
                else:
                    logger.warning(f"分类结果不在预定义类别中: {result.get('category')}")
                    return None

            except json.JSONDecodeError as e:
                logger.error(f"分类结果JSON解析失败: {str(e)}")
                logger.error(f"原始响应: {response}")
                return None
        
        return None
    
    async def extract_keywords(self, title: str, content: str) -> Optional[List[Dict[str, Any]]]:
        """
        提取关键词
        
        Args:
            title: 文章标题
            content: 文章内容
            
        Returns:
            关键词列表，包含词汇和权重
        """
        max_keywords = settings.CONTENT_KEYWORDS_MAX_COUNT
        
        prompt = f"""
请从以下财经新闻中提取关键词，要求：
1. 提取最多{max_keywords}个关键词
2. 优先提取公司名称、股票代码、金额数字、时间信息
3. 按重要性排序
4. 每个关键词包含权重评分（0-1）

标题：{title}
内容：{content[:800]}...

请返回JSON格式的结果，格式如下：
[
    {{"keyword": "关键词", "weight": 0.95, "type": "company"}},
    {{"keyword": "关键词", "weight": 0.85, "type": "amount"}},
    ...
]

关键词类型包括：company（公司）、stock_code（股票代码）、amount（金额）、time（时间）、event（事件）、other（其他）
"""
        
        messages = [
            {"role": "user", "content": prompt}
        ]
        
        response = await self._call_api(messages, temperature=0.2)
        
        if response:
            try:
                import json
                import re

                # 尝试提取JSON部分
                response_clean = response.strip()

                # 如果响应包含```json标记，提取其中的JSON
                json_match = re.search(r'```json\s*(.*?)\s*```', response_clean, re.DOTALL)
                if json_match:
                    response_clean = json_match.group(1).strip()

                # 如果响应包含```标记，提取其中的内容
                elif '```' in response_clean:
                    parts = response_clean.split('```')
                    if len(parts) >= 3:
                        response_clean = parts[1].strip()

                # 尝试找到JSON数组
                json_match = re.search(r'\[.*\]', response_clean, re.DOTALL)
                if json_match:
                    response_clean = json_match.group(0)

                keywords = json.loads(response_clean)

                if isinstance(keywords, list):
                    return keywords[:max_keywords]
                else:
                    logger.warning("关键词提取结果格式错误")
                    return None

            except json.JSONDecodeError as e:
                logger.error(f"关键词提取结果JSON解析失败: {str(e)}")
                logger.error(f"原始响应: {response}")
                return None
        
        return None
    
    async def check_content_safety(self, content: str) -> Optional[Dict[str, Any]]:
        """
        内容安全检测

        Args:
            content: 待检测的内容

        Returns:
            安全检测结果
        """
        try:
            # 使用智谱AI官方内容安全API
            response = self.client.moderations.create(
                input=content
            )

            if response.results and len(response.results) > 0:
                result = response.results[0]
                return {
                    "flagged": result.flagged,
                    "categories": result.categories,
                    "category_scores": result.category_scores
                }
            else:
                logger.warning("内容安全检测返回空结果")
                return None

        except Exception as e:
            logger.error(f"内容安全检测失败: {str(e)}")
            return None

    async def search_web(self, query: str) -> Optional[Dict[str, Any]]:
        """
        网络搜索

        Args:
            query: 搜索查询

        Returns:
            搜索结果
        """
        try:
            # 使用智谱AI的网络搜索工具
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "user", "content": f"请搜索并总结关于'{query}'的最新信息"}
                ],
                tools=[
                    {
                        'type': 'web_search',
                        'web_search': {
                            'search_query': query,
                            'search_result': True,
                        },
                    }
                ],
                temperature=0.3,
                max_tokens=2000,
            )

            if response.choices and len(response.choices) > 0:
                return {
                    "content": response.choices[0].message.content,
                    "search_query": query
                }
            else:
                logger.warning("网络搜索返回空结果")
                return None

        except Exception as e:
            logger.error(f"网络搜索失败: {str(e)}")
            return None

    async def analyze_sentiment(self, title: str, content: str) -> Optional[Dict[str, Any]]:
        """
        情感分析
        
        Args:
            title: 文章标题
            content: 文章内容
            
        Returns:
            情感分析结果
        """
        prompt = f"""
请对以下财经新闻进行情感分析，判断其情绪倾向和风险等级：

标题：{title}
内容：{content[:600]}...

请返回JSON格式的结果，包含以下字段：
- sentiment: 情感倾向（positive/negative/neutral）
- sentiment_score: 情感得分（-1到1之间，-1最负面，1最正面）
- risk_level: 风险等级（low/medium/high）
- risk_keywords: 风险关键词列表
- analysis: 分析说明

示例格式：
{{
    "sentiment": "negative",
    "sentiment_score": -0.6,
    "risk_level": "high",
    "risk_keywords": ["业绩下滑", "重大风险"],
    "analysis": "文章涉及公司业绩大幅下滑，存在较高投资风险"
}}
"""
        
        messages = [
            {"role": "user", "content": prompt}
        ]
        
        response = await self._call_api(messages, temperature=0.1)
        
        if response:
            try:
                import json
                import re

                # 尝试提取JSON部分
                response_clean = response.strip()

                # 如果响应包含```json标记，提取其中的JSON
                json_match = re.search(r'```json\s*(.*?)\s*```', response_clean, re.DOTALL)
                if json_match:
                    response_clean = json_match.group(1).strip()

                # 如果响应包含```标记，提取其中的内容
                elif '```' in response_clean:
                    parts = response_clean.split('```')
                    if len(parts) >= 3:
                        response_clean = parts[1].strip()

                # 尝试找到JSON对象
                json_match = re.search(r'\{.*\}', response_clean, re.DOTALL)
                if json_match:
                    response_clean = json_match.group(0)

                result = json.loads(response_clean)
                return result

            except json.JSONDecodeError as e:
                logger.error(f"情感分析结果JSON解析失败: {str(e)}")
                logger.error(f"原始响应: {response}")
                return None
        
        return None

    async def generate_section_content(
        self,
        prompt: str,
        max_length: int = 500
    ) -> str:
        """
        生成简报章节内容

        Args:
            prompt: 生成提示
            max_length: 最大长度

        Returns:
            生成的章节内容
        """
        try:
            messages = [
                {
                    "role": "system",
                    "content": "你是一个专业的财经简报编辑，擅长将新闻信息整理成简洁、专业、易读的简报内容。"
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ]

            response = await self._call_api(
                messages=messages,
                max_tokens=min(max_length * 2, self.max_tokens),
                temperature=0.3
            )

            if response:
                # 确保内容长度不超过限制
                if len(response) > max_length:
                    response = response[:max_length] + "..."

                return response.strip()
            else:
                return "内容生成失败，请稍后重试。"

        except Exception as e:
            logger.error(f"生成章节内容失败: {e}")
            return "内容生成失败，请稍后重试。"

    async def generate_text(
        self,
        prompt: str,
        max_length: int = 200,
        temperature: float = 0.5
    ) -> str:
        """
        生成通用文本内容

        Args:
            prompt: 生成提示
            max_length: 最大长度
            temperature: 温度参数

        Returns:
            生成的文本内容
        """
        try:
            messages = [
                {
                    "role": "user",
                    "content": prompt
                }
            ]

            response = await self._call_api(
                messages=messages,
                max_tokens=min(max_length * 2, self.max_tokens),
                temperature=temperature
            )

            if response:
                if len(response) > max_length:
                    response = response[:max_length] + "..."
                return response.strip()
            else:
                return ""

        except Exception as e:
            logger.error(f"生成文本失败: {e}")
            return ""


# AIService别名，用于简报生成
AIService = GLMService


# 全局GLM服务实例
glm_service = None

def get_glm_service() -> GLMService:
    """获取GLM服务实例"""
    global glm_service
    if glm_service is None:
        glm_service = GLMService()
    return glm_service
