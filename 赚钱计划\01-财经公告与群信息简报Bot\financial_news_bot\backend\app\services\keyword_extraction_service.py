"""
关键词提取服务
统一处理所有关键词提取需求，解决服务层功能重叠问题
"""
import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import math
from collections import Counter

from ..utils.text_processor import get_text_processor

logger = logging.getLogger(__name__)


@dataclass
class KeywordResult:
    """关键词提取结果"""
    word: str
    score: float
    frequency: int
    position_weight: float
    category: Optional[str] = None


class KeywordExtractionService:
    """统一关键词提取服务"""
    
    def __init__(self):
        self.text_processor = get_text_processor()
        self._financial_keywords = self._load_financial_keywords()
    
    def _load_financial_keywords(self) -> Dict[str, float]:
        """加载金融领域关键词及其权重"""
        return {
            # 股票相关
            '股票': 2.0, '股市': 2.0, '股价': 1.8, '涨停': 1.8, '跌停': 1.8,
            '牛市': 1.5, '熊市': 1.5, '大盘': 1.5, '个股': 1.3, '板块': 1.3,
            
            # 基金相关
            '基金': 2.0, '净值': 1.8, '申购': 1.5, '赎回': 1.5, '分红': 1.5,
            '基金经理': 1.3, '投资组合': 1.3,
            
            # 债券相关
            '债券': 2.0, '国债': 1.8, '企业债': 1.8, '收益率': 1.8, '票面利率': 1.5,
            '信用评级': 1.5, '违约': 1.8,
            
            # 货币政策
            '央行': 2.0, '货币政策': 2.0, '利率': 2.0, '降准': 1.8, '降息': 1.8,
            '流动性': 1.5, '通胀': 1.5, '通缩': 1.5,
            
            # 经济指标
            'GDP': 2.0, 'CPI': 2.0, 'PMI': 1.8, '失业率': 1.5, '贸易顺差': 1.5,
            '外汇储备': 1.5, '汇率': 1.8,
            
            # 公司财务
            '营收': 1.8, '净利润': 1.8, '毛利率': 1.5, '净资产': 1.5, '负债率': 1.5,
            '现金流': 1.5, '分红': 1.5, '重组': 1.8, '并购': 1.8,
            
            # 监管政策
            '证监会': 1.8, '银保监会': 1.8, '监管': 1.5, '政策': 1.3, '法规': 1.3,
            '合规': 1.3, '风控': 1.5
        }
    
    def extract_keywords_basic(self, text: str, max_keywords: int = 10) -> List[KeywordResult]:
        """
        基础关键词提取（使用统一文本处理器）
        
        Args:
            text: 输入文本
            max_keywords: 最大关键词数量
            
        Returns:
            关键词结果列表
        """
        try:
            # 使用统一的文本处理器
            keywords = self.text_processor.extract_keywords(text, max_keywords)
            
            # 转换为KeywordResult格式
            results = []
            for i, keyword in enumerate(keywords):
                results.append(KeywordResult(
                    word=keyword,
                    score=1.0 - (i * 0.1),  # 简单的分数递减
                    frequency=1,  # 基础版本不计算频率
                    position_weight=1.0
                ))
            
            return results
            
        except Exception as e:
            logger.error(f"基础关键词提取失败: {str(e)}")
            return []
    
    def extract_keywords_advanced(
        self, 
        title: str, 
        content: str, 
        max_keywords: int = 10
    ) -> List[KeywordResult]:
        """
        高级关键词提取（TF-IDF + 位置权重 + 领域权重）
        
        Args:
            title: 标题
            content: 内容
            max_keywords: 最大关键词数量
            
        Returns:
            关键词结果列表
        """
        try:
            # 合并标题和内容，标题权重更高
            full_text = f"{title} {title} {content}"  # 标题重复两次增加权重
            
            # 分词
            words = self.text_processor.segment_text(full_text)
            
            # 过滤停用词和短词
            filtered_words = [
                word for word in words 
                if len(word) > 1 and word not in self.text_processor.stopwords
            ]
            
            if not filtered_words:
                return []
            
            # 计算词频
            word_freq = Counter(filtered_words)
            total_words = len(filtered_words)
            
            # 计算TF-IDF和综合分数
            results = []
            for word, freq in word_freq.items():
                # TF (词频)
                tf = freq / total_words
                
                # 位置权重（标题中的词权重更高）
                position_weight = 2.0 if word in title else 1.0
                
                # 领域权重
                domain_weight = self._financial_keywords.get(word, 1.0)
                
                # 综合分数
                score = tf * position_weight * domain_weight
                
                results.append(KeywordResult(
                    word=word,
                    score=score,
                    frequency=freq,
                    position_weight=position_weight,
                    category=self._get_keyword_category(word)
                ))
            
            # 按分数排序并返回前N个
            results.sort(key=lambda x: x.score, reverse=True)
            return results[:max_keywords]
            
        except Exception as e:
            logger.error(f"高级关键词提取失败: {str(e)}")
            return self.extract_keywords_basic(f"{title} {content}", max_keywords)
    
    def extract_keywords_for_search(self, text: str, max_keywords: int = 20) -> List[str]:
        """
        为搜索优化的关键词提取
        
        Args:
            text: 输入文本
            max_keywords: 最大关键词数量
            
        Returns:
            关键词字符串列表
        """
        try:
            results = self.extract_keywords_advanced("", text, max_keywords)
            return [result.word for result in results]
        except Exception as e:
            logger.error(f"搜索关键词提取失败: {str(e)}")
            return []
    
    def extract_keywords_for_recommendation(
        self, 
        title: str, 
        content: str, 
        max_keywords: int = 15
    ) -> Dict[str, float]:
        """
        为推荐系统优化的关键词提取
        
        Args:
            title: 标题
            content: 内容
            max_keywords: 最大关键词数量
            
        Returns:
            关键词及其权重的字典
        """
        try:
            results = self.extract_keywords_advanced(title, content, max_keywords)
            return {result.word: result.score for result in results}
        except Exception as e:
            logger.error(f"推荐关键词提取失败: {str(e)}")
            return {}
    
    def _get_keyword_category(self, word: str) -> Optional[str]:
        """获取关键词所属类别"""
        category_mapping = {
            '股票': ['股票', '股市', '股价', '涨停', '跌停', '牛市', '熊市', '大盘', '个股', '板块'],
            '基金': ['基金', '净值', '申购', '赎回', '分红', '基金经理', '投资组合'],
            '债券': ['债券', '国债', '企业债', '收益率', '票面利率', '信用评级', '违约'],
            '货币政策': ['央行', '货币政策', '利率', '降准', '降息', '流动性', '通胀', '通缩'],
            '经济指标': ['GDP', 'CPI', 'PMI', '失业率', '贸易顺差', '外汇储备', '汇率'],
            '公司财务': ['营收', '净利润', '毛利率', '净资产', '负债率', '现金流', '分红', '重组', '并购'],
            '监管政策': ['证监会', '银保监会', '监管', '政策', '法规', '合规', '风控']
        }
        
        for category, keywords in category_mapping.items():
            if word in keywords:
                return category
        return None
    
    def get_keyword_statistics(self, keywords: List[KeywordResult]) -> Dict[str, Any]:
        """获取关键词统计信息"""
        if not keywords:
            return {}
        
        categories = {}
        total_score = sum(kw.score for kw in keywords)
        
        for keyword in keywords:
            if keyword.category:
                if keyword.category not in categories:
                    categories[keyword.category] = {'count': 0, 'score': 0.0}
                categories[keyword.category]['count'] += 1
                categories[keyword.category]['score'] += keyword.score
        
        return {
            'total_keywords': len(keywords),
            'total_score': total_score,
            'average_score': total_score / len(keywords),
            'categories': categories,
            'top_keyword': keywords[0].word if keywords else None,
            'financial_keywords_count': sum(1 for kw in keywords if kw.category)
        }


# 全局实例
keyword_service = KeywordExtractionService()


def get_keyword_service() -> KeywordExtractionService:
    """获取关键词提取服务实例"""
    return keyword_service
