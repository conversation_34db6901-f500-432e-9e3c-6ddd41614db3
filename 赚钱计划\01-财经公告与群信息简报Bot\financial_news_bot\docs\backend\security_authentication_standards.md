# 身份认证和授权机制

## 认证机制

### 1. JWT Token认证
```python
# JWT配置 - 从环境变量获取，确保安全性
JWT_SECRET_KEY = os.getenv("SECRET_KEY")  # 从环境变量获取
JWT_ALGORITHM = "HS256"
JWT_ACCESS_TOKEN_EXPIRE_MINUTES = 30
JWT_REFRESH_TOKEN_EXPIRE_DAYS = 7

# Token结构
{
    "sub": "user_id",
    "username": "user_name", 
    "role": "USER_ROLE",
    "exp": 1640995200,
    "iat": 1640908800,
    "type": "access"  # access 或 refresh
}
```

### 2. 密码安全策略
```python
# 密码要求
- 最小长度：8位
- 必须包含：大写字母、小写字母、数字
- 可选包含：特殊字符
- 密码历史：不能重复最近5次密码
- 密码有效期：90天

# 密码加密
- 使用 bcrypt 算法
- Salt rounds: 12
- 存储格式：$2b$12$hash_value
```

### 3. 多因素认证(MFA)
```python
# TOTP配置
TOTP_ISSUER = "Financial News Bot"
TOTP_ALGORITHM = "SHA1"
TOTP_DIGITS = 6
TOTP_PERIOD = 30

# 备用码
- 生成10个一次性备用码
- 每个备用码只能使用一次
- 备用码有效期：30天
```

## 授权机制

### 1. 基于角色的访问控制(RBAC)
```python
# 角色定义
ROLES = {
    "FREE": {
        "permissions": [
            "news:read",
            "subscription:create:limited",  # 最多3个订阅
            "profile:update"
        ],
        "limits": {
            "subscriptions": 3,
            "api_calls_per_hour": 100
        }
    },
    "PRO": {
        "permissions": [
            "news:read",
            "subscription:create",  # 最多20个订阅
            "subscription:advanced",
            "analytics:basic",
            "profile:update"
        ],
        "limits": {
            "subscriptions": 20,
            "api_calls_per_hour": 1000
        }
    },
    "ENTERPRISE": {
        "permissions": [
            "news:read",
            "subscription:create",  # 无限制
            "subscription:advanced",
            "analytics:advanced",
            "api:access",
            "profile:update"
        ],
        "limits": {
            "subscriptions": -1,  # 无限制
            "api_calls_per_hour": 10000
        }
    },
    "ADMIN": {
        "permissions": [
            "*"  # 所有权限
        ],
        "limits": {}
    }
}
```

### 2. 权限检查装饰器
```python
from functools import wraps
from flask import request, jsonify
from app.utils.auth import verify_token, check_permission

def require_permission(permission):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            token = request.headers.get('Authorization')
            if not token:
                return jsonify({'error': 'Token required'}), 401
            
            user_data = verify_token(token)
            if not user_data:
                return jsonify({'error': 'Invalid token'}), 401
            
            if not check_permission(user_data['role'], permission):
                return jsonify({'error': 'Insufficient permissions'}), 403
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

# 使用示例
@app.route('/api/v1/subscriptions', methods=['POST'])
@require_permission('subscription:create')
def create_subscription():
    pass
```

### 3. API限流机制
```python
# Redis限流配置
RATE_LIMITS = {
    "FREE": "100/hour",
    "PRO": "1000/hour", 
    "ENTERPRISE": "10000/hour",
    "ADMIN": "unlimited"
}

# 限流实现
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

limiter = Limiter(
    app,
    key_func=lambda: get_user_role(),
    default_limits=["1000/hour"]
)

@app.route('/api/v1/news')
@limiter.limit(lambda: RATE_LIMITS.get(get_user_role(), "100/hour"))
def get_news():
    pass
```

## 数据加密和传输安全

### 1. 数据加密标准
```python
# 敏感数据加密
from cryptography.fernet import Fernet

# 字段级加密
ENCRYPTED_FIELDS = [
    'users.email',
    'users.phone',
    'push_logs.error_message'
]

# 加密配置
ENCRYPTION_KEY = Fernet.generate_key()
cipher_suite = Fernet(ENCRYPTION_KEY)

def encrypt_field(value):
    return cipher_suite.encrypt(value.encode()).decode()

def decrypt_field(encrypted_value):
    return cipher_suite.decrypt(encrypted_value.encode()).decode()
```

### 2. HTTPS配置
```nginx
# Nginx SSL配置
server {
    listen 443 ssl http2;
    server_name api.financialnews.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    # HSTS
    add_header Strict-Transport-Security "max-age=63072000" always;
    
    # 其他安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
}
```

### 3. API安全配置
```python
# CORS配置
CORS_ORIGINS = [
    "https://app.financialnews.com",
    "https://admin.financialnews.com"
]

# 安全头配置
SECURITY_HEADERS = {
    'X-Frame-Options': 'DENY',
    'X-Content-Type-Options': 'nosniff',
    'X-XSS-Protection': '1; mode=block',
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
    'Content-Security-Policy': "default-src 'self'"
}
```

## 安全审计和合规检查

### 1. 审计日志
```python
# 审计事件类型
AUDIT_EVENTS = [
    'USER_LOGIN',
    'USER_LOGOUT', 
    'PASSWORD_CHANGE',
    'PERMISSION_CHANGE',
    'DATA_ACCESS',
    'DATA_MODIFICATION',
    'ADMIN_ACTION'
]

# 审计日志结构
{
    "timestamp": "2025-01-15T10:30:00Z",
    "user_id": "12345",
    "username": "john_doe",
    "event_type": "USER_LOGIN",
    "resource": "/api/v1/auth/login",
    "ip_address": "*************",
    "user_agent": "Mozilla/5.0...",
    "result": "SUCCESS",
    "details": {
        "login_method": "password",
        "mfa_used": true
    }
}
```

### 2. 合规检查清单
```yaml
# GDPR合规检查
gdpr_compliance:
  - data_minimization: true
  - consent_management: true
  - right_to_erasure: true
  - data_portability: true
  - privacy_by_design: true

# 金融行业合规
financial_compliance:
  - data_encryption: true
  - access_logging: true
  - incident_response: true
  - regular_audits: true
  - staff_training: true
```

### 3. 安全监控
```python
# 异常行为检测
SECURITY_RULES = [
    {
        "name": "Multiple Failed Logins",
        "condition": "failed_logins > 5 in 10 minutes",
        "action": "lock_account"
    },
    {
        "name": "Unusual API Usage",
        "condition": "api_calls > normal_rate * 10",
        "action": "rate_limit"
    },
    {
        "name": "Privilege Escalation",
        "condition": "role_change without admin approval",
        "action": "alert_admin"
    }
]
```

## 实施计划

### 阶段1：基础认证(1周)
- [ ] JWT Token认证实现
- [ ] 密码安全策略
- [ ] 基础RBAC权限系统

### 阶段2：高级安全(1周)  
- [ ] 多因素认证
- [ ] API限流机制
- [ ] 数据加密实现

### 阶段3：审计合规(1周)
- [ ] 审计日志系统
- [ ] 安全监控告警
- [ ] 合规检查流程

### 阶段4：测试验证(3天)
- [ ] 安全测试
- [ ] 渗透测试
- [ ] 合规验证
