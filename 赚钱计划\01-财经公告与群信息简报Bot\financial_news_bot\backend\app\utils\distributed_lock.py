"""
分布式锁机制
基于Redis实现分布式锁，解决并发访问控制问题
"""
import time
import uuid
import logging
from typing import Optional, Any
from contextlib import contextmanager
import redis
from redis.exceptions import RedisError

from ..services.cache_service import cache_service

logger = logging.getLogger(__name__)


class DistributedLock:
    """Redis分布式锁"""
    
    def __init__(self, redis_client: redis.Redis, key: str, timeout: int = 10, retry_delay: float = 0.1):
        """
        初始化分布式锁
        
        Args:
            redis_client: Redis客户端
            key: 锁的键名
            timeout: 锁超时时间（秒）
            retry_delay: 重试间隔（秒）
        """
        self.redis_client = redis_client
        self.key = f"lock:{key}"
        self.timeout = timeout
        self.retry_delay = retry_delay
        self.identifier = str(uuid.uuid4())
        self.acquired = False
    
    def acquire(self, blocking: bool = True, timeout: Optional[int] = None) -> bool:
        """
        获取锁
        
        Args:
            blocking: 是否阻塞等待
            timeout: 等待超时时间
            
        Returns:
            是否成功获取锁
        """
        if timeout is None:
            timeout = self.timeout
        
        end_time = time.time() + timeout
        
        while True:
            try:
                # 使用SET命令的NX和EX选项实现原子操作
                result = self.redis_client.set(
                    self.key, 
                    self.identifier, 
                    nx=True,  # 只在键不存在时设置
                    ex=self.timeout  # 设置过期时间
                )
                
                if result:
                    self.acquired = True
                    logger.debug(f"成功获取分布式锁: {self.key}")
                    return True
                
                if not blocking:
                    return False
                
                if time.time() >= end_time:
                    logger.warning(f"获取分布式锁超时: {self.key}")
                    return False
                
                time.sleep(self.retry_delay)
                
            except RedisError as e:
                logger.error(f"获取分布式锁失败: {self.key}, 错误: {str(e)}")
                if not blocking:
                    return False
                time.sleep(self.retry_delay)
    
    def release(self) -> bool:
        """
        释放锁
        
        Returns:
            是否成功释放锁
        """
        if not self.acquired:
            return True
        
        try:
            # 使用Lua脚本确保原子性：只有锁的持有者才能释放锁
            lua_script = """
            if redis.call("GET", KEYS[1]) == ARGV[1] then
                return redis.call("DEL", KEYS[1])
            else
                return 0
            end
            """
            
            result = self.redis_client.eval(lua_script, 1, self.key, self.identifier)
            
            if result:
                self.acquired = False
                logger.debug(f"成功释放分布式锁: {self.key}")
                return True
            else:
                logger.warning(f"释放分布式锁失败，可能已被其他进程释放: {self.key}")
                return False
                
        except RedisError as e:
            logger.error(f"释放分布式锁失败: {self.key}, 错误: {str(e)}")
            return False
    
    def extend(self, additional_time: int) -> bool:
        """
        延长锁的持有时间
        
        Args:
            additional_time: 额外的时间（秒）
            
        Returns:
            是否成功延长
        """
        if not self.acquired:
            return False
        
        try:
            # 使用Lua脚本确保原子性
            lua_script = """
            if redis.call("GET", KEYS[1]) == ARGV[1] then
                return redis.call("EXPIRE", KEYS[1], ARGV[2])
            else
                return 0
            end
            """
            
            result = self.redis_client.eval(
                lua_script, 1, self.key, self.identifier, str(self.timeout + additional_time)
            )
            
            if result:
                logger.debug(f"成功延长分布式锁: {self.key}, 延长时间: {additional_time}秒")
                return True
            else:
                logger.warning(f"延长分布式锁失败: {self.key}")
                return False
                
        except RedisError as e:
            logger.error(f"延长分布式锁失败: {self.key}, 错误: {str(e)}")
            return False
    
    def is_locked(self) -> bool:
        """检查锁是否被持有"""
        try:
            value = self.redis_client.get(self.key)
            return value is not None
        except RedisError:
            return False
    
    def __enter__(self):
        """上下文管理器入口"""
        if not self.acquire():
            raise RuntimeError(f"无法获取分布式锁: {self.key}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.release()


class DistributedLockManager:
    """分布式锁管理器"""
    
    def __init__(self):
        self.redis_client = None
        self._initialize_redis()
    
    def _initialize_redis(self):
        """初始化Redis连接"""
        try:
            if cache_service and cache_service.is_available():
                self.redis_client = cache_service.redis_client
            else:
                logger.warning("Redis缓存服务不可用，分布式锁功能将被禁用")
        except Exception as e:
            logger.error(f"初始化Redis连接失败: {str(e)}")
    
    def create_lock(self, key: str, timeout: int = 10, retry_delay: float = 0.1) -> Optional[DistributedLock]:
        """
        创建分布式锁
        
        Args:
            key: 锁的键名
            timeout: 锁超时时间（秒）
            retry_delay: 重试间隔（秒）
            
        Returns:
            分布式锁实例，如果Redis不可用则返回None
        """
        if not self.redis_client:
            logger.warning("Redis不可用，无法创建分布式锁")
            return None
        
        return DistributedLock(self.redis_client, key, timeout, retry_delay)
    
    @contextmanager
    def lock(self, key: str, timeout: int = 10, blocking: bool = True, retry_delay: float = 0.1):
        """
        分布式锁上下文管理器
        
        Args:
            key: 锁的键名
            timeout: 锁超时时间（秒）
            blocking: 是否阻塞等待
            retry_delay: 重试间隔（秒）
        """
        lock = self.create_lock(key, timeout, retry_delay)
        
        if not lock:
            # Redis不可用时，使用本地锁作为降级方案
            logger.warning(f"使用本地锁作为降级方案: {key}")
            import threading
            local_lock = threading.Lock()
            with local_lock:
                yield
            return
        
        try:
            if lock.acquire(blocking=blocking, timeout=timeout):
                yield lock
            else:
                raise RuntimeError(f"无法获取分布式锁: {key}")
        finally:
            lock.release()
    
    def is_available(self) -> bool:
        """检查分布式锁服务是否可用"""
        return self.redis_client is not None
    
    def get_lock_info(self, key: str) -> dict:
        """获取锁信息"""
        if not self.redis_client:
            return {"available": False}
        
        try:
            lock_key = f"lock:{key}"
            value = self.redis_client.get(lock_key)
            ttl = self.redis_client.ttl(lock_key)
            
            return {
                "available": True,
                "locked": value is not None,
                "holder": value.decode() if value else None,
                "ttl": ttl if ttl > 0 else None
            }
        except RedisError as e:
            logger.error(f"获取锁信息失败: {str(e)}")
            return {"available": False, "error": str(e)}


# 全局实例
distributed_lock_manager = DistributedLockManager()


def get_distributed_lock_manager() -> DistributedLockManager:
    """获取分布式锁管理器实例"""
    return distributed_lock_manager


# 便捷函数
def with_distributed_lock(key: str, timeout: int = 10):
    """分布式锁装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            with distributed_lock_manager.lock(key, timeout):
                return func(*args, **kwargs)
        return wrapper
    return decorator


# 使用示例
"""
# 方式1：使用上下文管理器
with distributed_lock_manager.lock("critical_section", timeout=30):
    # 执行需要互斥的代码
    pass

# 方式2：使用装饰器
@with_distributed_lock("user_update", timeout=15)
def update_user_data(user_id):
    # 更新用户数据的代码
    pass

# 方式3：手动管理锁
lock = distributed_lock_manager.create_lock("manual_lock", timeout=20)
if lock and lock.acquire():
    try:
        # 执行代码
        pass
    finally:
        lock.release()
"""
