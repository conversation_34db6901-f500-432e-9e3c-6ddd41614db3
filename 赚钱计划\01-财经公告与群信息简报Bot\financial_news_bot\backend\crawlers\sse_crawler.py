"""
上海证券交易所（SSE）公告爬虫

目标网站：http://www.sse.com.cn/disclosure/listedinfo/announcement/
功能：爬取上市公司公告、监管信息等
"""

import asyncio
import re
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from bs4 import BeautifulSoup
import logging

from .base_crawler import BaseCrawler

logger = logging.getLogger(__name__)


class SSECrawler(BaseCrawler):
    """上海证券交易所爬虫"""
    
    def __init__(self, rate_limit: float = 2.0):
        """
        初始化SSE爬虫
        
        Args:
            rate_limit: 请求间隔（秒），默认2秒
        """
        super().__init__(
            name="SSE_Crawler",
            base_url="http://www.sse.com.cn",
            rate_limit=rate_limit
        )
        
        # SSE特定的URL模式
        self.announcement_url = "http://www.sse.com.cn/disclosure/listedinfo/announcement/"
        self.api_url = "http://query.sse.com.cn/security/stock/getStockListData2.do"
        
        # 公告类型映射
        self.announcement_types = {
            "1": "年报",
            "2": "半年报", 
            "3": "一季报",
            "4": "三季报",
            "5": "业绩预告",
            "6": "业绩快报",
            "7": "权益分派",
            "8": "董事会决议",
            "9": "监事会决议",
            "10": "股东大会决议",
            "category_ndbg_szsh": "年报",
            "category_bndbg_szsh": "半年报",
            "category_yjygjxz_szsh": "业绩预告"
        }
    
    def _parse_date(self, date_str: str) -> Optional[datetime]:
        """
        解析日期字符串
        
        Args:
            date_str: 日期字符串
            
        Returns:
            解析后的datetime对象或None
        """
        if not date_str:
            return None
        
        # 常见的日期格式
        date_formats = [
            "%Y-%m-%d",
            "%Y/%m/%d", 
            "%Y年%m月%d日",
            "%m/%d/%Y",
            "%d/%m/%Y"
        ]
        
        for fmt in date_formats:
            try:
                return datetime.strptime(date_str.strip(), fmt)
            except ValueError:
                continue
        
        logger.warning(f"无法解析日期格式: {date_str}")
        return None
    
    def _extract_stock_code(self, text: str) -> Optional[str]:
        """
        从文本中提取股票代码
        
        Args:
            text: 文本内容
            
        Returns:
            股票代码或None
        """
        # 上交所股票代码格式：6位数字，以6开头
        pattern = r'\b6\d{5}\b'
        match = re.search(pattern, text)
        return match.group() if match else None
    
    def _categorize_announcement(self, title: str, content: str = "") -> str:
        """
        根据标题和内容对公告进行分类
        
        Args:
            title: 公告标题
            content: 公告内容
            
        Returns:
            分类结果
        """
        text = (title + " " + content).lower()
        
        if any(keyword in text for keyword in ["年报", "年度报告"]):
            return "finance"
        elif any(keyword in text for keyword in ["半年报", "中期报告"]):
            return "finance"
        elif any(keyword in text for keyword in ["季报", "季度报告"]):
            return "finance"
        elif any(keyword in text for keyword in ["业绩", "盈利", "亏损"]):
            return "finance"
        elif any(keyword in text for keyword in ["董事会", "股东大会", "监事会"]):
            return "governance"
        elif any(keyword in text for keyword in ["分红", "派息", "股息"]):
            return "announcement"
        elif any(keyword in text for keyword in ["重组", "并购", "收购"]):
            return "announcement"
        elif any(keyword in text for keyword in ["停牌", "复牌"]):
            return "market"
        elif any(keyword in text for keyword in ["违规", "处罚", "调查"]):
            return "regulation"
        else:
            return "announcement"
    
    async def fetch_news_list(self, start_date: Optional[datetime] = None, 
                             end_date: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """
        获取新闻列表
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            新闻项目列表
        """
        if not start_date:
            start_date = datetime.now() - timedelta(days=7)
        if not end_date:
            end_date = datetime.now()
        
        news_list = []
        
        try:
            # 构建查询参数
            params = {
                "jsonCallBack": "jsonpCallback",
                "isPagination": "true",
                "stockCode": "",
                "stockType": "",
                "pageHelp.pageSize": "25",
                "pageHelp.pageNo": "1",
                "pageHelp.beginPage": "1",
                "pageHelp.endPage": "5"
            }
            
            # 发起请求
            response = await self.make_request(
                self.api_url,
                method='GET',
                params=params
            )
            
            if not response:
                logger.error(f"[{self.name}] 获取新闻列表失败")
                return []
            
            # 解析响应
            content = response.text
            
            # 移除JSONP包装
            if content.startswith("jsonpCallback("):
                content = content[15:-1]  # 移除 "jsonpCallback(" 和 ")"
            
            # 如果是JSON格式，尝试解析
            try:
                import json
                data = json.loads(content)
                
                if "result" in data and isinstance(data["result"], list):
                    for item in data["result"]:
                        news_item = self._parse_api_item(item)
                        if news_item:
                            news_list.append(news_item)
                
            except json.JSONDecodeError:
                # 如果不是JSON，尝试HTML解析
                soup = BeautifulSoup(content, 'html.parser')
                news_items = self._parse_html_list(soup)
                news_list.extend(news_items)
            
            # 按日期过滤
            filtered_news = []
            for item in news_list:
                pub_date = item.get('published_at')
                if pub_date and start_date <= pub_date <= end_date:
                    filtered_news.append(item)
            
            logger.info(f"[{self.name}] 获取到 {len(filtered_news)} 条新闻")
            return filtered_news
            
        except Exception as e:
            logger.error(f"[{self.name}] 获取新闻列表异常: {str(e)}")
            return []
    
    def _parse_api_item(self, item: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        解析API返回的单个项目
        
        Args:
            item: API返回的项目数据
            
        Returns:
            标准化的新闻项目或None
        """
        try:
            title = item.get("TITLE", "").strip()
            if not title:
                return None
            
            # 提取基本信息
            stock_code = item.get("STOCK_CODE", "")
            company_name = item.get("COMPANY_ABBR", "")
            pub_date_str = item.get("SSE_DATE", "")
            
            # 解析日期
            pub_date = self._parse_date(pub_date_str)
            
            # 构建详情URL
            detail_url = ""
            if "URL" in item:
                detail_url = self.base_url + item["URL"]
            
            return {
                'title': title,
                'source': 'sse',
                'source_url': detail_url,
                'source_id': item.get("ID", ""),
                'published_at': pub_date,
                'stock_code': stock_code,
                'company_name': company_name,
                'category': self._categorize_announcement(title),
                'raw_data': item
            }
            
        except Exception as e:
            logger.error(f"[{self.name}] 解析API项目失败: {str(e)}")
            return None
    
    def _parse_html_list(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """
        解析HTML页面中的新闻列表
        
        Args:
            soup: BeautifulSoup对象
            
        Returns:
            新闻项目列表
        """
        news_list = []
        
        try:
            # 查找新闻列表容器
            news_containers = soup.find_all(['tr', 'li', 'div'], class_=re.compile(r'.*list.*|.*item.*|.*row.*'))
            
            for container in news_containers:
                # 提取标题
                title_elem = container.find(['a', 'span'], string=re.compile(r'.+'))
                if not title_elem:
                    continue
                
                title = title_elem.get_text(strip=True)
                if len(title) < 5:  # 过滤过短的标题
                    continue
                
                # 提取链接
                link_elem = container.find('a', href=True)
                detail_url = ""
                if link_elem:
                    href = link_elem['href']
                    if href.startswith('http'):
                        detail_url = href
                    else:
                        detail_url = self.base_url + href
                
                # 提取日期
                date_elem = container.find(string=re.compile(r'\d{4}[-/年]\d{1,2}[-/月]\d{1,2}'))
                pub_date = None
                if date_elem:
                    pub_date = self._parse_date(date_elem.strip())
                
                # 提取股票代码
                stock_code = self._extract_stock_code(title)
                
                news_item = {
                    'title': title,
                    'source': 'sse',
                    'source_url': detail_url,
                    'source_id': "",
                    'published_at': pub_date,
                    'stock_code': stock_code,
                    'company_name': "",
                    'category': self._categorize_announcement(title)
                }
                
                news_list.append(news_item)
            
        except Exception as e:
            logger.error(f"[{self.name}] 解析HTML列表失败: {str(e)}")
        
        return news_list
    
    async def fetch_news_detail(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取新闻详情
        
        Args:
            item: 新闻项目基本信息
            
        Returns:
            完整的新闻数据
        """
        detail_url = item.get('source_url', '')
        if not detail_url:
            logger.warning(f"[{self.name}] 新闻项目缺少详情URL: {item.get('title', '')}")
            return self._build_news_item(item, "", "")
        
        try:
            # 获取详情页面
            response = await self.make_request(detail_url)
            if not response:
                logger.warning(f"[{self.name}] 获取详情失败: {detail_url}")
                return self._build_news_item(item, "", "")
            
            # 解析详情页面
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 提取正文内容
            content = self._extract_content(soup)
            
            # 生成摘要（取前200字符）
            summary = content[:200] + "..." if len(content) > 200 else content
            
            return self._build_news_item(item, content, summary)
            
        except Exception as e:
            logger.error(f"[{self.name}] 获取详情异常: {detail_url} - {str(e)}")
            return self._build_news_item(item, "", "")
    
    def _extract_content(self, soup: BeautifulSoup) -> str:
        """
        从详情页面提取正文内容
        
        Args:
            soup: BeautifulSoup对象
            
        Returns:
            正文内容
        """
        content = ""
        
        # 常见的内容容器选择器
        content_selectors = [
            '.content',
            '.article-content', 
            '.news-content',
            '#content',
            '.main-content',
            'div[class*="content"]',
            'div[id*="content"]'
        ]
        
        for selector in content_selectors:
            content_elem = soup.select_one(selector)
            if content_elem:
                content = content_elem.get_text(strip=True)
                break
        
        # 如果没有找到特定容器，尝试提取所有段落
        if not content:
            paragraphs = soup.find_all(['p', 'div'], string=re.compile(r'.{10,}'))
            content = '\n'.join([p.get_text(strip=True) for p in paragraphs])
        
        return self.clean_text(content)
    
    def _build_news_item(self, item: Dict[str, Any], content: str, summary: str) -> Dict[str, Any]:
        """
        构建标准化的新闻项目
        
        Args:
            item: 基本新闻信息
            content: 正文内容
            summary: 摘要
            
        Returns:
            标准化的新闻项目
        """
        # 提取实体信息
        companies, stock_codes = self.extract_companies_and_codes(item['title'] + " " + content)
        
        # 如果从标题中提取到股票代码，添加到列表中
        if item.get('stock_code'):
            stock_codes.append(item['stock_code'])
        
        # 如果有公司名称，添加到列表中
        if item.get('company_name'):
            companies.append(item['company_name'])
        
        # 去重
        companies = list(set(companies))
        stock_codes = list(set(stock_codes))
        
        return {
            'title': item['title'],
            'content': content,
            'summary': summary,
            'source': 'sse',
            'source_url': item.get('source_url', ''),
            'source_id': item.get('source_id', ''),
            'published_at': item.get('published_at'),
            'category': item.get('category', 'announcement'),
            'companies': companies,
            'stock_codes': stock_codes,
            'importance_score': self.calculate_importance_score(item),
            'content_hash': self.generate_content_hash(item['title'] + content)
        }
