"""
邮件推送提供商
支持SMTP邮件推送
"""
import logging
import re
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from typing import Dict, Any, List
from datetime import datetime

from ..unified_push_service import BasePushProvider, PushMessage, PushResult, PushChannel, MessageType

logger = logging.getLogger(__name__)

class EmailProvider(BasePushProvider):
    """邮件推送提供商"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.channel = PushChannel.EMAIL
        
        # SMTP配置
        self.smtp_server = config.get('smtp_server', 'smtp.gmail.com')
        self.smtp_port = config.get('smtp_port', 587)
        self.username = config.get('username', '')
        self.password = config.get('password', '')
        self.use_tls = config.get('use_tls', True)
        self.sender_name = config.get('sender_name', '财经新闻Bot')
        self.sender_email = config.get('sender_email', self.username)
        
        # 邮件配置
        self.max_recipients = config.get('max_recipients', 50)
        self.timeout = config.get('timeout', 30)
    
    def validate_target(self, target: str) -> bool:
        """
        验证邮箱地址是否有效
        
        Args:
            target: 邮箱地址
        
        Returns:
            是否有效
        """
        if not target:
            return False
        
        # 邮箱格式验证
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, target))
    
    async def send_message(self, target: str, message: PushMessage) -> PushResult:
        """
        发送邮件消息
        
        Args:
            target: 邮箱地址
            message: 推送消息
        
        Returns:
            推送结果
        """
        try:
            # 创建邮件消息
            msg = self._create_email_message(target, message)
            
            # 发送邮件
            with smtplib.SMTP(self.smtp_server, self.smtp_port, timeout=self.timeout) as server:
                if self.use_tls:
                    server.starttls()
                
                if self.username and self.password:
                    server.login(self.username, self.password)
                
                server.send_message(msg)
            
            return PushResult(
                success=True,
                message="邮件发送成功",
                response_data={"recipient": target}
            )
            
        except smtplib.SMTPAuthenticationError:
            return PushResult(
                success=False,
                message="SMTP认证失败，请检查用户名和密码",
                error_code="AUTH_ERROR"
            )
        except smtplib.SMTPRecipientsRefused:
            return PushResult(
                success=False,
                message=f"收件人地址被拒绝: {target}",
                error_code="RECIPIENT_REFUSED"
            )
        except smtplib.SMTPServerDisconnected:
            return PushResult(
                success=False,
                message="SMTP服务器连接断开",
                error_code="SERVER_DISCONNECTED"
            )
        except Exception as e:
            return PushResult(
                success=False,
                message=f"邮件发送异常: {str(e)}",
                error_code="EXCEPTION"
            )
    
    def _create_email_message(self, recipient: str, message: PushMessage) -> MIMEMultipart:
        """
        创建邮件消息对象
        
        Args:
            recipient: 收件人
            message: 推送消息
        
        Returns:
            邮件消息对象
        """
        msg = MIMEMultipart('alternative')
        
        # 设置邮件头
        msg['Subject'] = message.title or "财经新闻推送"
        msg['From'] = f"{self.sender_name} <{self.sender_email}>"
        msg['To'] = recipient
        msg['Date'] = datetime.now().strftime('%a, %d %b %Y %H:%M:%S %z')
        
        # 根据消息类型创建邮件内容
        if message.message_type == MessageType.HTML:
            html_content = message.content
            text_content = self._html_to_text(html_content)
        elif message.message_type == MessageType.MARKDOWN:
            html_content = self._markdown_to_html(message.content)
            text_content = self._markdown_to_text(message.content)
        else:
            text_content = message.content
            html_content = self._text_to_html(text_content)
        
        # 添加纯文本版本
        text_part = MIMEText(text_content, 'plain', 'utf-8')
        msg.attach(text_part)
        
        # 添加HTML版本
        html_part = MIMEText(html_content, 'html', 'utf-8')
        msg.attach(html_part)
        
        return msg
    
    def _markdown_to_html(self, markdown_content: str) -> str:
        """
        将Markdown转换为HTML
        
        Args:
            markdown_content: Markdown内容
        
        Returns:
            HTML内容
        """
        # 简单的Markdown到HTML转换
        html_lines = []
        lines = markdown_content.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                html_lines.append('<br>')
                continue
            
            # 标题
            if line.startswith('# '):
                html_lines.append(f'<h1>{line[2:]}</h1>')
            elif line.startswith('## '):
                html_lines.append(f'<h2>{line[3:]}</h2>')
            elif line.startswith('### '):
                html_lines.append(f'<h3>{line[4:]}</h3>')
            # 列表
            elif line.startswith('- ') or line.startswith('• '):
                html_lines.append(f'<li>{line[2:]}</li>')
            # 粗体
            elif '**' in line:
                line = re.sub(r'\*\*(.*?)\*\*', r'<strong>\1</strong>', line)
                html_lines.append(f'<p>{line}</p>')
            # 普通段落
            else:
                html_lines.append(f'<p>{line}</p>')
        
        # 包装在HTML文档中
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>财经新闻推送</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                h1, h2, h3 {{ color: #2c3e50; }}
                .header {{ background-color: #3498db; color: white; padding: 20px; text-align: center; }}
                .content {{ padding: 20px; }}
                .footer {{ background-color: #ecf0f1; padding: 10px; text-align: center; font-size: 12px; color: #7f8c8d; }}
                li {{ margin-bottom: 5px; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>📰 财经新闻推送</h1>
            </div>
            <div class="content">
                {''.join(html_lines)}
            </div>
            <div class="footer">
                <p>财经新闻Bot | 智能推送服务</p>
                <p>推送时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
        </body>
        </html>
        """
        
        return html_content
    
    def _markdown_to_text(self, markdown_content: str) -> str:
        """
        将Markdown转换为纯文本
        
        Args:
            markdown_content: Markdown内容
        
        Returns:
            纯文本内容
        """
        # 移除Markdown标记
        text = re.sub(r'#{1,6}\s+', '', markdown_content)  # 移除标题标记
        text = re.sub(r'\*\*(.*?)\*\*', r'\1', text)  # 移除粗体标记
        text = re.sub(r'\*(.*?)\*', r'\1', text)  # 移除斜体标记
        text = re.sub(r'- ', '• ', text)  # 转换列表标记
        
        return text
    
    def _text_to_html(self, text_content: str) -> str:
        """
        将纯文本转换为HTML
        
        Args:
            text_content: 纯文本内容
        
        Returns:
            HTML内容
        """
        # 转义HTML特殊字符
        html_content = text_content.replace('&', '&amp;')
        html_content = html_content.replace('<', '&lt;')
        html_content = html_content.replace('>', '&gt;')
        
        # 转换换行为<br>
        html_content = html_content.replace('\n', '<br>')
        
        # 包装在HTML文档中
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>财经新闻推送</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; padding: 20px; }}
                .header {{ background-color: #3498db; color: white; padding: 20px; text-align: center; margin-bottom: 20px; }}
                .footer {{ background-color: #ecf0f1; padding: 10px; text-align: center; font-size: 12px; color: #7f8c8d; margin-top: 20px; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>📰 财经新闻推送</h1>
            </div>
            <div>
                {html_content}
            </div>
            <div class="footer">
                <p>财经新闻Bot | 智能推送服务</p>
                <p>推送时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
        </body>
        </html>
        """
    
    def _html_to_text(self, html_content: str) -> str:
        """
        将HTML转换为纯文本
        
        Args:
            html_content: HTML内容
        
        Returns:
            纯文本内容
        """
        # 简单的HTML标签移除
        text = re.sub(r'<[^>]+>', '', html_content)
        text = text.replace('&amp;', '&')
        text = text.replace('&lt;', '<')
        text = text.replace('&gt;', '>')
        text = text.replace('&nbsp;', ' ')
        
        return text.strip()
    
    def format_news_list_email(self, news_list, subscription_name: str) -> PushMessage:
        """
        格式化新闻列表为邮件消息
        
        Args:
            news_list: 新闻列表
            subscription_name: 订阅名称
        
        Returns:
            格式化后的推送消息
        """
        if not news_list:
            return PushMessage(
                title=f"📰 {subscription_name} - 财经新闻推送",
                content="暂无新闻更新",
                message_type=MessageType.TEXT
            )
        
        title = f"📰 {subscription_name} - 财经新闻推送 ({len(news_list)}条)"
        
        # 构建Markdown格式的邮件内容
        content_lines = [
            f"# 📊 推送概览",
            f"- **订阅名称**: {subscription_name}",
            f"- **新闻数量**: {len(news_list)} 条",
            f"- **推送时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "# 📰 新闻详情"
        ]
        
        # 按重要性排序
        sorted_news = sorted(news_list, key=lambda x: x.importance_score, reverse=True)
        
        for i, news in enumerate(sorted_news, 1):
            importance_icon = "🔥" if news.importance_score >= 80 else "📌"
            content_lines.extend([
                f"## {i}. {importance_icon} {news.title}",
                f"**📅 发布时间**: {news.published_at.strftime('%Y-%m-%d %H:%M')}",
                f"**🏢 新闻来源**: {news.source}",
                f"**📊 重要性评分**: {news.importance_score}/100",
                ""
            ])
            
            if news.summary:
                content_lines.extend([
                    f"**📝 新闻摘要**:",
                    news.summary,
                    ""
                ])
            
            if news.source_url:
                content_lines.extend([
                    f"**🔗 原文链接**: {news.source_url}",
                    ""
                ])
            
            content_lines.append("---")
            content_lines.append("")
        
        content_lines.extend([
            "# 📧 推送说明",
            "本邮件由财经新闻Bot自动推送，基于您的订阅配置生成。",
            "如需修改推送设置或取消订阅，请登录系统进行配置。",
            "",
            "**财经新闻Bot** | 智能推送服务"
        ])
        
        return PushMessage(
            title=title,
            content="\n".join(content_lines),
            message_type=MessageType.MARKDOWN
        )
    
    async def send_batch_emails(self, recipients: List[str], message: PushMessage) -> List[PushResult]:
        """
        批量发送邮件
        
        Args:
            recipients: 收件人列表
            message: 推送消息
        
        Returns:
            推送结果列表
        """
        if len(recipients) > self.max_recipients:
            logger.warning(f"收件人数量 {len(recipients)} 超过限制 {self.max_recipients}")
            recipients = recipients[:self.max_recipients]
        
        results = []
        
        try:
            # 创建SMTP连接
            with smtplib.SMTP(self.smtp_server, self.smtp_port, timeout=self.timeout) as server:
                if self.use_tls:
                    server.starttls()
                
                if self.username and self.password:
                    server.login(self.username, self.password)
                
                # 批量发送
                for recipient in recipients:
                    try:
                        msg = self._create_email_message(recipient, message)
                        server.send_message(msg)
                        
                        results.append(PushResult(
                            success=True,
                            message="邮件发送成功",
                            response_data={"recipient": recipient}
                        ))
                        
                    except Exception as e:
                        results.append(PushResult(
                            success=False,
                            message=f"发送给 {recipient} 失败: {str(e)}",
                            error_code="SEND_ERROR",
                            response_data={"recipient": recipient}
                        ))
        
        except Exception as e:
            # 连接失败，所有邮件都失败
            for recipient in recipients:
                results.append(PushResult(
                    success=False,
                    message=f"SMTP连接失败: {str(e)}",
                    error_code="CONNECTION_ERROR",
                    response_data={"recipient": recipient}
                ))
        
        return results
