# 财经新闻Bot - 用户流程图和线框图设计

## 1. 用户注册到使用的完整流程图

### 1.1 新用户注册流程
```
开始 → 访问首页 → 点击注册 → 填写注册信息 → 邮箱验证 → 完善个人资料 → 创建首个订阅 → 开始使用
  ↓
决策点：
- 是否已有账户？ → 是：转到登录流程
- 邮箱验证是否成功？ → 否：重新发送验证邮件
- 是否跳过个人资料？ → 是：直接进入主界面
- 是否立即创建订阅？ → 否：进入空状态引导
```

### 1.2 用户登录流程
```
开始 → 访问登录页 → 输入凭据 → 身份验证 → 进入仪表板
  ↓
决策点：
- 登录失败？ → 显示错误信息，提供忘记密码选项
- 账户被锁定？ → 显示锁定信息和解锁方式
- 首次登录？ → 显示欢迎引导
```

### 1.3 订阅创建流程
```
仪表板 → 创建订阅 → 5步配置向导 → 测试推送 → 确认创建 → 订阅激活
  ↓
步骤详解：
1. 基本信息：名称、描述
2. 主题关键词：选择主题、添加关键词
3. 推送渠道：配置邮箱/微信/飞书
4. 过滤规则：设置来源、重要程度
5. 确认预览：检查配置、测试推送
```

### 1.4 新闻浏览流程
```
进入新闻中心 → 浏览列表 → 筛选搜索 → 查看详情 → 互动操作
  ↓
互动选项：
- 点赞/取消点赞
- 收藏到文件夹
- 分享到社交媒体
- 评论和讨论
```

## 2. 信息架构和页面跳转关系图

### 2.1 主要页面层级结构
```
财经新闻Bot
├── 认证模块
│   ├── 登录页面 (/auth/login)
│   ├── 注册页面 (/auth/register)
│   └── 忘记密码 (/auth/forgot-password)
├── 主应用模块
│   ├── 仪表板 (/dashboard)
│   ├── 新闻中心
│   │   ├── 新闻列表 (/news)
│   │   ├── 新闻详情 (/news/:id)
│   │   └── 我的收藏 (/bookmarks)
│   ├── 订阅管理
│   │   ├── 订阅列表 (/subscriptions)
│   │   ├── 创建订阅 (/subscriptions/create)
│   │   └── 编辑订阅 (/subscriptions/:id/edit)
│   ├── 用户中心
│   │   ├── 个人资料 (/profile)
│   │   └── 系统设置 (/settings)
│   └── 帮助支持 (/help)
```

### 2.2 页面跳转关系
```
登录成功 → 仪表板 → 各功能模块
仪表板 ← → 新闻中心 ← → 新闻详情
仪表板 ← → 订阅管理 ← → 创建/编辑订阅
仪表板 ← → 用户中心 ← → 个人资料/设置
任意页面 → 收藏页面 → 新闻详情
```

## 3. 主要页面线框图设计

### 3.1 登录页面线框图
```
┌─────────────────────────────────────────────────────────────┐
│                    财经新闻Bot                                │
├─────────────────────┬───────────────────────────────────────┤
│                     │           欢迎回来                      │
│   品牌展示区          │                                       │
│   - Logo            │   [用户名/邮箱输入框]                    │
│   - 特色介绍         │   [密码输入框]                          │
│   - 背景动画         │   □ 记住我    忘记密码？                 │
│                     │   [登录按钮]                            │
│                     │                                       │
│                     │   ─── 或使用以下方式登录 ───              │
│                     │   [微信登录] [QQ登录]                   │
│                     │                                       │
│                     │   还没有账户？立即注册                    │
└─────────────────────┴───────────────────────────────────────┘
```

### 3.2 仪表板页面线框图
```
┌─────────────────────────────────────────────────────────────┐
│ [Logo] 财经新闻Bot    导航菜单    [通知] [用户菜单]            │
├─────────────────────────────────────────────────────────────┤
│ 侧边栏              │ 欢迎回来，用户名！                        │
│ □ 仪表板            │ 今天是 2024年1月15日 星期一               │
│ □ 新闻中心          │                                       │
│ □ 我的收藏          │ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐        │
│ □ 订阅管理          │ │总新闻│ │今日新│ │我的订│ │推送次│        │
│ □ 个人资料          │ │ 1234│ │  56 │ │  8  │ │ 89  │        │
│ □ 系统设置          │ └─────┘ └─────┘ └─────┘ └─────┘        │
│                     │                                       │
│                     │ 快速操作              最近活动          │
│                     │ [创建新订阅]          暂无最近活动        │
│                     │ [浏览最新新闻]                          │
│                     │ [管理我的订阅]        最新财经新闻        │
│                     │                     正在加载最新新闻... │
└─────────────────────┴───────────────────────────────────────┘
```

### 3.3 订阅创建页面线框图
```
┌─────────────────────────────────────────────────────────────┐
│ [返回] 创建新订阅                                            │
├─────────────────────────────────────────────────────────────┤
│ 步骤指示器：● ○ ○ ○ ○                                        │
│ 基本信息  主题关键词  推送渠道  过滤规则  确认创建              │
│                                                             │
│ 基本信息                                                     │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 订阅名称：[输入框]                                        │ │
│ │ 订阅描述：[文本域]                                        │ │
│ │                                                         │ │
│ │ 💡 提示：订阅名称将显示在推送消息的标题中                   │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│                                    [上一步] [下一步 →]       │
└─────────────────────────────────────────────────────────────┘
```

### 3.4 新闻列表页面线框图
```
┌─────────────────────────────────────────────────────────────┐
│ 新闻中心                                                     │
├─────────────────────────────────────────────────────────────┤
│ [搜索框：搜索新闻标题、内容或标签...]  [筛选]                  │
│ 分类：[全部▼] 重要程度：[全部▼] 视图：[列表▼]                 │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 🔥 央行发布最新货币政策报告，释放重要信号                   │ │
│ │ 中国人民银行今日发布第三季度货币政策执行报告...             │ │
│ │ 📅 01-15 09:30  📰 新华网  👤 张三                        │ │
│ │ #货币政策 #央行 #经济                                     │ │
│ │                                    👁 1250 ❤ 89 📤 💾    │ │
│ └─────────────────────────────────────────────────────────┘ │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 科技巨头发布AI新产品，引领行业变革                          │ │
│ │ 某知名科技公司今日正式发布了其最新的人工智能产品...          │ │
│ │ 📅 01-15 08:45  📰 36氪  👤 李四                          │ │
│ │ #人工智能 #科技 #创新                                     │ │
│ │                                    👁 890 ❤ 67 📤 💾     │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ [分页控件] 第1-10条，共156条                                 │
└─────────────────────────────────────────────────────────────┘
```

## 4. 用户任务流程和决策点

### 4.1 核心用户任务
1. **新用户注册并创建首个订阅**
   - 决策点：选择注册方式（邮箱/手机/第三方）
   - 决策点：是否立即验证邮箱
   - 决策点：是否完善个人资料
   - 决策点：选择订阅主题和关键词

2. **老用户管理订阅**
   - 决策点：修改现有订阅还是创建新订阅
   - 决策点：调整推送频率和时间
   - 决策点：添加或删除关键词

3. **用户浏览和收藏新闻**
   - 决策点：使用搜索还是浏览分类
   - 决策点：收藏到哪个文件夹
   - 决策点：是否分享到社交媒体

### 4.2 关键决策点设计
- **空状态引导**：首次使用时的引导流程
- **错误恢复**：操作失败时的恢复路径
- **权限提示**：需要权限时的友好提示
- **数据同步**：跨设备数据同步的处理

## 5. 响应式设计断点

### 5.1 断点定义
- **Mobile**: 320px - 767px
- **Tablet**: 768px - 1023px  
- **Desktop**: 1024px - 1199px
- **Large Desktop**: 1200px+

### 5.2 各断点下的布局调整
- **Mobile**: 单列布局，抽屉导航，触摸优化
- **Tablet**: 双列布局，侧边栏可收缩
- **Desktop**: 多列布局，固定侧边栏
- **Large Desktop**: 宽屏优化，更多信息密度

## 6. 交互规范和动效标准

### 6.1 交互规范
- **按钮点击**：0.2s 缓动动画
- **页面切换**：0.3s 滑动过渡
- **模态框**：0.25s 淡入淡出
- **加载状态**：骨架屏 + 进度指示

### 6.2 动效时长标准
- **微交互**：100-200ms
- **页面过渡**：200-300ms
- **复杂动画**：300-500ms
- **加载动画**：持续进行直到完成

这个设计文档为前端开发提供了完整的用户体验指导和视觉规范。
