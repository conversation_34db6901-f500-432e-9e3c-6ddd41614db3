#!/usr/bin/env python3
"""
系统稳定性测试
进行7x24小时稳定性测试，监控内存泄漏和CPU使用率，测试异常情况下的系统恢复能力
"""
import asyncio
import aiohttp
import psutil
import time
import json
import logging
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import argparse
import signal
import sys
from dataclasses import dataclass, asdict

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class SystemMetrics:
    """系统指标"""
    timestamp: str
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    disk_usage_percent: float
    network_bytes_sent: int
    network_bytes_recv: int
    active_connections: int
    process_count: int

@dataclass
class ApplicationMetrics:
    """应用指标"""
    timestamp: str
    response_time: float
    status_code: int
    success: bool
    error_message: Optional[str] = None

class StabilityTester:
    """稳定性测试器"""
    
    def __init__(self, base_url: str, test_duration_hours: int = 168):  # 默认7天
        self.base_url = base_url
        self.test_duration_hours = test_duration_hours
        self.test_start_time = None
        self.test_end_time = None
        self.running = False
        
        # 数据收集
        self.system_metrics = []
        self.application_metrics = []
        self.error_events = []
        self.recovery_events = []
        
        # 配置
        self.monitoring_interval = 30  # 30秒监控一次
        self.health_check_interval = 10  # 10秒健康检查一次
        self.memory_leak_threshold = 1000  # MB内存增长阈值
        self.cpu_high_threshold = 80  # CPU高使用率阈值
        
        # 信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        logger.info(f"收到信号 {signum}，正在停止测试...")
        self.running = False
    
    async def start_stability_test(self):
        """开始稳定性测试"""
        logger.info(f"开始{self.test_duration_hours}小时稳定性测试...")
        
        self.test_start_time = datetime.now()
        self.test_end_time = self.test_start_time + timedelta(hours=self.test_duration_hours)
        self.running = True
        
        # 启动监控任务
        tasks = [
            asyncio.create_task(self._monitor_system_resources()),
            asyncio.create_task(self._monitor_application_health()),
            asyncio.create_task(self._detect_memory_leaks()),
            asyncio.create_task(self._test_error_recovery()),
            asyncio.create_task(self._generate_periodic_reports())
        ]
        
        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            logger.error(f"稳定性测试异常: {e}")
        finally:
            self.running = False
            await self._generate_final_report()
    
    async def _monitor_system_resources(self):
        """监控系统资源"""
        logger.info("开始系统资源监控...")
        
        while self.running and datetime.now() < self.test_end_time:
            try:
                # 收集系统指标
                cpu_percent = psutil.cpu_percent(interval=1)
                memory = psutil.virtual_memory()
                disk = psutil.disk_usage('/')
                network = psutil.net_io_counters()
                
                # 获取网络连接数
                connections = len(psutil.net_connections())
                
                # 获取进程数
                process_count = len(psutil.pids())
                
                metrics = SystemMetrics(
                    timestamp=datetime.now().isoformat(),
                    cpu_percent=cpu_percent,
                    memory_percent=memory.percent,
                    memory_used_mb=memory.used / 1024 / 1024,
                    disk_usage_percent=(disk.used / disk.total) * 100,
                    network_bytes_sent=network.bytes_sent,
                    network_bytes_recv=network.bytes_recv,
                    active_connections=connections,
                    process_count=process_count
                )
                
                self.system_metrics.append(metrics)
                
                # 检查异常情况
                if cpu_percent > self.cpu_high_threshold:
                    self.error_events.append({
                        'timestamp': datetime.now().isoformat(),
                        'type': 'high_cpu',
                        'value': cpu_percent,
                        'threshold': self.cpu_high_threshold
                    })
                
                if memory.percent > 90:
                    self.error_events.append({
                        'timestamp': datetime.now().isoformat(),
                        'type': 'high_memory',
                        'value': memory.percent,
                        'threshold': 90
                    })
                
                # 日志记录
                if len(self.system_metrics) % 120 == 0:  # 每小时记录一次
                    logger.info(f"系统状态 - CPU: {cpu_percent:.1f}%, "
                              f"内存: {memory.percent:.1f}%, "
                              f"磁盘: {(disk.used / disk.total) * 100:.1f}%")
                
            except Exception as e:
                logger.error(f"系统资源监控异常: {e}")
            
            await asyncio.sleep(self.monitoring_interval)
    
    async def _monitor_application_health(self):
        """监控应用健康状态"""
        logger.info("开始应用健康监控...")
        
        # 测试端点列表
        health_endpoints = [
            '/health',
            '/api/v1/health',
            '/api/v1/news/',
            '/api/v1/auth/me'
        ]
        
        connector = aiohttp.TCPConnector(limit=100)
        
        async with aiohttp.ClientSession(connector=connector) as session:
            while self.running and datetime.now() < self.test_end_time:
                try:
                    for endpoint in health_endpoints:
                        start_time = time.time()
                        error_message = None
                        
                        try:
                            async with session.get(
                                f"{self.base_url}{endpoint}",
                                timeout=aiohttp.ClientTimeout(total=30)
                            ) as response:
                                response_time = time.time() - start_time
                                
                                metrics = ApplicationMetrics(
                                    timestamp=datetime.now().isoformat(),
                                    response_time=response_time,
                                    status_code=response.status,
                                    success=response.status < 400
                                )
                                
                                if not metrics.success:
                                    error_message = f"HTTP {response.status}"
                                    metrics.error_message = error_message
                                
                        except Exception as e:
                            response_time = time.time() - start_time
                            error_message = str(e)
                            
                            metrics = ApplicationMetrics(
                                timestamp=datetime.now().isoformat(),
                                response_time=response_time,
                                status_code=0,
                                success=False,
                                error_message=error_message
                            )
                        
                        self.application_metrics.append(metrics)
                        
                        # 记录错误事件
                        if not metrics.success:
                            self.error_events.append({
                                'timestamp': datetime.now().isoformat(),
                                'type': 'application_error',
                                'endpoint': endpoint,
                                'error': error_message,
                                'response_time': response_time
                            })
                
                except Exception as e:
                    logger.error(f"应用健康监控异常: {e}")
                
                await asyncio.sleep(self.health_check_interval)
    
    async def _detect_memory_leaks(self):
        """检测内存泄漏"""
        logger.info("开始内存泄漏检测...")
        
        baseline_memory = None
        memory_samples = []
        
        while self.running and datetime.now() < self.test_end_time:
            try:
                current_memory = psutil.virtual_memory().used / 1024 / 1024  # MB
                memory_samples.append({
                    'timestamp': datetime.now().isoformat(),
                    'memory_mb': current_memory
                })
                
                if baseline_memory is None:
                    baseline_memory = current_memory
                    logger.info(f"内存基线设定: {baseline_memory:.1f} MB")
                
                # 保持最近24小时的样本
                cutoff_time = datetime.now() - timedelta(hours=24)
                memory_samples = [
                    sample for sample in memory_samples
                    if datetime.fromisoformat(sample['timestamp']) > cutoff_time
                ]
                
                # 检测内存泄漏
                if len(memory_samples) > 100:  # 至少有足够的样本
                    recent_avg = sum(s['memory_mb'] for s in memory_samples[-50:]) / 50
                    early_avg = sum(s['memory_mb'] for s in memory_samples[:50]) / 50
                    
                    memory_growth = recent_avg - early_avg
                    
                    if memory_growth > self.memory_leak_threshold:
                        self.error_events.append({
                            'timestamp': datetime.now().isoformat(),
                            'type': 'memory_leak_detected',
                            'baseline_memory': baseline_memory,
                            'current_memory': current_memory,
                            'growth_mb': memory_growth,
                            'threshold': self.memory_leak_threshold
                        })
                        logger.warning(f"检测到内存泄漏: 增长 {memory_growth:.1f} MB")
                
            except Exception as e:
                logger.error(f"内存泄漏检测异常: {e}")
            
            await asyncio.sleep(300)  # 5分钟检测一次
    
    async def _test_error_recovery(self):
        """测试错误恢复能力"""
        logger.info("开始错误恢复测试...")
        
        # 模拟各种错误场景
        error_scenarios = [
            {'name': '高并发请求', 'interval': 3600},  # 每小时一次
            {'name': '大数据量查询', 'interval': 7200},  # 每2小时一次
            {'name': '网络超时模拟', 'interval': 1800},  # 每30分钟一次
        ]
        
        last_test_times = {scenario['name']: datetime.now() for scenario in error_scenarios}
        
        while self.running and datetime.now() < self.test_end_time:
            try:
                current_time = datetime.now()
                
                for scenario in error_scenarios:
                    scenario_name = scenario['name']
                    interval = scenario['interval']
                    
                    if (current_time - last_test_times[scenario_name]).total_seconds() >= interval:
                        logger.info(f"执行错误恢复测试: {scenario_name}")
                        
                        recovery_start = time.time()
                        recovery_success = await self._execute_recovery_test(scenario_name)
                        recovery_time = time.time() - recovery_start
                        
                        self.recovery_events.append({
                            'timestamp': current_time.isoformat(),
                            'scenario': scenario_name,
                            'recovery_time': recovery_time,
                            'success': recovery_success
                        })
                        
                        last_test_times[scenario_name] = current_time
                
            except Exception as e:
                logger.error(f"错误恢复测试异常: {e}")
            
            await asyncio.sleep(60)  # 每分钟检查一次
    
    async def _execute_recovery_test(self, scenario_name: str) -> bool:
        """执行具体的恢复测试"""
        try:
            if scenario_name == '高并发请求':
                # 发送大量并发请求测试系统恢复
                connector = aiohttp.TCPConnector(limit=200)
                async with aiohttp.ClientSession(connector=connector) as session:
                    tasks = []
                    for _ in range(100):
                        task = session.get(
                            f"{self.base_url}/api/v1/news/",
                            timeout=aiohttp.ClientTimeout(total=30)
                        )
                        tasks.append(task)
                    
                    responses = await asyncio.gather(*tasks, return_exceptions=True)
                    success_count = sum(1 for r in responses if not isinstance(r, Exception))
                    return success_count > 50  # 50%以上成功认为恢复正常
            
            elif scenario_name == '大数据量查询':
                # 测试大数据量查询的恢复
                async with aiohttp.ClientSession() as session:
                    async with session.get(
                        f"{self.base_url}/api/v1/news/",
                        params={'size': 1000},  # 大数据量
                        timeout=aiohttp.ClientTimeout(total=60)
                    ) as response:
                        return response.status == 200
            
            elif scenario_name == '网络超时模拟':
                # 测试网络超时后的恢复
                async with aiohttp.ClientSession() as session:
                    try:
                        async with session.get(
                            f"{self.base_url}/api/v1/health",
                            timeout=aiohttp.ClientTimeout(total=1)  # 很短的超时
                        ) as response:
                            return response.status == 200
                    except asyncio.TimeoutError:
                        # 超时后再次尝试正常请求
                        await asyncio.sleep(5)
                        async with session.get(
                            f"{self.base_url}/api/v1/health",
                            timeout=aiohttp.ClientTimeout(total=30)
                        ) as response:
                            return response.status == 200
            
            return False
            
        except Exception as e:
            logger.error(f"恢复测试执行失败 {scenario_name}: {e}")
            return False
    
    async def _generate_periodic_reports(self):
        """生成周期性报告"""
        logger.info("开始周期性报告生成...")
        
        while self.running and datetime.now() < self.test_end_time:
            try:
                await asyncio.sleep(3600)  # 每小时生成一次报告
                
                if self.system_metrics and self.application_metrics:
                    await self._generate_hourly_report()
                
            except Exception as e:
                logger.error(f"周期性报告生成异常: {e}")
    
    async def _generate_hourly_report(self):
        """生成小时报告"""
        current_time = datetime.now()
        hour_ago = current_time - timedelta(hours=1)
        
        # 过滤最近一小时的数据
        recent_system_metrics = [
            m for m in self.system_metrics
            if datetime.fromisoformat(m.timestamp) > hour_ago
        ]
        
        recent_app_metrics = [
            m for m in self.application_metrics
            if datetime.fromisoformat(m.timestamp) > hour_ago
        ]
        
        if recent_system_metrics and recent_app_metrics:
            # 计算统计信息
            avg_cpu = sum(m.cpu_percent for m in recent_system_metrics) / len(recent_system_metrics)
            avg_memory = sum(m.memory_percent for m in recent_system_metrics) / len(recent_system_metrics)
            
            success_rate = sum(1 for m in recent_app_metrics if m.success) / len(recent_app_metrics) * 100
            avg_response_time = sum(m.response_time for m in recent_app_metrics if m.success) / max(1, sum(1 for m in recent_app_metrics if m.success))
            
            logger.info(f"小时报告 - CPU: {avg_cpu:.1f}%, 内存: {avg_memory:.1f}%, "
                       f"成功率: {success_rate:.1f}%, 响应时间: {avg_response_time:.3f}s")
    
    async def _generate_final_report(self):
        """生成最终报告"""
        logger.info("生成最终稳定性测试报告...")
        
        test_duration = datetime.now() - self.test_start_time
        
        # 计算总体统计
        if self.system_metrics:
            avg_cpu = sum(m.cpu_percent for m in self.system_metrics) / len(self.system_metrics)
            max_cpu = max(m.cpu_percent for m in self.system_metrics)
            avg_memory = sum(m.memory_percent for m in self.system_metrics) / len(self.system_metrics)
            max_memory = max(m.memory_percent for m in self.system_metrics)
        else:
            avg_cpu = max_cpu = avg_memory = max_memory = 0
        
        if self.application_metrics:
            total_requests = len(self.application_metrics)
            successful_requests = sum(1 for m in self.application_metrics if m.success)
            success_rate = (successful_requests / total_requests) * 100
            
            successful_response_times = [m.response_time for m in self.application_metrics if m.success]
            avg_response_time = sum(successful_response_times) / len(successful_response_times) if successful_response_times else 0
        else:
            total_requests = successful_requests = success_rate = avg_response_time = 0
        
        # 生成报告
        report = {
            'test_summary': {
                'start_time': self.test_start_time.isoformat(),
                'end_time': datetime.now().isoformat(),
                'planned_duration_hours': self.test_duration_hours,
                'actual_duration_hours': test_duration.total_seconds() / 3600,
                'completed_successfully': datetime.now() >= self.test_end_time
            },
            'system_performance': {
                'avg_cpu_percent': avg_cpu,
                'max_cpu_percent': max_cpu,
                'avg_memory_percent': avg_memory,
                'max_memory_percent': max_memory,
                'total_system_samples': len(self.system_metrics)
            },
            'application_performance': {
                'total_requests': total_requests,
                'successful_requests': successful_requests,
                'success_rate_percent': success_rate,
                'avg_response_time_seconds': avg_response_time
            },
            'stability_events': {
                'total_error_events': len(self.error_events),
                'error_events': self.error_events,
                'recovery_tests': len(self.recovery_events),
                'recovery_events': self.recovery_events
            },
            'assessment': self._assess_stability(),
            'recommendations': self._generate_recommendations()
        }
        
        # 保存报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"stability_test_report_{timestamp}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"稳定性测试报告已保存: {report_file}")
        
        # 输出摘要
        print(f"\n=== 稳定性测试摘要 ===")
        print(f"测试时长: {test_duration.total_seconds() / 3600:.1f} 小时")
        print(f"系统平均CPU使用率: {avg_cpu:.1f}%")
        print(f"系统平均内存使用率: {avg_memory:.1f}%")
        print(f"应用成功率: {success_rate:.1f}%")
        print(f"平均响应时间: {avg_response_time:.3f}s")
        print(f"错误事件数量: {len(self.error_events)}")
        print(f"恢复测试次数: {len(self.recovery_events)}")
        print(f"稳定性评估: {report['assessment']['overall_rating']}")
    
    def _assess_stability(self) -> Dict[str, Any]:
        """评估系统稳定性"""
        assessment = {
            'overall_rating': 'Unknown',
            'cpu_stability': 'Unknown',
            'memory_stability': 'Unknown',
            'application_stability': 'Unknown',
            'recovery_capability': 'Unknown'
        }
        
        if self.system_metrics:
            # CPU稳定性评估
            high_cpu_events = [e for e in self.error_events if e.get('type') == 'high_cpu']
            if len(high_cpu_events) == 0:
                assessment['cpu_stability'] = 'Excellent'
            elif len(high_cpu_events) < 5:
                assessment['cpu_stability'] = 'Good'
            else:
                assessment['cpu_stability'] = 'Poor'
            
            # 内存稳定性评估
            memory_leak_events = [e for e in self.error_events if e.get('type') == 'memory_leak_detected']
            high_memory_events = [e for e in self.error_events if e.get('type') == 'high_memory']
            
            if len(memory_leak_events) == 0 and len(high_memory_events) == 0:
                assessment['memory_stability'] = 'Excellent'
            elif len(memory_leak_events) == 0 and len(high_memory_events) < 3:
                assessment['memory_stability'] = 'Good'
            else:
                assessment['memory_stability'] = 'Poor'
        
        if self.application_metrics:
            # 应用稳定性评估
            success_rate = sum(1 for m in self.application_metrics if m.success) / len(self.application_metrics) * 100
            
            if success_rate >= 99:
                assessment['application_stability'] = 'Excellent'
            elif success_rate >= 95:
                assessment['application_stability'] = 'Good'
            elif success_rate >= 90:
                assessment['application_stability'] = 'Fair'
            else:
                assessment['application_stability'] = 'Poor'
        
        if self.recovery_events:
            # 恢复能力评估
            successful_recoveries = sum(1 for e in self.recovery_events if e.get('success'))
            recovery_rate = successful_recoveries / len(self.recovery_events) * 100
            
            if recovery_rate >= 90:
                assessment['recovery_capability'] = 'Excellent'
            elif recovery_rate >= 75:
                assessment['recovery_capability'] = 'Good'
            else:
                assessment['recovery_capability'] = 'Poor'
        
        # 综合评估
        ratings = [assessment['cpu_stability'], assessment['memory_stability'], 
                  assessment['application_stability'], assessment['recovery_capability']]
        
        if all(r == 'Excellent' for r in ratings):
            assessment['overall_rating'] = 'Excellent'
        elif all(r in ['Excellent', 'Good'] for r in ratings):
            assessment['overall_rating'] = 'Good'
        elif any(r == 'Poor' for r in ratings):
            assessment['overall_rating'] = 'Poor'
        else:
            assessment['overall_rating'] = 'Fair'
        
        return assessment
    
    def _generate_recommendations(self) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        # 基于错误事件生成建议
        error_types = [e.get('type') for e in self.error_events]
        
        if 'high_cpu' in error_types:
            recommendations.append("优化CPU密集型操作，考虑增加服务器资源或优化算法")
        
        if 'high_memory' in error_types:
            recommendations.append("检查内存使用模式，优化数据结构和缓存策略")
        
        if 'memory_leak_detected' in error_types:
            recommendations.append("修复内存泄漏问题，检查对象生命周期管理")
        
        if 'application_error' in error_types:
            recommendations.append("改进错误处理机制，增加重试和熔断策略")
        
        # 基于恢复测试结果生成建议
        if self.recovery_events:
            failed_recoveries = [e for e in self.recovery_events if not e.get('success')]
            if failed_recoveries:
                recommendations.append("改进系统恢复机制，增强容错能力")
        
        # 通用建议
        recommendations.extend([
            "建立完善的监控和告警系统",
            "定期进行稳定性测试",
            "制定详细的故障恢复预案",
            "实施自动化运维和部署"
        ])
        
        return recommendations


async def main():
    parser = argparse.ArgumentParser(description='系统稳定性测试')
    parser.add_argument('--base-url', default='http://localhost:8000', help='目标服务URL')
    parser.add_argument('--duration', type=int, default=24, help='测试持续时间（小时）')
    parser.add_argument('--monitoring-interval', type=int, default=30, help='监控间隔（秒）')
    
    args = parser.parse_args()
    
    tester = StabilityTester(args.base_url, args.duration)
    tester.monitoring_interval = args.monitoring_interval
    
    try:
        await tester.start_stability_test()
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
    except Exception as e:
        logger.error(f"稳定性测试失败: {e}")
        return 1
    
    return 0


if __name__ == '__main__':
    asyncio.run(main())
