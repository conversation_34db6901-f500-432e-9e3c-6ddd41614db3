import { test, expect, devices } from '@playwright/test';

/**
 * 跨浏览器兼容性测试
 * 测试在不同浏览器和设备上的功能一致性
 */
test.describe('跨浏览器兼容性测试', () => {
  
  // 测试不同浏览器的基本功能
  ['chromium', 'firefox', 'webkit'].forEach(browserName => {
    test(`${browserName} - 基本功能测试`, async ({ browser }) => {
      const context = await browser.newContext();
      const page = await context.newPage();

      await test.step(`${browserName} - 页面加载`, async () => {
        await page.goto('/dashboard');
        await expect(page.locator('[data-testid="dashboard-content"]')).toBeVisible();
      });

      await test.step(`${browserName} - 导航功能`, async () => {
        // 测试侧边栏导航
        await page.click('[data-testid="nav-news"]');
        await expect(page).toHaveURL('/news');
        
        await page.click('[data-testid="nav-subscriptions"]');
        await expect(page).toHaveURL('/subscriptions');
        
        await page.click('[data-testid="nav-dashboard"]');
        await expect(page).toHaveURL('/dashboard');
      });

      await test.step(`${browserName} - 表单功能`, async () => {
        // 测试搜索表单
        await page.keyboard.press('Control+k');
        await expect(page.locator('[data-testid="search-modal"]')).toBeVisible();
        
        await page.fill('[data-testid="search-input"]', '测试搜索');
        await page.keyboard.press('Enter');
        
        // 验证搜索结果页面
        await expect(page).toHaveURL(/\/news\?.*q=测试搜索/);
      });

      await test.step(`${browserName} - JavaScript功能`, async () => {
        await page.goto('/news');
        
        // 测试动态加载
        const initialCount = await page.locator('[data-testid="news-item"]').count();
        
        // 滚动触发懒加载
        await page.mouse.wheel(0, 2000);
        await page.waitForTimeout(1000);
        
        const afterScrollCount = await page.locator('[data-testid="news-item"]').count();
        expect(afterScrollCount).toBeGreaterThanOrEqual(initialCount);
      });

      await test.step(`${browserName} - CSS样式`, async () => {
        await page.goto('/dashboard');
        
        // 检查关键元素的样式
        const header = page.locator('[data-testid="main-header"]');
        await expect(header).toBeVisible();
        
        const headerStyles = await header.evaluate(el => {
          const styles = window.getComputedStyle(el);
          return {
            display: styles.display,
            position: styles.position,
            backgroundColor: styles.backgroundColor,
          };
        });
        
        expect(headerStyles.display).not.toBe('none');
        expect(headerStyles.position).toBe('fixed');
      });

      await context.close();
    });
  });

  // 测试不同设备尺寸
  const deviceTests = [
    { name: 'Desktop Chrome', device: devices['Desktop Chrome'] },
    { name: 'Desktop Firefox', device: devices['Desktop Firefox'] },
    { name: 'Desktop Safari', device: devices['Desktop Safari'] },
    { name: 'iPhone 12', device: devices['iPhone 12'] },
    { name: 'iPad Pro', device: devices['iPad Pro'] },
    { name: 'Pixel 5', device: devices['Pixel 5'] },
  ];

  deviceTests.forEach(({ name, device }) => {
    test(`${name} - 响应式布局测试`, async ({ browser }) => {
      const context = await browser.newContext({
        ...device,
      });
      const page = await context.newPage();

      await test.step(`${name} - 布局适配`, async () => {
        await page.goto('/dashboard');
        
        const viewport = page.viewportSize();
        const isMobile = viewport!.width < 768;
        const isTablet = viewport!.width >= 768 && viewport!.width < 1024;
        
        if (isMobile) {
          // 移动端布局检查
          await expect(page.locator('[data-testid="mobile-bottom-nav"]')).toBeVisible();
          await expect(page.locator('[data-testid="desktop-sidebar"]')).not.toBeVisible();
        } else {
          // 桌面端布局检查
          await expect(page.locator('[data-testid="desktop-sidebar"]')).toBeVisible();
          await expect(page.locator('[data-testid="mobile-bottom-nav"]')).not.toBeVisible();
        }
      });

      await test.step(`${name} - 触摸/点击交互`, async () => {
        await page.goto('/news');
        
        const newsItem = page.locator('[data-testid="news-item"]').first();
        await expect(newsItem).toBeVisible();
        
        // 测试点击/触摸
        await newsItem.click();
        await expect(page.locator('[data-testid="news-detail"]')).toBeVisible();
      });

      await test.step(`${name} - 文本可读性`, async () => {
        await page.goto('/dashboard');
        
        // 检查文本大小是否合适
        const titleElement = page.locator('[data-testid="page-title"]').first();
        if (await titleElement.isVisible()) {
          const fontSize = await titleElement.evaluate(el => {
            return window.getComputedStyle(el).fontSize;
          });
          
          const fontSizeValue = parseInt(fontSize);
          
          // 移动端文本应该足够大
          const viewport = page.viewportSize();
          if (viewport!.width < 768) {
            expect(fontSizeValue).toBeGreaterThanOrEqual(16);
          }
        }
      });

      await context.close();
    });
  });

  // 测试特定浏览器功能
  test('Chrome - 高级功能测试', async ({ browser }) => {
    const context = await browser.newContext();
    const page = await context.newPage();

    await test.step('Service Worker支持', async () => {
      await page.goto('/dashboard');
      
      // 检查Service Worker是否注册
      const swRegistered = await page.evaluate(() => {
        return 'serviceWorker' in navigator;
      });
      
      expect(swRegistered).toBe(true);
    });

    await test.step('Web API支持', async () => {
      // 检查现代Web API支持
      const apiSupport = await page.evaluate(() => {
        return {
          fetch: typeof fetch !== 'undefined',
          localStorage: typeof localStorage !== 'undefined',
          sessionStorage: typeof sessionStorage !== 'undefined',
          indexedDB: typeof indexedDB !== 'undefined',
          webSocket: typeof WebSocket !== 'undefined',
          notification: 'Notification' in window,
          geolocation: 'geolocation' in navigator,
        };
      });
      
      expect(apiSupport.fetch).toBe(true);
      expect(apiSupport.localStorage).toBe(true);
      expect(apiSupport.sessionStorage).toBe(true);
    });

    await context.close();
  });

  test('Firefox - 兼容性测试', async ({ browser }) => {
    const context = await browser.newContext();
    const page = await context.newPage();

    await test.step('CSS Grid支持', async () => {
      await page.goto('/dashboard');
      
      const gridSupport = await page.evaluate(() => {
        const testEl = document.createElement('div');
        testEl.style.display = 'grid';
        return testEl.style.display === 'grid';
      });
      
      expect(gridSupport).toBe(true);
    });

    await test.step('ES6+功能支持', async () => {
      const es6Support = await page.evaluate(() => {
        try {
          // 测试箭头函数
          const arrow = () => true;
          
          // 测试模板字符串
          const template = `test ${arrow()}`;
          
          // 测试解构赋值
          const [a, b] = [1, 2];
          
          // 测试Promise
          const promise = Promise.resolve(true);
          
          return true;
        } catch (e) {
          return false;
        }
      });
      
      expect(es6Support).toBe(true);
    });

    await context.close();
  });

  test('Safari - WebKit特性测试', async ({ browser }) => {
    const context = await browser.newContext();
    const page = await context.newPage();

    await test.step('CSS变量支持', async () => {
      await page.goto('/dashboard');
      
      const cssVarSupport = await page.evaluate(() => {
        const testEl = document.createElement('div');
        testEl.style.setProperty('--test-var', 'red');
        testEl.style.color = 'var(--test-var)';
        document.body.appendChild(testEl);
        
        const computedColor = window.getComputedStyle(testEl).color;
        document.body.removeChild(testEl);
        
        return computedColor === 'red' || computedColor === 'rgb(255, 0, 0)';
      });
      
      expect(cssVarSupport).toBe(true);
    });

    await test.step('Flexbox支持', async () => {
      const flexSupport = await page.evaluate(() => {
        const testEl = document.createElement('div');
        testEl.style.display = 'flex';
        return testEl.style.display === 'flex';
      });
      
      expect(flexSupport).toBe(true);
    });

    await context.close();
  });

  // 测试不同分辨率
  const resolutions = [
    { name: '1366x768', width: 1366, height: 768 },
    { name: '1920x1080', width: 1920, height: 1080 },
    { name: '2560x1440', width: 2560, height: 1440 },
    { name: '3840x2160', width: 3840, height: 2160 },
  ];

  resolutions.forEach(({ name, width, height }) => {
    test(`分辨率 ${name} - 布局测试`, async ({ browser }) => {
      const context = await browser.newContext({
        viewport: { width, height },
      });
      const page = await context.newPage();

      await test.step(`${name} - 内容适配`, async () => {
        await page.goto('/dashboard');
        
        // 检查内容是否正确显示
        await expect(page.locator('[data-testid="dashboard-content"]')).toBeVisible();
        
        // 检查是否有水平滚动条（通常不应该有）
        const hasHorizontalScroll = await page.evaluate(() => {
          return document.documentElement.scrollWidth > document.documentElement.clientWidth;
        });
        
        expect(hasHorizontalScroll).toBe(false);
      });

      await test.step(`${name} - 导航可用性`, async () => {
        // 确保导航元素在当前分辨率下可见且可用
        const navItems = page.locator('[data-testid^="nav-"]');
        const navCount = await navItems.count();
        
        expect(navCount).toBeGreaterThan(0);
        
        // 测试第一个导航项
        if (navCount > 0) {
          await navItems.first().click();
          // 验证导航成功
          await page.waitForLoadState('networkidle');
        }
      });

      await context.close();
    });
  });
});
