#!/usr/bin/env python3
"""
审计日志有效性验证
抽样校验登录、权限变更、敏感资源访问等关键事件是否写入审计日志
核对字段完整性和时间一致性，验证查询/导出权限与最小化可见性
"""
import os
import sys
import json
import logging
import requests
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import argparse
import random

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'backend'))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AuditLogValidator:
    """审计日志验证器"""
    
    def __init__(self, base_url: str, admin_token: str = None):
        self.base_url = base_url
        self.admin_token = admin_token
        self.test_results = []
        
        # 测试用户
        self.test_user = {
            'email': '<EMAIL>',
            'password': 'AuditTest123!',
            'username': 'audit_tester'
        }
        
        # 关键事件类型
        self.critical_events = [
            'user_login',
            'user_logout',
            'user_registration',
            'password_change',
            'role_change',
            'permission_grant',
            'permission_revoke',
            'sensitive_data_access',
            'admin_action',
            'data_export',
            'configuration_change',
            'security_event'
        ]
        
        # 必需的审计字段
        self.required_fields = [
            'timestamp',
            'event_type',
            'user_id',
            'user_email',
            'source_ip',
            'user_agent',
            'resource',
            'action',
            'result',
            'details'
        ]
    
    def validate_audit_log_completeness(self) -> Dict[str, Any]:
        """验证审计日志完整性"""
        logger.info("验证审计日志完整性...")
        
        results = {
            'test_name': '审计日志完整性验证',
            'timestamp': datetime.now().isoformat(),
            'tests': []
        }
        
        # 检查审计日志API是否存在
        try:
            headers = {}
            if self.admin_token:
                headers['Authorization'] = f'Bearer {self.admin_token}'
            
            response = requests.get(
                f"{self.base_url}/api/v1/admin/audit-logs",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                audit_logs = response.json()
                
                # 验证日志结构
                structure_test = self._validate_log_structure(audit_logs)
                results['tests'].append(structure_test)
                
                # 验证关键事件覆盖
                coverage_test = self._validate_event_coverage(audit_logs)
                results['tests'].append(coverage_test)
                
                # 验证字段完整性
                field_test = self._validate_field_completeness(audit_logs)
                results['tests'].append(field_test)
                
            elif response.status_code == 404:
                results['tests'].append({
                    'test_type': '审计日志API可用性',
                    'status': 'FAIL',
                    'details': '审计日志API不存在'
                })
            elif response.status_code == 403:
                results['tests'].append({
                    'test_type': '审计日志API可用性',
                    'status': 'FAIL',
                    'details': '无权限访问审计日志API'
                })
            else:
                results['tests'].append({
                    'test_type': '审计日志API可用性',
                    'status': 'FAIL',
                    'details': f'API返回错误状态码: {response.status_code}'
                })
                
        except Exception as e:
            results['tests'].append({
                'test_type': '审计日志API可用性',
                'status': 'ERROR',
                'details': f'API访问失败: {str(e)}'
            })
        
        return results
    
    def _validate_log_structure(self, audit_logs: Dict[str, Any]) -> Dict[str, Any]:
        """验证日志结构"""
        if not isinstance(audit_logs, dict) or 'logs' not in audit_logs:
            return {
                'test_type': '日志结构验证',
                'status': 'FAIL',
                'details': '审计日志响应结构不正确'
            }
        
        logs = audit_logs.get('logs', [])
        if not logs:
            return {
                'test_type': '日志结构验证',
                'status': 'WARN',
                'details': '审计日志为空，无法验证结构'
            }
        
        # 检查前10条日志的结构
        sample_logs = logs[:10]
        structure_issues = []
        
        for i, log_entry in enumerate(sample_logs):
            missing_fields = [field for field in self.required_fields if field not in log_entry]
            if missing_fields:
                structure_issues.append(f"日志{i+1}缺少字段: {missing_fields}")
        
        if structure_issues:
            return {
                'test_type': '日志结构验证',
                'status': 'FAIL',
                'details': f'发现结构问题: {"; ".join(structure_issues)}'
            }
        else:
            return {
                'test_type': '日志结构验证',
                'status': 'PASS',
                'details': f'检查了{len(sample_logs)}条日志，结构完整'
            }
    
    def _validate_event_coverage(self, audit_logs: Dict[str, Any]) -> Dict[str, Any]:
        """验证事件覆盖范围"""
        logs = audit_logs.get('logs', [])
        
        # 统计事件类型
        event_types = set()
        for log_entry in logs:
            event_type = log_entry.get('event_type')
            if event_type:
                event_types.add(event_type)
        
        # 检查关键事件覆盖
        missing_events = [event for event in self.critical_events if event not in event_types]
        covered_events = [event for event in self.critical_events if event in event_types]
        
        coverage_rate = len(covered_events) / len(self.critical_events) * 100
        
        if coverage_rate >= 80:
            status = 'PASS'
        elif coverage_rate >= 60:
            status = 'WARN'
        else:
            status = 'FAIL'
        
        return {
            'test_type': '关键事件覆盖验证',
            'status': status,
            'details': f'覆盖率: {coverage_rate:.1f}% ({len(covered_events)}/{len(self.critical_events)})',
            'covered_events': covered_events,
            'missing_events': missing_events
        }
    
    def _validate_field_completeness(self, audit_logs: Dict[str, Any]) -> Dict[str, Any]:
        """验证字段完整性"""
        logs = audit_logs.get('logs', [])
        
        if not logs:
            return {
                'test_type': '字段完整性验证',
                'status': 'SKIP',
                'details': '无日志数据可验证'
            }
        
        # 随机抽样检查
        sample_size = min(50, len(logs))
        sample_logs = random.sample(logs, sample_size)
        
        field_issues = []
        empty_field_counts = {field: 0 for field in self.required_fields}
        
        for log_entry in sample_logs:
            for field in self.required_fields:
                value = log_entry.get(field)
                if value is None or value == '' or value == {}:
                    empty_field_counts[field] += 1
        
        # 检查字段完整性
        for field, empty_count in empty_field_counts.items():
            empty_rate = empty_count / sample_size * 100
            if empty_rate > 10:  # 超过10%的记录该字段为空
                field_issues.append(f"{field}: {empty_rate:.1f}%为空")
        
        if field_issues:
            return {
                'test_type': '字段完整性验证',
                'status': 'FAIL',
                'details': f'字段完整性问题: {"; ".join(field_issues)}',
                'sample_size': sample_size
            }
        else:
            return {
                'test_type': '字段完整性验证',
                'status': 'PASS',
                'details': f'检查了{sample_size}条日志，字段完整性良好'
            }
    
    def test_audit_log_generation(self) -> Dict[str, Any]:
        """测试审计日志生成"""
        logger.info("测试审计日志生成...")
        
        results = {
            'test_name': '审计日志生成测试',
            'timestamp': datetime.now().isoformat(),
            'tests': []
        }
        
        # 执行一系列操作并验证是否生成了相应的审计日志
        test_operations = [
            {
                'name': '用户注册',
                'action': self._test_user_registration,
                'expected_event': 'user_registration'
            },
            {
                'name': '用户登录',
                'action': self._test_user_login,
                'expected_event': 'user_login'
            },
            {
                'name': '敏感资源访问',
                'action': self._test_sensitive_access,
                'expected_event': 'sensitive_data_access'
            }
        ]
        
        for operation in test_operations:
            try:
                # 记录操作前的时间
                before_time = datetime.now()
                
                # 执行操作
                operation_result = operation['action']()
                
                # 等待日志写入
                time.sleep(2)
                
                # 检查是否生成了相应的审计日志
                log_found = self._check_audit_log_exists(
                    operation['expected_event'],
                    before_time
                )
                
                if log_found:
                    results['tests'].append({
                        'operation': operation['name'],
                        'status': 'PASS',
                        'details': f'成功生成{operation["expected_event"]}审计日志'
                    })
                else:
                    results['tests'].append({
                        'operation': operation['name'],
                        'status': 'FAIL',
                        'details': f'未找到{operation["expected_event"]}审计日志'
                    })
                    
            except Exception as e:
                results['tests'].append({
                    'operation': operation['name'],
                    'status': 'ERROR',
                    'details': f'操作执行失败: {str(e)}'
                })
        
        return results
    
    def _test_user_registration(self) -> bool:
        """测试用户注册操作"""
        try:
            response = requests.post(
                f"{self.base_url}/api/v1/auth/register",
                json=self.test_user,
                timeout=10
            )
            return response.status_code in [200, 201, 409]  # 409表示用户已存在
        except Exception:
            return False
    
    def _test_user_login(self) -> bool:
        """测试用户登录操作"""
        try:
            response = requests.post(
                f"{self.base_url}/api/v1/auth/login",
                json={
                    'email': self.test_user['email'],
                    'password': self.test_user['password']
                },
                timeout=10
            )
            return response.status_code == 200
        except Exception:
            return False
    
    def _test_sensitive_access(self) -> bool:
        """测试敏感资源访问"""
        try:
            # 尝试访问管理员端点
            response = requests.get(
                f"{self.base_url}/api/v1/admin/users",
                timeout=10
            )
            return True  # 无论成功还是失败，都应该记录访问日志
        except Exception:
            return False
    
    def _check_audit_log_exists(self, event_type: str, after_time: datetime) -> bool:
        """检查指定时间后是否存在特定类型的审计日志"""
        try:
            headers = {}
            if self.admin_token:
                headers['Authorization'] = f'Bearer {self.admin_token}'
            
            # 获取最近的审计日志
            response = requests.get(
                f"{self.base_url}/api/v1/admin/audit-logs",
                headers=headers,
                params={
                    'limit': 100,
                    'after': after_time.isoformat()
                },
                timeout=10
            )
            
            if response.status_code != 200:
                return False
            
            audit_logs = response.json()
            logs = audit_logs.get('logs', [])
            
            # 查找匹配的日志
            for log_entry in logs:
                if log_entry.get('event_type') == event_type:
                    log_time = datetime.fromisoformat(log_entry.get('timestamp', ''))
                    if log_time >= after_time:
                        return True
            
            return False
            
        except Exception as e:
            logger.warning(f"检查审计日志失败: {e}")
            return False
    
    def test_audit_log_access_control(self) -> Dict[str, Any]:
        """测试审计日志访问控制"""
        logger.info("测试审计日志访问控制...")
        
        results = {
            'test_name': '审计日志访问控制测试',
            'timestamp': datetime.now().isoformat(),
            'tests': []
        }
        
        # 测试无认证访问
        try:
            response = requests.get(
                f"{self.base_url}/api/v1/admin/audit-logs",
                timeout=10
            )
            
            if response.status_code == 401:
                results['tests'].append({
                    'test_type': '无认证访问控制',
                    'status': 'PASS',
                    'details': '正确拒绝无认证访问'
                })
            else:
                results['tests'].append({
                    'test_type': '无认证访问控制',
                    'status': 'FAIL',
                    'details': f'未拒绝无认证访问，状态码: {response.status_code}'
                })
                
        except Exception as e:
            results['tests'].append({
                'test_type': '无认证访问控制',
                'status': 'ERROR',
                'details': f'测试失败: {str(e)}'
            })
        
        # 测试普通用户访问（如果有普通用户token）
        # 这里可以扩展测试普通用户是否能访问审计日志
        
        # 测试数据最小化可见性
        if self.admin_token:
            try:
                headers = {'Authorization': f'Bearer {self.admin_token}'}
                response = requests.get(
                    f"{self.base_url}/api/v1/admin/audit-logs",
                    headers=headers,
                    params={'limit': 10},
                    timeout=10
                )
                
                if response.status_code == 200:
                    audit_logs = response.json()
                    logs = audit_logs.get('logs', [])
                    
                    # 检查是否包含敏感信息
                    sensitive_data_found = False
                    for log_entry in logs:
                        details = str(log_entry.get('details', {})).lower()
                        if any(keyword in details for keyword in ['password', 'secret', 'key', 'token']):
                            sensitive_data_found = True
                            break
                    
                    if sensitive_data_found:
                        results['tests'].append({
                            'test_type': '敏感信息最小化',
                            'status': 'FAIL',
                            'details': '审计日志中发现敏感信息'
                        })
                    else:
                        results['tests'].append({
                            'test_type': '敏感信息最小化',
                            'status': 'PASS',
                            'details': '审计日志未泄露敏感信息'
                        })
                else:
                    results['tests'].append({
                        'test_type': '敏感信息最小化',
                        'status': 'SKIP',
                        'details': '无法获取审计日志进行检查'
                    })
                    
            except Exception as e:
                results['tests'].append({
                    'test_type': '敏感信息最小化',
                    'status': 'ERROR',
                    'details': f'测试失败: {str(e)}'
                })
        
        return results
    
    def test_audit_log_integrity(self) -> Dict[str, Any]:
        """测试审计日志完整性"""
        logger.info("测试审计日志完整性...")
        
        results = {
            'test_name': '审计日志完整性测试',
            'timestamp': datetime.now().isoformat(),
            'tests': []
        }
        
        if not self.admin_token:
            results['tests'].append({
                'test_type': '时间一致性验证',
                'status': 'SKIP',
                'details': '需要管理员权限'
            })
            return results
        
        try:
            headers = {'Authorization': f'Bearer {self.admin_token}'}
            response = requests.get(
                f"{self.base_url}/api/v1/admin/audit-logs",
                headers=headers,
                params={'limit': 100},
                timeout=10
            )
            
            if response.status_code != 200:
                results['tests'].append({
                    'test_type': '时间一致性验证',
                    'status': 'ERROR',
                    'details': '无法获取审计日志'
                })
                return results
            
            audit_logs = response.json()
            logs = audit_logs.get('logs', [])
            
            if not logs:
                results['tests'].append({
                    'test_type': '时间一致性验证',
                    'status': 'SKIP',
                    'details': '无审计日志数据'
                })
                return results
            
            # 验证时间戳格式和一致性
            time_issues = []
            for i, log_entry in enumerate(logs[:20]):  # 检查前20条
                timestamp_str = log_entry.get('timestamp')
                if not timestamp_str:
                    time_issues.append(f"日志{i+1}缺少时间戳")
                    continue
                
                try:
                    timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                    # 检查时间戳是否合理（不能是未来时间，不能太久远）
                    now = datetime.now()
                    if timestamp > now:
                        time_issues.append(f"日志{i+1}时间戳为未来时间")
                    elif (now - timestamp).days > 365:
                        time_issues.append(f"日志{i+1}时间戳过于久远")
                except ValueError:
                    time_issues.append(f"日志{i+1}时间戳格式无效")
            
            if time_issues:
                results['tests'].append({
                    'test_type': '时间一致性验证',
                    'status': 'FAIL',
                    'details': f'发现时间问题: {"; ".join(time_issues[:5])}'  # 只显示前5个问题
                })
            else:
                results['tests'].append({
                    'test_type': '时间一致性验证',
                    'status': 'PASS',
                    'details': f'检查了{min(20, len(logs))}条日志，时间戳一致性良好'
                })
                
        except Exception as e:
            results['tests'].append({
                'test_type': '时间一致性验证',
                'status': 'ERROR',
                'details': f'测试失败: {str(e)}'
            })
        
        return results
    
    def run_all_audit_tests(self) -> Dict[str, Any]:
        """运行所有审计日志测试"""
        logger.info("开始运行所有审计日志验证测试...")
        
        all_results = {
            'timestamp': datetime.now().isoformat(),
            'test_suite': '审计日志有效性验证',
            'tests': []
        }
        
        # 运行各项测试
        all_results['tests'].append(self.validate_audit_log_completeness())
        all_results['tests'].append(self.test_audit_log_generation())
        all_results['tests'].append(self.test_audit_log_access_control())
        all_results['tests'].append(self.test_audit_log_integrity())
        
        # 计算总体结果
        total_tests = 0
        passed_tests = 0
        failed_tests = 0
        
        for test_group in all_results['tests']:
            for test in test_group.get('tests', []):
                total_tests += 1
                if test['status'] == 'PASS':
                    passed_tests += 1
                elif test['status'] == 'FAIL':
                    failed_tests += 1
        
        all_results['summary'] = {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': failed_tests,
            'success_rate': (passed_tests / total_tests * 100) if total_tests > 0 else 0
        }
        
        return all_results
    
    def save_results(self, results: Dict[str, Any], output_file: str):
        """保存测试结果"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            logger.info(f"审计日志验证结果已保存: {output_file}")
        except Exception as e:
            logger.error(f"保存结果失败: {e}")


def main():
    parser = argparse.ArgumentParser(description='审计日志有效性验证')
    parser.add_argument('--base-url', default='http://localhost:8000', help='目标服务URL')
    parser.add_argument('--admin-token', help='管理员访问令牌')
    parser.add_argument('--output', default='audit_log_validation_results.json', help='输出文件')
    
    args = parser.parse_args()
    
    validator = AuditLogValidator(args.base_url, args.admin_token)
    
    try:
        results = validator.run_all_audit_tests()
        validator.save_results(results, args.output)
        
        # 输出摘要
        summary = results['summary']
        print(f"\n=== 审计日志验证摘要 ===")
        print(f"总测试数: {summary['total_tests']}")
        print(f"通过测试: {summary['passed_tests']}")
        print(f"失败测试: {summary['failed_tests']}")
        print(f"成功率: {summary['success_rate']:.1f}%")
        
        # 显示失败的测试
        failed_tests = []
        for test_group in results['tests']:
            for test in test_group.get('tests', []):
                if test['status'] == 'FAIL':
                    test_name = f"{test_group['test_name']} - "
                    if 'test_type' in test:
                        test_name += test['test_type']
                    elif 'operation' in test:
                        test_name += test['operation']
                    failed_tests.append(test_name)
        
        if failed_tests:
            print(f"\n❌ 失败的测试:")
            for i, test in enumerate(failed_tests, 1):
                print(f"{i}. {test}")
        else:
            print(f"\n✅ 所有测试通过!")
        
        print(f"\n📋 详细结果: {args.output}")
        
    except Exception as e:
        logger.error(f"审计日志验证失败: {e}")
        return 1
    
    return 0


if __name__ == '__main__':
    exit(main())
