"""
统一数据库配置管理
解决DATABASE_URL在多个文件中重复获取的问题
"""
import os
from typing import Optional
from urllib.parse import urlparse
import logging

logger = logging.getLogger(__name__)


class DatabaseConfig:
    """统一数据库配置管理类"""
    
    _instance = None
    _database_url = None
    _parsed_config = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._database_url is None:
            self._load_config()
    
    def _load_config(self):
        """加载数据库配置"""
        self._database_url = os.getenv("DATABASE_URL")
        if not self._database_url:
            raise ValueError("DATABASE_URL environment variable must be set")
        
        # 解析数据库URL
        try:
            parsed = urlparse(self._database_url)
            self._parsed_config = {
                'scheme': parsed.scheme,
                'username': parsed.username,
                'password': parsed.password,
                'hostname': parsed.hostname,
                'port': parsed.port,
                'database': parsed.path.lstrip('/') if parsed.path else None
            }
            logger.info(f"数据库配置加载成功: {parsed.scheme}://{parsed.hostname}:{parsed.port}/{self._parsed_config['database']}")
        except Exception as e:
            logger.error(f"数据库URL解析失败: {str(e)}")
            raise ValueError(f"Invalid DATABASE_URL format: {str(e)}")
    
    @property
    def url(self) -> str:
        """获取完整的数据库URL"""
        return self._database_url
    
    @property
    def scheme(self) -> str:
        """获取数据库类型（如postgresql, mysql等）"""
        return self._parsed_config['scheme']
    
    @property
    def host(self) -> str:
        """获取数据库主机地址"""
        return self._parsed_config['hostname']
    
    @property
    def port(self) -> Optional[int]:
        """获取数据库端口"""
        return self._parsed_config['port']
    
    @property
    def database(self) -> str:
        """获取数据库名称"""
        return self._parsed_config['database']
    
    @property
    def username(self) -> str:
        """获取数据库用户名"""
        return self._parsed_config['username']
    
    @property
    def password(self) -> str:
        """获取数据库密码"""
        return self._parsed_config['password']
    
    def get_connection_params(self) -> dict:
        """获取连接参数字典"""
        return {
            'host': self.host,
            'port': self.port,
            'database': self.database,
            'username': self.username,
            'password': self.password
        }
    
    def get_sqlalchemy_url(self) -> str:
        """获取SQLAlchemy格式的URL"""
        return self.url
    
    def get_alembic_url(self) -> str:
        """获取Alembic迁移使用的URL"""
        return self.url
    
    def is_postgresql(self) -> bool:
        """判断是否为PostgreSQL数据库"""
        return self.scheme.startswith('postgresql')
    
    def is_mysql(self) -> bool:
        """判断是否为MySQL数据库"""
        return self.scheme.startswith('mysql')
    
    def is_sqlite(self) -> bool:
        """判断是否为SQLite数据库"""
        return self.scheme.startswith('sqlite')
    
    def validate_connection(self) -> bool:
        """验证数据库连接配置是否有效"""
        try:
            # 基本格式验证
            if not self._database_url:
                return False
            
            if not self.host and not self.is_sqlite():
                return False
            
            if not self.database:
                return False
            
            return True
        except Exception as e:
            logger.error(f"数据库配置验证失败: {str(e)}")
            return False
    
    def get_pool_config(self) -> dict:
        """获取连接池配置"""
        # 根据数据库类型和网络环境返回优化的连接池配置
        base_config = {
            'pool_size': int(os.getenv('DB_POOL_SIZE', '10')),
            'max_overflow': int(os.getenv('DB_MAX_OVERFLOW', '20')),
            'pool_timeout': int(os.getenv('DB_POOL_TIMEOUT', '30')),
            'pool_recycle': int(os.getenv('DB_POOL_RECYCLE', '3600')),
            'pool_pre_ping': True
        }

        # 远程数据库优化配置（针对***********这样的远程MySQL）
        if self.host and self.host not in ['localhost', '127.0.0.1']:
            # 远程数据库连接池优化
            base_config.update({
                'pool_size': int(os.getenv('DB_POOL_SIZE', '3')),  # 远程连接减少到3个核心连接
                'max_overflow': int(os.getenv('DB_MAX_OVERFLOW', '7')),  # 最大10个连接
                'pool_timeout': int(os.getenv('DB_POOL_TIMEOUT', '60')),  # 网络延迟考虑，增加到60秒
                'pool_recycle': int(os.getenv('DB_POOL_RECYCLE', '1800')),  # 30分钟回收，避免长连接超时
                'pool_pre_ping': True,  # 远程连接必须启用预检查
                # MySQL远程连接优化参数
                'connect_args': {
                    'connect_timeout': 20,  # 连接超时20秒
                    'read_timeout': 30,     # 读取超时30秒
                    'write_timeout': 30,    # 写入超时30秒
                    'charset': 'utf8mb4',   # 使用utf8mb4字符集
                    'autocommit': True,     # 启用自动提交
                    'sql_mode': 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO',
                }
            })

            # 如果是MySQL，添加MySQL特定的优化
            if self.is_mysql():
                base_config['connect_args'].update({
                    'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
                    'use_unicode': True,
                    'binary_prefix': True,
                })

        # Docker环境特殊处理
        if os.getenv('DOCKER_ENV') == 'true':
            # Docker容器内访问远程数据库的优化
            base_config.update({
                'pool_size': int(os.getenv('DB_POOL_SIZE', '2')),  # Docker环境进一步减少连接
                'max_overflow': int(os.getenv('DB_MAX_OVERFLOW', '5')),
                'pool_timeout': int(os.getenv('DB_POOL_TIMEOUT', '90')),  # Docker网络可能更慢
            })

        return base_config


# 全局实例
database_config = DatabaseConfig()


# 便捷函数
def get_database_url() -> str:
    """获取数据库URL（统一入口）"""
    return database_config.url


def get_database_config() -> DatabaseConfig:
    """获取数据库配置实例"""
    return database_config


def validate_database_config() -> bool:
    """验证数据库配置"""
    return database_config.validate_connection()


def get_connection_info() -> dict:
    """获取数据库连接信息（用于日志和监控）"""
    return {
        'type': database_config.scheme,
        'host': database_config.host,
        'port': database_config.port,
        'database': database_config.database,
        'is_remote': database_config.host not in ['localhost', '127.0.0.1'] if database_config.host else False
    }


# 向后兼容的函数
def get_db_url() -> str:
    """获取数据库URL（向后兼容）"""
    return get_database_url()


def check_database_url() -> str:
    """检查并返回数据库URL（向后兼容）"""
    url = get_database_url()
    if not url:
        raise ValueError("DATABASE_URL environment variable must be set")
    return url
