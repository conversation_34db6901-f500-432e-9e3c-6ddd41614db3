"""
推送任务模块
实现异步推送任务和调度
"""
import logging
from typing import List, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import or_, desc
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../'))

from app.celery_app import celery_app
from app.database import get_db
from app.models.user import User
from app.models.subscription import Subscription
from app.models.news import News
from app.models.push_log import PushLog
from app.services.push_service import push_service, PushChannel, PushMessage, MessageType
from app.services.push_providers.wechat_work_provider import WeChatWorkProvider
from app.services.push_providers.feishu_provider import FeishuProvider
from app.services.push_providers.email_provider import EmailProvider
from app.services.push_providers.wechat_group_provider import WeChatGroupProvider
from app.services.news_service import NewsService
from app.config import settings

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 初始化推送提供商
def initialize_push_providers():
    """初始化所有推送提供商"""
    try:
        # 企业微信提供商
        wechat_work_config = {
            'enabled': True,
            'timeout': 30,
            'max_content_length': 4096
        }
        push_service.register_provider(
            PushChannel.WECHAT_WORK,
            WeChatWorkProvider(wechat_work_config)
        )
        
        # 飞书提供商
        feishu_config = {
            'enabled': True,
            'timeout': 30,
            'max_content_length': 8192
        }
        push_service.register_provider(
            PushChannel.FEISHU,
            FeishuProvider(feishu_config)
        )
        
        # 邮件提供商
        email_config = {
            'enabled': True,
            'smtp_server': 'smtp.gmail.com',
            'smtp_port': 587,
            'username': '',  # 需要配置
            'password': '',  # 需要配置
            'use_tls': True,
            'sender_name': '财经新闻Bot',
            'timeout': 30
        }
        push_service.register_provider(
            PushChannel.EMAIL,
            EmailProvider(email_config)
        )
        
        # 微信群提供商
        wechat_group_config = {
            'enabled': True,
            'timeout': 30,
            'max_content_length': 2048,
            'rate_limit_delay': 1
        }
        push_service.register_provider(
            PushChannel.WECHAT_GROUP,
            WeChatGroupProvider(wechat_group_config)
        )
        
        logger.info("推送提供商初始化完成")
        
    except Exception as e:
        logger.error(f"推送提供商初始化失败: {str(e)}")

# 在模块加载时初始化
initialize_push_providers()

@celery_app.task(bind=True, max_retries=3)
def send_subscription_notifications(self):
    """
    发送订阅通知任务
    检查所有活跃订阅，发送符合条件的新闻
    """
    try:
        logger.info("开始执行订阅通知推送任务")
        
        # 获取数据库会话
        db = next(get_db())
        
        # 获取所有活跃订阅
        active_subscriptions = db.query(Subscription).filter(
            Subscription.is_active == True
        ).all()
        
        if not active_subscriptions:
            logger.info("没有活跃的订阅")
            return {"status": "no_subscriptions", "count": 0}
        
        total_sent = 0
        success_count = 0
        
        for subscription in active_subscriptions:
            try:
                # 检查推送时间
                if not _should_push_now(subscription):
                    continue
                
                # 获取用户
                user = db.query(User).filter(User.id == subscription.user_id).first()
                if not user:
                    continue
                
                # 获取符合条件的新闻
                news_list = _get_subscription_news(db, subscription)
                
                if not news_list:
                    continue
                
                # 发送推送（同步调用，传递数据库会话）
                import asyncio
                result = asyncio.run(push_service.send_news_notification(user, subscription, news_list, db))
                
                # 记录推送日志
                _log_push_result(db, subscription, news_list, result)
                
                if result.get('success_count', 0) > 0:
                    success_count += 1
                
                total_sent += 1
                
            except Exception as e:
                logger.error(f"处理订阅 {subscription.id} 时出错: {str(e)}")
                continue
        
        logger.info(f"订阅通知推送完成，处理了 {total_sent} 个订阅，成功 {success_count} 个")
        
        return {
            "status": "completed",
            "total_processed": total_sent,
            "success_count": success_count,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as exc:
        logger.error(f"订阅通知推送失败: {str(exc)}")
        if self.request.retries < self.max_retries:
            logger.info(f"将在60秒后重试，当前重试次数: {self.request.retries + 1}")
            raise self.retry(countdown=60, exc=exc)
        else:
            return {
                "status": "error",
                "message": f"订阅通知推送失败: {str(exc)}",
                "count": 0
            }
    finally:
        if 'db' in locals():
            db.close()

@celery_app.task(bind=True, max_retries=3)
def send_urgent_news_alert(self, news_id: int):
    """
    发送紧急新闻警报
    
    Args:
        news_id: 新闻ID
    """
    try:
        logger.info(f"开始发送紧急新闻警报: {news_id}")
        
        # 获取数据库会话
        db = next(get_db())
        news_service = NewsService(db)
        
        # 获取新闻
        news = news_service.get_news_by_id(news_id)
        if not news:
            return {"status": "news_not_found", "news_id": news_id}
        
        # 检查是否为紧急新闻
        if news.importance_score < 90:
            return {"status": "not_urgent", "importance_score": news.importance_score}
        
        # 获取所有启用紧急推送的订阅
        urgent_subscriptions = db.query(Subscription).filter(
            Subscription.is_active == True,
            Subscription.push_config.contains('"urgent_push": true')
        ).all()
        
        if not urgent_subscriptions:
            return {"status": "no_urgent_subscriptions", "count": 0}
        
        success_count = 0
        total_count = 0
        
        for subscription in urgent_subscriptions:
            try:
                # 获取用户
                user = db.query(User).filter(User.id == subscription.user_id).first()
                if not user:
                    continue
                
                # 发送紧急推送（同步调用，传递数据库会话）
                import asyncio
                result = asyncio.run(push_service.send_news_notification(user, subscription, [news], db))
                
                # 记录推送日志
                _log_push_result(db, subscription, [news], result, is_urgent=True)
                
                if result.get('success_count', 0) > 0:
                    success_count += 1
                
                total_count += 1
                
            except Exception as e:
                logger.error(f"发送紧急推送到订阅 {subscription.id} 失败: {str(e)}")
                continue
        
        logger.info(f"紧急新闻警报发送完成，处理了 {total_count} 个订阅，成功 {success_count} 个")
        
        return {
            "status": "completed",
            "news_id": news_id,
            "total_sent": total_count,
            "success_count": success_count
        }
        
    except Exception as exc:
        logger.error(f"发送紧急新闻警报失败: {str(exc)}")
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=30, exc=exc)
        else:
            return {
                "status": "error",
                "message": f"发送紧急新闻警报失败: {str(exc)}",
                "news_id": news_id
            }
    finally:
        if 'db' in locals():
            db.close()

@celery_app.task(bind=True)
def send_custom_message(self, channel: str, target: str, title: str, content: str, message_type: str = "text"):
    """
    发送自定义消息任务
    
    Args:
        channel: 推送渠道
        target: 推送目标
        title: 消息标题
        content: 消息内容
        message_type: 消息类型
    """
    try:
        logger.info(f"发送自定义消息: {channel} -> {target}")
        
        # 创建推送消息
        message = PushMessage(
            title=title,
            content=content,
            message_type=MessageType(message_type)
        )
        
        # 发送消息（同步调用）
        import asyncio
        result = asyncio.run(push_service.send_single_message(
            PushChannel(channel),
            target,
            message
        ))
        
        return result.to_dict()
        
    except Exception as exc:
        logger.error(f"发送自定义消息失败: {str(exc)}")
        return {
            "success": False,
            "message": f"发送失败: {str(exc)}",
            "error_code": "TASK_ERROR"
        }

@celery_app.task(bind=True)
def cleanup_old_push_logs(self, days: int = 30):
    """
    清理旧的推送日志
    
    Args:
        days: 保留天数
    """
    try:
        logger.info(f"开始清理 {days} 天前的推送日志")
        
        # 获取数据库会话
        db = next(get_db())
        
        # 计算截止时间
        cutoff_date = datetime.now() - timedelta(days=days)
        
        # 删除旧日志
        deleted_count = db.query(PushLog).filter(
            PushLog.created_at < cutoff_date
        ).delete()
        
        db.commit()
        
        logger.info(f"清理完成，删除了 {deleted_count} 条推送日志")
        
        return {
            "status": "success",
            "deleted_count": deleted_count,
            "cutoff_date": cutoff_date.isoformat()
        }
        
    except Exception as exc:
        logger.error(f"清理推送日志失败: {str(exc)}")
        return {
            "status": "error",
            "message": f"清理失败: {str(exc)}"
        }
    finally:
        if 'db' in locals():
            db.close()

def _should_push_now(subscription: Subscription) -> bool:
    """
    检查是否应该现在推送
    
    Args:
        subscription: 订阅对象
    
    Returns:
        是否应该推送
    """
    push_config = subscription.push_config or {}
    
    # 检查推送频率
    frequency = push_config.get('frequency', 'daily')
    last_push = subscription.last_push_at
    
    if not last_push:
        return True
    
    now = datetime.now()
    
    if frequency == 'realtime':
        return True
    elif frequency == 'hourly':
        return (now - last_push).total_seconds() >= 3600
    elif frequency == 'daily':
        return (now - last_push).total_seconds() >= 86400
    elif frequency == 'weekly':
        return (now - last_push).total_seconds() >= 604800
    
    return False

def _get_subscription_news(db: Session, subscription: Subscription) -> List[News]:
    """
    获取订阅相关的新闻
    
    Args:
        db: 数据库会话
        subscription: 订阅对象
    
    Returns:
        新闻列表
    """
    # 获取上次推送时间
    last_push = subscription.last_push_at or (datetime.now() - timedelta(days=1))
    
    # 构建查询
    query = db.query(News).filter(
        News.created_at > last_push,
        News.is_processed == True
    )
    
    # 应用订阅过滤条件
    if subscription.keywords:
        keyword_conditions = []
        for keyword in subscription.keywords:
            keyword_conditions.append(News.title.contains(keyword))
            keyword_conditions.append(News.content.contains(keyword))
        query = query.filter(or_(*keyword_conditions))
    
    if subscription.companies:
        company_conditions = []
        for company in subscription.companies:
            company_conditions.append(News.companies.contains(company))
        query = query.filter(or_(*company_conditions))
    
    if subscription.categories:
        query = query.filter(News.category.in_(subscription.categories))
    
    if subscription.sources:
        query = query.filter(News.source.in_(subscription.sources))
    
    # 按重要性排序
    query = query.order_by(desc(News.importance_score), desc(News.published_at))
    
    # 限制数量
    max_news = subscription.push_config.get('max_news_per_push', 10)
    news_list = query.limit(max_news).all()
    
    return news_list

def _log_push_result(db: Session, subscription: Subscription, news_list: List[News], 
                    result: Dict[str, Any], is_urgent: bool = False):
    """
    记录推送结果
    
    Args:
        db: 数据库会话
        subscription: 订阅对象
        news_list: 新闻列表
        result: 推送结果
        is_urgent: 是否为紧急推送
    """
    try:
        # 创建推送日志
        push_log = PushLog(
            user_id=subscription.user_id,
            subscription_id=subscription.id,
            news_count=len(news_list),
            push_channels=list(result.get('results', {}).keys()),
            success_count=result.get('success_count', 0),
            total_count=result.get('total_count', 0),
            is_urgent=is_urgent,
            push_result=result,
            created_at=datetime.now()
        )
        
        db.add(push_log)
        
        # 更新订阅的最后推送时间
        subscription.last_push_at = datetime.now()
        
        db.commit()
        
    except Exception as e:
        logger.error(f"记录推送日志失败: {str(e)}")
        db.rollback()
