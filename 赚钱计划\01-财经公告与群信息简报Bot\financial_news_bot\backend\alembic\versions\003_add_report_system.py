"""Add report and push system

Revision ID: 003_add_report_system
Revises: 002_add_permission_system
Create Date: 2025-08-18 14:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '003_add_report_system'
down_revision = '002_add_permission_system'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """升级数据库结构以支持简报和推送系统"""
    
    # 1. 创建简报模板表
    op.create_table('report_templates',
        sa.Column('id', sa.Integer(), nullable=False, autoincrement=True),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('report_type', sa.Enum('morning', 'evening', 'hourly', 'special', name='reporttype'), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('template_config', sa.J<PERSON>(), nullable=False),
        sa.Column('sections_config', sa.JSON(), nullable=False),
        sa.Column('style_config', sa.JSON(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),
        sa.Column('is_default', sa.Boolean(), nullable=False, default=False),
        sa.Column('version', sa.String(length=20), nullable=False, default='1.0'),
        sa.Column('created_at', sa.TIMESTAMP(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('updated_at', sa.TIMESTAMP(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ondelete='SET NULL'),
        sa.Index('idx_report_templates_type', 'report_type'),
        sa.Index('idx_report_templates_active', 'is_active')
    )
    
    # 2. 创建简报表
    op.create_table('reports',
        sa.Column('id', sa.Integer(), nullable=False, autoincrement=True),
        sa.Column('title', sa.String(length=255), nullable=False),
        sa.Column('type', sa.Enum('morning', 'evening', 'hourly', 'special', name='reporttype'), nullable=False),
        sa.Column('status', sa.Enum('draft', 'generating', 'completed', 'published', 'failed', name='reportstatus'), nullable=False, default='draft'),
        sa.Column('priority', sa.Enum('low', 'medium', 'high', 'urgent', name='reportpriority'), nullable=False, default='medium'),
        sa.Column('report_date', sa.DateTime(), nullable=False),
        sa.Column('generated_at', sa.DateTime(), nullable=True),
        sa.Column('published_at', sa.DateTime(), nullable=True),
        sa.Column('summary', sa.Text(), nullable=True),
        sa.Column('content', sa.Text(), nullable=True),
        sa.Column('template_id', sa.Integer(), nullable=True),
        sa.Column('metadata', sa.JSON(), nullable=True),
        sa.Column('statistics', sa.JSON(), nullable=True),
        sa.Column('generation_config', sa.JSON(), nullable=True),
        sa.Column('generation_time', sa.Integer(), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('created_at', sa.TIMESTAMP(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('updated_at', sa.TIMESTAMP(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['template_id'], ['report_templates.id'], ondelete='SET NULL'),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ondelete='SET NULL'),
        sa.Index('idx_reports_type', 'type'),
        sa.Index('idx_reports_status', 'status'),
        sa.Index('idx_reports_date', 'report_date')
    )
    
    # 3. 创建简报章节表
    op.create_table('report_sections',
        sa.Column('id', sa.Integer(), nullable=False, autoincrement=True),
        sa.Column('report_id', sa.Integer(), nullable=False),
        sa.Column('section_name', sa.String(length=100), nullable=False),
        sa.Column('section_type', sa.String(length=50), nullable=False),
        sa.Column('order_index', sa.Integer(), nullable=False),
        sa.Column('title', sa.String(length=255), nullable=False),
        sa.Column('content', sa.Text(), nullable=True),
        sa.Column('summary', sa.Text(), nullable=True),
        sa.Column('source_data', sa.JSON(), nullable=True),
        sa.Column('news_ids', sa.JSON(), nullable=True),
        sa.Column('generation_config', sa.JSON(), nullable=True),
        sa.Column('ai_generated', sa.Boolean(), nullable=False, default=False),
        sa.Column('word_count', sa.Integer(), nullable=True),
        sa.Column('read_time', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.TIMESTAMP(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('updated_at', sa.TIMESTAMP(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['report_id'], ['reports.id'], ondelete='CASCADE'),
        sa.Index('idx_report_sections_report_id', 'report_id'),
        sa.Index('idx_report_sections_order', 'order_index')
    )
    
    # 4. 创建简报订阅表
    op.create_table('report_subscriptions',
        sa.Column('id', sa.Integer(), nullable=False, autoincrement=True),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('report_types', sa.JSON(), nullable=False),
        sa.Column('delivery_time', sa.JSON(), nullable=True),
        sa.Column('delivery_channels', sa.JSON(), nullable=True),
        sa.Column('personalization_config', sa.JSON(), nullable=True),
        sa.Column('filter_config', sa.JSON(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),
        sa.Column('last_delivered_at', sa.DateTime(), nullable=True),
        sa.Column('created_at', sa.TIMESTAMP(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('updated_at', sa.TIMESTAMP(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.Index('idx_report_subscriptions_user_id', 'user_id'),
        sa.Index('idx_report_subscriptions_active', 'is_active')
    )
    
    # 5. 创建简报推送日志表
    op.create_table('report_delivery_logs',
        sa.Column('id', sa.Integer(), nullable=False, autoincrement=True),
        sa.Column('report_id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('delivery_channel', sa.String(length=50), nullable=False),
        sa.Column('delivery_status', sa.String(length=20), nullable=False),
        sa.Column('delivery_time', sa.DateTime(), nullable=False),
        sa.Column('success', sa.Boolean(), nullable=False),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('response_data', sa.JSON(), nullable=True),
        sa.Column('opened_at', sa.DateTime(), nullable=True),
        sa.Column('read_duration', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.TIMESTAMP(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['report_id'], ['reports.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.Index('idx_report_delivery_logs_report_id', 'report_id'),
        sa.Index('idx_report_delivery_logs_user_id', 'user_id'),
        sa.Index('idx_report_delivery_logs_status', 'delivery_status')
    )
    
    # 6. 创建简报反馈表
    op.create_table('report_feedback',
        sa.Column('id', sa.Integer(), nullable=False, autoincrement=True),
        sa.Column('report_id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('rating', sa.Integer(), nullable=True),
        sa.Column('feedback_type', sa.String(length=50), nullable=True),
        sa.Column('content', sa.Text(), nullable=True),
        sa.Column('tags', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.TIMESTAMP(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('updated_at', sa.TIMESTAMP(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['report_id'], ['reports.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.Index('idx_report_feedback_report_id', 'report_id'),
        sa.Index('idx_report_feedback_user_id', 'user_id'),
        sa.Index('idx_report_feedback_rating', 'rating')
    )


def downgrade() -> None:
    """回滚简报和推送系统的数据库结构"""
    
    # 按相反顺序删除表
    op.drop_table('report_feedback')
    op.drop_table('report_delivery_logs')
    op.drop_table('report_subscriptions')
    op.drop_table('report_sections')
    op.drop_table('reports')
    op.drop_table('report_templates')
    
    # 删除枚举类型
    op.execute("DROP TYPE IF EXISTS reporttype")
    op.execute("DROP TYPE IF EXISTS reportstatus")
    op.execute("DROP TYPE IF EXISTS reportpriority")
