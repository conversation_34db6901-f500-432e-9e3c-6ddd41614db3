from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum


class SubscriptionStatus(str, Enum):
    ACTIVE = "active"
    PAUSED = "paused"


class PushChannel(str, Enum):
    WECHAT = "wechat"
    FEISHU = "feishu"
    EMAIL = "email"
    WEBHOOK = "webhook"


class NewsCategory(str, Enum):
    ANNOUNCEMENT = "announcement"  # 公告
    REGULATION = "regulation"      # 监管
    MARKET = "market"             # 市场动态
    FINANCE = "finance"           # 财务报告
    GOVERNANCE = "governance"     # 公司治理
    OTHER = "other"              # 其他


class KeywordConfig(BaseModel):
    """关键词配置"""
    keyword: str = Field(..., min_length=1, max_length=100, description="关键词")
    weight: float = Field(default=1.0, ge=0.1, le=10.0, description="权重，范围0.1-10.0")
    
    class Config:
        schema_extra = {
            "example": {
                "keyword": "股票回购",
                "weight": 2.0
            }
        }


class CompanyConfig(BaseModel):
    """公司配置"""
    name: str = Field(..., min_length=1, max_length=200, description="公司名称")
    stock_code: Optional[str] = Field(None, max_length=20, description="股票代码")
    
    class Config:
        schema_extra = {
            "example": {
                "name": "中国平安",
                "stock_code": "000001"
            }
        }


class ChannelConfig(BaseModel):
    """推送渠道配置"""
    channel: PushChannel = Field(..., description="推送渠道")
    config: Dict[str, Any] = Field(..., description="渠道特定配置")
    enabled: bool = Field(default=True, description="是否启用")
    
    class Config:
        schema_extra = {
            "example": {
                "channel": "wechat",
                "config": {
                    "webhook_url": "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxx",
                    "mentioned_list": ["@all"]
                },
                "enabled": True
            }
        }


class ScheduleConfig(BaseModel):
    """推送时间安排配置"""
    enabled: bool = Field(default=True, description="是否启用定时推送")
    weekdays: List[int] = Field(default=[1, 2, 3, 4, 5], description="推送日期，1-7表示周一到周日")
    hours: List[int] = Field(default=[9, 12, 18], description="推送小时，0-23")
    timezone: str = Field(default="Asia/Shanghai", description="时区")
    
    @validator('weekdays')
    def validate_weekdays(cls, v):
        if not v:
            raise ValueError('weekdays cannot be empty')
        for day in v:
            if day < 1 or day > 7:
                raise ValueError('weekdays must be between 1 and 7')
        return sorted(list(set(v)))  # 去重并排序
    
    @validator('hours')
    def validate_hours(cls, v):
        if not v:
            raise ValueError('hours cannot be empty')
        for hour in v:
            if hour < 0 or hour > 23:
                raise ValueError('hours must be between 0 and 23')
        return sorted(list(set(v)))  # 去重并排序
    
    class Config:
        schema_extra = {
            "example": {
                "enabled": True,
                "weekdays": [1, 2, 3, 4, 5],
                "hours": [9, 12, 18],
                "timezone": "Asia/Shanghai"
            }
        }


class SubscriptionCreate(BaseModel):
    """创建订阅请求"""
    name: str = Field(..., min_length=1, max_length=100, description="订阅名称")
    keywords: Optional[List[KeywordConfig]] = Field(default=[], description="关键词列表")
    companies: Optional[List[CompanyConfig]] = Field(default=[], description="关注公司列表")
    categories: Optional[List[NewsCategory]] = Field(default=[], description="新闻分类列表")
    channels: List[ChannelConfig] = Field(..., min_items=1, description="推送渠道配置")
    schedule: Optional[ScheduleConfig] = Field(default_factory=ScheduleConfig, description="推送时间安排")
    
    @validator('keywords')
    def validate_keywords(cls, v):
        if v and len(v) > 50:  # 限制关键词数量
            raise ValueError('keywords count cannot exceed 50')
        return v
    
    @validator('companies')
    def validate_companies(cls, v):
        if v and len(v) > 100:  # 限制公司数量
            raise ValueError('companies count cannot exceed 100')
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "name": "科技股动态",
                "keywords": [
                    {"keyword": "人工智能", "weight": 2.0},
                    {"keyword": "芯片", "weight": 1.5}
                ],
                "companies": [
                    {"name": "腾讯控股", "stock_code": "00700"},
                    {"name": "阿里巴巴", "stock_code": "09988"}
                ],
                "categories": ["announcement", "market"],
                "channels": [
                    {
                        "channel": "wechat",
                        "config": {
                            "webhook_url": "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxx"
                        },
                        "enabled": True
                    }
                ],
                "schedule": {
                    "enabled": True,
                    "weekdays": [1, 2, 3, 4, 5],
                    "hours": [9, 18],
                    "timezone": "Asia/Shanghai"
                }
            }
        }


class SubscriptionUpdate(BaseModel):
    """更新订阅请求"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="订阅名称")
    keywords: Optional[List[KeywordConfig]] = Field(None, description="关键词列表")
    companies: Optional[List[CompanyConfig]] = Field(None, description="关注公司列表")
    categories: Optional[List[NewsCategory]] = Field(None, description="新闻分类列表")
    channels: Optional[List[ChannelConfig]] = Field(None, min_items=1, description="推送渠道配置")
    schedule: Optional[ScheduleConfig] = Field(None, description="推送时间安排")
    status: Optional[SubscriptionStatus] = Field(None, description="订阅状态")
    
    @validator('keywords')
    def validate_keywords(cls, v):
        if v is not None and len(v) > 50:
            raise ValueError('keywords count cannot exceed 50')
        return v
    
    @validator('companies')
    def validate_companies(cls, v):
        if v is not None and len(v) > 100:
            raise ValueError('companies count cannot exceed 100')
        return v


class SubscriptionResponse(BaseModel):
    """订阅响应"""
    id: int
    user_id: int
    name: str
    keywords: List[KeywordConfig]
    companies: List[CompanyConfig]
    categories: List[NewsCategory]
    channels: List[ChannelConfig]
    schedule: ScheduleConfig
    status: SubscriptionStatus
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True
        schema_extra = {
            "example": {
                "id": 1,
                "user_id": 1,
                "name": "科技股动态",
                "keywords": [
                    {"keyword": "人工智能", "weight": 2.0}
                ],
                "companies": [
                    {"name": "腾讯控股", "stock_code": "00700"}
                ],
                "categories": ["announcement", "market"],
                "channels": [
                    {
                        "channel": "wechat",
                        "config": {"webhook_url": "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxx"},
                        "enabled": True
                    }
                ],
                "schedule": {
                    "enabled": True,
                    "weekdays": [1, 2, 3, 4, 5],
                    "hours": [9, 18],
                    "timezone": "Asia/Shanghai"
                },
                "status": "active",
                "created_at": "2024-01-15T10:30:00",
                "updated_at": "2024-01-15T10:30:00"
            }
        }


class SubscriptionListResponse(BaseModel):
    """订阅列表响应"""
    subscriptions: List[SubscriptionResponse]
    total: int
    page: int
    size: int
    
    class Config:
        schema_extra = {
            "example": {
                "subscriptions": [],
                "total": 0,
                "page": 1,
                "size": 10
            }
        }


class SubscriptionStats(BaseModel):
    """订阅统计信息"""
    subscription_id: int
    total_news_matched: int = Field(default=0, description="匹配的新闻总数")
    total_pushes_sent: int = Field(default=0, description="发送的推送总数")
    successful_pushes: int = Field(default=0, description="成功的推送数")
    failed_pushes: int = Field(default=0, description="失败的推送数")
    last_push_at: Optional[datetime] = Field(None, description="最后推送时间")
    
    class Config:
        orm_mode = True
        schema_extra = {
            "example": {
                "subscription_id": 1,
                "total_news_matched": 150,
                "total_pushes_sent": 45,
                "successful_pushes": 43,
                "failed_pushes": 2,
                "last_push_at": "2024-01-15T18:00:00"
            }
        }
