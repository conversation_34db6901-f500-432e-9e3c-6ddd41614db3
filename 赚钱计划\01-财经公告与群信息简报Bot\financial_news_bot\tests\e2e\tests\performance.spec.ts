import { test, expect, Page } from '@playwright/test';

/**
 * 性能测试
 * 测试页面加载速度、响应时间等性能指标
 */
test.describe('性能测试', () => {
  test.beforeEach(async ({ page }) => {
    // 使用保存的认证状态
    await page.goto('/dashboard');
  });

  test('页面加载性能测试', async ({ page }) => {
    await test.step('首页加载性能', async () => {
      const startTime = Date.now();
      
      await page.goto('/dashboard');
      await page.waitForLoadState('networkidle');
      
      const loadTime = Date.now() - startTime;
      console.log(`首页加载时间: ${loadTime}ms`);
      
      // 验证加载时间在合理范围内（3秒以内）
      expect(loadTime).toBeLessThan(3000);
      
      // 检查Core Web Vitals
      const metrics = await page.evaluate(() => {
        return new Promise((resolve) => {
          new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const metrics: any = {};
            
            entries.forEach((entry) => {
              if (entry.name === 'first-contentful-paint') {
                metrics.FCP = entry.startTime;
              }
            });
            
            resolve(metrics);
          }).observe({ entryTypes: ['paint'] });
          
          // 如果没有获取到指标，5秒后返回空对象
          setTimeout(() => resolve({}), 5000);
        });
      });
      
      console.log('Core Web Vitals:', metrics);
    });

    await test.step('新闻列表加载性能', async () => {
      const startTime = Date.now();
      
      await page.goto('/news');
      await page.waitForSelector('[data-testid="news-list"]');
      
      const loadTime = Date.now() - startTime;
      console.log(`新闻列表加载时间: ${loadTime}ms`);
      
      expect(loadTime).toBeLessThan(2000);
      
      // 检查新闻项数量
      const newsItems = await page.locator('[data-testid="news-item"]').count();
      expect(newsItems).toBeGreaterThan(0);
    });

    await test.step('搜索响应性能', async () => {
      await page.goto('/news');
      
      // 打开搜索
      await page.keyboard.press('Control+k');
      await page.waitForSelector('[data-testid="search-modal"]');
      
      const startTime = Date.now();
      
      // 输入搜索关键词
      await page.fill('[data-testid="search-input"]', '央行');
      
      // 等待搜索结果
      await page.waitForSelector('[data-testid="search-results"]');
      
      const searchTime = Date.now() - startTime;
      console.log(`搜索响应时间: ${searchTime}ms`);
      
      // 搜索应该在1秒内响应
      expect(searchTime).toBeLessThan(1000);
    });
  });

  test('网络性能测试', async ({ page }) => {
    await test.step('API响应时间测试', async () => {
      // 监听网络请求
      const apiRequests: any[] = [];
      
      page.on('response', (response) => {
        if (response.url().includes('/api/')) {
          apiRequests.push({
            url: response.url(),
            status: response.status(),
            timing: response.timing(),
          });
        }
      });
      
      await page.goto('/news');
      await page.waitForLoadState('networkidle');
      
      // 分析API请求性能
      for (const request of apiRequests) {
        console.log(`API请求: ${request.url}`);
        console.log(`状态码: ${request.status}`);
        console.log(`响应时间: ${request.timing?.responseEnd - request.timing?.requestStart}ms`);
        
        // API响应时间应该在2秒内
        const responseTime = request.timing?.responseEnd - request.timing?.requestStart;
        expect(responseTime).toBeLessThan(2000);
      }
    });

    await test.step('资源加载性能测试', async () => {
      const resourceTimings = await page.evaluate(() => {
        const resources = performance.getEntriesByType('resource');
        return resources.map((resource: any) => ({
          name: resource.name,
          duration: resource.duration,
          size: resource.transferSize,
          type: resource.initiatorType,
        }));
      });
      
      console.log('资源加载统计:');
      
      // 分析不同类型资源的加载时间
      const resourceTypes = ['script', 'stylesheet', 'img', 'fetch'];
      
      resourceTypes.forEach(type => {
        const typeResources = resourceTimings.filter(r => r.type === type);
        if (typeResources.length > 0) {
          const avgDuration = typeResources.reduce((sum, r) => sum + r.duration, 0) / typeResources.length;
          const totalSize = typeResources.reduce((sum, r) => sum + (r.size || 0), 0);
          
          console.log(`${type}: 平均加载时间 ${avgDuration.toFixed(2)}ms, 总大小 ${(totalSize / 1024).toFixed(2)}KB`);
          
          // 资源加载时间不应超过5秒
          expect(avgDuration).toBeLessThan(5000);
        }
      });
    });
  });

  test('内存使用测试', async ({ page }) => {
    await test.step('内存泄漏检测', async () => {
      // 获取初始内存使用
      const initialMemory = await page.evaluate(() => {
        return (performance as any).memory ? {
          used: (performance as any).memory.usedJSHeapSize,
          total: (performance as any).memory.totalJSHeapSize,
          limit: (performance as any).memory.jsHeapSizeLimit,
        } : null;
      });
      
      if (initialMemory) {
        console.log('初始内存使用:', initialMemory);
        
        // 执行一系列操作
        for (let i = 0; i < 10; i++) {
          await page.goto('/news');
          await page.waitForLoadState('networkidle');
          await page.goto('/subscriptions');
          await page.waitForLoadState('networkidle');
          await page.goto('/dashboard');
          await page.waitForLoadState('networkidle');
        }
        
        // 强制垃圾回收（如果支持）
        await page.evaluate(() => {
          if ((window as any).gc) {
            (window as any).gc();
          }
        });
        
        // 获取最终内存使用
        const finalMemory = await page.evaluate(() => {
          return (performance as any).memory ? {
            used: (performance as any).memory.usedJSHeapSize,
            total: (performance as any).memory.totalJSHeapSize,
            limit: (performance as any).memory.jsHeapSizeLimit,
          } : null;
        });
        
        if (finalMemory) {
          console.log('最终内存使用:', finalMemory);
          
          const memoryIncrease = finalMemory.used - initialMemory.used;
          const memoryIncreasePercent = (memoryIncrease / initialMemory.used) * 100;
          
          console.log(`内存增长: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB (${memoryIncreasePercent.toFixed(2)}%)`);
          
          // 内存增长不应超过50%
          expect(memoryIncreasePercent).toBeLessThan(50);
        }
      }
    });
  });

  test('大数据量性能测试', async ({ page }) => {
    await test.step('新闻列表滚动性能', async () => {
      await page.goto('/news');
      
      // 等待初始新闻加载
      await page.waitForSelector('[data-testid="news-list"]');
      
      const startTime = Date.now();
      
      // 模拟快速滚动
      for (let i = 0; i < 10; i++) {
        await page.mouse.wheel(0, 1000);
        await page.waitForTimeout(100);
      }
      
      const scrollTime = Date.now() - startTime;
      console.log(`滚动性能测试时间: ${scrollTime}ms`);
      
      // 滚动应该保持流畅
      expect(scrollTime).toBeLessThan(5000);
      
      // 检查是否有新的新闻项加载
      const newsItemsAfterScroll = await page.locator('[data-testid="news-item"]').count();
      expect(newsItemsAfterScroll).toBeGreaterThan(10);
    });

    await test.step('搜索大量结果性能', async () => {
      await page.goto('/news');
      
      // 打开搜索
      await page.keyboard.press('Control+k');
      
      const startTime = Date.now();
      
      // 搜索常见关键词（可能返回大量结果）
      await page.fill('[data-testid="search-input"]', '政策');
      await page.waitForSelector('[data-testid="search-results"]');
      
      const searchTime = Date.now() - startTime;
      console.log(`大量结果搜索时间: ${searchTime}ms`);
      
      // 即使结果很多，搜索也应该在2秒内完成
      expect(searchTime).toBeLessThan(2000);
    });
  });

  test('移动端性能测试', async ({ browser }) => {
    // 创建移动端上下文
    const context = await browser.newContext({
      viewport: { width: 375, height: 667 },
      userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
    });
    
    const mobilePage = await context.newPage();

    await test.step('移动端页面加载性能', async () => {
      const startTime = Date.now();
      
      await mobilePage.goto('/dashboard');
      await mobilePage.waitForLoadState('networkidle');
      
      const loadTime = Date.now() - startTime;
      console.log(`移动端首页加载时间: ${loadTime}ms`);
      
      // 移动端加载时间可能稍长，但不应超过5秒
      expect(loadTime).toBeLessThan(5000);
    });

    await test.step('移动端触摸响应性能', async () => {
      await mobilePage.goto('/news');
      
      const startTime = Date.now();
      
      // 模拟触摸操作
      await mobilePage.touchscreen.tap(100, 200);
      await mobilePage.waitForTimeout(100);
      
      const touchTime = Date.now() - startTime;
      console.log(`触摸响应时间: ${touchTime}ms`);
      
      // 触摸响应应该非常快
      expect(touchTime).toBeLessThan(500);
    });

    await context.close();
  });

  test('并发用户性能测试', async ({ browser }) => {
    await test.step('模拟多用户并发访问', async () => {
      const contexts = [];
      const pages = [];
      
      // 创建5个并发用户
      for (let i = 0; i < 5; i++) {
        const context = await browser.newContext();
        const page = await context.newPage();
        contexts.push(context);
        pages.push(page);
      }
      
      const startTime = Date.now();
      
      // 所有用户同时访问首页
      await Promise.all(pages.map(page => 
        page.goto('/dashboard').then(() => page.waitForLoadState('networkidle'))
      ));
      
      const concurrentTime = Date.now() - startTime;
      console.log(`5个并发用户加载时间: ${concurrentTime}ms`);
      
      // 并发访问不应显著影响性能
      expect(concurrentTime).toBeLessThan(10000);
      
      // 清理
      await Promise.all(contexts.map(context => context.close()));
    });
  });
});
