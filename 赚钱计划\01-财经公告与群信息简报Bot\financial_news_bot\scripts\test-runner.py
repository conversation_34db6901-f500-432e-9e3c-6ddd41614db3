#!/usr/bin/env python3
"""
统一测试运行器
整合单元测试、集成测试、覆盖率报告等功能
"""
import os
import sys
import subprocess
import argparse
from pathlib import Path
import json

class TestRunner:
    """测试运行器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.backend_dir = self.project_root / "backend"
        self.tests_dir = self.project_root / "tests"
    
    def run_unit_tests(self, verbose=False, coverage=False):
        """运行单元测试"""
        print("🧪 运行单元测试...")
        
        cmd = ["python", "-m", "pytest"]
        
        # 指定单元测试文件
        unit_test_files = [
            "tests/unit/backend/test_unified_push_service.py",
            "tests/unit/backend/test_data_processing_pipeline.py",
            "tests/unit/backend/test_simplified_crawler_service.py",
            "tests/unit/backend/test_ai_service.py",
            "tests/unit/backend/test_integrated_monitoring_service.py",
            "tests/unit/backend/test_user_service.py"
        ]
        
        cmd.extend(unit_test_files)
        
        if verbose:
            cmd.append("-v")
        
        if coverage:
            cmd.extend([
                "--cov=app",
                "--cov-report=html:htmlcov",
                "--cov-report=term-missing",
                "--cov-report=json"
            ])
        
        result = subprocess.run(cmd, cwd=self.backend_dir)
        return result.returncode == 0
    
    def run_integration_tests(self, verbose=False):
        """运行集成测试"""
        print("🔗 运行集成测试...")
        
        cmd = ["python", "-m", "pytest", "tests/integration/backend/"]
        
        if verbose:
            cmd.append("-v")
        
        result = subprocess.run(cmd, cwd=self.backend_dir)
        return result.returncode == 0
    
    def run_all_tests(self, verbose=False, coverage=False):
        """运行所有测试"""
        print("🚀 运行所有测试...")
        
        cmd = ["python", "-m", "pytest", "tests/"]
        
        if verbose:
            cmd.append("-v")
        
        if coverage:
            cmd.extend([
                "--cov=app",
                "--cov-report=html:htmlcov",
                "--cov-report=term-missing",
                "--cov-report=json"
            ])
        
        result = subprocess.run(cmd, cwd=self.backend_dir)
        return result.returncode == 0
    
    def run_specific_test(self, test_file, verbose=False):
        """运行特定测试文件"""
        print(f"🎯 运行测试: {test_file}")
        
        cmd = ["python", "-m", "pytest", f"tests/{test_file}"]
        
        if verbose:
            cmd.append("-v")
        
        result = subprocess.run(cmd, cwd=self.backend_dir)
        return result.returncode == 0
    
    def generate_coverage_report(self):
        """生成覆盖率报告"""
        print("📊 生成覆盖率报告...")
        
        # 检查是否有覆盖率数据
        coverage_file = self.backend_dir / "coverage.json"
        if not coverage_file.exists():
            print("❌ 没有覆盖率数据，请先运行带覆盖率的测试")
            return False
        
        try:
            with open(coverage_file, 'r') as f:
                coverage_data = json.load(f)
            
            total_coverage = coverage_data.get("totals", {}).get("percent_covered", 0)
            covered_lines = coverage_data.get("totals", {}).get("covered_lines", 0)
            total_lines = coverage_data.get("totals", {}).get("num_statements", 0)
            
            print(f"📈 总体覆盖率: {total_coverage:.1f}%")
            print(f"📏 覆盖行数: {covered_lines}/{total_lines}")
            
            # 按文件显示覆盖率
            print("\n📁 文件覆盖率详情:")
            files = coverage_data.get("files", {})
            for file_path, file_data in files.items():
                if "app/" in file_path:  # 只显示应用代码
                    file_coverage = file_data.get("summary", {}).get("percent_covered", 0)
                    print(f"  {file_path}: {file_coverage:.1f}%")
            
            # 覆盖率评级
            if total_coverage >= 90:
                grade = "A (优秀)"
                emoji = "🎉"
            elif total_coverage >= 80:
                grade = "B (良好)"
                emoji = "✅"
            elif total_coverage >= 70:
                grade = "C (一般)"
                emoji = "⚠️"
            elif total_coverage >= 60:
                grade = "D (需改进)"
                emoji = "❌"
            else:
                grade = "F (较差)"
                emoji = "🚨"
            
            print(f"\n{emoji} 覆盖率评级: {grade}")
            
            # HTML报告位置
            html_report = self.backend_dir / "htmlcov" / "index.html"
            if html_report.exists():
                print(f"📄 详细HTML报告: {html_report}")
            
            return True
            
        except Exception as e:
            print(f"❌ 生成覆盖率报告失败: {str(e)}")
            return False
    
    def list_tests(self):
        """列出所有测试文件"""
        print("📋 测试文件列表:")
        
        # 单元测试
        print("\n🧪 单元测试:")
        unit_tests = [
            "test_unified_push_service.py",
            "test_data_processing_pipeline.py", 
            "test_simplified_crawler_service.py",
            "test_ai_service.py",
            "test_integrated_monitoring_service.py",
            "test_user_service.py"
        ]
        
        for test in unit_tests:
            test_file = self.tests_dir / test
            if test_file.exists():
                print(f"  ✅ {test}")
            else:
                print(f"  ❌ {test} (不存在)")
        
        # 集成测试
        print("\n🔗 集成测试:")
        integration_dir = self.tests_dir / "integration"
        if integration_dir.exists():
            for test_file in integration_dir.glob("test_*.py"):
                print(f"  ✅ integration/{test_file.name}")
        else:
            print("  ❌ integration目录不存在")
    
    def clean_test_artifacts(self):
        """清理测试产生的文件"""
        print("🧹 清理测试文件...")
        
        artifacts = [
            ".pytest_cache",
            "htmlcov",
            "coverage.json",
            ".coverage",
            "**/__pycache__",
            "**/*.pyc"
        ]
        
        for pattern in artifacts:
            for path in self.backend_dir.rglob(pattern):
                if path.is_file():
                    path.unlink()
                    print(f"删除文件: {path}")
                elif path.is_dir():
                    import shutil
                    shutil.rmtree(path)
                    print(f"删除目录: {path}")
        
        print("✅ 测试文件清理完成")
    
    def check_test_dependencies(self):
        """检查测试依赖"""
        print("🔍 检查测试依赖...")
        
        required_packages = [
            "pytest",
            "pytest-cov", 
            "pytest-asyncio",
            "pytest-mock"
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package.replace("-", "_"))
                print(f"✅ {package}")
            except ImportError:
                print(f"❌ {package}")
                missing_packages.append(package)
        
        if missing_packages:
            print(f"\n📦 缺少依赖包，请安装:")
            print(f"pip install {' '.join(missing_packages)}")
            return False
        else:
            print("\n✅ 所有测试依赖已安装")
            return True
    
    def run_test_suite(self, suite_type="all", verbose=False, coverage=False):
        """运行测试套件"""
        print(f"🚀 运行测试套件: {suite_type}")
        
        # 检查依赖
        if not self.check_test_dependencies():
            return False
        
        success = True
        
        if suite_type == "unit":
            success = self.run_unit_tests(verbose, coverage)
        elif suite_type == "integration":
            success = self.run_integration_tests(verbose)
        elif suite_type == "all":
            success = self.run_all_tests(verbose, coverage)
        else:
            print(f"❌ 未知的测试套件类型: {suite_type}")
            return False
        
        # 生成覆盖率报告
        if coverage and success:
            self.generate_coverage_report()
        
        return success

def main():
    parser = argparse.ArgumentParser(description="财经新闻Bot测试运行器")
    parser.add_argument("command", choices=[
        "unit", "integration", "all", "coverage", "list", "clean", "check", "file"
    ], help="要执行的命令")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    parser.add_argument("--coverage", "-c", action="store_true", help="生成覆盖率报告")
    parser.add_argument("--file", help="运行特定测试文件")
    
    args = parser.parse_args()
    runner = TestRunner()
    
    if args.command == "unit":
        success = runner.run_unit_tests(args.verbose, args.coverage)
    elif args.command == "integration":
        success = runner.run_integration_tests(args.verbose)
    elif args.command == "all":
        success = runner.run_all_tests(args.verbose, args.coverage)
    elif args.command == "coverage":
        success = runner.generate_coverage_report()
    elif args.command == "list":
        runner.list_tests()
        success = True
    elif args.command == "clean":
        runner.clean_test_artifacts()
        success = True
    elif args.command == "check":
        success = runner.check_test_dependencies()
    elif args.command == "file":
        if not args.file:
            print("❌ 请指定测试文件: --file test_example.py")
            success = False
        else:
            success = runner.run_specific_test(args.file, args.verbose)
    else:
        parser.print_help()
        success = False
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
