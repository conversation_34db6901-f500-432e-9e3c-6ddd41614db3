"""
微信群机器人推送提供商
支持微信群机器人消息推送
"""
import logging
import json
import re
from typing import Dict, Any
import httpx

from ..unified_push_service import BasePushProvider, PushMessage, PushResult, PushChannel, MessageType

logger = logging.getLogger(__name__)

class WeChatGroupProvider(BasePushProvider):
    """微信群机器人推送提供商"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.channel = PushChannel.WECHAT_GROUP
        self.timeout = config.get('timeout', 30)
        self.max_content_length = config.get('max_content_length', 2048)
        self.rate_limit_delay = config.get('rate_limit_delay', 1)  # 发送间隔（秒）
    
    def validate_target(self, target: str) -> bool:
        """
        验证微信群机器人Webhook地址是否有效
        
        Args:
            target: Webhook URL
        
        Returns:
            是否有效
        """
        if not target:
            return False
        
        # 微信群机器人webhook格式验证
        pattern = r'https://qyapi\.weixin\.qq\.com/cgi-bin/webhook/send\?key=[a-zA-Z0-9\-_]+'
        return bool(re.match(pattern, target))
    
    async def send_message(self, target: str, message: PushMessage) -> PushResult:
        """
        发送微信群消息
        
        Args:
            target: 微信群机器人Webhook URL
            message: 推送消息
        
        Returns:
            推送结果
        """
        try:
            # 根据消息类型格式化消息
            payload = self._format_message_payload(message)
            
            # 发送HTTP请求
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    target,
                    json=payload,
                    headers={'Content-Type': 'application/json'}
                )
                
                response_data = response.json()
                
                if response.status_code == 200 and response_data.get('errcode') == 0:
                    return PushResult(
                        success=True,
                        message="微信群消息发送成功",
                        response_data=response_data
                    )
                else:
                    error_msg = response_data.get('errmsg', '未知错误')
                    return PushResult(
                        success=False,
                        message=f"微信群消息发送失败: {error_msg}",
                        error_code=str(response_data.get('errcode', 'UNKNOWN')),
                        response_data=response_data
                    )
                    
        except httpx.TimeoutException:
            return PushResult(
                success=False,
                message="微信群消息发送超时",
                error_code="TIMEOUT"
            )
        except Exception as e:
            return PushResult(
                success=False,
                message=f"微信群消息发送异常: {str(e)}",
                error_code="EXCEPTION"
            )
    
    def _format_message_payload(self, message: PushMessage) -> Dict[str, Any]:
        """
        格式化微信群消息载荷
        
        Args:
            message: 推送消息
        
        Returns:
            微信群机器人API格式的消息载荷
        """
        # 截断过长的内容
        content = self._truncate_content(message.content)
        
        if message.message_type == MessageType.MARKDOWN:
            return self._format_markdown_message(message.title, content)
        elif message.message_type == MessageType.CARD:
            return self._format_card_message(message.title, content, message.extra_data)
        else:
            return self._format_text_message(message.title, content)
    
    def _format_text_message(self, title: str, content: str) -> Dict[str, Any]:
        """格式化文本消息"""
        full_content = f"{title}\n\n{content}" if title else content
        
        return {
            "msgtype": "text",
            "text": {
                "content": full_content,
                "mentioned_list": [],  # @所有人可以用["@all"]
                "mentioned_mobile_list": []
            }
        }
    
    def _format_markdown_message(self, title: str, content: str) -> Dict[str, Any]:
        """格式化Markdown消息"""
        # 微信群机器人支持的Markdown格式相对简单
        markdown_content = self._simplify_markdown(content)
        
        # 如果有标题，添加到Markdown内容中
        if title:
            markdown_content = f"# {title}\n\n{markdown_content}"
        
        return {
            "msgtype": "markdown",
            "markdown": {
                "content": markdown_content
            }
        }
    
    def _format_card_message(self, title: str, content: str, extra_data: Dict[str, Any]) -> Dict[str, Any]:
        """格式化卡片消息（使用图文消息）"""
        articles = []
        
        # 主文章
        main_article = {
            "title": title,
            "description": content[:100] + "..." if len(content) > 100 else content,
            "url": extra_data.get('url', ''),
            "picurl": extra_data.get('image_url', '')
        }
        articles.append(main_article)
        
        # 额外文章
        extra_articles = extra_data.get('articles', [])
        for article in extra_articles[:7]:  # 最多8篇文章
            articles.append({
                "title": article.get('title', ''),
                "description": article.get('description', ''),
                "url": article.get('url', ''),
                "picurl": article.get('image_url', '')
            })
        
        return {
            "msgtype": "news",
            "news": {
                "articles": articles
            }
        }
    
    def _simplify_markdown(self, markdown_content: str) -> str:
        """
        简化Markdown内容，适配微信群机器人
        
        Args:
            markdown_content: 原始Markdown内容
        
        Returns:
            简化后的Markdown内容
        """
        lines = markdown_content.split('\n')
        simplified_lines = []
        
        for line in lines:
            # 保留基本的Markdown格式
            if line.startswith('#'):
                # 标题
                simplified_lines.append(line)
            elif line.startswith('-') or line.startswith('•'):
                # 列表项
                simplified_lines.append(line)
            elif '**' in line:
                # 粗体文本
                simplified_lines.append(line)
            elif line.strip() == '---':
                # 分隔线
                simplified_lines.append(line)
            else:
                # 普通文本
                simplified_lines.append(line)
        
        return '\n'.join(simplified_lines)
    
    def _truncate_content(self, content: str) -> str:
        """
        截断过长的内容
        
        Args:
            content: 原始内容
        
        Returns:
            截断后的内容
        """
        if len(content) <= self.max_content_length:
            return content
        
        # 截断并添加省略号
        truncated = content[:self.max_content_length - 10]
        return truncated + "\n\n... (内容过长已截断)"
    
    def format_news_list_message(self, news_list, subscription_name: str) -> PushMessage:
        """
        格式化新闻列表为微信群消息
        
        Args:
            news_list: 新闻列表
            subscription_name: 订阅名称
        
        Returns:
            格式化后的推送消息
        """
        if not news_list:
            return PushMessage(
                title="📰 财经新闻推送",
                content="暂无新闻更新",
                message_type=MessageType.TEXT
            )
        
        # 构建简洁的Markdown格式消息
        title = f"📰 {subscription_name} 新闻推送"
        
        content_lines = [
            f"## 📊 推送概览",
            f"**新闻数量**: {len(news_list)} 条",
            f"**推送时间**: {news_list[0].created_at.strftime('%m-%d %H:%M')}",
            "",
            "## 🔥 重要新闻"
        ]
        
        # 显示重要新闻
        important_news = [n for n in news_list if n.importance_score >= 80][:3]
        if important_news:
            for i, news in enumerate(important_news, 1):
                content_lines.append(f"{i}. **{news.title}**")
                content_lines.append(f"   📊 {news.importance_score}分 | 🏢 {news.source}")
                content_lines.append("")
        else:
            # 如果没有重要新闻，显示前3条
            for i, news in enumerate(news_list[:3], 1):
                content_lines.append(f"{i}. **{news.title}**")
                content_lines.append(f"   📊 {news.importance_score}分 | 🏢 {news.source}")
                content_lines.append("")
        
        # 显示其他新闻数量
        remaining_count = len(news_list) - len(important_news) if important_news else len(news_list) - 3
        if remaining_count > 0:
            content_lines.append(f"📋 还有 **{remaining_count}** 条其他新闻")
            content_lines.append("")
        
        content_lines.extend([
            "---",
            "💼 财经新闻Bot | 智能推送"
        ])
        
        return PushMessage(
            title=title,
            content="\n".join(content_lines),
            message_type=MessageType.MARKDOWN
        )
    
    def create_simple_text_message(self, news_list, subscription_name: str) -> PushMessage:
        """
        创建简单文本消息（适用于频率限制严格的场景）
        
        Args:
            news_list: 新闻列表
            subscription_name: 订阅名称
        
        Returns:
            文本格式的推送消息
        """
        if not news_list:
            return PushMessage(
                title="📰 财经新闻推送",
                content="暂无新闻更新",
                message_type=MessageType.TEXT
            )
        
        title = f"📰 {subscription_name} 新闻推送"
        
        content_lines = [
            f"📊 本次推送: {len(news_list)} 条新闻",
            f"⏰ 推送时间: {news_list[0].created_at.strftime('%m-%d %H:%M')}",
            ""
        ]
        
        # 显示前5条新闻
        for i, news in enumerate(news_list[:5], 1):
            importance_icon = "🔥" if news.importance_score >= 80 else "📌"
            content_lines.append(f"{i}. {importance_icon} {news.title}")
            content_lines.append(f"   {news.source} | {news.importance_score}分")
            content_lines.append("")
        
        if len(news_list) > 5:
            content_lines.append(f"... 还有 {len(news_list) - 5} 条新闻")
            content_lines.append("")
        
        content_lines.append("💼 财经新闻Bot")
        
        return PushMessage(
            title=title,
            content="\n".join(content_lines),
            message_type=MessageType.TEXT
        )
    
    def create_at_all_message(self, news_list, subscription_name: str, urgent: bool = False) -> PushMessage:
        """
        创建@所有人的紧急消息
        
        Args:
            news_list: 新闻列表
            subscription_name: 订阅名称
            urgent: 是否为紧急消息
        
        Returns:
            @所有人的推送消息
        """
        # 筛选高重要性新闻
        urgent_news = [n for n in news_list if n.importance_score >= 90]
        
        if not urgent_news and not urgent:
            return self.format_news_list_message(news_list, subscription_name)
        
        title = f"🚨 {subscription_name} 重要财经新闻"
        
        content_lines = [
            "🚨 发现重要财经新闻，请关注！",
            ""
        ]
        
        display_news = urgent_news if urgent_news else news_list[:2]
        for news in display_news:
            content_lines.extend([
                f"🔥 {news.title}",
                f"📊 重要性: {news.importance_score}/100",
                f"🏢 来源: {news.source}",
                f"📅 时间: {news.published_at.strftime('%m-%d %H:%M')}",
                ""
            ])
        
        content_lines.extend([
            "💼 财经新闻Bot | 重要推送"
        ])
        
        message = PushMessage(
            title=title,
            content="\n".join(content_lines),
            message_type=MessageType.TEXT
        )
        
        # 添加@所有人的额外数据
        message.extra_data = {
            "mentioned_list": ["@all"],
            "mentioned_mobile_list": []
        }
        
        return message
    
    def _format_text_message_with_mentions(self, title: str, content: str, extra_data: Dict[str, Any]) -> Dict[str, Any]:
        """格式化带@功能的文本消息"""
        full_content = f"{title}\n\n{content}" if title else content
        
        return {
            "msgtype": "text",
            "text": {
                "content": full_content,
                "mentioned_list": extra_data.get("mentioned_list", []),
                "mentioned_mobile_list": extra_data.get("mentioned_mobile_list", [])
            }
        }
