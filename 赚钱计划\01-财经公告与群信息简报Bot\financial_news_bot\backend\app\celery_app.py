"""
Celery应用配置
集成Redis作为消息代理，配置任务路由和调度
"""
import os
from celery import Celery
from celery.schedules import crontab
from kombu import Queue

# 创建Celery应用实例
celery_app = Celery("financial_news_bot")

# Redis配置
REDIS_URL = os.getenv("REDIS_URL", "redis://redis:6379/0")

# Celery配置
celery_app.conf.update(
    # 消息代理配置
    broker_url=REDIS_URL,
    result_backend=REDIS_URL,
    
    # 任务序列化
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='Asia/Shanghai',
    enable_utc=True,
    
    # 任务路由配置
    task_routes={
        'app.tasks.crawler_tasks.crawl_sse': {'queue': 'crawler'},
        'app.tasks.crawler_tasks.crawl_szse': {'queue': 'crawler'},
        'app.tasks.crawler_tasks.crawl_csrc': {'queue': 'crawler'},
        'app.tasks.crawler_tasks.crawl_rss': {'queue': 'crawler'},
        'app.tasks.crawler_tasks.process_news_batch': {'queue': 'processor'},
        'app.tasks.push_tasks.send_subscription_notifications': {'queue': 'pusher'},
        'app.tasks.push_tasks.send_urgent_news_alert': {'queue': 'pusher'},
        'app.tasks.push_tasks.send_custom_message': {'queue': 'pusher'},
        'app.tasks.push_tasks.cleanup_old_push_logs': {'queue': 'processor'},
    },
    
    # 队列配置
    task_default_queue='default',
    task_queues=(
        Queue('default'),
        Queue('crawler'),
        Queue('processor'),
        Queue('pusher'),
    ),
    
    # 任务执行配置
    task_acks_late=True,
    task_reject_on_worker_lost=True,
    worker_prefetch_multiplier=1,
    
    # 任务重试配置
    task_default_retry_delay=60,  # 60秒后重试
    task_max_retries=3,
    
    # 定时任务配置
    beat_schedule={
        # 上交所爬虫 - 每5分钟
        'crawl-sse-every-5-minutes': {
            'task': 'app.tasks.crawler_tasks.crawl_sse',
            'schedule': crontab(minute='*/5'),
            'options': {'queue': 'crawler'}
        },
        # 深交所爬虫 - 每5分钟
        'crawl-szse-every-5-minutes': {
            'task': 'app.tasks.crawler_tasks.crawl_szse',
            'schedule': crontab(minute='*/5'),
            'options': {'queue': 'crawler'}
        },
        # 证监会爬虫 - 每30分钟
        'crawl-csrc-every-30-minutes': {
            'task': 'app.tasks.crawler_tasks.crawl_csrc',
            'schedule': crontab(minute='*/30'),
            'options': {'queue': 'crawler'}
        },
        # RSS爬虫 - 每15分钟
        'crawl-rss-every-15-minutes': {
            'task': 'app.tasks.crawler_tasks.crawl_rss',
            'schedule': crontab(minute='*/15'),
            'options': {'queue': 'crawler'}
        },
        # 批量处理新闻 - 每10分钟
        'process-news-batch-every-10-minutes': {
            'task': 'app.tasks.crawler_tasks.process_news_batch',
            'schedule': crontab(minute='*/10'),
            'options': {'queue': 'processor'}
        },
        # 订阅推送 - 每15分钟
        'send-subscription-notifications-every-15-minutes': {
            'task': 'app.tasks.push_tasks.send_subscription_notifications',
            'schedule': crontab(minute='*/15'),
            'options': {'queue': 'pusher'}
        },
        # 清理推送日志 - 每天凌晨2点
        'cleanup-push-logs-daily': {
            'task': 'app.tasks.push_tasks.cleanup_old_push_logs',
            'schedule': crontab(hour=2, minute=0),
            'options': {'queue': 'processor'},
            'kwargs': {'days': 30}
        },
    },
    
    # 监控配置
    worker_send_task_events=True,
    task_send_sent_event=True,
    
    # 结果过期时间
    result_expires=3600,  # 1小时

    # Beat调度文件路径
    beat_schedule_filename='/app/beat-data/celerybeat-schedule',
)

# 自动发现任务
celery_app.autodiscover_tasks(['app.tasks.crawler_tasks', 'app.tasks.push_tasks'])

if __name__ == '__main__':
    celery_app.start()
