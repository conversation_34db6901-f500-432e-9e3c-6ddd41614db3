#!/usr/bin/env python3
"""
代码格式化工具
自动格式化Python代码，确保代码风格一致性
"""
import os
import sys
import subprocess
from pathlib import Path
from typing import List, Dict, Any
import argparse

class CodeFormatter:
    """代码格式化器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.app_dir = self.project_root / "app"
        
    def format_with_black(self, check_only: bool = False) -> Dict[str, Any]:
        """使用Black格式化代码"""
        print("🖤 使用Black格式化代码...")
        
        cmd = ["python", "-m", "black"]
        if check_only:
            cmd.append("--check")
        cmd.extend([
            "--line-length", "120",
            "--target-version", "py39",
            str(self.app_dir)
        ])
        
        try:
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                cwd=self.project_root
            )
            
            return {
                "success": result.returncode == 0,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "files_changed": self._parse_black_output(result.stdout)
            }
            
        except FileNotFoundError:
            return {
                "success": False,
                "error": "Black未安装，请运行: pip install black",
                "files_changed": []
            }
    
    def format_with_isort(self, check_only: bool = False) -> Dict[str, Any]:
        """使用isort整理导入语句"""
        print("📦 使用isort整理导入语句...")
        
        cmd = ["python", "-m", "isort"]
        if check_only:
            cmd.append("--check-only")
        cmd.extend([
            "--profile", "black",
            "--line-length", "120",
            "--multi-line", "3",
            "--trailing-comma",
            "--force-grid-wrap", "0",
            "--combine-as",
            "--use-parentheses",
            str(self.app_dir)
        ])
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            
            return {
                "success": result.returncode == 0,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "files_changed": self._parse_isort_output(result.stdout)
            }
            
        except FileNotFoundError:
            return {
                "success": False,
                "error": "isort未安装，请运行: pip install isort",
                "files_changed": []
            }
    
    def format_with_autopep8(self, check_only: bool = False) -> Dict[str, Any]:
        """使用autopep8修复PEP8问题"""
        print("🔧 使用autopep8修复PEP8问题...")
        
        cmd = ["python", "-m", "autopep8"]
        if not check_only:
            cmd.extend(["--in-place", "--recursive"])
        cmd.extend([
            "--max-line-length", "120",
            "--aggressive", "--aggressive",
            str(self.app_dir)
        ])
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            
            return {
                "success": result.returncode == 0,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "files_changed": []  # autopep8不提供详细的文件列表
            }
            
        except FileNotFoundError:
            return {
                "success": False,
                "error": "autopep8未安装，请运行: pip install autopep8",
                "files_changed": []
            }
    
    def add_type_annotations(self) -> Dict[str, Any]:
        """添加基础类型注解"""
        print("📝 添加基础类型注解...")
        
        files_modified = []
        
        for py_file in self.app_dir.rglob("*.py"):
            if py_file.name.startswith("__"):
                continue
                
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                original_content = content
                
                # 简单的类型注解添加
                # 为常见的函数参数添加类型注解
                import re
                
                # 添加返回None的注解
                content = re.sub(
                    r'def (\w+)\([^)]*\):(\s*\n\s*"""[^"]*"""\s*\n)?(\s*)',
                    r'def \1(\g<0>) -> None:\2\3',
                    content
                )
                
                if content != original_content:
                    with open(py_file, 'w', encoding='utf-8') as f:
                        f.write(content)
                    files_modified.append(str(py_file.relative_to(self.project_root)))
                    
            except Exception as e:
                print(f"处理文件 {py_file} 时出错: {str(e)}")
                continue
        
        return {
            "success": True,
            "files_changed": files_modified,
            "message": f"为 {len(files_modified)} 个文件添加了类型注解"
        }
    
    def add_docstrings(self) -> Dict[str, Any]:
        """为缺少文档字符串的函数和类添加基础文档字符串"""
        print("📚 添加基础文档字符串...")
        
        import ast
        files_modified = []
        
        for py_file in self.app_dir.rglob("*.py"):
            if py_file.name.startswith("__"):
                continue
                
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                lines = content.split('\n')
                tree = ast.parse(content)
                
                modified = False
                
                for node in ast.walk(tree):
                    if isinstance(node, (ast.FunctionDef, ast.ClassDef, ast.AsyncFunctionDef)):
                        if node.name.startswith("_"):
                            continue
                            
                        docstring = ast.get_docstring(node)
                        if not docstring:
                            # 添加基础文档字符串
                            node_type = "类" if isinstance(node, ast.ClassDef) else "函数"
                            basic_docstring = f'"""{node_type}的基础描述"""'
                            
                            # 在函数/类定义后插入文档字符串
                            insert_line = node.lineno
                            while insert_line < len(lines) and not lines[insert_line].strip().endswith(':'):
                                insert_line += 1
                            
                            if insert_line < len(lines):
                                indent = len(lines[insert_line]) - len(lines[insert_line].lstrip())
                                docstring_line = ' ' * (indent + 4) + basic_docstring
                                lines.insert(insert_line + 1, docstring_line)
                                modified = True
                
                if modified:
                    with open(py_file, 'w', encoding='utf-8') as f:
                        f.write('\n'.join(lines))
                    files_modified.append(str(py_file.relative_to(self.project_root)))
                    
            except Exception as e:
                print(f"处理文件 {py_file} 时出错: {str(e)}")
                continue
        
        return {
            "success": True,
            "files_changed": files_modified,
            "message": f"为 {len(files_modified)} 个文件添加了文档字符串"
        }
    
    def remove_unused_imports(self) -> Dict[str, Any]:
        """移除未使用的导入"""
        print("🧹 移除未使用的导入...")
        
        cmd = ["python", "-m", "autoflake"]
        cmd.extend([
            "--in-place",
            "--remove-all-unused-imports",
            "--remove-unused-variables",
            "--recursive",
            str(self.app_dir)
        ])
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            
            return {
                "success": result.returncode == 0,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "message": "已移除未使用的导入"
            }
            
        except FileNotFoundError:
            return {
                "success": False,
                "error": "autoflake未安装，请运行: pip install autoflake",
                "files_changed": []
            }
    
    def _parse_black_output(self, output: str) -> List[str]:
        """解析Black输出，提取修改的文件列表"""
        files = []
        for line in output.split('\n'):
            if 'reformatted' in line:
                # 提取文件路径
                parts = line.split()
                if len(parts) > 1:
                    files.append(parts[1])
        return files
    
    def _parse_isort_output(self, output: str) -> List[str]:
        """解析isort输出，提取修改的文件列表"""
        files = []
        for line in output.split('\n'):
            if 'Fixing' in line:
                # 提取文件路径
                parts = line.split()
                if len(parts) > 1:
                    files.append(parts[1])
        return files
    
    def format_all(self, check_only: bool = False, add_missing: bool = False) -> Dict[str, Any]:
        """执行所有格式化操作"""
        print("🚀 开始代码格式化...")
        
        results = {}
        
        # 1. 移除未使用的导入
        if not check_only:
            results["unused_imports"] = self.remove_unused_imports()
        
        # 2. 使用isort整理导入
        results["isort"] = self.format_with_isort(check_only)
        
        # 3. 使用autopep8修复基础问题
        if not check_only:
            results["autopep8"] = self.format_with_autopep8(check_only)
        
        # 4. 使用Black格式化
        results["black"] = self.format_with_black(check_only)
        
        # 5. 添加缺失的内容（可选）
        if add_missing and not check_only:
            results["docstrings"] = self.add_docstrings()
            # results["type_annotations"] = self.add_type_annotations()  # 暂时禁用，可能破坏代码
        
        # 统计结果
        total_files_changed = set()
        for result in results.values():
            if isinstance(result, dict) and "files_changed" in result:
                total_files_changed.update(result["files_changed"])
        
        results["summary"] = {
            "total_files_changed": len(total_files_changed),
            "files_changed": list(total_files_changed),
            "operations_completed": len([r for r in results.values() if isinstance(r, dict) and r.get("success", False)])
        }
        
        return results
    
    def generate_report(self, results: Dict[str, Any]) -> str:
        """生成格式化报告"""
        report = []
        report.append("# 代码格式化报告")
        report.append(f"格式化时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # 总结
        summary = results.get("summary", {})
        report.append("## 📊 总结")
        report.append(f"- 修改文件数: {summary.get('total_files_changed', 0)}")
        report.append(f"- 完成操作数: {summary.get('operations_completed', 0)}")
        report.append("")
        
        # 详细结果
        for operation, result in results.items():
            if operation == "summary" or not isinstance(result, dict):
                continue
                
            report.append(f"## {operation.replace('_', ' ').title()}")
            
            if result.get("success"):
                report.append("✅ 成功")
                if "files_changed" in result and result["files_changed"]:
                    report.append("修改的文件:")
                    for file in result["files_changed"][:10]:  # 只显示前10个
                        report.append(f"- {file}")
                    if len(result["files_changed"]) > 10:
                        report.append(f"- ... 还有 {len(result['files_changed']) - 10} 个文件")
            else:
                report.append("❌ 失败")
                if "error" in result:
                    report.append(f"错误: {result['error']}")
            
            if "message" in result:
                report.append(f"说明: {result['message']}")
            
            report.append("")
        
        return "\n".join(report)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="代码格式化工具")
    parser.add_argument("--check", action="store_true", help="只检查，不修改文件")
    parser.add_argument("--add-missing", action="store_true", help="添加缺失的文档字符串和类型注解")
    parser.add_argument("--report", help="报告输出文件", default="code_format_report.md")
    
    args = parser.parse_args()
    
    formatter = CodeFormatter()
    results = formatter.format_all(check_only=args.check, add_missing=args.add_missing)
    
    # 生成报告
    report = formatter.generate_report(results)
    
    # 保存报告
    with open(args.report, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n🎉 代码格式化完成！")
    print(f"📄 报告已保存到: {args.report}")
    
    if args.check:
        print("🔍 检查模式：未修改任何文件")
    else:
        files_changed = results.get("summary", {}).get("total_files_changed", 0)
        print(f"📝 共修改了 {files_changed} 个文件")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
