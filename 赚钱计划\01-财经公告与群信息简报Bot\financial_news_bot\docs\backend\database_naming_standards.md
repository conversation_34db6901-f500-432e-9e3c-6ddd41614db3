# 数据库命名规范

## 表命名规范
- 使用复数形式：users, subscriptions, news
- 使用 snake_case 命名风格
- 表名应该描述性强，避免缩写

## 字段命名规范
- 主键统一使用 id
- 外键使用 {table_name}_id 格式
- 时间字段统一使用 created_at, updated_at
- 布尔字段使用 is_ 前缀
- 枚举字段使用描述性名称

## 索引命名规范
- 普通索引：idx_{table_name}_{column_name}
- 唯一索引：uk_{table_name}_{column_name}
- 外键索引：fk_{table_name}_{column_name}

## 约束命名规范
- 主键约束：pk_{table_name}
- 外键约束：fk_{table_name}_{referenced_table}
- 唯一约束：uk_{table_name}_{column_name}
- 检查约束：ck_{table_name}_{column_name}
