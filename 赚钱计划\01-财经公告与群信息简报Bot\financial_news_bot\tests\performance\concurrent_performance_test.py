#!/usr/bin/env python3
"""
并发性能测试
测试数据采集、文本处理、消息推送的并发处理能力
"""
import asyncio
import aiohttp
import time
import json
import threading
import concurrent.futures
from typing import List, Dict, Any
import logging
import statistics
from datetime import datetime
import argparse

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ConcurrentPerformanceTester:
    """并发性能测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.results = {
            'data_collection': [],
            'text_processing': [],
            'message_push': [],
            'database_operations': [],
            'redis_cache': []
        }
    
    async def test_data_collection_concurrency(self, concurrent_tasks: int = 100) -> Dict[str, Any]:
        """测试数据采集并发性能"""
        logger.info(f"开始测试数据采集并发性能，并发任务数: {concurrent_tasks}")
        
        async def single_data_collection_task(session: aiohttp.ClientSession, task_id: int):
            """单个数据采集任务"""
            start_time = time.time()
            try:
                # 模拟RSS数据采集请求
                async with session.post(
                    f"{self.base_url}/api/v1/admin/crawl/rss",
                    json={
                        "url": f"https://example.com/rss/feed_{task_id % 10}.xml",
                        "source_name": f"test_source_{task_id}",
                        "category": "finance"
                    },
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    result = await response.json()
                    end_time = time.time()
                    
                    return {
                        'task_id': task_id,
                        'status_code': response.status,
                        'response_time': end_time - start_time,
                        'success': response.status == 200,
                        'data_size': len(str(result))
                    }
            except Exception as e:
                end_time = time.time()
                return {
                    'task_id': task_id,
                    'status_code': 0,
                    'response_time': end_time - start_time,
                    'success': False,
                    'error': str(e)
                }
        
        # 执行并发任务
        connector = aiohttp.TCPConnector(limit=200, limit_per_host=100)
        async with aiohttp.ClientSession(connector=connector) as session:
            tasks = [
                single_data_collection_task(session, i) 
                for i in range(concurrent_tasks)
            ]
            
            start_time = time.time()
            results = await asyncio.gather(*tasks, return_exceptions=True)
            total_time = time.time() - start_time
        
        # 分析结果
        successful_results = [r for r in results if isinstance(r, dict) and r.get('success')]
        failed_results = [r for r in results if isinstance(r, dict) and not r.get('success')]
        
        if successful_results:
            response_times = [r['response_time'] for r in successful_results]
            analysis = {
                'test_type': 'data_collection',
                'concurrent_tasks': concurrent_tasks,
                'total_time': total_time,
                'successful_tasks': len(successful_results),
                'failed_tasks': len(failed_results),
                'success_rate': len(successful_results) / concurrent_tasks * 100,
                'avg_response_time': statistics.mean(response_times),
                'min_response_time': min(response_times),
                'max_response_time': max(response_times),
                'median_response_time': statistics.median(response_times),
                'throughput': len(successful_results) / total_time,
                'errors': [r.get('error') for r in failed_results if r.get('error')]
            }
        else:
            analysis = {
                'test_type': 'data_collection',
                'concurrent_tasks': concurrent_tasks,
                'total_time': total_time,
                'successful_tasks': 0,
                'failed_tasks': len(failed_results),
                'success_rate': 0,
                'errors': [r.get('error') for r in failed_results if r.get('error')]
            }
        
        self.results['data_collection'].append(analysis)
        logger.info(f"数据采集并发测试完成: 成功率 {analysis.get('success_rate', 0):.1f}%")
        return analysis
    
    def test_text_processing_concurrency(self, concurrent_tasks: int = 50) -> Dict[str, Any]:
        """测试文本处理并发性能"""
        logger.info(f"开始测试文本处理并发性能，并发任务数: {concurrent_tasks}")
        
        def single_text_processing_task(task_id: int):
            """单个文本处理任务"""
            start_time = time.time()
            try:
                import requests
                
                # 模拟文本处理请求
                response = requests.post(
                    f"{self.base_url}/api/v1/admin/process-text",
                    json={
                        "text": f"这是一条测试新闻内容 {task_id}。央行今日发布了最新的货币政策报告，强调将继续实施稳健的货币政策。股市表现良好，投资者信心增强。",
                        "task_type": "extract_keywords"
                    },
                    timeout=30
                )
                
                end_time = time.time()
                return {
                    'task_id': task_id,
                    'status_code': response.status_code,
                    'response_time': end_time - start_time,
                    'success': response.status_code == 200,
                    'processed_length': len(response.text)
                }
            except Exception as e:
                end_time = time.time()
                return {
                    'task_id': task_id,
                    'status_code': 0,
                    'response_time': end_time - start_time,
                    'success': False,
                    'error': str(e)
                }
        
        # 使用线程池执行并发任务
        start_time = time.time()
        with concurrent.futures.ThreadPoolExecutor(max_workers=concurrent_tasks) as executor:
            futures = [
                executor.submit(single_text_processing_task, i) 
                for i in range(concurrent_tasks)
            ]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        total_time = time.time() - start_time
        
        # 分析结果
        successful_results = [r for r in results if r.get('success')]
        failed_results = [r for r in results if not r.get('success')]
        
        if successful_results:
            response_times = [r['response_time'] for r in successful_results]
            analysis = {
                'test_type': 'text_processing',
                'concurrent_tasks': concurrent_tasks,
                'total_time': total_time,
                'successful_tasks': len(successful_results),
                'failed_tasks': len(failed_results),
                'success_rate': len(successful_results) / concurrent_tasks * 100,
                'avg_response_time': statistics.mean(response_times),
                'min_response_time': min(response_times),
                'max_response_time': max(response_times),
                'median_response_time': statistics.median(response_times),
                'throughput': len(successful_results) / total_time,
                'errors': [r.get('error') for r in failed_results if r.get('error')]
            }
        else:
            analysis = {
                'test_type': 'text_processing',
                'concurrent_tasks': concurrent_tasks,
                'total_time': total_time,
                'successful_tasks': 0,
                'failed_tasks': len(failed_results),
                'success_rate': 0,
                'errors': [r.get('error') for r in failed_results if r.get('error')]
            }
        
        self.results['text_processing'].append(analysis)
        logger.info(f"文本处理并发测试完成: 成功率 {analysis.get('success_rate', 0):.1f}%")
        return analysis
    
    async def test_message_push_concurrency(self, concurrent_tasks: int = 30) -> Dict[str, Any]:
        """测试消息推送并发性能"""
        logger.info(f"开始测试消息推送并发性能，并发任务数: {concurrent_tasks}")
        
        async def single_message_push_task(session: aiohttp.ClientSession, task_id: int):
            """单个消息推送任务"""
            start_time = time.time()
            try:
                # 模拟消息推送请求
                async with session.post(
                    f"{self.base_url}/api/v1/admin/push-message",
                    json={
                        "user_id": f"test_user_{task_id % 100}",
                        "message": f"测试推送消息 {task_id}",
                        "channels": ["email", "wechat"],
                        "priority": "normal"
                    },
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    result = await response.json()
                    end_time = time.time()
                    
                    return {
                        'task_id': task_id,
                        'status_code': response.status,
                        'response_time': end_time - start_time,
                        'success': response.status == 200,
                        'message_size': len(str(result))
                    }
            except Exception as e:
                end_time = time.time()
                return {
                    'task_id': task_id,
                    'status_code': 0,
                    'response_time': end_time - start_time,
                    'success': False,
                    'error': str(e)
                }
        
        # 执行并发任务
        connector = aiohttp.TCPConnector(limit=100, limit_per_host=50)
        async with aiohttp.ClientSession(connector=connector) as session:
            tasks = [
                single_message_push_task(session, i) 
                for i in range(concurrent_tasks)
            ]
            
            start_time = time.time()
            results = await asyncio.gather(*tasks, return_exceptions=True)
            total_time = time.time() - start_time
        
        # 分析结果
        successful_results = [r for r in results if isinstance(r, dict) and r.get('success')]
        failed_results = [r for r in results if isinstance(r, dict) and not r.get('success')]
        
        if successful_results:
            response_times = [r['response_time'] for r in successful_results]
            analysis = {
                'test_type': 'message_push',
                'concurrent_tasks': concurrent_tasks,
                'total_time': total_time,
                'successful_tasks': len(successful_results),
                'failed_tasks': len(failed_results),
                'success_rate': len(successful_results) / concurrent_tasks * 100,
                'avg_response_time': statistics.mean(response_times),
                'min_response_time': min(response_times),
                'max_response_time': max(response_times),
                'median_response_time': statistics.median(response_times),
                'throughput': len(successful_results) / total_time,
                'errors': [r.get('error') for r in failed_results if r.get('error')]
            }
        else:
            analysis = {
                'test_type': 'message_push',
                'concurrent_tasks': concurrent_tasks,
                'total_time': total_time,
                'successful_tasks': 0,
                'failed_tasks': len(failed_results),
                'success_rate': 0,
                'errors': [r.get('error') for r in failed_results if r.get('error')]
            }
        
        self.results['message_push'].append(analysis)
        logger.info(f"消息推送并发测试完成: 成功率 {analysis.get('success_rate', 0):.1f}%")
        return analysis
    
    def test_database_connection_pool(self, concurrent_connections: int = 50) -> Dict[str, Any]:
        """测试数据库连接池性能"""
        logger.info(f"开始测试数据库连接池性能，并发连接数: {concurrent_connections}")
        
        def single_db_operation(task_id: int):
            """单个数据库操作"""
            start_time = time.time()
            try:
                import requests
                
                # 模拟数据库查询请求
                response = requests.get(
                    f"{self.base_url}/api/v1/news/",
                    params={
                        "page": task_id % 10 + 1,
                        "size": 10
                    },
                    timeout=30
                )
                
                end_time = time.time()
                return {
                    'task_id': task_id,
                    'status_code': response.status_code,
                    'response_time': end_time - start_time,
                    'success': response.status_code == 200,
                    'data_size': len(response.text)
                }
            except Exception as e:
                end_time = time.time()
                return {
                    'task_id': task_id,
                    'status_code': 0,
                    'response_time': end_time - start_time,
                    'success': False,
                    'error': str(e)
                }
        
        # 使用线程池模拟并发数据库连接
        start_time = time.time()
        with concurrent.futures.ThreadPoolExecutor(max_workers=concurrent_connections) as executor:
            futures = [
                executor.submit(single_db_operation, i) 
                for i in range(concurrent_connections)
            ]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        total_time = time.time() - start_time
        
        # 分析结果
        successful_results = [r for r in results if r.get('success')]
        failed_results = [r for r in results if not r.get('success')]
        
        if successful_results:
            response_times = [r['response_time'] for r in successful_results]
            analysis = {
                'test_type': 'database_operations',
                'concurrent_connections': concurrent_connections,
                'total_time': total_time,
                'successful_operations': len(successful_results),
                'failed_operations': len(failed_results),
                'success_rate': len(successful_results) / concurrent_connections * 100,
                'avg_response_time': statistics.mean(response_times),
                'min_response_time': min(response_times),
                'max_response_time': max(response_times),
                'median_response_time': statistics.median(response_times),
                'throughput': len(successful_results) / total_time,
                'errors': [r.get('error') for r in failed_results if r.get('error')]
            }
        else:
            analysis = {
                'test_type': 'database_operations',
                'concurrent_connections': concurrent_connections,
                'total_time': total_time,
                'successful_operations': 0,
                'failed_operations': len(failed_results),
                'success_rate': 0,
                'errors': [r.get('error') for r in failed_results if r.get('error')]
            }
        
        self.results['database_operations'].append(analysis)
        logger.info(f"数据库连接池测试完成: 成功率 {analysis.get('success_rate', 0):.1f}%")
        return analysis
    
    async def run_full_concurrent_test(self) -> Dict[str, Any]:
        """运行完整的并发性能测试"""
        logger.info("开始运行完整的并发性能测试...")
        
        # 数据采集并发测试
        await self.test_data_collection_concurrency(100)
        
        # 文本处理并发测试
        self.test_text_processing_concurrency(50)
        
        # 消息推送并发测试
        await self.test_message_push_concurrency(30)
        
        # 数据库连接池测试
        self.test_database_connection_pool(50)
        
        # 生成综合报告
        report = {
            'timestamp': datetime.now().isoformat(),
            'test_summary': {
                'data_collection': self.results['data_collection'][-1] if self.results['data_collection'] else None,
                'text_processing': self.results['text_processing'][-1] if self.results['text_processing'] else None,
                'message_push': self.results['message_push'][-1] if self.results['message_push'] else None,
                'database_operations': self.results['database_operations'][-1] if self.results['database_operations'] else None,
            },
            'overall_assessment': self._generate_assessment(),
            'recommendations': self._generate_recommendations()
        }
        
        logger.info("并发性能测试完成")
        return report
    
    def _generate_assessment(self) -> Dict[str, str]:
        """生成性能评估"""
        assessment = {}
        
        for test_type, results in self.results.items():
            if results:
                latest_result = results[-1]
                success_rate = latest_result.get('success_rate', 0)
                avg_response_time = latest_result.get('avg_response_time', 0)
                
                if success_rate >= 95 and avg_response_time <= 1.0:
                    assessment[test_type] = "优秀"
                elif success_rate >= 90 and avg_response_time <= 2.0:
                    assessment[test_type] = "良好"
                elif success_rate >= 80 and avg_response_time <= 5.0:
                    assessment[test_type] = "一般"
                else:
                    assessment[test_type] = "需要优化"
        
        return assessment
    
    def _generate_recommendations(self) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        for test_type, results in self.results.items():
            if results:
                latest_result = results[-1]
                success_rate = latest_result.get('success_rate', 0)
                avg_response_time = latest_result.get('avg_response_time', 0)
                
                if success_rate < 90:
                    recommendations.append(f"优化{test_type}的错误处理和重试机制")
                
                if avg_response_time > 2.0:
                    recommendations.append(f"优化{test_type}的响应时间，考虑增加缓存或异步处理")
                
                if test_type == 'database_operations' and avg_response_time > 1.0:
                    recommendations.append("优化数据库查询，增加索引，调整连接池配置")
        
        if not recommendations:
            recommendations.append("当前并发性能表现良好，建议定期监控")
        
        return recommendations
    
    def save_report(self, output_file: str, report: Dict[str, Any]):
        """保存测试报告"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            logger.info(f"并发性能测试报告已保存: {output_file}")
        except Exception as e:
            logger.error(f"保存报告失败: {e}")


async def main():
    parser = argparse.ArgumentParser(description='并发性能测试')
    parser.add_argument('--base-url', default='http://localhost:8000', help='目标服务URL')
    parser.add_argument('--output', default='concurrent_performance_report.json', help='输出文件')
    
    args = parser.parse_args()
    
    tester = ConcurrentPerformanceTester(args.base_url)
    
    try:
        report = await tester.run_full_concurrent_test()
        tester.save_report(args.output, report)
        
        # 输出摘要
        print(f"\n=== 并发性能测试摘要 ===")
        for test_type, result in report['test_summary'].items():
            if result:
                print(f"{test_type}: 成功率 {result.get('success_rate', 0):.1f}%, "
                      f"平均响应时间 {result.get('avg_response_time', 0):.3f}s")
        
        print(f"\n=== 性能评估 ===")
        for test_type, assessment in report['overall_assessment'].items():
            print(f"{test_type}: {assessment}")
        
        print(f"\n=== 优化建议 ===")
        for i, recommendation in enumerate(report['recommendations'], 1):
            print(f"{i}. {recommendation}")
    
    except Exception as e:
        logger.error(f"测试过程中出错: {e}")
        return 1
    
    return 0


if __name__ == '__main__':
    asyncio.run(main())
