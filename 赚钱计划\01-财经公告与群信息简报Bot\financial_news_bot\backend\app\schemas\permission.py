"""
权限管理相关数据模式
定义权限管理API的请求和响应数据结构
"""
from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional
from datetime import datetime


class PermissionCreate(BaseModel):
    """权限创建请求模式"""
    name: str = Field(..., max_length=100, description="权限名称")
    code: str = Field(..., max_length=100, description="权限代码")
    description: Optional[str] = Field(None, description="权限描述")
    resource: str = Field(..., max_length=100, description="资源类型")
    action: str = Field(..., max_length=50, description="操作类型")
    group_id: Optional[int] = Field(None, description="权限组ID")

    @validator('code')
    def validate_code(cls, v):
        if not v.replace('_', '').isalnum():
            raise ValueError('权限代码只能包含字母、数字和下划线')
        return v.lower()

    class Config:
        json_schema_extra = {
            "example": {
                "name": "查看用户",
                "code": "view_users",
                "description": "查看用户列表和详情的权限",
                "resource": "user",
                "action": "view",
                "group_id": 1
            }
        }


class PermissionResponse(BaseModel):
    """权限响应模式"""
    id: int = Field(..., description="权限ID")
    name: str = Field(..., description="权限名称")
    code: str = Field(..., description="权限代码")
    description: Optional[str] = Field(None, description="权限描述")
    resource: str = Field(..., description="资源类型")
    action: str = Field(..., description="操作类型")
    group_id: Optional[int] = Field(None, description="权限组ID")
    is_active: bool = Field(..., description="是否启用")
    is_system: bool = Field(..., description="是否系统权限")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class RoleCreate(BaseModel):
    """角色创建请求模式"""
    name: str = Field(..., max_length=100, description="角色名称")
    code: str = Field(..., max_length=100, description="角色代码")
    description: Optional[str] = Field(None, description="角色描述")
    level: int = Field(1, ge=1, le=10, description="角色层级")
    parent_id: Optional[int] = Field(None, description="父角色ID")
    config: Optional[Dict[str, Any]] = Field(None, description="角色配置")

    @validator('code')
    def validate_code(cls, v):
        if not v.replace('_', '').isalnum():
            raise ValueError('角色代码只能包含字母、数字和下划线')
        return v.lower()

    class Config:
        json_schema_extra = {
            "example": {
                "name": "高级用户",
                "code": "advanced_user",
                "description": "拥有高级功能权限的用户角色",
                "level": 3,
                "parent_id": None,
                "config": {
                    "max_reports_per_day": 10,
                    "can_schedule_reports": True
                }
            }
        }


class RoleResponse(BaseModel):
    """角色响应模式"""
    id: int = Field(..., description="角色ID")
    name: str = Field(..., description="角色名称")
    code: str = Field(..., description="角色代码")
    description: Optional[str] = Field(None, description="角色描述")
    level: int = Field(..., description="角色层级")
    parent_id: Optional[int] = Field(None, description="父角色ID")
    is_active: bool = Field(..., description="是否启用")
    is_system: bool = Field(..., description="是否系统角色")
    config: Optional[Dict[str, Any]] = Field(None, description="角色配置")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class UserRoleCreate(BaseModel):
    """用户角色分配请求模式"""
    user_id: int = Field(..., description="用户ID")
    role_id: int = Field(..., description="角色ID")
    expires_at: Optional[datetime] = Field(None, description="过期时间")

    @validator('expires_at')
    def validate_expires_at(cls, v):
        if v and v <= datetime.now():
            raise ValueError('过期时间必须是未来时间')
        return v

    class Config:
        json_schema_extra = {
            "example": {
                "user_id": 1,
                "role_id": 2,
                "expires_at": "2024-12-31T23:59:59"
            }
        }


class UserRoleResponse(BaseModel):
    """用户角色响应模式"""
    id: int = Field(..., description="关联ID")
    user_id: int = Field(..., description="用户ID")
    role_id: int = Field(..., description="角色ID")
    assigned_by: Optional[int] = Field(None, description="分配者ID")
    assigned_at: datetime = Field(..., description="分配时间")
    expires_at: Optional[datetime] = Field(None, description="过期时间")
    is_active: bool = Field(..., description="是否启用")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class RolePermissionCreate(BaseModel):
    """角色权限分配请求模式"""
    role_id: int = Field(..., description="角色ID")
    permission_ids: List[int] = Field(..., description="权限ID列表")

    @validator('permission_ids')
    def validate_permission_ids(cls, v):
        if not v:
            raise ValueError('权限ID列表不能为空')
        if len(v) != len(set(v)):
            raise ValueError('权限ID列表不能有重复')
        return v

    class Config:
        json_schema_extra = {
            "example": {
                "role_id": 2,
                "permission_ids": [1, 2, 3, 4, 5]
            }
        }


class PermissionGroupCreate(BaseModel):
    """权限组创建请求模式"""
    name: str = Field(..., max_length=100, description="权限组名称")
    code: str = Field(..., max_length=100, description="权限组代码")
    description: Optional[str] = Field(None, description="权限组描述")
    parent_id: Optional[int] = Field(None, description="父权限组ID")
    sort_order: int = Field(0, description="排序顺序")

    @validator('code')
    def validate_code(cls, v):
        if not v.replace('_', '').isalnum():
            raise ValueError('权限组代码只能包含字母、数字和下划线')
        return v.lower()

    class Config:
        json_schema_extra = {
            "example": {
                "name": "用户管理",
                "code": "user_management",
                "description": "用户相关权限组",
                "parent_id": None,
                "sort_order": 1
            }
        }


class PermissionGroupResponse(BaseModel):
    """权限组响应模式"""
    id: int = Field(..., description="权限组ID")
    name: str = Field(..., description="权限组名称")
    code: str = Field(..., description="权限组代码")
    description: Optional[str] = Field(None, description="权限组描述")
    parent_id: Optional[int] = Field(None, description="父权限组ID")
    sort_order: int = Field(..., description="排序顺序")
    is_active: bool = Field(..., description="是否启用")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class AuditLogResponse(BaseModel):
    """审计日志响应模式"""
    id: int = Field(..., description="日志ID")
    action: str = Field(..., description="操作类型")
    resource: str = Field(..., description="资源类型")
    resource_id: Optional[str] = Field(None, description="资源ID")
    user_id: Optional[int] = Field(None, description="操作用户ID")
    username: Optional[str] = Field(None, description="用户名")
    user_ip: Optional[str] = Field(None, description="用户IP")
    description: Optional[str] = Field(None, description="操作描述")
    old_values: Optional[Dict[str, Any]] = Field(None, description="修改前的值")
    new_values: Optional[Dict[str, Any]] = Field(None, description="修改后的值")
    success: bool = Field(..., description="操作是否成功")
    error_message: Optional[str] = Field(None, description="错误信息")
    created_at: datetime = Field(..., description="创建时间")

    class Config:
        from_attributes = True


class UserPermissionResponse(BaseModel):
    """用户权限响应模式"""
    user_id: int = Field(..., description="用户ID")
    permissions: List[str] = Field(..., description="权限代码列表")
    roles: List[str] = Field(..., description="角色代码列表")
    is_admin: bool = Field(..., description="是否管理员")
    is_super_admin: bool = Field(..., description="是否超级管理员")

    class Config:
        json_schema_extra = {
            "example": {
                "user_id": 1,
                "permissions": ["view_users", "create_reports", "basic_push"],
                "roles": ["advanced_user"],
                "is_admin": False,
                "is_super_admin": False
            }
        }


class PermissionCheckRequest(BaseModel):
    """权限检查请求模式"""
    user_id: int = Field(..., description="用户ID")
    permission_code: str = Field(..., description="权限代码")
    resource_id: Optional[str] = Field(None, description="资源ID")

    class Config:
        json_schema_extra = {
            "example": {
                "user_id": 1,
                "permission_code": "edit_reports",
                "resource_id": "report_123"
            }
        }


class PermissionCheckResponse(BaseModel):
    """权限检查响应模式"""
    has_permission: bool = Field(..., description="是否有权限")
    reason: Optional[str] = Field(None, description="原因说明")
    granted_by: Optional[str] = Field(None, description="权限来源")

    class Config:
        json_schema_extra = {
            "example": {
                "has_permission": True,
                "reason": "用户拥有 edit_reports 权限",
                "granted_by": "advanced_user 角色"
            }
        }
