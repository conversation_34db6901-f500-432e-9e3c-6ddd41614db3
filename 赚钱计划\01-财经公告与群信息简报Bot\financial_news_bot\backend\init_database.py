"""
数据库初始化脚本
创建所有数据表和初始数据
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import asyncio
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# 检查环境变量是否已设置
if not os.getenv("DATABASE_URL"):
    print("警告：DATABASE_URL 环境变量未设置，请在 .env 文件中配置")
    print("示例：DATABASE_URL=mysql+pymysql://username:password@host:port/dbname")
    sys.exit(1)

if not os.getenv("GLM_API_KEY"):
    print("警告：GLM_API_KEY 环境变量未设置，请在 .env 文件中配置")
    print("示例：GLM_API_KEY=your-glm-api-key-here")
    sys.exit(1)

from app.database import Base, engine
from app.models import *  # 导入所有模型


def create_tables():
    """创建所有数据表"""
    print("正在创建数据库表...")
    
    try:
        # 创建所有表
        Base.metadata.create_all(bind=engine)
        print("✅ 数据库表创建成功")
        
        # 验证表是否创建成功
        with engine.connect() as conn:
            result = conn.execute(text("SHOW TABLES"))
            tables = [row[0] for row in result]
            print(f"✅ 创建的表: {', '.join(tables)}")
            
    except Exception as e:
        print(f"❌ 创建数据库表失败: {str(e)}")
        raise


def insert_initial_data():
    """插入初始数据"""
    print("正在插入初始数据...")
    
    try:
        from sqlalchemy.orm import sessionmaker
        Session = sessionmaker(bind=engine)
        session = Session()
        
        # 插入敏感词数据
        from app.models.sensitive_word import SensitiveWord
        
        sensitive_words = [
            # 投资建议类
            {"word": "建议买入", "category": "investment_advice", "type": "FORBIDDEN"},
            {"word": "推荐持有", "category": "investment_advice", "type": "FORBIDDEN"},
            {"word": "强烈推荐", "category": "investment_advice", "type": "FORBIDDEN"},
            {"word": "必涨", "category": "investment_advice", "type": "FORBIDDEN"},
            {"word": "稳赚不赔", "category": "investment_advice", "type": "FORBIDDEN"},

            # 收益承诺类
            {"word": "保证收益", "category": "return_promise", "type": "FORBIDDEN"},
            {"word": "年化收益", "category": "return_promise", "type": "WARNING"},
            {"word": "预期收益", "category": "return_promise", "type": "WARNING"},

            # 风险提示类
            {"word": "重大风险", "category": "risk_warning", "type": "WARNING"},
            {"word": "业绩预警", "category": "risk_warning", "type": "WARNING"},
            {"word": "退市风险", "category": "risk_warning", "type": "WARNING"},
        ]
        
        for word_data in sensitive_words:
            existing = session.query(SensitiveWord).filter_by(word=word_data["word"]).first()
            if not existing:
                sensitive_word = SensitiveWord(**word_data)
                session.add(sensitive_word)
        
        session.commit()
        print("✅ 敏感词数据插入成功")
        
        # 生产环境不创建默认用户 - 用户需要通过注册流程创建
        print("✅ 数据库表创建完成，请通过注册页面创建管理员账户")
        
        session.close()
        
    except Exception as e:
        print(f"❌ 插入初始数据失败: {str(e)}")
        if 'session' in locals():
            session.rollback()
            session.close()
        raise


def test_database_connection():
    """测试数据库连接"""
    print("正在测试数据库连接...")
    
    try:
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            print("✅ 数据库连接成功")
            return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")
        return False


def main():
    """主函数"""
    print("=" * 50)
    print("财经新闻Bot - 数据库初始化")
    print("=" * 50)
    
    # 测试数据库连接
    if not test_database_connection():
        print("数据库连接失败，请检查配置")
        return
    
    try:
        # 创建数据表
        create_tables()
        
        # 插入初始数据
        insert_initial_data()
        
        print("\n" + "=" * 50)
        print("✅ 数据库初始化完成！")
        print("=" * 50)
        print("测试用户信息:")
        print("  用户名: admin")
        print("  密码: admin123")
        print("  角色: admin")
        print("=" * 50)
        
    except Exception as e:
        print(f"\n❌ 数据库初始化失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
