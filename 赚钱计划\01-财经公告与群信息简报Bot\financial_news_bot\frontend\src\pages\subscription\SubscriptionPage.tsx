import React, { useEffect } from 'react';
import { Card, Typography, Empty, Button, Space } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch } from '@/store';
import { setPageTitle } from '@/store/slices/uiSlice';

const { Title } = Typography;

const SubscriptionPage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(setPageTitle('订阅管理'));
  }, [dispatch]);

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <Title level={2} style={{ margin: 0 }}>
          我的订阅
        </Title>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => navigate('/subscriptions/create')}
        >
          创建订阅
        </Button>
      </div>
      
      <Card>
        <Empty 
          description="您还没有创建任何订阅"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        >
          <Button type="primary" onClick={() => navigate('/subscriptions/create')}>
            立即创建
          </Button>
        </Empty>
      </Card>
    </div>
  );
};

export default SubscriptionPage;
