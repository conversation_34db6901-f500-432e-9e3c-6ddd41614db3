[tool:pytest]
# pytest配置文件

# 测试目录
testpaths = ../tests

# 最小版本要求
minversion = 6.0

# 添加选项
addopts = 
    --strict-markers
    --strict-config
    --disable-warnings
    --tb=short
    --maxfail=10
    --durations=10

# 标记定义
markers =
    unit: 单元测试
    integration: 集成测试
    performance: 性能测试
    slow: 慢速测试
    external: 需要外部服务的测试
    database: 需要数据库的测试
    redis: 需要Redis的测试
    api: API测试
    crawler: 爬虫测试
    auth: 认证测试
    subscription: 订阅测试
    notification: 通知测试
    backend: 后端测试
    frontend: 前端测试
    security: 安全测试
    e2e: 端到端测试
    stability: 稳定性测试

# 过滤警告
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning

# 测试发现模式
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# 覆盖率配置
[coverage:run]
source = app
omit = 
    */tests/*
    */venv/*
    */migrations/*
    */alembic/*
    app/main.py

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

show_missing = True
precision = 2

[coverage:html]
directory = htmlcov
