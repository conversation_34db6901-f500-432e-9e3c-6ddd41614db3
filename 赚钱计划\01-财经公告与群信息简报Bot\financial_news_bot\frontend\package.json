{"name": "financial-news-bot-frontend", "version": "1.0.0", "description": "财经公告与群信息简报Bot前端应用", "private": true, "dependencies": {"@ant-design/icons": "^5.2.6", "@reduxjs/toolkit": "^1.9.7", "@types/node": "^20.8.7", "@types/react": "^18.2.31", "@types/react-dom": "^18.2.14", "antd": "^5.11.0", "axios": "^1.5.1", "dayjs": "^1.11.10", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^8.1.3", "react-router-dom": "^6.17.0", "react-scripts": "5.0.1", "typescript": "^5.2.2", "web-vitals": "^3.5.0"}, "devDependencies": {"@types/jest": "^29.5.6", "@typescript-eslint/eslint-plugin": "^6.9.0", "@typescript-eslint/parser": "^6.9.0", "eslint": "^8.52.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.0.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write src/**/*.{ts,tsx,css,scss,json}"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8000", "_comment": "代理配置：开发环境使用localhost:8000，生产环境通过环境变量REACT_APP_API_URL配置"}