/**
 * 图片优化工具
 * 支持WebP格式检测、图片压缩、懒加载等功能
 */

// 检测浏览器是否支持WebP格式
export const supportsWebP = (): Promise<boolean> => {
  return new Promise((resolve) => {
    const webP = new Image();
    webP.onload = webP.onerror = () => {
      resolve(webP.height === 2);
    };
    webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
  });
};

// 图片格式转换
export const getOptimizedImageUrl = (originalUrl: string, options: {
  width?: number;
  height?: number;
  quality?: number;
  format?: 'webp' | 'jpeg' | 'png';
} = {}): string => {
  if (!originalUrl) return '';

  const { width, height, quality = 80, format } = options;
  
  // 如果是外部URL，直接返回
  if (originalUrl.startsWith('http')) {
    return originalUrl;
  }

  // 构建优化参数
  const params = new URLSearchParams();
  if (width) params.append('w', width.toString());
  if (height) params.append('h', height.toString());
  if (quality !== 80) params.append('q', quality.toString());
  if (format) params.append('f', format);

  const queryString = params.toString();
  const separator = originalUrl.includes('?') ? '&' : '?';
  
  return queryString ? `${originalUrl}${separator}${queryString}` : originalUrl;
};

// 响应式图片组件的props
export interface ResponsiveImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  style?: React.CSSProperties;
  lazy?: boolean;
  placeholder?: string;
  quality?: number;
  sizes?: string;
  onLoad?: () => void;
  onError?: () => void;
}

// 生成响应式图片的srcSet
export const generateSrcSet = (src: string, sizes: number[] = [320, 640, 1024, 1280]): string => {
  return sizes
    .map(size => `${getOptimizedImageUrl(src, { width: size })} ${size}w`)
    .join(', ');
};

// 图片预加载
export const preloadImage = (src: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve();
    img.onerror = reject;
    img.src = src;
  });
};

// 批量预加载图片
export const preloadImages = async (urls: string[]): Promise<void> => {
  const promises = urls.map(url => preloadImage(url));
  await Promise.allSettled(promises);
};

// 图片懒加载观察器
class LazyImageObserver {
  private observer: IntersectionObserver | null = null;
  private images: Set<HTMLImageElement> = new Set();

  constructor() {
    if (typeof window !== 'undefined' && 'IntersectionObserver' in window) {
      this.observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              const img = entry.target as HTMLImageElement;
              this.loadImage(img);
              this.observer?.unobserve(img);
              this.images.delete(img);
            }
          });
        },
        {
          rootMargin: '50px',
          threshold: 0.1,
        }
      );
    }
  }

  observe(img: HTMLImageElement) {
    if (this.observer) {
      this.images.add(img);
      this.observer.observe(img);
    } else {
      // 降级处理：直接加载图片
      this.loadImage(img);
    }
  }

  unobserve(img: HTMLImageElement) {
    if (this.observer) {
      this.observer.unobserve(img);
      this.images.delete(img);
    }
  }

  private loadImage(img: HTMLImageElement) {
    const src = img.dataset.src;
    const srcset = img.dataset.srcset;
    
    if (src) {
      img.src = src;
      img.removeAttribute('data-src');
    }
    
    if (srcset) {
      img.srcset = srcset;
      img.removeAttribute('data-srcset');
    }

    img.classList.remove('lazy');
    img.classList.add('loaded');
  }

  disconnect() {
    if (this.observer) {
      this.observer.disconnect();
      this.images.clear();
    }
  }
}

// 全局懒加载观察器实例
export const lazyImageObserver = new LazyImageObserver();

// 图片压缩工具
export const compressImage = (
  file: File,
  options: {
    maxWidth?: number;
    maxHeight?: number;
    quality?: number;
    format?: string;
  } = {}
): Promise<Blob> => {
  return new Promise((resolve, reject) => {
    const {
      maxWidth = 1920,
      maxHeight = 1080,
      quality = 0.8,
      format = 'image/jpeg'
    } = options;

    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // 计算新的尺寸
      let { width, height } = img;
      
      if (width > maxWidth) {
        height = (height * maxWidth) / width;
        width = maxWidth;
      }
      
      if (height > maxHeight) {
        width = (width * maxHeight) / height;
        height = maxHeight;
      }

      canvas.width = width;
      canvas.height = height;

      // 绘制图片
      ctx?.drawImage(img, 0, 0, width, height);

      // 转换为Blob
      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('图片压缩失败'));
          }
        },
        format,
        quality
      );
    };

    img.onerror = () => reject(new Error('图片加载失败'));
    img.src = URL.createObjectURL(file);
  });
};

// 获取图片信息
export const getImageInfo = (file: File): Promise<{
  width: number;
  height: number;
  size: number;
  type: string;
}> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    
    img.onload = () => {
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight,
        size: file.size,
        type: file.type,
      });
    };

    img.onerror = () => reject(new Error('无法读取图片信息'));
    img.src = URL.createObjectURL(file);
  });
};

// 图片格式检测
export const detectImageFormat = (file: File): string => {
  const extension = file.name.split('.').pop()?.toLowerCase();
  
  const formatMap: Record<string, string> = {
    jpg: 'image/jpeg',
    jpeg: 'image/jpeg',
    png: 'image/png',
    gif: 'image/gif',
    webp: 'image/webp',
    svg: 'image/svg+xml',
    bmp: 'image/bmp',
  };

  return formatMap[extension || ''] || file.type || 'image/jpeg';
};

// 生成缩略图
export const generateThumbnail = (
  file: File,
  size: number = 200
): Promise<string> => {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      canvas.width = size;
      canvas.height = size;

      // 计算裁剪区域（居中裁剪）
      const { width, height } = img;
      const minDimension = Math.min(width, height);
      const x = (width - minDimension) / 2;
      const y = (height - minDimension) / 2;

      ctx?.drawImage(
        img,
        x, y, minDimension, minDimension,
        0, 0, size, size
      );

      resolve(canvas.toDataURL('image/jpeg', 0.8));
    };

    img.onerror = () => reject(new Error('缩略图生成失败'));
    img.src = URL.createObjectURL(file);
  });
};

// 图片加载状态管理
export class ImageLoadManager {
  private loadingImages: Set<string> = new Set();
  private loadedImages: Set<string> = new Set();
  private failedImages: Set<string> = new Set();

  isLoading(src: string): boolean {
    return this.loadingImages.has(src);
  }

  isLoaded(src: string): boolean {
    return this.loadedImages.has(src);
  }

  isFailed(src: string): boolean {
    return this.failedImages.has(src);
  }

  startLoading(src: string): void {
    this.loadingImages.add(src);
    this.loadedImages.delete(src);
    this.failedImages.delete(src);
  }

  markLoaded(src: string): void {
    this.loadingImages.delete(src);
    this.loadedImages.add(src);
    this.failedImages.delete(src);
  }

  markFailed(src: string): void {
    this.loadingImages.delete(src);
    this.loadedImages.delete(src);
    this.failedImages.add(src);
  }

  clear(): void {
    this.loadingImages.clear();
    this.loadedImages.clear();
    this.failedImages.clear();
  }
}

// 全局图片加载管理器
export const imageLoadManager = new ImageLoadManager();
