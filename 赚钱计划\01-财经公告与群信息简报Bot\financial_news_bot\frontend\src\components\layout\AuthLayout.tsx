import React from 'react';
import { Outlet } from 'react-router-dom';
import { Layout, Row, Col, Typography, Space } from 'antd';
import { TrophyOutlined, SafetyOutlined, RocketOutlined } from '@ant-design/icons';

const { Content } = Layout;
const { Title, Text } = Typography;

const AuthLayout: React.FC = () => {
  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Content>
        <Row style={{ minHeight: '100vh' }}>
          {/* 左侧品牌展示区 */}
          <Col xs={0} md={12} lg={14} xl={16}>
            <div
              style={{
                height: '100%',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
                padding: '40px',
                color: 'white',
                position: 'relative',
                overflow: 'hidden',
              }}
            >
              {/* 背景装饰 */}
              <div
                style={{
                  position: 'absolute',
                  top: '-50%',
                  left: '-50%',
                  width: '200%',
                  height: '200%',
                  background: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="4"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
                  animation: 'float 20s ease-in-out infinite',
                }}
              />

              <div style={{ textAlign: 'center', zIndex: 1 }}>
                <Title level={1} style={{ color: 'white', fontSize: '3rem', marginBottom: '1rem' }}>
                  财经新闻Bot
                </Title>
                <Text style={{ color: 'rgba(255,255,255,0.9)', fontSize: '1.2rem', display: 'block', marginBottom: '3rem' }}>
                  专业的财经信息推送服务平台
                </Text>

                <Space direction="vertical" size="large" style={{ width: '100%' }}>
                  <div style={{ textAlign: 'center' }}>
                    <RocketOutlined style={{ fontSize: '2rem', marginBottom: '1rem' }} />
                    <Title level={4} style={{ color: 'white', margin: 0 }}>
                      实时推送
                    </Title>
                    <Text style={{ color: 'rgba(255,255,255,0.8)' }}>
                      第一时间获取重要财经资讯
                    </Text>
                  </div>

                  <div style={{ textAlign: 'center' }}>
                    <SafetyOutlined style={{ fontSize: '2rem', marginBottom: '1rem' }} />
                    <Title level={4} style={{ color: 'white', margin: 0 }}>
                      智能过滤
                    </Title>
                    <Text style={{ color: 'rgba(255,255,255,0.8)' }}>
                      AI智能筛选，只推送您关心的内容
                    </Text>
                  </div>

                  <div style={{ textAlign: 'center' }}>
                    <TrophyOutlined style={{ fontSize: '2rem', marginBottom: '1rem' }} />
                    <Title level={4} style={{ color: 'white', margin: 0 }}>
                      多渠道支持
                    </Title>
                    <Text style={{ color: 'rgba(255,255,255,0.8)' }}>
                      支持微信、邮件、企业微信等多种推送方式
                    </Text>
                  </div>
                </Space>
              </div>

              {/* 底部装饰 */}
              <div
                style={{
                  position: 'absolute',
                  bottom: '2rem',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  textAlign: 'center',
                }}
              >
                <Text style={{ color: 'rgba(255,255,255,0.6)', fontSize: '0.9rem' }}>
                  © 2024 财经新闻Bot. 专业可靠的财经信息服务
                </Text>
              </div>
            </div>
          </Col>

          {/* 右侧表单区 */}
          <Col xs={24} md={12} lg={10} xl={8}>
            <div
              style={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
                padding: '40px',
                background: '#fff',
              }}
            >
              <div style={{ width: '100%', maxWidth: '400px' }}>
                <Outlet />
              </div>
            </div>
          </Col>
        </Row>
      </Content>

      <style>
        {`
          @keyframes float {
            0%, 100% {
              transform: translateY(0px) rotate(0deg);
            }
            50% {
              transform: translateY(-20px) rotate(180deg);
            }
          }
          
          @media (max-width: 768px) {
            .ant-col-xs-24 {
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            }
          }
        `}
      </style>
    </Layout>
  );
};

export default AuthLayout;
