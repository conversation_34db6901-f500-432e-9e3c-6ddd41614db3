// 性能监控工具
interface PerformanceMetrics {
  // Core Web Vitals
  FCP?: number; // First Contentful Paint
  LCP?: number; // Largest Contentful Paint
  FID?: number; // First Input Delay
  CLS?: number; // Cumulative Layout Shift
  TTFB?: number; // Time to First Byte
  
  // 自定义指标
  pageLoadTime?: number;
  domContentLoaded?: number;
  resourceLoadTime?: number;
  apiResponseTime?: number;
  
  // 内存使用
  memoryUsage?: {
    used: number;
    total: number;
    limit: number;
  };
  
  // 网络信息
  networkInfo?: {
    effectiveType: string;
    downlink: number;
    rtt: number;
  };
}

interface PerformanceEntry {
  timestamp: number;
  url: string;
  userAgent: string;
  metrics: PerformanceMetrics;
  errors?: string[];
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics = {};
  private observers: PerformanceObserver[] = [];
  private startTime: number = performance.now();
  private apiTimings: Map<string, number> = new Map();

  constructor() {
    this.initializeObservers();
    this.monitorPageLoad();
    this.monitorMemoryUsage();
    this.monitorNetworkInfo();
  }

  private initializeObservers() {
    // 监控 Core Web Vitals
    if ('PerformanceObserver' in window) {
      // FCP 和 LCP
      const paintObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.name === 'first-contentful-paint') {
            this.metrics.FCP = entry.startTime;
          }
        }
      });

      try {
        paintObserver.observe({ entryTypes: ['paint'] });
        this.observers.push(paintObserver);
      } catch (e) {
        console.warn('Paint observer not supported');
      }

      // LCP
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        this.metrics.LCP = lastEntry.startTime;
      });

      try {
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        this.observers.push(lcpObserver);
      } catch (e) {
        console.warn('LCP observer not supported');
      }

      // FID
      const fidObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.metrics.FID = (entry as any).processingStart - entry.startTime;
        }
      });

      try {
        fidObserver.observe({ entryTypes: ['first-input'] });
        this.observers.push(fidObserver);
      } catch (e) {
        console.warn('FID observer not supported');
      }

      // CLS
      let clsValue = 0;
      const clsObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (!(entry as any).hadRecentInput) {
            clsValue += (entry as any).value;
            this.metrics.CLS = clsValue;
          }
        }
      });

      try {
        clsObserver.observe({ entryTypes: ['layout-shift'] });
        this.observers.push(clsObserver);
      } catch (e) {
        console.warn('CLS observer not supported');
      }
    }
  }

  private monitorPageLoad() {
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        
        this.metrics.pageLoadTime = performance.now() - this.startTime;
        this.metrics.domContentLoaded = navigation.domContentLoadedEventEnd - navigation.navigationStart;
        this.metrics.TTFB = navigation.responseStart - navigation.navigationStart;
        
        // 资源加载时间
        const resources = performance.getEntriesByType('resource');
        const totalResourceTime = resources.reduce((total, resource) => {
          return total + (resource.responseEnd - resource.startTime);
        }, 0);
        this.metrics.resourceLoadTime = totalResourceTime / resources.length;
      }, 0);
    });
  }

  private monitorMemoryUsage() {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      this.metrics.memoryUsage = {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        limit: memory.jsHeapSizeLimit,
      };
    }
  }

  private monitorNetworkInfo() {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      this.metrics.networkInfo = {
        effectiveType: connection.effectiveType,
        downlink: connection.downlink,
        rtt: connection.rtt,
      };
    }
  }

  // 监控API响应时间
  public startApiTiming(url: string): void {
    this.apiTimings.set(url, performance.now());
  }

  public endApiTiming(url: string): number {
    const startTime = this.apiTimings.get(url);
    if (startTime) {
      const duration = performance.now() - startTime;
      this.apiTimings.delete(url);
      
      // 更新平均API响应时间
      if (!this.metrics.apiResponseTime) {
        this.metrics.apiResponseTime = duration;
      } else {
        this.metrics.apiResponseTime = (this.metrics.apiResponseTime + duration) / 2;
      }
      
      return duration;
    }
    return 0;
  }

  // 获取当前性能指标
  public getMetrics(): PerformanceMetrics {
    this.monitorMemoryUsage(); // 更新内存使用情况
    return { ...this.metrics };
  }

  // 生成性能报告
  public generateReport(): PerformanceEntry {
    return {
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      metrics: this.getMetrics(),
    };
  }

  // 检查性能是否达标
  public checkPerformanceThresholds(): { passed: boolean; issues: string[] } {
    const issues: string[] = [];
    const metrics = this.getMetrics();

    // Core Web Vitals 阈值
    if (metrics.FCP && metrics.FCP > 1800) {
      issues.push(`FCP过慢: ${metrics.FCP.toFixed(0)}ms (建议 < 1800ms)`);
    }

    if (metrics.LCP && metrics.LCP > 2500) {
      issues.push(`LCP过慢: ${metrics.LCP.toFixed(0)}ms (建议 < 2500ms)`);
    }

    if (metrics.FID && metrics.FID > 100) {
      issues.push(`FID过高: ${metrics.FID.toFixed(0)}ms (建议 < 100ms)`);
    }

    if (metrics.CLS && metrics.CLS > 0.1) {
      issues.push(`CLS过高: ${metrics.CLS.toFixed(3)} (建议 < 0.1)`);
    }

    // 自定义阈值
    if (metrics.pageLoadTime && metrics.pageLoadTime > 3000) {
      issues.push(`页面加载时间过长: ${metrics.pageLoadTime.toFixed(0)}ms (建议 < 3000ms)`);
    }

    if (metrics.apiResponseTime && metrics.apiResponseTime > 500) {
      issues.push(`API响应时间过长: ${metrics.apiResponseTime.toFixed(0)}ms (建议 < 500ms)`);
    }

    // 内存使用检查
    if (metrics.memoryUsage) {
      const memoryUsagePercent = (metrics.memoryUsage.used / metrics.memoryUsage.limit) * 100;
      if (memoryUsagePercent > 80) {
        issues.push(`内存使用过高: ${memoryUsagePercent.toFixed(1)}% (建议 < 80%)`);
      }
    }

    return {
      passed: issues.length === 0,
      issues,
    };
  }

  // 发送性能数据到服务器
  public async sendMetrics(): Promise<void> {
    try {
      const report = this.generateReport();
      
      // 使用 sendBeacon 或 fetch 发送数据
      if (navigator.sendBeacon) {
        navigator.sendBeacon('/api/v1/analytics/performance', JSON.stringify(report));
      } else {
        await fetch('/api/v1/analytics/performance', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(report),
        });
      }
    } catch (error) {
      console.warn('Failed to send performance metrics:', error);
    }
  }

  // 清理观察者
  public cleanup(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }
}

// 单例实例
export const performanceMonitor = new PerformanceMonitor();

// 性能优化建议
export const getPerformanceRecommendations = (metrics: PerformanceMetrics): string[] => {
  const recommendations: string[] = [];

  if (metrics.FCP && metrics.FCP > 1800) {
    recommendations.push('优化关键渲染路径，减少阻塞资源');
    recommendations.push('使用资源预加载和预连接');
  }

  if (metrics.LCP && metrics.LCP > 2500) {
    recommendations.push('优化最大内容元素的加载速度');
    recommendations.push('使用图片懒加载和压缩');
  }

  if (metrics.FID && metrics.FID > 100) {
    recommendations.push('减少主线程阻塞时间');
    recommendations.push('使用 Web Workers 处理计算密集型任务');
  }

  if (metrics.CLS && metrics.CLS > 0.1) {
    recommendations.push('为图片和广告设置尺寸属性');
    recommendations.push('避免在现有内容上方插入内容');
  }

  if (metrics.memoryUsage) {
    const memoryUsagePercent = (metrics.memoryUsage.used / metrics.memoryUsage.limit) * 100;
    if (memoryUsagePercent > 80) {
      recommendations.push('检查内存泄漏，及时清理不用的对象');
      recommendations.push('优化大型数据结构的使用');
    }
  }

  return recommendations;
};

// 页面卸载时发送数据
window.addEventListener('beforeunload', () => {
  performanceMonitor.sendMetrics();
});

export default PerformanceMonitor;
