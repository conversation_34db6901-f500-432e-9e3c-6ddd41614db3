"""
API端点集成测试
测试所有API接口的请求响应、状态码、数据格式等
"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.models.user import User
from app.models.subscription import Subscription
from app.models.news import News


@pytest.mark.integration
class TestAuthEndpoints:
    """认证相关API测试"""

    def test_register_user_success(self, client: TestClient):
        """测试用户注册成功"""
        user_data = {
            "username": "newuser",
            "email": "<EMAIL>",
            "password": "password123"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["username"] == user_data["username"]
        assert data["email"] == user_data["email"]
        assert "id" in data
        assert "hashed_password" not in data

    def test_register_user_duplicate_username(self, client: TestClient, test_user: User):
        """测试注册重复用户名"""
        user_data = {
            "username": test_user.username,
            "email": "<EMAIL>",
            "password": "password123"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == 400
        assert "already registered" in response.json()["detail"]

    def test_login_success(self, client: TestClient, test_user: User):
        """测试登录成功"""
        login_data = {
            "username": test_user.username,
            "password": "testpassword"
        }
        
        response = client.post("/api/v1/auth/login", data=login_data)
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"

    def test_login_invalid_credentials(self, client: TestClient, test_user: User):
        """测试登录失败"""
        login_data = {
            "username": test_user.username,
            "password": "wrongpassword"
        }
        
        response = client.post("/api/v1/auth/login", data=login_data)
        
        assert response.status_code == 401
        assert "Incorrect username or password" in response.json()["detail"]

    def test_get_current_user(self, client: TestClient, auth_headers: dict):
        """测试获取当前用户信息"""
        response = client.get("/api/v1/auth/me", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "username" in data
        assert "email" in data
        assert "hashed_password" not in data

    def test_unauthorized_access(self, client: TestClient):
        """测试未授权访问"""
        response = client.get("/api/v1/auth/me")
        
        assert response.status_code == 401


@pytest.mark.integration
class TestUserEndpoints:
    """用户管理API测试"""

    def test_get_users_list(self, client: TestClient, admin_auth_headers: dict):
        """测试获取用户列表（管理员权限）"""
        response = client.get("/api/v1/users/", headers=admin_auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

    def test_get_users_list_unauthorized(self, client: TestClient, auth_headers: dict):
        """测试普通用户获取用户列表（无权限）"""
        response = client.get("/api/v1/users/", headers=auth_headers)
        
        assert response.status_code == 403

    def test_get_user_by_id(self, client: TestClient, admin_auth_headers: dict, test_user: User):
        """测试通过ID获取用户"""
        response = client.get(f"/api/v1/users/{test_user.id}", headers=admin_auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == test_user.id
        assert data["username"] == test_user.username

    def test_update_user_profile(self, client: TestClient, auth_headers: dict, test_user: User):
        """测试更新用户资料"""
        update_data = {
            "email": "<EMAIL>"
        }

        response = client.put(f"/api/v1/users/{test_user.id}", json=update_data, headers=auth_headers)

        assert response.status_code == 200
        data = response.json()
        assert data["email"] == update_data["email"]

    def test_change_password(self, client: TestClient, auth_headers: dict, test_user: User):
        """测试修改密码"""
        password_data = {
            "current_password": "testpassword",
            "new_password": "newpassword123"
        }
        
        response = client.post(f"/api/v1/users/{test_user.id}/change-password", 
                             json=password_data, headers=auth_headers)
        
        assert response.status_code == 200
        assert response.json()["message"] == "Password changed successfully"


@pytest.mark.integration
class TestSubscriptionEndpoints:
    """订阅管理API测试"""

    def test_create_subscription(self, client: TestClient, auth_headers: dict, sample_subscription_data: dict):
        """测试创建订阅"""
        response = client.post("/api/v1/subscriptions/", 
                             json=sample_subscription_data, headers=auth_headers)
        
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == sample_subscription_data["name"]
        assert data["is_active"] is True
        assert "id" in data

    def test_get_user_subscriptions(self, client: TestClient, auth_headers: dict, test_subscription: Subscription):
        """测试获取用户订阅列表"""
        response = client.get("/api/v1/subscriptions/", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 1

    def test_get_subscription_by_id(self, client: TestClient, auth_headers: dict, test_subscription: Subscription):
        """测试通过ID获取订阅"""
        response = client.get(f"/api/v1/subscriptions/{test_subscription.id}", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == test_subscription.id
        assert data["name"] == test_subscription.name

    def test_update_subscription(self, client: TestClient, auth_headers: dict, test_subscription: Subscription):
        """测试更新订阅"""
        update_data = {
            "name": "Updated Subscription",
            "description": "Updated description"
        }
        
        response = client.put(f"/api/v1/subscriptions/{test_subscription.id}", 
                            json=update_data, headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == update_data["name"]
        assert data["description"] == update_data["description"]

    def test_delete_subscription(self, client: TestClient, auth_headers: dict, test_subscription: Subscription):
        """测试删除订阅"""
        response = client.delete(f"/api/v1/subscriptions/{test_subscription.id}", headers=auth_headers)
        
        assert response.status_code == 204

    def test_toggle_subscription_status(self, client: TestClient, auth_headers: dict, test_subscription: Subscription):
        """测试切换订阅状态"""
        response = client.post(f"/api/v1/subscriptions/{test_subscription.id}/toggle", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["is_active"] != test_subscription.is_active


@pytest.mark.integration
class TestNewsEndpoints:
    """新闻相关API测试"""

    def test_get_news_list(self, client: TestClient, auth_headers: dict):
        """测试获取新闻列表"""
        response = client.get("/api/v1/news/", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "items" in data
        assert "total" in data
        assert "page" in data
        assert "size" in data

    def test_get_news_with_pagination(self, client: TestClient, auth_headers: dict):
        """测试新闻列表分页"""
        response = client.get("/api/v1/news/?page=1&size=10", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["page"] == 1
        assert data["size"] == 10

    def test_get_news_by_id(self, client: TestClient, auth_headers: dict, test_news: News):
        """测试通过ID获取新闻"""
        response = client.get(f"/api/v1/news/{test_news.id}", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == test_news.id
        assert data["title"] == test_news.title

    def test_search_news(self, client: TestClient, auth_headers: dict):
        """测试新闻搜索"""
        response = client.get("/api/v1/news/search?q=测试", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "items" in data
        assert "total" in data

    def test_get_news_by_category(self, client: TestClient, auth_headers: dict):
        """测试按分类获取新闻"""
        response = client.get("/api/v1/news/?category=finance", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "items" in data

    def test_bookmark_news(self, client: TestClient, auth_headers: dict, test_news: News):
        """测试收藏新闻"""
        response = client.post(f"/api/v1/news/{test_news.id}/bookmark", headers=auth_headers)
        
        assert response.status_code == 200
        assert response.json()["message"] == "News bookmarked successfully"

    def test_unbookmark_news(self, client: TestClient, auth_headers: dict, test_news: News):
        """测试取消收藏新闻"""
        # 先收藏
        client.post(f"/api/v1/news/{test_news.id}/bookmark", headers=auth_headers)
        
        # 再取消收藏
        response = client.delete(f"/api/v1/news/{test_news.id}/bookmark", headers=auth_headers)
        
        assert response.status_code == 200
        assert response.json()["message"] == "News unbookmarked successfully"

    def test_get_bookmarked_news(self, client: TestClient, auth_headers: dict):
        """测试获取收藏的新闻"""
        response = client.get("/api/v1/news/bookmarks", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "items" in data


@pytest.mark.integration
class TestAnalyticsEndpoints:
    """分析统计API测试"""

    def test_get_dashboard_stats(self, client: TestClient, auth_headers: dict):
        """测试获取仪表板统计"""
        response = client.get("/api/v1/analytics/dashboard", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "total_news" in data
        assert "total_subscriptions" in data
        assert "recent_news" in data

    def test_get_subscription_stats(self, client: TestClient, auth_headers: dict):
        """测试获取订阅统计"""
        response = client.get("/api/v1/analytics/subscriptions", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, dict)

    def test_get_news_trends(self, client: TestClient, auth_headers: dict):
        """测试获取新闻趋势"""
        response = client.get("/api/v1/analytics/trends", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, dict)


@pytest.mark.integration
class TestErrorHandling:
    """错误处理测试"""

    def test_404_not_found(self, client: TestClient):
        """测试404错误"""
        response = client.get("/api/v1/nonexistent")
        
        assert response.status_code == 404

    def test_422_validation_error(self, client: TestClient):
        """测试422验证错误"""
        invalid_data = {
            "username": "",  # 空用户名
            "email": "invalid-email",  # 无效邮箱
            "password": "123"  # 密码太短
        }
        
        response = client.post("/api/v1/auth/register", json=invalid_data)
        
        assert response.status_code == 422
        data = response.json()
        assert "detail" in data

    def test_500_internal_error_handling(self, client: TestClient):
        """测试500内部错误处理"""
        # 这个测试需要模拟内部错误
        # 可以通过mock或者特殊的测试端点来触发
        pass
