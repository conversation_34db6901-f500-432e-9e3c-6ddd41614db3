# 财经新闻Bot测试套件

本目录包含财经新闻Bot项目的所有测试文件，采用统一的目录结构管理。

## 📁 目录结构

```
tests/
├── __init__.py                 # 测试套件初始化
├── conftest.py                 # pytest配置和fixtures
├── README.md                   # 本文档
│
├── unit/                       # 单元测试
│   ├── backend/               # 后端单元测试
│   │   ├── test_ai_service.py
│   │   ├── test_data_processing_pipeline.py
│   │   ├── test_integrated_monitoring_service.py
│   │   ├── test_simplified_crawler_service.py
│   │   ├── test_unified_push_service.py
│   │   └── test_user_service.py
│   └── frontend/              # 前端单元测试（预留）
│
├── integration/               # 集成测试
│   ├── backend/              # 后端集成测试
│   │   ├── test_api_endpoints.py
│   │   ├── test_database_operations.py
│   │   └── test_external_services.py
│   ├── frontend/             # 前端集成测试（预留）
│   ├── final_integration_test.py      # 最终集成测试
│   ├── test_docker_deployment.py     # Docker部署测试
│   ├── test_week6_integration.py     # 第6周集成测试
│   └── verify_data_authenticity.py   # 数据真实性验证
│
├── e2e/                      # 端到端测试
│   ├── example.spec.ts       # 示例E2E测试
│   ├── global-setup.ts       # 全局设置
│   ├── global-teardown.ts    # 全局清理
│   ├── playwright.config.ts  # Playwright配置
│   ├── run-tests.js          # 测试运行脚本
│   └── tests/                # E2E测试用例
│
├── api/                      # API测试
│   └── postman_collection.json
│
├── performance/              # 性能测试
│   ├── concurrent_performance_test.py
│   ├── jmeter-test-plan.jmx
│   └── locustfile.py
│
├── perf/                     # 性能测试（Locust）
│   └── locustfile.py
│
├── security/                 # 安全测试
│   ├── access_control_test.py
│   ├── audit_log_validator.py
│   ├── encryption_test.py
│   ├── protection_mechanisms_test.py
│   ├── security_config_checker.py
│   └── security_scanner.py
│
├── stability/                # 稳定性测试
│   └── stability_test.py
│
└── qa/                       # 质量保证测试
    └── summary_evaluation.py
```

## 🚀 运行测试

### 使用统一测试运行器

```bash
# 运行所有单元测试
python scripts/test-runner.py unit

# 运行所有集成测试
python scripts/test-runner.py integration

# 运行所有测试（带覆盖率）
python scripts/test-runner.py all --coverage

# 运行特定测试文件
python scripts/test-runner.py file --file unit/backend/test_ai_service.py

# 列出所有测试
python scripts/test-runner.py list

# 清理测试文件
python scripts/test-runner.py clean
```

### 直接使用pytest

```bash
# 从backend目录运行（推荐）
cd backend
python -m pytest ../tests/

# 运行特定类型的测试
python -m pytest ../tests/unit/backend/ -v
python -m pytest ../tests/integration/backend/ -v
python -m pytest ../tests/security/ -v
python -m pytest ../tests/performance/ -v

# 使用标记运行测试
python -m pytest ../tests/ -m "unit"
python -m pytest ../tests/ -m "integration"
python -m pytest ../tests/ -m "security"
python -m pytest ../tests/ -m "performance"

# 生成覆盖率报告
python -m pytest ../tests/ --cov=app --cov-report=html
```

## 📊 测试标记

项目使用pytest标记来分类测试：

- `@pytest.mark.unit` - 单元测试
- `@pytest.mark.integration` - 集成测试
- `@pytest.mark.performance` - 性能测试
- `@pytest.mark.security` - 安全测试
- `@pytest.mark.e2e` - 端到端测试
- `@pytest.mark.slow` - 慢速测试
- `@pytest.mark.external` - 需要外部服务的测试
- `@pytest.mark.database` - 需要数据库的测试
- `@pytest.mark.redis` - 需要Redis的测试

## 🔧 测试配置

### pytest配置

测试配置在 `backend/pytest.ini` 中定义：

- 测试目录：`../tests`
- 覆盖率源码：`app`
- HTML报告目录：`htmlcov`

### 测试fixtures

公共fixtures定义在 `tests/conftest.py` 中：

- 数据库连接
- Redis连接
- 测试客户端
- 模拟数据

## 📈 测试覆盖率

目标覆盖率：**80%+**

当前覆盖率可通过以下命令查看：

```bash
python scripts/test-runner.py coverage
```

## 🎯 测试最佳实践

### 单元测试
- 测试单个函数或方法
- 使用mock隔离外部依赖
- 快速执行，无外部依赖

### 集成测试
- 测试组件间交互
- 使用真实的数据库和Redis
- 测试API端点

### E2E测试
- 测试完整用户流程
- 使用Playwright进行浏览器自动化
- 测试前后端集成

### 性能测试
- 使用Locust进行负载测试
- 监控响应时间和吞吐量
- 测试并发场景

### 安全测试
- 测试认证和授权
- 验证输入验证
- 检查安全配置

## 🚨 注意事项

1. **运行位置**：建议从 `backend/` 目录运行pytest命令
2. **环境变量**：确保测试环境变量正确配置
3. **数据库**：集成测试需要测试数据库
4. **外部服务**：某些测试需要外部API服务

## 📝 添加新测试

1. **单元测试**：添加到 `tests/unit/backend/`
2. **集成测试**：添加到 `tests/integration/backend/`
3. **E2E测试**：添加到 `tests/e2e/tests/`
4. **性能测试**：添加到 `tests/performance/`
5. **安全测试**：添加到 `tests/security/`

记住为新测试添加适当的pytest标记！

---

## 🎉 测试整合成果

### 整合前的问题
- ❌ 测试文件分散在3个不同目录
- ❌ 配置文件不统一
- ❌ 难以统一管理和运行
- ❌ 缺乏清晰的分类

### 整合后的优势
- ✅ 统一的目录结构
- ✅ 清晰的测试分类
- ✅ 统一的配置管理
- ✅ 便于维护和扩展

现在所有测试都在 `tests/` 目录下，结构清晰，易于管理！
