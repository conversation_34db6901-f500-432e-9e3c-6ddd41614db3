"""
简报生成服务
提供晨报、晚报等简报的生成、管理和推送功能
"""
import logging
import json
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, func
from jinja2 import Environment, BaseLoader, Template

from ..models.report import Report, ReportTemplate, ReportSection, ReportType, ReportStatus
from ..models.news import News
from ..models.user import User
from ..services.ai_service import AIService
from ..services.content_aggregator import ContentAggregator
from ..utils.text_utils import calculate_read_time, extract_summary

logger = logging.getLogger(__name__)


class ReportService:
    """简报生成服务类"""
    
    def __init__(self, db: Session):
        self.db = db
        self.ai_service = AIService()
        self.content_aggregator = ContentAggregator(db)
        self.jinja_env = Environment(loader=BaseLoader())
        
        # 默认模板配置
        self.default_templates = {
            ReportType.MORNING: self._get_morning_template_config(),
            ReportType.EVENING: self._get_evening_template_config()
        }
    
    async def generate_report(
        self,
        report_type: ReportType,
        template_id: Optional[int] = None,
        report_date: Optional[datetime] = None,
        user: Optional[User] = None,
        personalization_config: Optional[Dict[str, Any]] = None
    ) -> Report:
        """
        生成简报
        
        Args:
            report_type: 简报类型
            template_id: 模板ID，不指定则使用默认模板
            report_date: 简报日期，不指定则使用当前日期
            user: 用户对象，用于个性化
            personalization_config: 个性化配置
            
        Returns:
            Report: 生成的简报对象
        """
        try:
            # 1. 准备生成参数
            if report_date is None:
                report_date = datetime.now()
            
            # 2. 获取模板
            template = await self._get_template(template_id, report_type)
            
            # 3. 创建简报记录
            report = Report(
                title=self._generate_report_title(report_type, report_date),
                type=report_type,
                status=ReportStatus.GENERATING,
                report_date=report_date,
                template_id=template.id if template else None,
                generation_started_at=datetime.now()
            )
            
            self.db.add(report)
            self.db.commit()
            self.db.refresh(report)
            
            logger.info(f"Started generating report {report.id} of type {report_type}")
            
            # 4. 生成简报内容
            await self._generate_report_content(report, template, user, personalization_config)
            
            # 5. 更新简报状态
            report.status = ReportStatus.COMPLETED
            report.generation_completed_at = datetime.now()
            report.generation_duration = int(
                (report.generation_completed_at - report.generation_started_at).total_seconds()
            )
            
            # 6. 计算质量评分
            await self._calculate_quality_scores(report)
            
            self.db.commit()
            
            logger.info(f"Successfully generated report {report.id}")
            return report
            
        except Exception as e:
            logger.error(f"Failed to generate report: {e}")
            if 'report' in locals():
                report.status = ReportStatus.FAILED
                report.error_message = str(e)
                report.generation_completed_at = datetime.now()
                self.db.commit()
            raise
    
    async def _generate_report_content(
        self,
        report: Report,
        template: Optional[ReportTemplate],
        user: Optional[User],
        personalization_config: Optional[Dict[str, Any]]
    ):
        """生成简报内容"""
        try:
            # 1. 获取模板配置
            template_config = template.template_config if template else self.default_templates[report.type]
            sections_config = template.sections if template else template_config["sections"]
            
            # 2. 确定内容时间范围
            time_range = self._calculate_time_range(report.type, report.report_date)
            
            # 3. 生成各个章节
            sections = []
            total_news_count = 0
            total_word_count = 0
            
            for i, section_config in enumerate(sections_config):
                section = await self._generate_section(
                    report, section_config, time_range, user, personalization_config, i
                )
                sections.append(section)
                total_news_count += section.news_count
                total_word_count += section.word_count
            
            # 4. 生成简报摘要
            summary = await self._generate_report_summary(report, sections)
            
            # 5. 生成完整内容
            content = await self._generate_full_content(report, sections, template_config)
            
            # 6. 更新简报信息
            report.content = content
            report.summary = summary
            report.news_count = total_news_count
            report.word_count = total_word_count
            report.read_time = calculate_read_time(total_word_count)
            
            # 7. 保存章节
            for section in sections:
                self.db.add(section)
            
            self.db.commit()
            
        except Exception as e:
            logger.error(f"Failed to generate report content: {e}")
            raise
    
    async def _generate_section(
        self,
        report: Report,
        section_config: Dict[str, Any],
        time_range: Tuple[datetime, datetime],
        user: Optional[User],
        personalization_config: Optional[Dict[str, Any]],
        order_index: int
    ) -> ReportSection:
        """生成单个章节"""
        try:
            section_name = section_config["name"]
            section_title = section_config["title"]
            content_type = section_config["content_type"]
            max_items = section_config.get("max_items", 5)
            
            logger.info(f"Generating section: {section_name}")
            
            # 1. 获取相关新闻
            news_list = await self.content_aggregator.get_section_news(
                content_type=content_type,
                time_range=time_range,
                max_items=max_items,
                user=user,
                personalization_config=personalization_config
            )
            
            # 2. 生成章节内容
            if news_list:
                content = await self._generate_section_content(
                    section_config, news_list, user
                )
                importance_score = await self._calculate_section_importance(
                    news_list, section_config
                )
            else:
                content = f"暂无{section_title}相关内容。"
                importance_score = 0
            
            # 3. 创建章节对象
            section = ReportSection(
                report_id=report.id,
                section_name=section_name,
                section_title=section_title,
                section_order=order_index,
                content=content,
                content_type=content_type,
                news_ids=[news.id for news in news_list],
                news_count=len(news_list),
                word_count=len(content),
                importance_score=importance_score
            )
            
            return section
            
        except Exception as e:
            logger.error(f"Failed to generate section {section_config.get('name', 'unknown')}: {e}")
            # 返回空章节而不是失败
            return ReportSection(
                report_id=report.id,
                section_name=section_config.get("name", "unknown"),
                section_title=section_config.get("title", "未知章节"),
                section_order=order_index,
                content="生成失败，请稍后重试。",
                content_type=section_config.get("content_type", "unknown"),
                news_ids=[],
                news_count=0,
                word_count=0,
                importance_score=0
            )
    
    async def _generate_section_content(
        self,
        section_config: Dict[str, Any],
        news_list: List[News],
        user: Optional[User]
    ) -> str:
        """生成章节内容"""
        try:
            # 1. 准备新闻数据
            news_data = []
            for news in news_list:
                news_data.append({
                    "title": news.title,
                    "summary": news.summary or news.content[:200] + "...",
                    "source": news.source,
                    "published_at": news.published_at.strftime("%H:%M"),
                    "importance": news.importance_score,
                    "url": news.url
                })
            
            # 2. 使用AI生成章节内容
            section_prompt = self._build_section_prompt(section_config, news_data)
            ai_content = await self.ai_service.generate_section_content(
                prompt=section_prompt,
                max_length=section_config.get("max_length", 500)
            )
            
            # 3. 格式化内容
            formatted_content = self._format_section_content(
                ai_content, news_data, section_config
            )
            
            return formatted_content
            
        except Exception as e:
            logger.error(f"Failed to generate section content: {e}")
            # 回退到简单格式
            return self._generate_simple_section_content(news_list, section_config)
    
    def _generate_simple_section_content(
        self,
        news_list: List[News],
        section_config: Dict[str, Any]
    ) -> str:
        """生成简单格式的章节内容（AI失败时的回退方案）"""
        content_lines = []
        
        for i, news in enumerate(news_list[:section_config.get("max_items", 5)], 1):
            time_str = news.published_at.strftime("%H:%M")
            content_lines.append(
                f"{i}. 【{time_str}】{news.title}"
            )
            if news.summary:
                content_lines.append(f"   {news.summary[:100]}...")
            content_lines.append("")
        
        return "\n".join(content_lines)
    
    async def _generate_report_summary(
        self,
        report: Report,
        sections: List[ReportSection]
    ) -> str:
        """生成简报摘要"""
        try:
            # 1. 收集重要章节信息
            important_sections = [
                s for s in sections 
                if s.importance_score >= 70 and s.news_count > 0
            ]
            
            # 2. 构建摘要提示
            summary_data = []
            for section in important_sections:
                summary_data.append({
                    "title": section.section_title,
                    "news_count": section.news_count,
                    "importance": section.importance_score
                })
            
            # 3. 使用AI生成摘要
            summary_prompt = f"""
            请为以下{report.type.value}简报生成一个简洁的摘要（不超过150字）：
            
            简报日期：{report.report_date.strftime('%Y年%m月%d日')}
            包含章节：{summary_data}
            总新闻数：{report.news_count}
            
            要求：
            1. 突出最重要的市场动态
            2. 语言简洁专业
            3. 适合快速阅读
            """
            
            summary = await self.ai_service.generate_text(
                prompt=summary_prompt,
                max_length=150
            )
            
            return summary.strip()
            
        except Exception as e:
            logger.error(f"Failed to generate report summary: {e}")
            return f"{report.type.value}简报包含{report.news_count}条重要财经资讯，涵盖市场动态、公司公告等关键信息。"
    
    async def _generate_full_content(
        self,
        report: Report,
        sections: List[ReportSection],
        template_config: Dict[str, Any]
    ) -> str:
        """生成完整的简报内容"""
        try:
            # 1. 准备模板数据
            template_data = {
                "report": {
                    "title": report.title,
                    "date": report.report_date.strftime('%Y年%m月%d日'),
                    "type": report.type.value,
                    "summary": report.summary
                },
                "sections": [
                    {
                        "title": section.section_title,
                        "content": section.content,
                        "news_count": section.news_count,
                        "importance": section.importance_score
                    }
                    for section in sections
                ],
                "statistics": {
                    "total_news": report.news_count,
                    "total_words": report.word_count,
                    "read_time": report.read_time
                }
            }
            
            # 2. 使用模板生成内容
            template_str = template_config.get("content_template", self._get_default_content_template())
            template = self.jinja_env.from_string(template_str)
            content = template.render(**template_data)
            
            return content
            
        except Exception as e:
            logger.error(f"Failed to generate full content: {e}")
            # 回退到简单拼接
            return self._generate_simple_full_content(report, sections)
    
    def _generate_simple_full_content(
        self,
        report: Report,
        sections: List[ReportSection]
    ) -> str:
        """生成简单格式的完整内容"""
        content_parts = [
            f"# {report.title}",
            f"*{report.report_date.strftime('%Y年%m月%d日')}*",
            "",
            f"**简报摘要：** {report.summary}",
            "",
            "---",
            ""
        ]
        
        for section in sections:
            content_parts.extend([
                f"## {section.section_title}",
                "",
                section.content,
                "",
                "---",
                ""
            ])
        
        content_parts.extend([
            f"*本简报包含{report.news_count}条资讯，预计阅读时间{report.read_time}分钟*"
        ])
        
        return "\n".join(content_parts)
    
    def _calculate_time_range(
        self,
        report_type: ReportType,
        report_date: datetime
    ) -> Tuple[datetime, datetime]:
        """计算内容时间范围"""
        if report_type == ReportType.MORNING:
            # 晨报：前一天15:00到当天07:30
            end_time = report_date.replace(hour=7, minute=30, second=0, microsecond=0)
            start_time = (report_date - timedelta(days=1)).replace(hour=15, minute=0, second=0, microsecond=0)
        elif report_type == ReportType.EVENING:
            # 晚报：当天07:30到18:00
            start_time = report_date.replace(hour=7, minute=30, second=0, microsecond=0)
            end_time = report_date.replace(hour=18, minute=0, second=0, microsecond=0)
        else:
            # 其他类型：最近24小时
            end_time = report_date
            start_time = report_date - timedelta(hours=24)
        
        return start_time, end_time
    
    async def _get_template(
        self,
        template_id: Optional[int],
        report_type: ReportType
    ) -> Optional[ReportTemplate]:
        """获取模板"""
        if template_id:
            return self.db.query(ReportTemplate).filter(
                ReportTemplate.id == template_id,
                ReportTemplate.is_active == True
            ).first()
        else:
            # 获取默认模板
            return self.db.query(ReportTemplate).filter(
                ReportTemplate.type == report_type,
                ReportTemplate.is_default == True,
                ReportTemplate.is_active == True
            ).first()
    
    def _generate_report_title(
        self,
        report_type: ReportType,
        report_date: datetime
    ) -> str:
        """生成简报标题"""
        date_str = report_date.strftime('%Y年%m月%d日')
        
        if report_type == ReportType.MORNING:
            return f"{date_str} 财经晨报"
        elif report_type == ReportType.EVENING:
            return f"{date_str} 财经晚报"
        else:
            return f"{date_str} 财经简报"
    
    def _build_section_prompt(
        self,
        section_config: Dict[str, Any],
        news_data: List[Dict[str, Any]]
    ) -> str:
        """构建章节生成提示"""
        section_title = section_config["title"]
        content_type = section_config["content_type"]
        
        prompt = f"""
        请为"{section_title}"章节生成专业的财经简报内容。
        
        新闻数据：
        {json.dumps(news_data, ensure_ascii=False, indent=2)}
        
        要求：
        1. 内容专业、简洁、易读
        2. 突出重要信息和关键数据
        3. 保持客观中性的语调
        4. 字数控制在{section_config.get('max_length', 500)}字以内
        5. 使用markdown格式
        
        请生成内容：
        """
        
        return prompt
    
    def _format_section_content(
        self,
        ai_content: str,
        news_data: List[Dict[str, Any]],
        section_config: Dict[str, Any]
    ) -> str:
        """格式化章节内容"""
        # 简单的格式化处理
        formatted_content = ai_content.strip()
        
        # 确保内容不为空
        if not formatted_content:
            formatted_content = "暂无相关内容。"
        
        return formatted_content
    
    async def _calculate_section_importance(
        self,
        news_list: List[News],
        section_config: Dict[str, Any]
    ) -> int:
        """计算章节重要性评分"""
        if not news_list:
            return 0
        
        # 基于新闻重要性评分计算章节重要性
        total_score = sum(news.importance_score or 0 for news in news_list)
        avg_score = total_score / len(news_list)
        
        # 根据章节配置调整权重
        priority_weight = {
            "high": 1.2,
            "medium": 1.0,
            "low": 0.8
        }.get(section_config.get("priority", "medium"), 1.0)
        
        final_score = int(avg_score * priority_weight)
        return min(100, max(0, final_score))
    
    async def _calculate_quality_scores(self, report: Report):
        """计算简报质量评分"""
        try:
            # 基础评分
            base_score = 60
            
            # 内容完整性评分
            completeness_score = min(100, (report.news_count / 10) * 100) if report.news_count else 0
            
            # 内容长度评分
            length_score = min(100, (report.word_count / 1000) * 100) if report.word_count else 0
            
            # 章节平衡性评分
            sections = self.db.query(ReportSection).filter(
                ReportSection.report_id == report.id
            ).all()
            
            if sections:
                section_scores = [s.importance_score for s in sections if s.importance_score > 0]
                balance_score = (sum(section_scores) / len(section_scores)) if section_scores else 0
            else:
                balance_score = 0
            
            # 综合评分
            quality_score = int(
                base_score * 0.3 +
                completeness_score * 0.3 +
                length_score * 0.2 +
                balance_score * 0.2
            )
            
            report.quality_score = min(100, max(0, quality_score))
            report.completeness_score = int(completeness_score)
            report.relevance_score = int(balance_score)
            
        except Exception as e:
            logger.error(f"Failed to calculate quality scores: {e}")
            report.quality_score = 60
            report.completeness_score = 60
            report.relevance_score = 60
    
    # 默认模板配置
    def _get_morning_template_config(self) -> Dict[str, Any]:
        """获取晨报默认模板配置"""
        return {
            "sections": [
                {
                    "name": "market_news",
                    "title": "市场要闻",
                    "content_type": "market_news",
                    "max_items": 5,
                    "priority": "high",
                    "max_length": 600
                },
                {
                    "name": "portfolio_news",
                    "title": "持仓公司要闻",
                    "content_type": "portfolio_news",
                    "max_items": 3,
                    "priority": "high",
                    "personalized": True,
                    "max_length": 400
                },
                {
                    "name": "regulatory_news",
                    "title": "监管风向",
                    "content_type": "regulatory_news",
                    "max_items": 2,
                    "priority": "medium",
                    "max_length": 300
                },
                {
                    "name": "industry_news",
                    "title": "行业动态",
                    "content_type": "industry_news",
                    "max_items": 4,
                    "priority": "medium",
                    "max_length": 500
                },
                {
                    "name": "upcoming_events",
                    "title": "今日关注",
                    "content_type": "upcoming_events",
                    "max_items": 3,
                    "priority": "medium",
                    "max_length": 300
                }
            ]
        }
    
    def _get_evening_template_config(self) -> Dict[str, Any]:
        """获取晚报默认模板配置"""
        return {
            "sections": [
                {
                    "name": "market_summary",
                    "title": "市场收盘",
                    "content_type": "market_summary",
                    "max_items": 3,
                    "priority": "high",
                    "max_length": 500
                },
                {
                    "name": "company_earnings",
                    "title": "公司业绩",
                    "content_type": "company_earnings",
                    "max_items": 4,
                    "priority": "high",
                    "max_length": 600
                },
                {
                    "name": "policy_news",
                    "title": "政策解读",
                    "content_type": "policy_news",
                    "max_items": 2,
                    "priority": "medium",
                    "max_length": 400
                },
                {
                    "name": "tomorrow_preview",
                    "title": "明日预览",
                    "content_type": "tomorrow_preview",
                    "max_items": 3,
                    "priority": "medium",
                    "max_length": 300
                }
            ]
        }
    
    def _get_default_content_template(self) -> str:
        """获取默认内容模板"""
        return """
# {{ report.title }}

*{{ report.date }}*

{{ report.summary }}

---

{% for section in sections %}
## {{ section.title }}

{{ section.content }}

---

{% endfor %}

*本简报包含{{ statistics.total_news }}条资讯，预计阅读时间{{ statistics.read_time }}分钟*
        """.strip()
