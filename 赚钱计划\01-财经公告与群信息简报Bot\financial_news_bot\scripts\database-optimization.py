#!/usr/bin/env python3
"""
数据库查询优化脚本
使用EXPLAIN分析SQL查询计划，为高频查询字段添加索引，优化复杂查询语句
"""
import os
import sys
import json
import time
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
import argparse

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

try:
    import pymysql
    from sqlalchemy import create_engine, text
    from sqlalchemy.orm import sessionmaker
    import pandas as pd
except ImportError as e:
    print(f"缺少依赖库: {e}")
    print("请安装: pip install pymysql sqlalchemy pandas")
    sys.exit(1)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseOptimizer:
    """数据库优化器"""
    
    def __init__(self, database_url: str):
        self.database_url = database_url
        self.engine = create_engine(database_url, echo=False)
        self.Session = sessionmaker(bind=self.engine)
        self.optimization_results = []
    
    def analyze_query_performance(self, query: str, description: str = "") -> Dict[str, Any]:
        """分析查询性能"""
        logger.info(f"分析查询性能: {description}")
        
        with self.engine.connect() as conn:
            try:
                # 执行EXPLAIN分析
                explain_query = f"EXPLAIN FORMAT=JSON {query}"
                explain_result = conn.execute(text(explain_query)).fetchone()
                explain_data = json.loads(explain_result[0])
                
                # 执行查询并测量时间
                start_time = time.time()
                result = conn.execute(text(query))
                rows = result.fetchall()
                execution_time = time.time() - start_time
                
                # 分析执行计划
                analysis = self._analyze_execution_plan(explain_data)
                analysis.update({
                    'query': query,
                    'description': description,
                    'execution_time': execution_time,
                    'rows_examined': len(rows),
                    'timestamp': datetime.now().isoformat()
                })
                
                return analysis
                
            except Exception as e:
                logger.error(f"查询分析失败: {e}")
                return {
                    'query': query,
                    'description': description,
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                }
    
    def _analyze_execution_plan(self, explain_data: Dict) -> Dict[str, Any]:
        """分析执行计划"""
        query_block = explain_data.get('query_block', {})
        
        analysis = {
            'cost_info': query_block.get('cost_info', {}),
            'table_scans': [],
            'index_usage': [],
            'join_operations': [],
            'optimization_opportunities': []
        }
        
        # 分析表扫描
        if 'table' in query_block:
            table_info = query_block['table']
            analysis['table_scans'].append({
                'table_name': table_info.get('table_name'),
                'access_type': table_info.get('access_type'),
                'rows_examined': table_info.get('rows_examined_per_scan'),
                'filtered': table_info.get('filtered')
            })
            
            # 检查是否使用了索引
            if table_info.get('key'):
                analysis['index_usage'].append({
                    'table': table_info.get('table_name'),
                    'key': table_info.get('key'),
                    'key_length': table_info.get('key_length')
                })
            else:
                analysis['optimization_opportunities'].append(
                    f"表 {table_info.get('table_name')} 未使用索引，考虑添加索引"
                )
        
        # 分析嵌套循环连接
        if 'nested_loop' in query_block:
            for table in query_block['nested_loop']:
                if 'table' in table:
                    table_info = table['table']
                    if table_info.get('access_type') == 'ALL':
                        analysis['optimization_opportunities'].append(
                            f"表 {table_info.get('table_name')} 进行全表扫描，建议添加索引"
                        )
        
        return analysis
    
    def get_slow_queries(self, min_execution_time: float = 1.0) -> List[Dict[str, Any]]:
        """获取慢查询"""
        logger.info("分析慢查询...")
        
        # 常见的查询模式
        common_queries = [
            {
                'query': "SELECT * FROM news WHERE created_at > DATE_SUB(NOW(), INTERVAL 1 DAY) ORDER BY created_at DESC LIMIT 20",
                'description': "获取最近24小时新闻"
            },
            {
                'query': "SELECT * FROM news WHERE title LIKE '%央行%' OR content LIKE '%央行%' ORDER BY created_at DESC",
                'description': "搜索包含关键词的新闻"
            },
            {
                'query': "SELECT u.*, COUNT(s.id) as subscription_count FROM users u LEFT JOIN subscriptions s ON u.id = s.user_id GROUP BY u.id",
                'description': "用户订阅统计"
            },
            {
                'query': "SELECT n.*, COUNT(b.id) as bookmark_count FROM news n LEFT JOIN bookmarks b ON n.id = b.news_id GROUP BY n.id ORDER BY bookmark_count DESC LIMIT 10",
                'description': "热门收藏新闻"
            },
            {
                'query': "SELECT * FROM push_logs WHERE created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR) AND status = 'failed'",
                'description': "最近1小时失败的推送记录"
            }
        ]
        
        slow_queries = []
        
        for query_info in common_queries:
            analysis = self.analyze_query_performance(
                query_info['query'], 
                query_info['description']
            )
            
            if analysis.get('execution_time', 0) > min_execution_time:
                slow_queries.append(analysis)
        
        return slow_queries
    
    def suggest_indexes(self) -> List[Dict[str, Any]]:
        """建议添加的索引"""
        logger.info("分析索引建议...")
        
        index_suggestions = []
        
        # 基于常见查询模式的索引建议
        suggestions = [
            {
                'table': 'news',
                'columns': ['created_at'],
                'type': 'INDEX',
                'reason': '按时间排序查询频繁'
            },
            {
                'table': 'news',
                'columns': ['title', 'content'],
                'type': 'FULLTEXT',
                'reason': '全文搜索需求'
            },
            {
                'table': 'news',
                'columns': ['category', 'created_at'],
                'type': 'INDEX',
                'reason': '按分类和时间查询'
            },
            {
                'table': 'subscriptions',
                'columns': ['user_id', 'is_active'],
                'type': 'INDEX',
                'reason': '用户订阅查询'
            },
            {
                'table': 'bookmarks',
                'columns': ['user_id', 'created_at'],
                'type': 'INDEX',
                'reason': '用户收藏查询'
            },
            {
                'table': 'push_logs',
                'columns': ['created_at', 'status'],
                'type': 'INDEX',
                'reason': '推送日志查询'
            },
            {
                'table': 'users',
                'columns': ['email'],
                'type': 'UNIQUE',
                'reason': '邮箱唯一性约束'
            },
            {
                'table': 'users',
                'columns': ['is_active', 'created_at'],
                'type': 'INDEX',
                'reason': '活跃用户查询'
            }
        ]
        
        # 检查索引是否已存在
        for suggestion in suggestions:
            if not self._index_exists(suggestion['table'], suggestion['columns']):
                index_suggestions.append(suggestion)
        
        return index_suggestions
    
    def _index_exists(self, table: str, columns: List[str]) -> bool:
        """检查索引是否存在"""
        try:
            with self.engine.connect() as conn:
                query = f"SHOW INDEX FROM {table}"
                result = conn.execute(text(query)).fetchall()
                
                # 检查是否有匹配的索引
                existing_indexes = {}
                for row in result:
                    key_name = row[2]  # Key_name
                    column_name = row[4]  # Column_name
                    seq_in_index = row[3]  # Seq_in_index
                    
                    if key_name not in existing_indexes:
                        existing_indexes[key_name] = []
                    existing_indexes[key_name].append((seq_in_index, column_name))
                
                # 检查是否有匹配的列组合
                for index_name, index_columns in existing_indexes.items():
                    index_columns.sort()  # 按序号排序
                    index_column_names = [col[1] for col in index_columns]
                    
                    if index_column_names == columns:
                        return True
                
                return False
        except Exception as e:
            logger.warning(f"检查索引失败 {table}.{columns}: {e}")
            return False
    
    def create_indexes(self, index_suggestions: List[Dict[str, Any]], dry_run: bool = True) -> List[Dict[str, Any]]:
        """创建索引"""
        logger.info(f"创建索引 (dry_run={dry_run})...")
        
        results = []
        
        for suggestion in index_suggestions:
            table = suggestion['table']
            columns = suggestion['columns']
            index_type = suggestion['type']
            
            # 生成索引名称
            if index_type == 'UNIQUE':
                index_name = f"uk_{table}_{'_'.join(columns)}"
            elif index_type == 'FULLTEXT':
                index_name = f"ft_{table}_{'_'.join(columns)}"
            else:
                index_name = f"idx_{table}_{'_'.join(columns)}"
            
            # 生成CREATE INDEX语句
            if index_type == 'FULLTEXT':
                sql = f"CREATE FULLTEXT INDEX {index_name} ON {table} ({', '.join(columns)})"
            elif index_type == 'UNIQUE':
                sql = f"CREATE UNIQUE INDEX {index_name} ON {table} ({', '.join(columns)})"
            else:
                sql = f"CREATE INDEX {index_name} ON {table} ({', '.join(columns)})"
            
            result = {
                'table': table,
                'columns': columns,
                'index_name': index_name,
                'sql': sql,
                'type': index_type,
                'reason': suggestion['reason']
            }
            
            if not dry_run:
                try:
                    with self.engine.connect() as conn:
                        start_time = time.time()
                        conn.execute(text(sql))
                        conn.commit()
                        execution_time = time.time() - start_time
                        
                        result.update({
                            'status': 'success',
                            'execution_time': execution_time
                        })
                        logger.info(f"索引创建成功: {index_name}")
                        
                except Exception as e:
                    result.update({
                        'status': 'failed',
                        'error': str(e)
                    })
                    logger.error(f"索引创建失败 {index_name}: {e}")
            else:
                result['status'] = 'dry_run'
                logger.info(f"[DRY RUN] 将创建索引: {sql}")
            
            results.append(result)
        
        return results
    
    def optimize_connection_pool(self) -> Dict[str, Any]:
        """优化连接池配置"""
        logger.info("分析连接池配置...")
        
        with self.engine.connect() as conn:
            # 获取当前连接池状态
            variables = {}
            
            # 获取相关配置变量
            config_vars = [
                'max_connections',
                'max_user_connections',
                'thread_cache_size',
                'table_open_cache',
                'query_cache_size',
                'innodb_buffer_pool_size',
                'wait_timeout',
                'interactive_timeout'
            ]
            
            for var in config_vars:
                try:
                    result = conn.execute(text(f"SHOW VARIABLES LIKE '{var}'")).fetchone()
                    if result:
                        variables[var] = result[1]
                except Exception as e:
                    logger.warning(f"获取变量失败 {var}: {e}")
            
            # 获取连接状态
            status_vars = [
                'Threads_connected',
                'Threads_running',
                'Max_used_connections',
                'Aborted_connects',
                'Connection_errors_max_connections'
            ]
            
            status = {}
            for var in status_vars:
                try:
                    result = conn.execute(text(f"SHOW STATUS LIKE '{var}'")).fetchone()
                    if result:
                        status[var] = result[1]
                except Exception as e:
                    logger.warning(f"获取状态失败 {var}: {e}")
        
        # 生成优化建议
        recommendations = []
        
        max_connections = int(variables.get('max_connections', 0))
        max_used_connections = int(status.get('Max_used_connections', 0))
        
        if max_used_connections > max_connections * 0.8:
            recommendations.append("考虑增加max_connections值")
        
        if int(status.get('Aborted_connects', 0)) > 0:
            recommendations.append("检查连接超时配置和网络稳定性")
        
        return {
            'current_config': variables,
            'current_status': status,
            'recommendations': recommendations,
            'suggested_config': {
                'max_connections': max(max_connections, 200),
                'wait_timeout': 28800,
                'interactive_timeout': 28800,
                'thread_cache_size': 16
            }
        }
    
    def generate_optimization_report(self) -> Dict[str, Any]:
        """生成优化报告"""
        logger.info("生成数据库优化报告...")
        
        # 分析慢查询
        slow_queries = self.get_slow_queries()
        
        # 获取索引建议
        index_suggestions = self.suggest_indexes()
        
        # 分析连接池
        connection_pool_analysis = self.optimize_connection_pool()
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'database_url': self.database_url.split('@')[1] if '@' in self.database_url else 'hidden',
            'slow_queries': slow_queries,
            'index_suggestions': index_suggestions,
            'connection_pool_analysis': connection_pool_analysis,
            'summary': {
                'slow_queries_count': len(slow_queries),
                'suggested_indexes_count': len(index_suggestions),
                'total_optimization_opportunities': len(slow_queries) + len(index_suggestions)
            },
            'next_steps': [
                "审查慢查询并优化SQL语句",
                "创建建议的索引（建议先在测试环境验证）",
                "调整数据库连接池配置",
                "定期监控数据库性能",
                "建立查询性能基线"
            ]
        }
        
        return report
    
    def save_report(self, report: Dict[str, Any], output_file: str):
        """保存优化报告"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            logger.info(f"数据库优化报告已保存: {output_file}")
        except Exception as e:
            logger.error(f"保存报告失败: {e}")


def main():
    parser = argparse.ArgumentParser(description='数据库查询优化工具')
    parser.add_argument('--database-url', required=True, help='数据库连接URL')
    parser.add_argument('--output', default='database_optimization_report.json', help='输出文件')
    parser.add_argument('--create-indexes', action='store_true', help='实际创建索引（默认为dry-run）')
    parser.add_argument('--min-slow-time', type=float, default=1.0, help='慢查询阈值（秒）')
    
    args = parser.parse_args()
    
    try:
        optimizer = DatabaseOptimizer(args.database_url)
        
        # 生成优化报告
        report = optimizer.generate_optimization_report()
        
        # 创建索引（如果指定）
        if args.create_indexes and report['index_suggestions']:
            logger.info("开始创建建议的索引...")
            index_results = optimizer.create_indexes(
                report['index_suggestions'], 
                dry_run=False
            )
            report['index_creation_results'] = index_results
        
        # 保存报告
        optimizer.save_report(report, args.output)
        
        # 输出摘要
        print(f"\n=== 数据库优化分析摘要 ===")
        print(f"慢查询数量: {report['summary']['slow_queries_count']}")
        print(f"建议索引数量: {report['summary']['suggested_indexes_count']}")
        print(f"总优化机会: {report['summary']['total_optimization_opportunities']}")
        
        if report['slow_queries']:
            print(f"\n=== 慢查询 ===")
            for i, query in enumerate(report['slow_queries'], 1):
                print(f"{i}. {query['description']}: {query['execution_time']:.3f}s")
        
        if report['index_suggestions']:
            print(f"\n=== 索引建议 ===")
            for i, suggestion in enumerate(report['index_suggestions'], 1):
                print(f"{i}. {suggestion['table']}.{suggestion['columns']}: {suggestion['reason']}")
        
        print(f"\n📋 详细报告: {args.output}")
        
    except Exception as e:
        logger.error(f"优化分析失败: {e}")
        return 1
    
    return 0


if __name__ == '__main__':
    exit(main())
