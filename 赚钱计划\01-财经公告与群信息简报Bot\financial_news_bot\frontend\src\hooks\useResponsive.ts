import { useState, useEffect } from 'react';

export interface BreakpointConfig {
  xs: number;  // 超小屏幕
  sm: number;  // 小屏幕
  md: number;  // 中等屏幕
  lg: number;  // 大屏幕
  xl: number;  // 超大屏幕
  xxl: number; // 超超大屏幕
}

export interface ResponsiveInfo {
  // 屏幕尺寸
  width: number;
  height: number;
  
  // 断点状态
  xs: boolean;
  sm: boolean;
  md: boolean;
  lg: boolean;
  xl: boolean;
  xxl: boolean;
  
  // 设备类型
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  
  // 方向
  isLandscape: boolean;
  isPortrait: boolean;
  
  // 特殊状态
  isTouchDevice: boolean;
  isHighDPI: boolean;
  
  // 当前断点
  currentBreakpoint: string;
}

const defaultBreakpoints: BreakpointConfig = {
  xs: 480,
  sm: 576,
  md: 768,
  lg: 1024,
  xl: 1200,
  xxl: 1600,
};

const useResponsive = (customBreakpoints?: Partial<BreakpointConfig>): ResponsiveInfo => {
  const breakpoints = { ...defaultBreakpoints, ...customBreakpoints };
  
  const [responsiveInfo, setResponsiveInfo] = useState<ResponsiveInfo>(() => {
    if (typeof window === 'undefined') {
      // SSR 默认值
      return {
        width: 1200,
        height: 800,
        xs: false,
        sm: false,
        md: false,
        lg: true,
        xl: true,
        xxl: false,
        isMobile: false,
        isTablet: false,
        isDesktop: true,
        isLandscape: true,
        isPortrait: false,
        isTouchDevice: false,
        isHighDPI: false,
        currentBreakpoint: 'lg',
      };
    }
    
    return calculateResponsiveInfo(window.innerWidth, window.innerHeight, breakpoints);
  });

  useEffect(() => {
    const handleResize = () => {
      const newInfo = calculateResponsiveInfo(
        window.innerWidth,
        window.innerHeight,
        breakpoints
      );
      setResponsiveInfo(newInfo);
    };

    const handleOrientationChange = () => {
      // 延迟处理方向变化，等待浏览器更新尺寸
      setTimeout(handleResize, 100);
    };

    // 初始计算
    handleResize();

    // 监听事件
    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleOrientationChange);

    // 清理事件监听器
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleOrientationChange);
    };
  }, [breakpoints]);

  return responsiveInfo;
};

function calculateResponsiveInfo(
  width: number,
  height: number,
  breakpoints: BreakpointConfig
): ResponsiveInfo {
  // 断点状态
  const xs = width >= breakpoints.xs;
  const sm = width >= breakpoints.sm;
  const md = width >= breakpoints.md;
  const lg = width >= breakpoints.lg;
  const xl = width >= breakpoints.xl;
  const xxl = width >= breakpoints.xxl;

  // 设备类型判断
  const isMobile = width < breakpoints.md;
  const isTablet = width >= breakpoints.md && width < breakpoints.lg;
  const isDesktop = width >= breakpoints.lg;

  // 方向判断
  const isLandscape = width > height;
  const isPortrait = height >= width;

  // 特殊状态检测
  const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  const isHighDPI = window.devicePixelRatio > 1;

  // 当前断点
  let currentBreakpoint = 'xs';
  if (xxl) currentBreakpoint = 'xxl';
  else if (xl) currentBreakpoint = 'xl';
  else if (lg) currentBreakpoint = 'lg';
  else if (md) currentBreakpoint = 'md';
  else if (sm) currentBreakpoint = 'sm';

  return {
    width,
    height,
    xs,
    sm,
    md,
    lg,
    xl,
    xxl,
    isMobile,
    isTablet,
    isDesktop,
    isLandscape,
    isPortrait,
    isTouchDevice,
    isHighDPI,
    currentBreakpoint,
  };
}

export default useResponsive;

// 响应式工具函数
export const useBreakpoint = (breakpoint: keyof BreakpointConfig) => {
  const responsive = useResponsive();
  return responsive[breakpoint];
};

export const useIsMobile = () => {
  const responsive = useResponsive();
  return responsive.isMobile;
};

export const useIsTablet = () => {
  const responsive = useResponsive();
  return responsive.isTablet;
};

export const useIsDesktop = () => {
  const responsive = useResponsive();
  return responsive.isDesktop;
};

// 响应式值Hook
export const useResponsiveValue = <T>(values: {
  xs?: T;
  sm?: T;
  md?: T;
  lg?: T;
  xl?: T;
  xxl?: T;
  default: T;
}): T => {
  const responsive = useResponsive();
  
  if (responsive.xxl && values.xxl !== undefined) return values.xxl;
  if (responsive.xl && values.xl !== undefined) return values.xl;
  if (responsive.lg && values.lg !== undefined) return values.lg;
  if (responsive.md && values.md !== undefined) return values.md;
  if (responsive.sm && values.sm !== undefined) return values.sm;
  if (responsive.xs && values.xs !== undefined) return values.xs;
  
  return values.default;
};
