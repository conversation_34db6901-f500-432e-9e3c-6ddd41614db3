"""
深圳证券交易所（SZSE）API爬虫

目标API：深交所公开API接口
功能：获取上市公司信息披露数据
"""

import asyncio
import json
import re
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from urllib.parse import urlencode
import logging

from .base_crawler import BaseCrawler

logger = logging.getLogger(__name__)


class SZSECrawler(BaseCrawler):
    """深圳证券交易所API爬虫"""
    
    def __init__(self, rate_limit: float = 1.5):
        """
        初始化SZSE爬虫
        
        Args:
            rate_limit: 请求间隔（秒），默认1.5秒
        """
        super().__init__(
            name="SZSE_Crawler",
            base_url="http://www.szse.cn",
            rate_limit=rate_limit
        )
        
        # SZSE API端点
        self.api_base = "http://www.szse.cn/api/report/ShowReport"
        self.disclosure_api = "http://reportdocs.static.szse.cn/files"
        
        # 报告类型映射
        self.report_types = {
            "010301": "年报",
            "010302": "半年报",
            "010303": "一季报", 
            "010304": "三季报",
            "010305": "业绩预告",
            "010306": "业绩快报",
            "010307": "权益分派",
            "010308": "重大事项",
            "010309": "公司治理"
        }
        
        # 板块代码
        self.board_codes = {
            "主板": "sz",
            "中小板": "sme", 
            "创业板": "gem"
        }
    
    def _format_date(self, date_obj: datetime) -> str:
        """
        格式化日期为API所需格式
        
        Args:
            date_obj: 日期对象
            
        Returns:
            格式化的日期字符串
        """
        return date_obj.strftime("%Y-%m-%d")
    
    def _parse_date(self, date_str: str) -> Optional[datetime]:
        """
        解析日期字符串
        
        Args:
            date_str: 日期字符串
            
        Returns:
            解析后的datetime对象或None
        """
        if not date_str:
            return None
        
        # 常见的日期格式
        date_formats = [
            "%Y-%m-%d",
            "%Y/%m/%d",
            "%Y年%m月%d日",
            "%Y-%m-%d %H:%M:%S",
            "%Y/%m/%d %H:%M:%S"
        ]
        
        for fmt in date_formats:
            try:
                return datetime.strptime(date_str.strip(), fmt)
            except ValueError:
                continue
        
        logger.warning(f"无法解析日期格式: {date_str}")
        return None
    
    def _extract_stock_code(self, text: str) -> Optional[str]:
        """
        从文本中提取深交所股票代码
        
        Args:
            text: 文本内容
            
        Returns:
            股票代码或None
        """
        # 深交所股票代码格式：6位数字，以0或3开头
        patterns = [
            r'\b0\d{5}\b',  # 主板、中小板
            r'\b3\d{5}\b'   # 创业板
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                return match.group()
        
        return None
    
    def _categorize_report(self, report_type: str, title: str) -> str:
        """
        根据报告类型和标题进行分类
        
        Args:
            report_type: 报告类型代码
            title: 标题
            
        Returns:
            分类结果
        """
        type_name = self.report_types.get(report_type, "")
        text = (title + " " + type_name).lower()
        
        if any(keyword in text for keyword in ["年报", "半年报", "季报", "业绩", "财务"]):
            return "finance"
        elif any(keyword in text for keyword in ["治理", "董事", "股东", "监事"]):
            return "governance"
        elif any(keyword in text for keyword in ["分红", "派息", "权益"]):
            return "announcement"
        elif any(keyword in text for keyword in ["重大", "重组", "并购"]):
            return "announcement"
        elif any(keyword in text for keyword in ["违规", "处罚", "监管"]):
            return "regulation"
        else:
            return "announcement"
    
    async def fetch_news_list(self, start_date: Optional[datetime] = None, 
                             end_date: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """
        获取新闻列表
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            新闻项目列表
        """
        if not start_date:
            start_date = datetime.now() - timedelta(days=7)
        if not end_date:
            end_date = datetime.now()
        
        news_list = []
        
        # 遍历不同的报告类型
        for report_type in ["010301", "010302", "010305", "010308"]:  # 主要类型
            try:
                type_news = await self._fetch_by_report_type(
                    report_type, start_date, end_date
                )
                news_list.extend(type_news)
                
                # 避免请求过快
                await asyncio.sleep(0.5)
                
            except Exception as e:
                logger.error(f"[{self.name}] 获取报告类型 {report_type} 失败: {str(e)}")
                continue
        
        logger.info(f"[{self.name}] 获取到 {len(news_list)} 条新闻")
        return news_list
    
    async def _fetch_by_report_type(self, report_type: str, 
                                   start_date: datetime, 
                                   end_date: datetime) -> List[Dict[str, Any]]:
        """
        根据报告类型获取数据
        
        Args:
            report_type: 报告类型代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            新闻项目列表
        """
        news_list = []
        page = 1
        page_size = 30
        
        while True:
            try:
                # 构建API参数
                params = {
                    "CATALOGID": report_type,
                    "PAGENO": str(page),
                    "PAGESIZE": str(page_size),
                    "txtStartDate": self._format_date(start_date),
                    "txtEndDate": self._format_date(end_date),
                    "random": str(int(datetime.now().timestamp() * 1000))
                }
                
                # 构建完整URL
                url = f"{self.api_base}?{urlencode(params)}"
                
                # 发起请求
                response = await self.make_request(url)
                if not response:
                    break
                
                # 解析JSON响应
                try:
                    data = response.json()
                except json.JSONDecodeError:
                    logger.error(f"[{self.name}] JSON解析失败: {report_type}")
                    break
                
                # 提取数据
                items = data.get("data", [])
                if not items:
                    break
                
                # 处理每个项目
                for item in items:
                    news_item = self._parse_api_item(item, report_type)
                    if news_item:
                        news_list.append(news_item)
                
                # 检查是否还有更多页面
                total_pages = data.get("totalPages", 1)
                if page >= total_pages:
                    break
                
                page += 1
                
            except Exception as e:
                logger.error(f"[{self.name}] 获取第{page}页数据失败: {str(e)}")
                break
        
        return news_list
    
    def _parse_api_item(self, item: Dict[str, Any], report_type: str) -> Optional[Dict[str, Any]]:
        """
        解析API返回的单个项目
        
        Args:
            item: API返回的项目数据
            report_type: 报告类型
            
        Returns:
            标准化的新闻项目或None
        """
        try:
            # 提取基本信息
            title = item.get("doctitle", "").strip()
            if not title:
                return None
            
            company_code = item.get("seccode", "")
            company_name = item.get("secname", "")
            pub_date_str = item.get("pubdate", "")
            
            # 解析日期
            pub_date = self._parse_date(pub_date_str)
            
            # 构建文档URL
            doc_url = ""
            if "dockey" in item:
                doc_url = f"{self.disclosure_api}/{item['dockey']}.PDF"
            
            return {
                'title': title,
                'source': 'szse',
                'source_url': doc_url,
                'source_id': item.get("dockey", ""),
                'published_at': pub_date,
                'stock_code': company_code,
                'company_name': company_name,
                'report_type': report_type,
                'category': self._categorize_report(report_type, title),
                'raw_data': item
            }
            
        except Exception as e:
            logger.error(f"[{self.name}] 解析API项目失败: {str(e)}")
            return None
    
    async def fetch_news_detail(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取新闻详情
        
        Args:
            item: 新闻项目基本信息
            
        Returns:
            完整的新闻数据
        """
        # 对于PDF文档，我们无法直接提取内容
        # 这里返回基本信息，内容留空
        title = item['title']
        
        # 根据标题生成简单摘要
        summary = self._generate_summary(item)
        
        return self._build_news_item(item, "", summary)
    
    def _generate_summary(self, item: Dict[str, Any]) -> str:
        """
        根据项目信息生成摘要
        
        Args:
            item: 新闻项目信息
            
        Returns:
            生成的摘要
        """
        title = item['title']
        company_name = item.get('company_name', '')
        stock_code = item.get('stock_code', '')
        report_type = item.get('report_type', '')
        
        type_name = self.report_types.get(report_type, "公告")
        
        if company_name and stock_code:
            summary = f"{company_name}({stock_code})发布{type_name}：{title}"
        elif company_name:
            summary = f"{company_name}发布{type_name}：{title}"
        else:
            summary = f"{type_name}：{title}"
        
        return summary[:200]  # 限制长度
    
    def _build_news_item(self, item: Dict[str, Any], content: str, summary: str) -> Dict[str, Any]:
        """
        构建标准化的新闻项目
        
        Args:
            item: 基本新闻信息
            content: 正文内容
            summary: 摘要
            
        Returns:
            标准化的新闻项目
        """
        # 提取实体信息
        companies, stock_codes = self.extract_companies_and_codes(item['title'] + " " + content)
        
        # 添加已知的公司和股票代码
        if item.get('stock_code'):
            stock_codes.append(item['stock_code'])
        if item.get('company_name'):
            companies.append(item['company_name'])
        
        # 去重
        companies = list(set(companies))
        stock_codes = list(set(stock_codes))
        
        return {
            'title': item['title'],
            'content': content,
            'summary': summary,
            'source': 'szse',
            'source_url': item.get('source_url', ''),
            'source_id': item.get('source_id', ''),
            'published_at': item.get('published_at'),
            'category': item.get('category', 'announcement'),
            'companies': companies,
            'stock_codes': stock_codes,
            'importance_score': self.calculate_importance_score(item),
            'content_hash': self.generate_content_hash(item['title'] + content)
        }
    
    async def test_connection(self) -> bool:
        """
        测试API连接
        
        Returns:
            连接是否成功
        """
        try:
            # 测试获取少量数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=1)
            
            params = {
                "CATALOGID": "010301",  # 年报
                "PAGENO": "1",
                "PAGESIZE": "1",
                "txtStartDate": self._format_date(start_date),
                "txtEndDate": self._format_date(end_date),
                "random": str(int(datetime.now().timestamp() * 1000))
            }
            
            url = f"{self.api_base}?{urlencode(params)}"
            response = await self.make_request(url)
            
            if response and response.status_code == 200:
                logger.info(f"[{self.name}] API连接测试成功")
                return True
            else:
                logger.error(f"[{self.name}] API连接测试失败")
                return False
                
        except Exception as e:
            logger.error(f"[{self.name}] API连接测试异常: {str(e)}")
            return False
