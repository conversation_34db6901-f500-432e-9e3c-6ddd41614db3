# 财经新闻Bot环境变量配置模板
# 请复制此文件为 .env 并填入实际的配置值
# 警告：请勿将 .env 文件提交到版本控制系统

# ===========================================
# 数据库配置（必需）
# ===========================================
# MySQL数据库连接URL - Docker内部数据库
# 格式：mysql+pymysql://用户名:密码@主机:端口/数据库名
DATABASE_URL=mysql+pymysql://velen:Lovejq4ever.@mysql:3306/financial_news_bot?charset=utf8mb4

# MySQL数据库配置（必需）- Docker内部服务
MYSQL_ROOT_PASSWORD=Lovejq4ever.
MYSQL_DATABASE=financial_news_bot
MYSQL_HOST=mysql
MYSQL_PORT=3306
MYSQL_USER=velen

# 数据库连接池配置（可选）
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600
DB_POOL_PRE_PING=true

# ===========================================
# JWT和安全配置（必需）
# ===========================================
# JWT密钥 - 必须至少32个字符，请生成强密钥
SECRET_KEY=X2yZsc_rMOiOUj66JqTFQVb_0E09xqCBod54KH7JVIBNqI4CrhGHrc-xSy9AMHIiK_WRGMndfDMmspp06OXcmw
JWT_SECRET_KEY=TxJlpUpwRlTFWQ6Vgoi_lkLrrS2wgWZgpxkKToqg7jkma7k64vpvGYBZyXYHjGPzfbKRhLqqOsfO7r_Wq5zODQ
REFRESH_SECRET_KEY=A2B3C4D5E6F7G8H9I0JkLmNpQrStUvWxYz1A2B3C4D5E6F7G8H9I0JkLmNpQrStUv
# JWT算法
ALGORITHM=HS256
# 访问令牌过期时间（分钟）
ACCESS_TOKEN_EXPIRE_MINUTES=30

# ===========================================
# AI服务配置（必需）
# ===========================================
# GLM-4.5 Flash API密钥 - 从智谱AI获取
GLM_API_KEY=51354999ac1e4c10bc35afea9b107ce8.LUWGMT4SFYqs8hQs
GLM_BASE_URL=https://open.bigmodel.cn/api/paas/v4
GLM_MODEL=glm-4.5-flash
GLM_MAX_TOKENS=4096
GLM_TEMPERATURE=0.7
GLM_TIMEOUT=60
GLM_MAX_RETRIES=3

# ===========================================
# Redis配置 - Docker内部服务
# ===========================================
REDIS_URL=redis://redis:6379/0
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0

# ===========================================
# 应用配置
# ===========================================
# 调试模式（生产环境必须设置为false）
DEBUG=false
# 允许的主机列表（逗号分隔）
ALLOWED_HOSTS=localhost,127.0.0.1
# 时区设置
TZ=Asia/Shanghai

# ===========================================
# 服务端口配置
# ===========================================
BACKEND_PORT=8000
FRONTEND_PORT=80
NGINX_PORT=8080

# ===========================================
# 推送服务配置（可选）
# ===========================================
# 微信群机器人Webhook
WECHAT_GROUP_WEBHOOK=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=your-key

# 飞书机器人Webhook
FEISHU_WEBHOOK=https://open.feishu.cn/open-apis/bot/v2/hook/your-hook-id

# 企业微信配置
WECHAT_WORK_CORP_ID=your-corp-id
WECHAT_WORK_CORP_SECRET=your-corp-secret
WECHAT_WORK_AGENT_ID=your-agent-id

# 邮件SMTP配置
EMAIL_SMTP_HOST=smtp.example.com
EMAIL_SMTP_PORT=587
EMAIL_SMTP_USER=<EMAIL>
EMAIL_SMTP_PASSWORD=your-email-password
EMAIL_SMTP_USE_TLS=true

# ===========================================
# 日志和监控配置
# ===========================================
LOG_LEVEL=INFO
DB_ECHO=false
DB_ECHO_POOL=false

# ===========================================
# 爬虫配置
# ===========================================
CRAWLER_REQUEST_DELAY=1.0
CRAWLER_TIMEOUT=30
CRAWLER_MAX_RETRIES=3

# ===========================================
# 缓存配置
# ===========================================
CACHE_EXPIRE_SECONDS=3600
CACHE_NEWS_LIST_EXPIRE=300
CACHE_NEWS_STATS_EXPIRE=600
CACHE_SEARCH_EXPIRE=300

# ===========================================
# 分页配置
# ===========================================
DEFAULT_PAGE_SIZE=20
MAX_PAGE_SIZE=100