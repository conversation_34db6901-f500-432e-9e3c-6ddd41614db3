"""
内容处理服务模块
实现内容分类、摘要生成、关键词提取、数据质量检查等功能
"""
import asyncio
import logging
import re
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass

import jieba
# import numpy as np
# from sklearn.feature_extraction.text import TfidfVectorizer
# from sklearn.naive_bayes import MultinomialNB
# from sklearn.pipeline import Pipeline
# import pickle
import os

from app.config import settings
from app.services.ai_service import get_glm_service

logger = logging.getLogger(__name__)


@dataclass
class ContentQualityResult:
    """内容质量检查结果"""
    is_valid: bool
    score: float
    issues: List[str]
    missing_fields: List[str]
    warnings: List[str]


@dataclass
class ProcessedContent:
    """处理后的内容数据"""
    original_title: str
    original_content: str
    summary: Optional[str]
    category: Optional[str]
    category_confidence: Optional[float]
    keywords: List[Dict[str, Any]]
    sentiment: Optional[Dict[str, Any]]
    quality_result: ContentQualityResult
    entities: List[Dict[str, Any]]
    processing_time: datetime


class ContentProcessor:
    """内容处理器"""
    
    def __init__(self):
        """初始化内容处理器"""
        self.glm_service = get_glm_service()
        self.tfidf_vectorizer = None
        self.classifier = None
        self.financial_entities = self._load_financial_entities()
        
        # 初始化jieba分词
        jieba.initialize()
        
        logger.info("内容处理器初始化完成")
    
    def _load_financial_entities(self) -> Dict[str, List[str]]:
        """加载金融实体词典"""
        return {
            "companies": [
                "中国平安", "招商银行", "工商银行", "建设银行", "农业银行",
                "中国银行", "交通银行", "民生银行", "浦发银行", "中信银行",
                "贵州茅台", "五粮液", "腾讯控股", "阿里巴巴", "美团",
                "比亚迪", "宁德时代", "隆基绿能", "中国石油", "中国石化"
            ],
            "stock_codes": [
                "000001", "000002", "600000", "600036", "600519",
                "000858", "002415", "300750", "601318", "600276"
            ],
            "financial_terms": [
                "营收", "净利润", "毛利率", "净资产收益率", "市盈率",
                "市净率", "资产负债率", "现金流", "分红", "配股",
                "增发", "重组", "并购", "IPO", "定增"
            ]
        }
    
    async def process_content(self, title: str, content: str, source: str = None) -> ProcessedContent:
        """
        处理内容的主要方法
        
        Args:
            title: 文章标题
            content: 文章内容
            source: 内容来源
            
        Returns:
            处理后的内容数据
        """
        start_time = datetime.now()
        
        # 1. 数据质量检查
        quality_result = self._check_content_quality(title, content, source)
        
        # 2. 如果质量检查不通过，返回基础结果
        if not quality_result.is_valid:
            return ProcessedContent(
                original_title=title,
                original_content=content,
                summary=None,
                category=None,
                category_confidence=None,
                keywords=[],
                sentiment=None,
                quality_result=quality_result,
                entities=[],
                processing_time=start_time
            )
        
        # 3. 并行处理各种AI任务
        tasks = [
            self.glm_service.generate_summary(content),
            self.glm_service.classify_content(title, content),
            self.glm_service.extract_keywords(title, content),
            self.glm_service.analyze_sentiment(title, content),
            self._extract_entities(title, content)
        ]
        
        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            summary = results[0] if not isinstance(results[0], Exception) else None
            classification = results[1] if not isinstance(results[1], Exception) else None
            keywords = results[2] if not isinstance(results[2], Exception) else []
            sentiment = results[3] if not isinstance(results[3], Exception) else None
            entities = results[4] if not isinstance(results[4], Exception) else []
            
            # 处理分类结果
            category = None
            category_confidence = None
            if classification:
                category = classification.get('category')
                category_confidence = classification.get('confidence')
            
            return ProcessedContent(
                original_title=title,
                original_content=content,
                summary=summary,
                category=category,
                category_confidence=category_confidence,
                keywords=keywords or [],
                sentiment=sentiment,
                quality_result=quality_result,
                entities=entities or [],
                processing_time=start_time
            )
            
        except Exception as e:
            logger.error(f"内容处理失败: {str(e)}")
            return ProcessedContent(
                original_title=title,
                original_content=content,
                summary=None,
                category=None,
                category_confidence=None,
                keywords=[],
                sentiment=None,
                quality_result=quality_result,
                entities=[],
                processing_time=start_time
            )
    
    def _check_content_quality(self, title: str, content: str, source: str = None) -> ContentQualityResult:
        """
        检查内容质量
        
        Args:
            title: 文章标题
            content: 文章内容
            source: 内容来源
            
        Returns:
            质量检查结果
        """
        issues = []
        warnings = []
        missing_fields = []
        score = 1.0
        
        # 检查必填字段
        if not title or not title.strip():
            missing_fields.append("title")
            score -= 0.3
        
        if not content or not content.strip():
            missing_fields.append("content")
            score -= 0.5
        
        # 检查标题长度
        if title:
            title_len = len(title.strip())
            if title_len < settings.DATA_QUALITY_MIN_TITLE_LENGTH:
                issues.append(f"标题过短（{title_len}字），最少需要{settings.DATA_QUALITY_MIN_TITLE_LENGTH}字")
                score -= 0.2
            elif title_len > settings.DATA_QUALITY_MAX_TITLE_LENGTH:
                warnings.append(f"标题过长（{title_len}字），建议不超过{settings.DATA_QUALITY_MAX_TITLE_LENGTH}字")
                score -= 0.1
        
        # 检查内容长度
        if content:
            content_len = len(content.strip())
            if content_len < settings.DATA_QUALITY_MIN_CONTENT_LENGTH:
                issues.append(f"内容过短（{content_len}字），最少需要{settings.DATA_QUALITY_MIN_CONTENT_LENGTH}字")
                score -= 0.3
        
        # 检查内容格式
        if content:
            # 检查是否包含HTML标签
            html_pattern = re.compile(r'<[^>]+>')
            if html_pattern.search(content):
                warnings.append("内容包含HTML标签，建议清理")
                score -= 0.05
            
            # 检查是否包含特殊字符
            special_chars = re.findall(r'[^\w\s\u4e00-\u9fff，。！？；：""''（）【】《》、]', content)
            if len(special_chars) > 10:
                warnings.append("内容包含过多特殊字符")
                score -= 0.05
        
        # 检查时效性（如果有发布时间）
        # 这里可以根据实际需求添加时效性检查
        
        # 计算最终得分
        score = max(0.0, min(1.0, score))
        is_valid = len(missing_fields) == 0 and len(issues) == 0
        
        return ContentQualityResult(
            is_valid=is_valid,
            score=score,
            issues=issues,
            missing_fields=missing_fields,
            warnings=warnings
        )
    
    async def _extract_entities(self, title: str, content: str) -> List[Dict[str, Any]]:
        """
        提取实体信息
        
        Args:
            title: 文章标题
            content: 文章内容
            
        Returns:
            实体列表
        """
        entities = []
        text = f"{title} {content}"
        
        # 提取公司名称
        for company in self.financial_entities["companies"]:
            if company in text:
                entities.append({
                    "text": company,
                    "type": "company",
                    "confidence": 0.9
                })
        
        # 提取股票代码
        stock_pattern = re.compile(r'\b\d{6}\b')
        stock_matches = stock_pattern.findall(text)
        for stock_code in stock_matches:
            if stock_code in self.financial_entities["stock_codes"]:
                entities.append({
                    "text": stock_code,
                    "type": "stock_code",
                    "confidence": 0.95
                })
        
        # 提取金额信息
        amount_pattern = re.compile(r'(\d+(?:\.\d+)?)\s*([万亿千百十]?元|万元|亿元|千万元)')
        amount_matches = amount_pattern.findall(text)
        for amount, unit in amount_matches:
            entities.append({
                "text": f"{amount}{unit}",
                "type": "amount",
                "confidence": 0.85
            })
        
        # 提取时间信息
        time_pattern = re.compile(r'(\d{4}年\d{1,2}月\d{1,2}日|\d{4}-\d{1,2}-\d{1,2}|\d{1,2}月\d{1,2}日)')
        time_matches = time_pattern.findall(text)
        for time_text in time_matches:
            entities.append({
                "text": time_text,
                "type": "time",
                "confidence": 0.8
            })
        
        return entities
    
    def extract_keywords_tfidf(self, title: str, content: str, max_keywords: int = 10) -> List[Dict[str, Any]]:
        """
        使用TF-IDF算法提取关键词
        
        Args:
            title: 文章标题
            content: 文章内容
            max_keywords: 最大关键词数量
            
        Returns:
            关键词列表
        """
        try:
            # 合并标题和内容
            text = f"{title} {content}"
            
            # 使用jieba分词
            words = jieba.lcut(text)
            
            # 过滤停用词和短词
            stop_words = {'的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这'}
            filtered_words = [word for word in words if len(word) > 1 and word not in stop_words]
            
            # 计算词频
            word_freq = {}
            for word in filtered_words:
                word_freq[word] = word_freq.get(word, 0) + 1
            
            # 按频率排序并返回前N个
            sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
            
            keywords = []
            for word, freq in sorted_words[:max_keywords]:
                # 简单的权重计算
                weight = min(1.0, freq / len(filtered_words) * 10)
                
                # 判断关键词类型
                keyword_type = "other"
                if word in self.financial_entities["companies"]:
                    keyword_type = "company"
                elif word in self.financial_entities["financial_terms"]:
                    keyword_type = "financial_term"
                elif re.match(r'\d{6}', word):
                    keyword_type = "stock_code"
                elif re.search(r'\d+', word):
                    keyword_type = "amount"
                
                keywords.append({
                    "keyword": word,
                    "weight": weight,
                    "type": keyword_type,
                    "frequency": freq
                })
            
            return keywords
            
        except Exception as e:
            logger.error(f"TF-IDF关键词提取失败: {str(e)}")
            return []


# 全局内容处理器实例
content_processor = None

def get_content_processor() -> ContentProcessor:
    """获取内容处理器实例"""
    global content_processor
    if content_processor is None:
        content_processor = ContentProcessor()
    return content_processor
