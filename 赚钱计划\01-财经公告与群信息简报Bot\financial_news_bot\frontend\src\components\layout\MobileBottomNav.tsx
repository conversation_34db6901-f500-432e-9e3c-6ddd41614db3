import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Badge } from 'antd';
import {
  DashboardOutlined,
  FileTextOutlined,
  HeartOutlined,
  BookOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { useAppSelector } from '@/store';
import { useResponsive } from '@/hooks/useResponsive';

interface NavItem {
  key: string;
  label: string;
  icon: React.ReactNode;
  path: string;
  badge?: number;
}

const MobileBottomNav: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { isMobile } = useResponsive();
  const { notifications } = useAppSelector(state => state.ui);

  if (!isMobile) return null;

  const unreadCount = notifications.filter(n => !n.read).length;

  const navItems: NavItem[] = [
    {
      key: 'dashboard',
      label: '首页',
      icon: <DashboardOutlined />,
      path: '/dashboard',
    },
    {
      key: 'news',
      label: '新闻',
      icon: <FileTextOutlined />,
      path: '/news',
    },
    {
      key: 'subscriptions',
      label: '订阅',
      icon: <BookOutlined />,
      path: '/subscriptions',
    },
    {
      key: 'bookmarks',
      label: '收藏',
      icon: <HeartOutlined />,
      path: '/bookmarks',
    },
    {
      key: 'profile',
      label: '我的',
      icon: <UserOutlined />,
      path: '/profile',
      badge: unreadCount,
    },
  ];

  const handleNavClick = (path: string) => {
    navigate(path);
  };

  const isActive = (path: string) => {
    if (path === '/dashboard') {
      return location.pathname === '/' || location.pathname === '/dashboard';
    }
    return location.pathname.startsWith(path);
  };

  return (
    <>
      {/* 占位空间，防止内容被底部导航遮挡 */}
      <div style={{ height: 60 }} />
      
      {/* 底部导航栏 */}
      <div
        style={{
          position: 'fixed',
          bottom: 0,
          left: 0,
          right: 0,
          height: 60,
          backgroundColor: '#fff',
          borderTop: '1px solid #f0f0f0',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-around',
          zIndex: 1000,
          paddingBottom: 'env(safe-area-inset-bottom)', // 适配iPhone X系列
          boxShadow: '0 -2px 8px rgba(0, 0, 0, 0.06)',
        }}
        className="mobile-bottom-nav mobile-safe-area"
      >
        {navItems.map((item) => {
          const active = isActive(item.path);
          
          return (
            <div
              key={item.key}
              onClick={() => handleNavClick(item.path)}
              style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                minWidth: 44,
                minHeight: 44,
                padding: '4px 8px',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                color: active ? '#1890ff' : '#666',
                transform: active ? 'scale(1.05)' : 'scale(1)',
              }}
              className="touch-target"
            >
              <div
                style={{
                  fontSize: 20,
                  marginBottom: 2,
                  position: 'relative',
                }}
              >
                {item.badge && item.badge > 0 ? (
                  <Badge
                    count={item.badge}
                    size="small"
                    style={{
                      fontSize: 10,
                      minWidth: 16,
                      height: 16,
                      lineHeight: '16px',
                    }}
                  >
                    {item.icon}
                  </Badge>
                ) : (
                  item.icon
                )}
              </div>
              <span
                style={{
                  fontSize: 10,
                  lineHeight: 1,
                  fontWeight: active ? 600 : 400,
                }}
              >
                {item.label}
              </span>
              
              {/* 活跃指示器 */}
              {active && (
                <div
                  style={{
                    position: 'absolute',
                    bottom: 0,
                    left: '50%',
                    transform: 'translateX(-50%)',
                    width: 4,
                    height: 4,
                    backgroundColor: '#1890ff',
                    borderRadius: '50%',
                  }}
                />
              )}
            </div>
          );
        })}
      </div>

      {/* 样式 */}
      <style>
        {`
          .mobile-bottom-nav .touch-target:active {
            background-color: rgba(24, 144, 255, 0.1);
            border-radius: 8px;
          }
          
          .mobile-bottom-nav .touch-target {
            position: relative;
            border-radius: 8px;
            transition: background-color 0.2s ease, transform 0.2s ease;
          }
          
          /* 适配深色模式 */
          @media (prefers-color-scheme: dark) {
            .mobile-bottom-nav {
              background-color: #1f1f1f !important;
              border-top-color: #434343 !important;
            }
            
            .mobile-bottom-nav .touch-target {
              color: #fff;
            }
            
            .mobile-bottom-nav .touch-target.active {
              color: #1890ff;
            }
          }
          
          /* 横屏模式优化 */
          @media (orientation: landscape) and (max-height: 600px) {
            .mobile-bottom-nav {
              height: 50px;
              padding: 2px 0;
            }
            
            .mobile-bottom-nav .touch-target {
              min-height: 40px;
              padding: 2px 6px;
            }
            
            .mobile-bottom-nav .touch-target span {
              font-size: 9px;
            }
            
            .mobile-bottom-nav .touch-target > div {
              font-size: 18px;
              margin-bottom: 1px;
            }
          }
          
          /* 减少动画模式 */
          @media (prefers-reduced-motion: reduce) {
            .mobile-bottom-nav .touch-target {
              transition: none !important;
            }
          }
          
          /* 高对比度模式 */
          @media (prefers-contrast: high) {
            .mobile-bottom-nav {
              border-top: 2px solid #000 !important;
            }
            
            .mobile-bottom-nav .touch-target {
              border: 1px solid transparent;
            }
            
            .mobile-bottom-nav .touch-target.active {
              border-color: #1890ff;
            }
          }
        `}
      </style>
    </>
  );
};

export default MobileBottomNav;
