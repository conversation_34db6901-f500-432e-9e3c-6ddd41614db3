"""
安全管理API路由 - 微服务代理版本
提供安全配置、合规检查、隐私保护等功能
现在作为微服务的代理和聚合器
"""
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, HTTPException, Depends, status, Request
from pydantic import BaseModel, Field
from datetime import datetime

from app.dependencies.auth import get_current_active_user
from app.services.audit_service import audit_service, AuditEventType, AuditSeverity
from app.services.enhanced_compliance_service import get_enhanced_compliance_service, RiskLevel
from app.services.privacy_service import privacy_service, DataSensitivity
from app.middleware.rate_limiter import rate_limiter
from app.models.user import User
from app.adapters.microservice_adapter import microservice_manager

# 创建路由器
router = APIRouter(prefix="/security", tags=["安全管理"])


class ComplianceCheckRequest(BaseModel):
    """合规检查请求模型"""
    content: str = Field(..., description="待检查内容")
    strict_mode: bool = Field(default=False, description="是否启用严格模式")


class PrivacyCheckRequest(BaseModel):
    """隐私检查请求模型"""
    data: Dict[str, Any] = Field(..., description="待检查数据")
    data_type: str = Field(default="unknown", description="数据类型")


class SecurityConfigRequest(BaseModel):
    """安全配置请求模型"""
    config_type: str = Field(..., description="配置类型")
    config_data: Dict[str, Any] = Field(..., description="配置数据")


@router.post("/compliance/check", response_model=Dict[str, Any])
async def check_compliance(
    request: ComplianceCheckRequest,
    http_request: Request,
    current_user: User = Depends(get_current_active_user)
):
    """
    内容合规检查
    """
    try:
        # 获取真实IP地址
        client_ip = http_request.client.host if http_request.client else "127.0.0.1"
        forwarded_for = http_request.headers.get("X-Forwarded-For")
        if forwarded_for:
            client_ip = forwarded_for.split(",")[0].strip()

        # 记录审计日志到微服务
        await microservice_manager.security_adapter.log_audit_event(
            event_type="compliance_check",
            user_id=current_user.id,
            ip_address=client_ip,
            action="compliance_check",
            resource="content",
            severity="LOW",
            details={"content_length": len(request.content)}
        )

        # 通过微服务执行增强合规检查
        result = await microservice_manager.compliance_adapter.check_enhanced_compliance(
            content=request.content,
            strict_mode=request.strict_mode,
            check_sensitive_words=True,
            check_investment_advice=True,
            check_financial_promise=True,
            filter_content=True
        )
        
        return {
            "success": True,
            "message": "合规检查完成",
            "data": {
                "is_compliant": result.get("is_compliant", False),
                "risk_level": result.get("risk_level", "UNKNOWN"),
                "violations": result.get("violations", []),
                "filtered_content": result.get("filtered_content", request.content),
                "recommendations": result.get("recommendations", []),
                "confidence_score": result.get("confidence_score", 0.0),
                "check_details": result.get("check_details", {}),
                "timestamp": datetime.now().isoformat()
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"合规检查失败: {str(e)}"
        )


@router.post("/privacy/check", response_model=Dict[str, Any])
async def check_privacy(
    request: PrivacyCheckRequest,
    http_request: Request,
    current_user: User = Depends(get_current_active_user)
):
    """
    隐私信息检查
    """
    try:
        # 检查用户权限
        if current_user.role not in ["admin", "enterprise"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，需要管理员或企业版权限"
            )

        # 获取真实IP地址
        client_ip = http_request.client.host if http_request.client else "127.0.0.1"
        forwarded_for = http_request.headers.get("X-Forwarded-For")
        if forwarded_for:
            client_ip = forwarded_for.split(",")[0].strip()

        # 通过微服务进行隐私检查
        privacy_result = await microservice_manager.compliance_adapter.check_privacy(
            content=str(request.data),
            data_type=request.data_type,
            mask_pii=True
        )

        # 通过微服务进行数据脱敏
        sanitization_result = await microservice_manager.compliance_adapter.sanitize_data(
            data=request.data,
            sensitivity_level="CONFIDENTIAL",
            preserve_structure=True
        )

        # 记录审计日志到微服务
        await microservice_manager.security_adapter.log_audit_event(
            event_type="privacy_check",
            user_id=current_user.id,
            ip_address=client_ip,
            action="privacy_check",
            resource="user_data",
            severity="MEDIUM",
            details={"data_type": request.data_type}
        )
        
        return {
            "success": True,
            "message": "隐私检查完成",
            "data": {
                "privacy_check": privacy_result,
                "sanitized_data": sanitization_result.get("sanitized_data", {}),
                "sensitivity_level": privacy_result.get("sensitivity_level", "UNKNOWN"),
                "pii_detected": privacy_result.get("has_pii", False),
                "recommendations": privacy_result.get("recommendations", []),
                "timestamp": datetime.now().isoformat()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"隐私检查失败: {str(e)}"
        )


@router.get("/audit/logs", response_model=Dict[str, Any])
async def get_audit_logs(
    start_time: Optional[str] = None,
    end_time: Optional[str] = None,
    user_id: Optional[str] = None,
    event_type: Optional[str] = None,
    limit: int = 100,
    offset: int = 0,
    current_user: User = Depends(get_current_active_user)
):
    """
    获取审计日志
    """
    try:
        # 检查用户权限
        if current_user.role != "admin":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，需要管理员权限"
            )
        
        # 解析时间参数
        start_datetime = None
        end_datetime = None
        
        if start_time:
            start_datetime = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
        if end_time:
            end_datetime = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
        
        # 解析事件类型
        audit_event_type = None
        if event_type:
            try:
                audit_event_type = AuditEventType(event_type)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"无效的事件类型: {event_type}"
                )
        
        # 查询审计日志
        logs = audit_service.query_logs(
            start_time=start_datetime,
            end_time=end_datetime,
            user_id=user_id,
            event_type=audit_event_type,
            limit=limit,
            offset=offset
        )
        
        # 记录查询操作
        audit_service.log_event(
            event_type=AuditEventType.DATA_READ,
            user_id=current_user.id,
            ip_address="unknown",
            action="query_audit_logs",
            resource="audit_logs",
            details={"query_params": {"start_time": start_time, "end_time": end_time, "limit": limit}}
        )
        
        return {
            "success": True,
            "message": "获取审计日志成功",
            "data": {
                "logs": logs,
                "total": len(logs),
                "offset": offset,
                "limit": limit,
                "timestamp": datetime.utcnow().isoformat()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取审计日志失败: {str(e)}"
        )


@router.get("/audit/statistics", response_model=Dict[str, Any])
async def get_audit_statistics(
    days: int = 7,
    current_user: User = Depends(get_current_active_user)
):
    """
    获取审计统计信息
    """
    try:
        # 检查用户权限
        if current_user.role not in ["admin", "enterprise"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，需要管理员或企业版权限"
            )
        
        # 获取统计信息
        statistics = audit_service.get_statistics(days)
        
        return {
            "success": True,
            "message": "获取审计统计成功",
            "data": statistics
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取审计统计失败: {str(e)}"
        )


@router.get("/rate-limit/stats", response_model=Dict[str, Any])
async def get_rate_limit_stats(current_user: User = Depends(get_current_active_user)):
    """
    获取速率限制统计信息
    """
    try:
        # 检查用户权限
        if current_user.role not in ["admin", "enterprise"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，需要管理员或企业版权限"
            )
        
        # 获取速率限制统计
        stats = rate_limiter.get_stats()
        
        return {
            "success": True,
            "message": "获取速率限制统计成功",
            "data": stats
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取速率限制统计失败: {str(e)}"
        )


@router.post("/rate-limit/blacklist", response_model=Dict[str, Any])
async def add_to_blacklist(
    ip_address: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    添加IP到黑名单
    """
    try:
        # 检查用户权限
        if current_user.role != "admin":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，需要管理员权限"
            )
        
        # 添加到黑名单
        rate_limiter.add_to_blacklist(ip_address)
        
        # 记录审计日志
        audit_service.log_event(
            event_type=AuditEventType.IP_BLOCKED,
            user_id=current_user.id,
            ip_address="unknown",
            action="add_to_blacklist",
            resource="ip_blacklist",
            severity=AuditSeverity.HIGH,
            details={"blocked_ip": ip_address}
        )
        
        return {
            "success": True,
            "message": f"IP {ip_address} 已添加到黑名单"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"添加黑名单失败: {str(e)}"
        )


@router.get("/compliance/statistics", response_model=Dict[str, Any])
async def get_compliance_statistics(current_user: User = Depends(get_current_active_user)):
    """
    获取合规统计信息
    """
    try:
        # 检查用户权限
        if current_user.role not in ["admin", "enterprise"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，需要管理员或企业版权限"
            )
        
        # 获取合规统计
        compliance_service = get_enhanced_compliance_service()
        stats = compliance_service.get_compliance_statistics()
        
        return {
            "success": True,
            "message": "获取合规统计成功",
            "data": stats
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取合规统计失败: {str(e)}"
        )


@router.get("/privacy/policies", response_model=Dict[str, Any])
async def get_privacy_policies(current_user: User = Depends(get_current_active_user)):
    """
    获取隐私保护策略
    """
    try:
        # 检查用户权限
        if current_user.role not in ["admin", "enterprise"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，需要管理员或企业版权限"
            )
        
        # 获取数据保留策略
        policies = privacy_service.get_retention_policies()
        
        # 转换为可序列化格式
        serializable_policies = {}
        for key, policy in policies.items():
            serializable_policies[key] = {
                "data_type": policy.data_type,
                "retention_days": policy.retention_days,
                "auto_delete": policy.auto_delete,
                "anonymize_after_days": policy.anonymize_after_days,
                "description": policy.description
            }
        
        return {
            "success": True,
            "message": "获取隐私策略成功",
            "data": {
                "retention_policies": serializable_policies,
                "timestamp": datetime.utcnow().isoformat()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取隐私策略失败: {str(e)}"
        )


@router.get("/health", response_model=Dict[str, Any])
async def security_health_check():
    """
    安全系统健康检查
    """
    try:
        health_status = {
            "audit_service": True,
            "compliance_service": True,
            "privacy_service": True,
            "rate_limiter": True,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # 测试各个服务
        try:
            audit_service.get_statistics(1)
        except Exception:
            health_status["audit_service"] = False
        
        try:
            # 测试增强合规服务
            compliance_service = get_enhanced_compliance_service()
            # 简单测试服务是否可用
            health_status["compliance_service"] = True
        except Exception:
            health_status["compliance_service"] = False
        
        try:
            privacy_service.get_retention_policies()
        except Exception:
            health_status["privacy_service"] = False
        
        try:
            rate_limiter.get_stats()
        except Exception:
            health_status["rate_limiter"] = False
        
        overall_health = all(health_status[k] for k in health_status if k != "timestamp")
        
        return {
            "success": True,
            "message": "安全系统健康检查完成",
            "data": {
                "overall_health": overall_health,
                "components": health_status
            }
        }
        
    except Exception as e:
        return {
            "success": False,
            "message": f"安全系统健康检查失败: {str(e)}",
            "data": {
                "overall_health": False,
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
        }
