"""
Redis缓存服务
提供统一的缓存接口和管理功能
"""
import json
import logging
from typing import Any, Optional, Dict, List
from datetime import datetime, timedelta
import redis
import pickle
from functools import wraps

from app.config import settings
from ..constants import TimeConstants, CacheKeys

logger = logging.getLogger(__name__)

class CacheService:
    """Redis缓存服务类"""
    
    def __init__(self):
        """初始化Redis连接"""
        try:
            self.redis_client = redis.Redis(
                host=settings.REDIS_HOST,
                port=settings.REDIS_PORT,
                db=settings.REDIS_DB,
                decode_responses=False,  # 使用bytes模式支持pickle
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True
            )
            # 测试连接
            self.redis_client.ping()
            logger.info("Redis缓存服务初始化成功")
        except Exception as e:
            logger.error(f"Redis连接失败: {str(e)}")
            self.redis_client = None
    
    def is_available(self) -> bool:
        """检查Redis是否可用"""
        if not self.redis_client:
            return False
        try:
            self.redis_client.ping()
            return True
        except:
            return False

    def get_smart_expire_time(self, cache_type: str) -> int:
        """
        根据缓存类型智能获取过期时间

        Args:
            cache_type: 缓存类型 (news_list, news_detail, user_info, search, etc.)

        Returns:
            过期时间（秒）
        """
        expire_mapping = {
            'news_list': TimeConstants.CACHE_SHORT,      # 5分钟 - 新闻列表变化较快
            'news_detail': TimeConstants.CACHE_MEDIUM,   # 30分钟 - 新闻详情相对稳定
            'news_stats': TimeConstants.CACHE_MEDIUM,    # 30分钟 - 统计数据
            'user_info': TimeConstants.CACHE_LONG,       # 1小时 - 用户信息
            'user_permissions': TimeConstants.CACHE_LONG, # 1小时 - 用户权限
            'search_results': TimeConstants.CACHE_SHORT, # 5分钟 - 搜索结果
            'search_suggestions': TimeConstants.CACHE_LONG, # 1小时 - 搜索建议
            'system_stats': TimeConstants.CACHE_SHORT,   # 5分钟 - 系统统计
            'system_health': TimeConstants.CACHE_SHORT,  # 5分钟 - 健康检查
            'trending': TimeConstants.CACHE_MEDIUM,      # 30分钟 - 热门内容
            'categories': TimeConstants.CACHE_VERY_LONG, # 1天 - 分类信息
            'config': TimeConstants.CACHE_VERY_LONG,     # 1天 - 配置信息
        }

        return expire_mapping.get(cache_type, TimeConstants.CACHE_LONG)
    
    def set(self, key: str, value: Any, expire: int = TimeConstants.CACHE_LONG) -> bool:
        """
        设置缓存
        
        Args:
            key: 缓存键
            value: 缓存值
            expire: 过期时间（秒），默认1小时
        """
        if not self.is_available():
            return False
        
        try:
            # 序列化数据
            serialized_value = pickle.dumps(value)
            
            # 设置缓存
            result = self.redis_client.setex(key, expire, serialized_value)
            
            logger.debug(f"设置缓存: {key}, 过期时间: {expire}秒")
            return result
        except Exception as e:
            logger.error(f"设置缓存失败 {key}: {str(e)}")
            return False
    
    def get(self, key: str) -> Optional[Any]:
        """
        获取缓存
        
        Args:
            key: 缓存键
        """
        if not self.is_available():
            return None
        
        try:
            # 获取缓存数据
            cached_data = self.redis_client.get(key)
            
            if cached_data is None:
                logger.debug(f"缓存未命中: {key}")
                return None
            
            # 反序列化数据
            value = pickle.loads(cached_data)
            logger.debug(f"缓存命中: {key}")
            return value
        except Exception as e:
            logger.error(f"获取缓存失败 {key}: {str(e)}")
            return None
    
    def delete(self, key: str) -> bool:
        """
        删除缓存
        
        Args:
            key: 缓存键
        """
        if not self.is_available():
            return False
        
        try:
            result = self.redis_client.delete(key)
            logger.debug(f"删除缓存: {key}")
            return bool(result)
        except Exception as e:
            logger.error(f"删除缓存失败 {key}: {str(e)}")
            return False
    
    def exists(self, key: str) -> bool:
        """
        检查缓存是否存在
        
        Args:
            key: 缓存键
        """
        if not self.is_available():
            return False
        
        try:
            return bool(self.redis_client.exists(key))
        except Exception as e:
            logger.error(f"检查缓存存在性失败 {key}: {str(e)}")
            return False
    
    def expire(self, key: str, seconds: int) -> bool:
        """
        设置缓存过期时间
        
        Args:
            key: 缓存键
            seconds: 过期时间（秒）
        """
        if not self.is_available():
            return False
        
        try:
            result = self.redis_client.expire(key, seconds)
            logger.debug(f"设置缓存过期时间: {key}, {seconds}秒")
            return bool(result)
        except Exception as e:
            logger.error(f"设置缓存过期时间失败 {key}: {str(e)}")
            return False
    
    def ttl(self, key: str) -> int:
        """
        获取缓存剩余过期时间
        
        Args:
            key: 缓存键
        
        Returns:
            剩余秒数，-1表示永不过期，-2表示不存在
        """
        if not self.is_available():
            return -2
        
        try:
            return self.redis_client.ttl(key)
        except Exception as e:
            logger.error(f"获取缓存TTL失败 {key}: {str(e)}")
            return -2
    
    def clear_pattern(self, pattern: str) -> int:
        """
        按模式清除缓存
        
        Args:
            pattern: 匹配模式，如 "news:*"
        
        Returns:
            删除的键数量
        """
        if not self.is_available():
            return 0
        
        try:
            keys = self.redis_client.keys(pattern)
            if keys:
                deleted = self.redis_client.delete(*keys)
                logger.info(f"按模式清除缓存: {pattern}, 删除了 {deleted} 个键")
                return deleted
            return 0
        except Exception as e:
            logger.error(f"按模式清除缓存失败 {pattern}: {str(e)}")
            return 0
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        """
        if not self.is_available():
            return {"status": "unavailable"}
        
        try:
            info = self.redis_client.info()
            return {
                "status": "available",
                "used_memory": info.get("used_memory_human", "unknown"),
                "connected_clients": info.get("connected_clients", 0),
                "total_commands_processed": info.get("total_commands_processed", 0),
                "keyspace_hits": info.get("keyspace_hits", 0),
                "keyspace_misses": info.get("keyspace_misses", 0),
                "hit_rate": self._calculate_hit_rate(
                    info.get("keyspace_hits", 0),
                    info.get("keyspace_misses", 0)
                )
            }
        except Exception as e:
            logger.error(f"获取缓存统计失败: {str(e)}")
            return {"status": "error", "error": str(e)}
    
    def _calculate_hit_rate(self, hits: int, misses: int) -> float:
        """计算缓存命中率"""
        total = hits + misses
        if total == 0:
            return 0.0
        return round((hits / total) * 100, 2)

# 全局缓存服务实例
cache_service = CacheService()

def cache_result(key_prefix: str, expire: int = 3600):
    """
    缓存装饰器
    
    Args:
        key_prefix: 缓存键前缀
        expire: 过期时间（秒）
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{key_prefix}:{hash(str(args) + str(sorted(kwargs.items())))}"
            
            # 尝试从缓存获取
            cached_result = cache_service.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 执行函数
            result = func(*args, **kwargs)
            
            # 缓存结果
            cache_service.set(cache_key, result, expire)
            
            return result
        return wrapper
    return decorator

def invalidate_cache_pattern(pattern: str):
    """
    清除匹配模式的缓存
    
    Args:
        pattern: 缓存键模式
    """
    return cache_service.clear_pattern(pattern)
