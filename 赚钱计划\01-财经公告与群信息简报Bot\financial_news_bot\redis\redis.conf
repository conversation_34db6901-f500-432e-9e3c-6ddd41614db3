# Redis配置文件 - 财经新闻Bot项目优化配置
# 基于Redis 7.x版本

# 网络配置
bind 0.0.0.0
port 6379
tcp-backlog 511
timeout 0
tcp-keepalive 300

# 通用配置
daemonize no
pidfile /var/run/redis_6379.pid
loglevel notice
logfile ""
databases 16

# 快照配置（RDB持久化）
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir ./

# 复制配置
replica-serve-stale-data yes
replica-read-only yes
repl-diskless-sync no
repl-diskless-sync-delay 5
repl-ping-replica-period 10
repl-timeout 60
repl-disable-tcp-nodelay no
repl-backlog-size 1mb
repl-backlog-ttl 3600

# 安全配置
# requirepass your_password_here
# rename-command FLUSHDB ""
# rename-command FLUSHALL ""
# rename-command KEYS ""
# rename-command CONFIG ""

# 内存管理
maxmemory 2gb
maxmemory-policy volatile-lru
maxmemory-samples 5

# AOF持久化配置
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb
aof-load-truncated yes
aof-use-rdb-preamble yes

# Lua脚本配置
lua-time-limit 5000

# 慢日志配置
slowlog-log-slower-than 10000
slowlog-max-len 128

# 延迟监控
latency-monitor-threshold 100

# 事件通知
notify-keyspace-events ""

# 高级配置
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64
hll-sparse-max-bytes 3000
stream-node-max-bytes 4096
stream-node-max-entries 100

# 活跃重新哈希
activerehashing yes

# 客户端输出缓冲区限制
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# 客户端查询缓冲区限制
client-query-buffer-limit 1gb

# 协议最大批量请求大小
proto-max-bulk-len 512mb

# HyperLogLog配置
hll-sparse-max-bytes 3000

# Streams配置
stream-node-max-bytes 4096
stream-node-max-entries 100

# 活跃过期配置
hz 10

# 动态HZ
dynamic-hz yes

# AOF重写增量fsync
aof-rewrite-incremental-fsync yes

# RDB保存增量fsync
rdb-save-incremental-fsync yes

# 机器学习配置（Redis 6.0+）
# acl-log-max-len 128

# TLS配置（如果需要）
# port 0
# tls-port 6380
# tls-cert-file redis.crt
# tls-key-file redis.key
# tls-ca-cert-file ca.crt

# 模块配置
# loadmodule /path/to/module.so

# 集群配置（如果需要）
# cluster-enabled yes
# cluster-config-file nodes-6379.conf
# cluster-node-timeout 15000
# cluster-replica-validity-factor 10
# cluster-migration-barrier 1
# cluster-require-full-coverage yes

# 性能优化配置
# 禁用透明大页
# echo never > /sys/kernel/mm/transparent_hugepage/enabled

# 内核参数优化建议
# vm.overcommit_memory = 1
# net.core.somaxconn = 65535
# net.ipv4.tcp_max_syn_backlog = 65535

# 监控配置
# 启用INFO命令的扩展信息
# info-extended yes

# 内存使用报告
# memory-usage-sample-size 5

# 键空间通知配置（用于缓存失效）
# notify-keyspace-events Ex

# 客户端连接配置
maxclients 10000
timeout 0

# 数据库配置
databases 16

# 日志配置
syslog-enabled no
syslog-ident redis
syslog-facility local0

# 进程配置
supervised no

# 性能调优
tcp-keepalive 300
tcp-backlog 511

# 内存碎片整理
activedefrag yes
active-defrag-ignore-bytes 100mb
active-defrag-threshold-lower 10
active-defrag-threshold-upper 100
active-defrag-cycle-min 1
active-defrag-cycle-max 25

# 惰性释放
lazyfree-lazy-eviction no
lazyfree-lazy-expire no
lazyfree-lazy-server-del no
replica-lazy-flush no

# 线程I/O配置（Redis 6.0+）
# io-threads 4
# io-threads-do-reads yes

# 跟踪配置
# tracking-table-max-keys 1000000

# 用户ACL配置示例
# user default on nopass ~* &* -@all +@read +@write +@connection +@dangerous

# 财经新闻Bot项目特定配置
# 缓存键命名规范：
# news:cache:{source}:{date}
# user:session:{user_id}
# rate_limit:{ip}:{endpoint}
# subscription:{user_id}
# analytics:{type}:{date}

# 推荐的键过期时间：
# 新闻缓存：3600秒（1小时）
# 用户会话：86400秒（24小时）
# 速率限制：300秒（5分钟）
# 分析数据：604800秒（7天）

# 内存使用建议：
# 新闻缓存：~500MB
# 用户会话：~100MB
# 速率限制：~50MB
# 其他缓存：~350MB
# 总计：~1GB（预留1GB用于增长）
