"""
用户行为追踪API路由
提供用户行为数据收集和分析功能
"""
from fastapi import APIRouter, Depends, HTTPException, Query, Body, Request, status
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from pydantic import BaseModel, Field

from ..database import get_db
from ..dependencies.auth import get_current_active_user, get_optional_current_user
from ..dependencies.permissions import require_admin, require_advanced_subscription
from ..models.user import User
from ..services.user_behavior_tracking_service import UserBehaviorTrackingService, EventType

router = APIRouter(prefix="/user-behavior", tags=["用户行为追踪"])


# Pydantic模型定义
class TrackEventRequest(BaseModel):
    """追踪事件请求模型"""
    event_type: EventType = Field(..., description="事件类型")
    event_data: Dict[str, Any] = Field(..., description="事件数据")
    session_id: Optional[str] = Field(None, description="会话ID")


class PushInteractionRequest(BaseModel):
    """推送交互请求模型"""
    push_log_id: int = Field(..., description="推送日志ID")
    interaction_type: str = Field(..., description="交互类型")
    additional_data: Optional[Dict[str, Any]] = Field(None, description="额外数据")


@router.post("/track", response_model=Dict[str, Any])
async def track_event(
    request: TrackEventRequest,
    http_request: Request,
    current_user: User = Depends(get_optional_current_user),
    db: Session = Depends(get_db)
):
    """
    追踪用户行为事件
    """
    try:
        # 获取请求信息
        ip_address = http_request.client.host if http_request.client else None
        user_agent = http_request.headers.get("user-agent")
        
        # 创建行为追踪服务
        tracking_service = UserBehaviorTrackingService(db)
        
        # 追踪事件
        success = await tracking_service.track_event(
            user_id=current_user.id if current_user else None,
            event_type=request.event_type,
            event_data=request.event_data,
            session_id=request.session_id,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        if success:
            return {
                "code": 200,
                "message": "事件追踪成功",
                "data": {
                    "event_type": request.event_type.value,
                    "timestamp": datetime.now().isoformat()
                }
            }
        else:
            return {
                "code": 400,
                "message": "事件追踪失败",
                "data": None
            }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"事件追踪失败: {str(e)}"
        )


@router.post("/track/push-interaction", response_model=Dict[str, Any])
async def track_push_interaction(
    request: PushInteractionRequest,
    current_user: User = Depends(get_optional_current_user),
    db: Session = Depends(get_db)
):
    """
    追踪推送交互事件
    """
    try:
        # 创建行为追踪服务
        tracking_service = UserBehaviorTrackingService(db)
        
        # 追踪推送交互
        success = await tracking_service.track_push_interaction(
            push_log_id=request.push_log_id,
            interaction_type=request.interaction_type,
            user_id=current_user.id if current_user else None,
            additional_data=request.additional_data
        )
        
        if success:
            return {
                "code": 200,
                "message": "推送交互追踪成功",
                "data": {
                    "push_log_id": request.push_log_id,
                    "interaction_type": request.interaction_type,
                    "timestamp": datetime.now().isoformat()
                }
            }
        else:
            return {
                "code": 400,
                "message": "推送交互追踪失败",
                "data": None
            }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"推送交互追踪失败: {str(e)}"
        )


@router.get("/summary", response_model=Dict[str, Any])
async def get_user_behavior_summary(
    days: int = Query(7, ge=1, le=90, description="统计天数"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    获取当前用户行为摘要
    """
    try:
        # 创建行为追踪服务
        tracking_service = UserBehaviorTrackingService(db)
        
        # 获取用户行为摘要
        summary = await tracking_service.get_user_behavior_summary(
            user_id=current_user.id,
            days=days
        )
        
        return {
            "code": 200,
            "message": "获取用户行为摘要成功",
            "data": summary
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取用户行为摘要失败: {str(e)}"
        )


@router.get("/summary/{user_id}", response_model=Dict[str, Any])
async def get_user_behavior_summary_by_id(
    user_id: int,
    days: int = Query(7, ge=1, le=90, description="统计天数"),
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    获取指定用户行为摘要（需要管理员权限）
    """
    try:
        # 创建行为追踪服务
        tracking_service = UserBehaviorTrackingService(db)
        
        # 获取用户行为摘要
        summary = await tracking_service.get_user_behavior_summary(
            user_id=user_id,
            days=days
        )
        
        return {
            "code": 200,
            "message": "获取用户行为摘要成功",
            "data": summary
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取用户行为摘要失败: {str(e)}"
        )


@router.get("/push-analytics", response_model=Dict[str, Any])
async def get_push_analytics(
    days: int = Query(7, ge=1, le=90, description="统计天数"),
    channel: Optional[str] = Query(None, description="推送渠道过滤"),
    current_user: User = Depends(require_advanced_subscription()),
    db: Session = Depends(get_db)
):
    """
    获取推送分析数据（需要PRO订阅）
    """
    try:
        # 创建行为追踪服务
        tracking_service = UserBehaviorTrackingService(db)
        
        # 获取推送分析数据
        analytics = await tracking_service.get_push_analytics(
            days=days,
            channel=channel
        )
        
        return {
            "code": 200,
            "message": "获取推送分析数据成功",
            "data": analytics
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取推送分析数据失败: {str(e)}"
        )


@router.get("/events", response_model=Dict[str, Any])
async def get_user_events(
    days: int = Query(7, ge=1, le=30, description="统计天数"),
    event_type: Optional[EventType] = Query(None, description="事件类型过滤"),
    limit: int = Query(100, ge=1, le=1000, description="返回数量限制"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    获取用户事件列表
    """
    try:
        # 这里可以从缓存或数据库中获取用户事件
        # 目前返回模拟数据结构
        events = {
            "user_id": current_user.id,
            "period": {
                "days": days,
                "start_date": (datetime.now() - timedelta(days=days)).strftime("%Y-%m-%d"),
                "end_date": datetime.now().strftime("%Y-%m-%d")
            },
            "events": [],
            "total_count": 0,
            "filtered_by": {
                "event_type": event_type.value if event_type else None,
                "limit": limit
            }
        }
        
        return {
            "code": 200,
            "message": "获取用户事件列表成功",
            "data": events
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取用户事件列表失败: {str(e)}"
        )


# 批量事件追踪端点（用于前端批量上报）
@router.post("/track/batch", response_model=Dict[str, Any])
async def track_events_batch(
    events: List[TrackEventRequest],
    http_request: Request,
    current_user: User = Depends(get_optional_current_user),
    db: Session = Depends(get_db)
):
    """
    批量追踪用户行为事件
    """
    try:
        if len(events) > 50:  # 限制批量大小
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="批量事件数量不能超过50个"
            )
        
        # 获取请求信息
        ip_address = http_request.client.host if http_request.client else None
        user_agent = http_request.headers.get("user-agent")
        
        # 创建行为追踪服务
        tracking_service = UserBehaviorTrackingService(db)
        
        success_count = 0
        failed_count = 0
        
        # 批量处理事件
        for event in events:
            success = await tracking_service.track_event(
                user_id=current_user.id if current_user else None,
                event_type=event.event_type,
                event_data=event.event_data,
                session_id=event.session_id,
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            if success:
                success_count += 1
            else:
                failed_count += 1
        
        return {
            "code": 200,
            "message": "批量事件追踪完成",
            "data": {
                "total_events": len(events),
                "success_count": success_count,
                "failed_count": failed_count,
                "timestamp": datetime.now().isoformat()
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量事件追踪失败: {str(e)}"
        )
