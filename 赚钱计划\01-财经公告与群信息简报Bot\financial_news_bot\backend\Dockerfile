FROM python:3.11-slim

# 安装编译工具和系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    make \
    && rm -rf /var/lib/apt/lists/*

# 创建非root用户
RUN useradd -m -s /bin/bash appuser

WORKDIR /app

# 先复制requirements.txt并安装依赖，利用Docker缓存
COPY requirements.txt /app/
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码并设置权限
COPY . /app
RUN chown -R appuser:appuser /app

# 切换到非root用户
USER appuser

EXPOSE 8000
CMD ["uvicorn","app.main:app","--host","0.0.0.0","--port","8000"]

