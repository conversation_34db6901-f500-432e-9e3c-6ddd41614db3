import React, { useState, useEffect, useRef } from 'react';
import { Skeleton } from 'antd';
import {
  ResponsiveImageProps,
  getOptimizedImageUrl,
  generateSrcSet,
  supportsWebP,
  lazyImageObserver,
  imageLoadManager,
} from '@/utils/imageOptimization';

const ResponsiveImage: React.FC<ResponsiveImageProps> = ({
  src,
  alt,
  width,
  height,
  className = '',
  style = {},
  lazy = true,
  placeholder,
  quality = 80,
  sizes = '(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw',
  onLoad,
  onError,
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [webpSupported, setWebpSupported] = useState<boolean | null>(null);
  const imgRef = useRef<HTMLImageElement>(null);

  // 检测WebP支持
  useEffect(() => {
    supportsWebP().then(setWebpSupported);
  }, []);

  // 获取优化后的图片URL
  const getImageUrl = (format?: 'webp' | 'jpeg') => {
    return getOptimizedImageUrl(src, {
      width,
      height,
      quality,
      format: format || (webpSupported ? 'webp' : 'jpeg'),
    });
  };

  // 生成srcSet
  const getSrcSet = () => {
    if (!src) return '';
    
    const format = webpSupported ? 'webp' : 'jpeg';
    return generateSrcSet(src, [320, 640, 1024, 1280]);
  };

  // 处理图片加载成功
  const handleLoad = () => {
    setIsLoaded(true);
    setHasError(false);
    imageLoadManager.markLoaded(src);
    onLoad?.();
  };

  // 处理图片加载失败
  const handleError = () => {
    setHasError(true);
    setIsLoaded(false);
    imageLoadManager.markFailed(src);
    onError?.();
  };

  // 设置懒加载
  useEffect(() => {
    const img = imgRef.current;
    if (!img || !lazy) return;

    if (imageLoadManager.isLoaded(src)) {
      setIsLoaded(true);
      return;
    }

    if (imageLoadManager.isFailed(src)) {
      setHasError(true);
      return;
    }

    imageLoadManager.startLoading(src);
    lazyImageObserver.observe(img);

    return () => {
      lazyImageObserver.unobserve(img);
    };
  }, [src, lazy]);

  // 渲染占位符
  const renderPlaceholder = () => {
    if (placeholder) {
      return (
        <img
          src={placeholder}
          alt={alt}
          className={`${className} placeholder`}
          style={{
            ...style,
            filter: 'blur(5px)',
            transition: 'filter 0.3s ease',
          }}
        />
      );
    }

    return (
      <Skeleton.Image
        style={{
          width: width || '100%',
          height: height || 'auto',
          ...style,
        }}
      />
    );
  };

  // 渲染错误状态
  const renderError = () => (
    <div
      className={`${className} error-placeholder`}
      style={{
        width: width || '100%',
        height: height || 200,
        backgroundColor: '#f5f5f5',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: '#999',
        fontSize: '14px',
        border: '1px dashed #d9d9d9',
        ...style,
      }}
    >
      图片加载失败
    </div>
  );

  // 如果WebP支持检测还未完成，显示占位符
  if (webpSupported === null) {
    return renderPlaceholder();
  }

  // 如果加载失败，显示错误状态
  if (hasError) {
    return renderError();
  }

  const imageProps = {
    ref: imgRef,
    alt,
    className: `responsive-image ${className} ${isLoaded ? 'loaded' : 'loading'}`,
    style: {
      ...style,
      opacity: isLoaded ? 1 : 0,
      transition: 'opacity 0.3s ease',
    },
    onLoad: handleLoad,
    onError: handleError,
    width,
    height,
    sizes,
  };

  if (lazy) {
    // 懒加载模式
    return (
      <>
        {!isLoaded && renderPlaceholder()}
        <img
          {...imageProps}
          data-src={getImageUrl()}
          data-srcset={getSrcSet()}
          className={`${imageProps.className} lazy`}
        />
        <style jsx>{`
          .responsive-image {
            max-width: 100%;
            height: auto;
          }
          
          .responsive-image.lazy {
            opacity: 0;
          }
          
          .responsive-image.loaded {
            opacity: 1;
          }
          
          .placeholder {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 1;
          }
          
          .error-placeholder {
            border-radius: 4px;
          }
        `}</style>
      </>
    );
  } else {
    // 立即加载模式
    return (
      <>
        {!isLoaded && renderPlaceholder()}
        <img
          {...imageProps}
          src={getImageUrl()}
          srcSet={getSrcSet()}
        />
      </>
    );
  }
};

// 高阶组件：为现有图片添加优化功能
export const withImageOptimization = <P extends object>(
  Component: React.ComponentType<P>
) => {
  return React.forwardRef<any, P>((props, ref) => {
    return <Component {...props} ref={ref} />;
  });
};

// 图片预览组件
interface ImagePreviewProps {
  src: string;
  alt: string;
  thumbnail?: string;
  width?: number;
  height?: number;
  className?: string;
  style?: React.CSSProperties;
}

export const ImagePreview: React.FC<ImagePreviewProps> = ({
  src,
  alt,
  thumbnail,
  width,
  height,
  className,
  style,
}) => {
  const [previewVisible, setPreviewVisible] = useState(false);

  return (
    <>
      <ResponsiveImage
        src={thumbnail || src}
        alt={alt}
        width={width}
        height={height}
        className={`${className} cursor-pointer`}
        style={{ cursor: 'pointer', ...style }}
        onClick={() => setPreviewVisible(true)}
      />
      
      {previewVisible && (
        <div
          className="image-preview-overlay"
          onClick={() => setPreviewVisible(false)}
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 10000,
            cursor: 'pointer',
          }}
        >
          <ResponsiveImage
            src={src}
            alt={alt}
            style={{
              maxWidth: '90vw',
              maxHeight: '90vh',
              objectFit: 'contain',
            }}
            lazy={false}
          />
        </div>
      )}
    </>
  );
};

// 图片网格组件
interface ImageGridProps {
  images: Array<{
    src: string;
    alt: string;
    thumbnail?: string;
  }>;
  columns?: number;
  gap?: number;
  className?: string;
}

export const ImageGrid: React.FC<ImageGridProps> = ({
  images,
  columns = 3,
  gap = 16,
  className,
}) => {
  return (
    <div
      className={`image-grid ${className}`}
      style={{
        display: 'grid',
        gridTemplateColumns: `repeat(${columns}, 1fr)`,
        gap: `${gap}px`,
      }}
    >
      {images.map((image, index) => (
        <ImagePreview
          key={index}
          src={image.src}
          alt={image.alt}
          thumbnail={image.thumbnail}
          style={{
            width: '100%',
            aspectRatio: '1',
            objectFit: 'cover',
            borderRadius: '8px',
          }}
        />
      ))}
    </div>
  );
};

export default ResponsiveImage;
