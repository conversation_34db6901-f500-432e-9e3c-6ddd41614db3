"""
证监会（CSRC）监管公告爬虫

目标网站：http://www.csrc.gov.cn/csrc/c100028/common_list.shtml
功能：爬取监管政策、处罚公告等
"""

import asyncio
import re
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import logging

from .base_crawler import BaseCrawler
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../'))
from app.models.news import NewsSource, NewsCategory

logger = logging.getLogger(__name__)


class CSRCCrawler(BaseCrawler):
    """证监会监管公告爬虫"""
    
    def __init__(self, rate_limit: float = 3.0):
        """
        初始化CSRC爬虫
        
        Args:
            rate_limit: 请求间隔（秒），默认3秒（政府网站需要更保守的访问频率）
        """
        super().__init__(
            name="CSRC_Crawler",
            base_url="http://www.csrc.gov.cn",
            rate_limit=rate_limit
        )
        
        # 证监会各类公告URL
        self.announcement_urls = {
            'regulation': 'http://www.csrc.gov.cn/csrc/c100028/common_list.shtml',  # 监管公告
            'policy': 'http://www.csrc.gov.cn/csrc/c100103/common_list.shtml',     # 政策法规
            'punishment': 'http://www.csrc.gov.cn/csrc/c100747/common_list.shtml', # 行政处罚
            'warning': 'http://www.csrc.gov.cn/csrc/c100029/common_list.shtml'     # 监管关注
        }
        
        # 公告类型映射
        self.announcement_types = {
            'regulation': '监管公告',
            'policy': '政策法规',
            'punishment': '行政处罚',
            'warning': '监管关注'
        }
    
    def _build_request_headers(self) -> Dict[str, str]:
        """构建请求头"""
        headers = {
            'User-Agent': self.ua.random,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'max-age=0'
        }
        return headers
    
    def _parse_date_string(self, date_str: str) -> Optional[datetime]:
        """
        解析日期字符串
        
        Args:
            date_str: 日期字符串
            
        Returns:
            datetime对象或None
        """
        if not date_str:
            return None
            
        try:
            # 清理日期字符串
            date_str = date_str.strip()
            
            # 尝试多种日期格式
            formats = [
                "%Y-%m-%d",
                "%Y/%m/%d",
                "%Y年%m月%d日",
                "%Y.%m.%d",
                "%Y-%m-%d %H:%M:%S"
            ]
            
            for fmt in formats:
                try:
                    return datetime.strptime(date_str, fmt)
                except ValueError:
                    continue
            
            # 尝试提取日期数字
            date_match = re.search(r'(\d{4})[-/年.](\d{1,2})[-/月.](\d{1,2})', date_str)
            if date_match:
                year, month, day = date_match.groups()
                return datetime(int(year), int(month), int(day))
                
            logger.warning(f"无法解析日期字符串: {date_str}")
            return None
            
        except Exception as e:
            logger.error(f"日期解析错误: {e}")
            return None
    
    def _categorize_announcement(self, title: str, ann_type: str) -> NewsCategory:
        """
        根据公告标题和类型进行分类
        
        Args:
            title: 公告标题
            ann_type: 公告类型
            
        Returns:
            新闻分类
        """
        title_lower = title.lower()
        
        # 监管类
        if ann_type in ['regulation', 'punishment', 'warning'] or any(keyword in title for keyword in ["监管", "处罚", "违规", "调查", "整改", "警示", "立案"]):
            return NewsCategory.REGULATION
        
        # 政策类
        elif ann_type == 'policy' or any(keyword in title for keyword in ["政策", "规定", "办法", "通知", "规则", "指引", "意见"]):
            return NewsCategory.POLICY
        
        # 默认为监管类（证监会主要发布监管信息）
        else:
            return NewsCategory.REGULATION
    
    async def fetch_news_list(self, start_date: Optional[datetime] = None, 
                             end_date: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """
        获取新闻列表
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            新闻项目列表
        """
        if not start_date:
            start_date = datetime.now() - timedelta(days=7)
        if not end_date:
            end_date = datetime.now()
        
        news_list = []
        
        logger.info(f"[{self.name}] 开始获取公告列表，日期范围: {start_date.date()} 到 {end_date.date()}")
        
        # 遍历各类公告页面
        for ann_type, url in self.announcement_urls.items():
            try:
                logger.info(f"[{self.name}] 开始爬取 {self.announcement_types[ann_type]} 页面")
                
                page = 1
                max_pages = 5  # 限制每个类型的最大页数
                
                while page <= max_pages:
                    try:
                        # 构建分页URL
                        if page == 1:
                            page_url = url
                        else:
                            # 证监会的分页URL格式
                            page_url = url.replace('.shtml', f'_{page}.shtml')
                        
                        # 发起请求
                        headers = self._build_request_headers()
                        response = await self.make_request(page_url, headers=headers)
                        
                        if not response:
                            logger.error(f"[{self.name}] {ann_type} 第{page}页请求失败")
                            break
                        
                        # 解析HTML
                        soup = BeautifulSoup(response.text, 'html.parser')
                        
                        # 提取公告列表
                        announcements = self._extract_announcements(soup, ann_type)
                        
                        if not announcements:
                            logger.info(f"[{self.name}] {ann_type} 第{page}页无数据，停止爬取")
                            break
                        
                        logger.info(f"[{self.name}] {ann_type} 第{page}页获取到 {len(announcements)} 条公告")
                        
                        # 过滤日期范围
                        filtered_announcements = []
                        for ann in announcements:
                            pub_date = ann.get('published_at')
                            if pub_date and start_date <= pub_date <= end_date:
                                filtered_announcements.append(ann)
                            elif pub_date and pub_date < start_date:
                                # 如果遇到更早的日期，停止当前类型的爬取
                                logger.info(f"[{self.name}] {ann_type} 遇到早期数据，停止爬取")
                                break
                        
                        news_list.extend(filtered_announcements)
                        
                        # 如果过滤后没有数据，可能已经超出日期范围
                        if not filtered_announcements:
                            break
                        
                        page += 1
                        
                    except Exception as e:
                        logger.error(f"[{self.name}] {ann_type} 第{page}页爬取失败: {e}")
                        break
                
            except Exception as e:
                logger.error(f"[{self.name}] 爬取 {ann_type} 失败: {e}")
                continue
        
        logger.info(f"[{self.name}] 共获取到 {len(news_list)} 条公告")
        return news_list
    
    def _extract_announcements(self, soup: BeautifulSoup, ann_type: str) -> List[Dict[str, Any]]:
        """
        从页面中提取公告信息
        
        Args:
            soup: BeautifulSoup对象
            ann_type: 公告类型
            
        Returns:
            公告列表
        """
        announcements = []
        
        # 尝试多种选择器来提取公告列表
        list_selectors = [
            '.zx_ml_list li',
            '.list_content li',
            '.news_list li',
            'ul.list li',
            '.content_list li'
        ]
        
        items = []
        for selector in list_selectors:
            items = soup.select(selector)
            if items:
                break
        
        if not items:
            # 如果没有找到列表，尝试提取表格中的数据
            table_rows = soup.select('table tr')
            if len(table_rows) > 1:  # 排除表头
                items = table_rows[1:]
        
        for item in items:
            try:
                # 提取标题和链接
                title_elem = item.find('a')
                if not title_elem:
                    continue
                
                title = title_elem.get_text(strip=True)
                href = title_elem.get('href', '')
                
                if not title or not href:
                    continue
                
                # 构建完整URL
                if href.startswith('http'):
                    source_url = href
                else:
                    source_url = urljoin(self.base_url, href)
                
                # 提取发布日期
                date_text = ""
                date_elem = item.find(class_=re.compile(r'date|time'))
                if date_elem:
                    date_text = date_elem.get_text(strip=True)
                else:
                    # 尝试从文本中提取日期
                    text = item.get_text()
                    date_match = re.search(r'(\d{4}[-/年.]\d{1,2}[-/月.]\d{1,2})', text)
                    if date_match:
                        date_text = date_match.group(1)
                
                published_at = self._parse_date_string(date_text)
                
                # 构建公告数据
                announcement = {
                    'title': title,
                    'source': NewsSource.CSRC.value,
                    'source_url': source_url,
                    'source_id': self._extract_id_from_url(source_url),
                    'published_at': published_at,
                    'announcement_type': ann_type,
                    'raw_data': {
                        'html': str(item),
                        'date_text': date_text
                    }
                }
                
                announcements.append(announcement)
                
            except Exception as e:
                logger.error(f"[{self.name}] 解析公告项失败: {e}")
                continue
        
        return announcements
    
    def _extract_id_from_url(self, url: str) -> str:
        """从URL中提取ID"""
        try:
            # 尝试从URL中提取文件名作为ID
            parsed = urlparse(url)
            path = parsed.path
            if path:
                filename = path.split('/')[-1]
                return filename.replace('.shtml', '').replace('.html', '')
            return ""
        except:
            return ""
    
    async def fetch_news_detail(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取新闻详情
        
        Args:
            item: 新闻项目基本信息
            
        Returns:
            完整的新闻数据
        """
        detail_url = item.get('source_url')
        if not detail_url:
            logger.warning(f"[{self.name}] 缺少详情URL: {item.get('title', '')}")
            return self._build_basic_item(item)
        
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                # 请求详情页面
                headers = self._build_request_headers()
                response = await self.make_request(detail_url, headers=headers)
                
                if not response:
                    retry_count += 1
                    wait_time = 2 ** retry_count
                    logger.warning(f"[{self.name}] 详情页请求失败，{wait_time}秒后重试 ({retry_count}/{max_retries})")
                    await asyncio.sleep(wait_time)
                    continue
                
                # 解析HTML内容
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 提取正文内容
                content = self._extract_content(soup)
                
                # 清理内容
                content = self.clean_text(content)
                
                # 提取摘要
                summary = content[:200] + "..." if len(content) > 200 else content
                
                # 计算重要性评分（证监会公告通常重要性较高）
                importance_score = self.calculate_importance_score(item) + 20  # 基础分数提高
                importance_score = min(100, importance_score)  # 限制最大值
                
                # 提取公司和股票代码
                companies, stock_codes = self.extract_companies_and_codes(
                    item['title'] + " " + content
                )
                
                # 生成内容哈希
                content_hash = self.generate_content_hash(item['title'] + content)
                
                # 构建完整数据
                detailed_item = {
                    'title': item['title'],
                    'content': content,
                    'summary': summary,
                    'source': item['source'],
                    'source_url': item['source_url'],
                    'source_id': item.get('source_id', ''),
                    'published_at': item.get('published_at'),
                    'category': self._categorize_announcement(
                        item['title'], 
                        item.get('announcement_type', '')
                    ).value,
                    'importance_score': importance_score,
                    'companies': companies,
                    'stock_codes': stock_codes,
                    'content_hash': content_hash,
                    'word_count': len(content),
                    'raw_data': item.get('raw_data', {})
                }
                
                logger.debug(f"[{self.name}] 成功获取详情: {item['title'][:50]}...")
                return detailed_item
                
            except Exception as e:
                retry_count += 1
                wait_time = 2 ** retry_count
                logger.error(f"[{self.name}] 获取详情失败: {e}, {wait_time}秒后重试 ({retry_count}/{max_retries})")
                
                if retry_count < max_retries:
                    await asyncio.sleep(wait_time)
                else:
                    logger.error(f"[{self.name}] 详情获取最终失败: {item.get('title', '')}")
                    return self._build_basic_item(item)
        
        return self._build_basic_item(item)
    
    def _extract_content(self, soup: BeautifulSoup) -> str:
        """从BeautifulSoup对象中提取内容"""
        content = ""
        
        # 尝试多种内容选择器
        content_selectors = [
            '.zx_ml_content',
            '.content',
            '.article-content',
            '.main-content',
            '#content',
            '.text-content',
            '.news-content'
        ]
        
        for selector in content_selectors:
            content_elem = soup.select_one(selector)
            if content_elem:
                content = content_elem.get_text(strip=True)
                break
        
        # 如果没找到，尝试提取body中的文本
        if not content:
            body = soup.find('body')
            if body:
                for script in body(["script", "style", "nav", "header", "footer"]):
                    script.decompose()
                content = body.get_text(strip=True)
        
        return content
    
    def _build_basic_item(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """构建基本的新闻项目（当无法获取详情时）"""
        return {
            'title': item['title'],
            'content': '',
            'summary': '',
            'source': item['source'],
            'source_url': item['source_url'],
            'source_id': item.get('source_id', ''),
            'published_at': item.get('published_at'),
            'category': NewsCategory.REGULATION.value,
            'importance_score': 70,  # 证监会公告默认重要性较高
            'companies': [],
            'stock_codes': [],
            'content_hash': self.generate_content_hash(item['title']),
            'word_count': len(item['title']),
            'raw_data': item.get('raw_data', {})
        }
