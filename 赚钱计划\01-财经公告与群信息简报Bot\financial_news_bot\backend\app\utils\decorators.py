"""
权限装饰器模块
提供基于装饰器的权限控制功能
"""
import functools
import logging
from typing import List, Callable, Any, Union
from fastapi import HTTPException, status

from ..models.user import User, UserRole
from ..utils.permissions import has_permission, has_all_permissions, has_any_permission, is_role_higher_or_equal
from ..exceptions.permissions import (
    InsufficientPermissionError,
    MultiplePermissionsRequiredError,
    RoleHierarchyError,
    AuthenticationRequiredError
)

logger = logging.getLogger(__name__)


def require_permission(permission: str):
    """
    要求单个权限的装饰器
    
    Args:
        permission: 所需权限名称
        
    Returns:
        装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # 从kwargs中获取current_user
            current_user = kwargs.get('current_user')
            
            if not current_user:
                logger.warning(f"Permission check failed: No user provided for {func.__name__}")
                raise AuthenticationRequiredError()
            
            if not isinstance(current_user, User):
                logger.error(f"Permission check failed: Invalid user type for {func.__name__}")
                raise AuthenticationRequiredError("Invalid user authentication")
            
            # 检查权限
            if not has_permission(current_user.role, permission):
                logger.warning(
                    f"Permission denied: User {current_user.id} (role: {current_user.role}) "
                    f"attempted to access {func.__name__} requiring '{permission}'"
                )
                raise InsufficientPermissionError(
                    required_permission=permission,
                    user_role=current_user.role.value
                )
            
            logger.info(
                f"Permission granted: User {current_user.id} (role: {current_user.role}) "
                f"accessing {func.__name__} with permission '{permission}'"
            )
            
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator


def require_permissions(permissions: List[str], require_all: bool = True):
    """
    要求多个权限的装饰器
    
    Args:
        permissions: 所需权限列表
        require_all: 是否需要所有权限（True）或任意权限（False）
        
    Returns:
        装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # 从kwargs中获取current_user
            current_user = kwargs.get('current_user')
            
            if not current_user:
                logger.warning(f"Permission check failed: No user provided for {func.__name__}")
                raise AuthenticationRequiredError()
            
            if not isinstance(current_user, User):
                logger.error(f"Permission check failed: Invalid user type for {func.__name__}")
                raise AuthenticationRequiredError("Invalid user authentication")
            
            # 检查权限
            if require_all:
                has_perms = has_all_permissions(current_user.role, permissions)
                missing_perms = [
                    perm for perm in permissions 
                    if not has_permission(current_user.role, perm)
                ]
            else:
                has_perms = has_any_permission(current_user.role, permissions)
                missing_perms = permissions if not has_perms else []
            
            if not has_perms:
                logger.warning(
                    f"Permission denied: User {current_user.id} (role: {current_user.role}) "
                    f"attempted to access {func.__name__} requiring permissions {permissions} "
                    f"(require_all: {require_all})"
                )
                raise MultiplePermissionsRequiredError(
                    required_permissions=permissions,
                    missing_permissions=missing_perms,
                    user_role=current_user.role.value
                )
            
            logger.info(
                f"Permission granted: User {current_user.id} (role: {current_user.role}) "
                f"accessing {func.__name__} with permissions {permissions}"
            )
            
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator


def require_role(min_role: UserRole):
    """
    要求最低角色级别的装饰器
    
    Args:
        min_role: 最低要求的角色
        
    Returns:
        装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # 从kwargs中获取current_user
            current_user = kwargs.get('current_user')
            
            if not current_user:
                logger.warning(f"Role check failed: No user provided for {func.__name__}")
                raise AuthenticationRequiredError()
            
            if not isinstance(current_user, User):
                logger.error(f"Role check failed: Invalid user type for {func.__name__}")
                raise AuthenticationRequiredError("Invalid user authentication")
            
            # 检查角色层级
            if not is_role_higher_or_equal(current_user.role, min_role):
                logger.warning(
                    f"Role check failed: User {current_user.id} (role: {current_user.role}) "
                    f"attempted to access {func.__name__} requiring role '{min_role}'"
                )
                raise RoleHierarchyError(
                    user_role=current_user.role.value,
                    required_role=min_role.value
                )
            
            logger.info(
                f"Role check passed: User {current_user.id} (role: {current_user.role}) "
                f"accessing {func.__name__} with minimum role '{min_role}'"
            )
            
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator


def admin_required(func: Callable) -> Callable:
    """
    要求管理员权限的装饰器（快捷方式）
    
    Args:
        func: 被装饰的函数
        
    Returns:
        装饰后的函数
    """
    return require_role(UserRole.ADMIN)(func)


def enterprise_or_admin_required(func: Callable) -> Callable:
    """
    要求企业版或管理员权限的装饰器（快捷方式）
    
    Args:
        func: 被装饰的函数
        
    Returns:
        装饰后的函数
    """
    return require_role(UserRole.ENTERPRISE)(func)


def pro_or_higher_required(func: Callable) -> Callable:
    """
    要求专业版或更高权限的装饰器（快捷方式）
    
    Args:
        func: 被装饰的函数
        
    Returns:
        装饰后的函数
    """
    return require_role(UserRole.PRO)(func)


class PermissionChecker:
    """权限检查器类，用于复杂的权限逻辑"""
    
    def __init__(self, user: User):
        self.user = user
    
    def check_permission(self, permission: str) -> bool:
        """检查单个权限"""
        return has_permission(self.user.role, permission)
    
    def check_permissions(self, permissions: List[str], require_all: bool = True) -> bool:
        """检查多个权限"""
        if require_all:
            return has_all_permissions(self.user.role, permissions)
        else:
            return has_any_permission(self.user.role, permissions)
    
    def check_role(self, min_role: UserRole) -> bool:
        """检查角色层级"""
        return is_role_higher_or_equal(self.user.role, min_role)
    
    def require_permission_or_raise(self, permission: str):
        """检查权限，不满足则抛出异常"""
        if not self.check_permission(permission):
            raise InsufficientPermissionError(
                required_permission=permission,
                user_role=self.user.role.value
            )
    
    def require_permissions_or_raise(self, permissions: List[str], require_all: bool = True):
        """检查多个权限，不满足则抛出异常"""
        if not self.check_permissions(permissions, require_all):
            missing_perms = [
                perm for perm in permissions 
                if not has_permission(self.user.role, perm)
            ] if require_all else permissions
            
            raise MultiplePermissionsRequiredError(
                required_permissions=permissions,
                missing_permissions=missing_perms,
                user_role=self.user.role.value
            )
    
    def require_role_or_raise(self, min_role: UserRole):
        """检查角色层级，不满足则抛出异常"""
        if not self.check_role(min_role):
            raise RoleHierarchyError(
                user_role=self.user.role.value,
                required_role=min_role.value
            )


def get_permission_checker(user: User) -> PermissionChecker:
    """
    获取权限检查器实例
    
    Args:
        user: 用户对象
        
    Returns:
        PermissionChecker: 权限检查器实例
    """
    return PermissionChecker(user)
