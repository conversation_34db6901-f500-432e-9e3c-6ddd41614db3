"""
权限管理服务
提供用户权限检查、角色管理等功能
"""
import logging
from typing import List, Dict, Any, Optional, Set
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc

from ..models.permission import (
    Permission, Role, UserRole, RolePermission, 
    PermissionGroup, AuditLog
)
from ..models.user import User

logger = logging.getLogger(__name__)


class PermissionService:
    """权限管理服务类"""
    
    def __init__(self, db: Session):
        self.db = db
        self._permission_cache = {}
        self._role_cache = {}
    
    async def check_permission(
        self,
        user_id: int,
        permission_code: str,
        resource_id: Optional[str] = None
    ) -> tuple[bool, Optional[str]]:
        """
        检查用户是否有指定权限
        
        Args:
            user_id: 用户ID
            permission_code: 权限代码
            resource_id: 资源ID（可选）
            
        Returns:
            tuple[bool, Optional[str]]: (是否有权限, 权限来源说明)
        """
        try:
            # 获取用户权限
            user_permissions = await self.get_user_permissions(user_id)
            
            # 检查是否有该权限
            if permission_code in user_permissions:
                # 查找权限来源
                source = await self._get_permission_source(user_id, permission_code)
                return True, source
            
            return False, f"用户缺少 {permission_code} 权限"
            
        except Exception as e:
            logger.error(f"Failed to check permission: {e}")
            return False, f"权限检查失败: {str(e)}"
    
    async def get_user_permissions(self, user_id: int) -> Set[str]:
        """获取用户的所有权限代码"""
        try:
            # 获取用户的所有角色
            user_roles = self.db.query(UserRole).filter(
                UserRole.user_id == user_id,
                UserRole.is_active == True,
                or_(
                    UserRole.expires_at.is_(None),
                    UserRole.expires_at > datetime.now()
                )
            ).all()
            
            if not user_roles:
                return set()
            
            # 获取所有角色的权限
            role_ids = [ur.role_id for ur in user_roles]
            role_permissions = self.db.query(RolePermission).filter(
                RolePermission.role_id.in_(role_ids),
                RolePermission.is_active == True
            ).all()
            
            if not role_permissions:
                return set()
            
            # 获取权限详情
            permission_ids = [rp.permission_id for rp in role_permissions]
            permissions = self.db.query(Permission).filter(
                Permission.id.in_(permission_ids),
                Permission.is_active == True
            ).all()
            
            return {p.code for p in permissions}
            
        except Exception as e:
            logger.error(f"Failed to get user permissions: {e}")
            return set()
    
    async def get_user_roles(self, user_id: int) -> List[Role]:
        """获取用户的所有角色"""
        try:
            user_roles = self.db.query(UserRole).filter(
                UserRole.user_id == user_id,
                UserRole.is_active == True,
                or_(
                    UserRole.expires_at.is_(None),
                    UserRole.expires_at > datetime.now()
                )
            ).all()
            
            if not user_roles:
                return []
            
            role_ids = [ur.role_id for ur in user_roles]
            roles = self.db.query(Role).filter(
                Role.id.in_(role_ids),
                Role.is_active == True
            ).all()
            
            return roles
            
        except Exception as e:
            logger.error(f"Failed to get user roles: {e}")
            return []
    
    async def assign_role_to_user(
        self,
        user_id: int,
        role_id: int,
        assigned_by: Optional[int] = None,
        expires_at: Optional[datetime] = None
    ) -> bool:
        """为用户分配角色"""
        try:
            # 检查用户是否存在
            user = self.db.query(User).filter(User.id == user_id).first()
            if not user:
                logger.error(f"User {user_id} not found")
                return False
            
            # 检查角色是否存在
            role = self.db.query(Role).filter(Role.id == role_id).first()
            if not role:
                logger.error(f"Role {role_id} not found")
                return False
            
            # 检查是否已经分配
            existing = self.db.query(UserRole).filter(
                UserRole.user_id == user_id,
                UserRole.role_id == role_id
            ).first()
            
            if existing:
                if existing.is_active:
                    logger.warning(f"User {user_id} already has role {role_id}")
                    return True
                else:
                    # 重新激活
                    existing.is_active = True
                    existing.assigned_by = assigned_by
                    existing.assigned_at = datetime.now()
                    existing.expires_at = expires_at
            else:
                # 创建新的角色分配
                user_role = UserRole(
                    user_id=user_id,
                    role_id=role_id,
                    assigned_by=assigned_by,
                    expires_at=expires_at
                )
                self.db.add(user_role)
            
            self.db.commit()
            
            # 记录审计日志
            await self._log_audit(
                action="assign_role",
                resource="user_role",
                resource_id=f"{user_id}_{role_id}",
                user_id=assigned_by,
                description=f"为用户 {user_id} 分配角色 {role.name}",
                success=True
            )
            
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to assign role to user: {e}")
            return False
    
    async def remove_role_from_user(
        self,
        user_id: int,
        role_id: int,
        removed_by: Optional[int] = None
    ) -> bool:
        """移除用户角色"""
        try:
            user_role = self.db.query(UserRole).filter(
                UserRole.user_id == user_id,
                UserRole.role_id == role_id,
                UserRole.is_active == True
            ).first()
            
            if not user_role:
                logger.warning(f"User {user_id} does not have role {role_id}")
                return True
            
            user_role.is_active = False
            self.db.commit()
            
            # 记录审计日志
            role = self.db.query(Role).filter(Role.id == role_id).first()
            await self._log_audit(
                action="remove_role",
                resource="user_role",
                resource_id=f"{user_id}_{role_id}",
                user_id=removed_by,
                description=f"移除用户 {user_id} 的角色 {role.name if role else role_id}",
                success=True
            )
            
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to remove role from user: {e}")
            return False
    
    async def create_role(
        self,
        name: str,
        code: str,
        description: Optional[str] = None,
        level: int = 1,
        parent_id: Optional[int] = None,
        config: Optional[Dict[str, Any]] = None,
        created_by: Optional[int] = None
    ) -> Optional[Role]:
        """创建角色"""
        try:
            # 检查代码是否已存在
            existing = self.db.query(Role).filter(Role.code == code).first()
            if existing:
                logger.error(f"Role code {code} already exists")
                return None
            
            role = Role(
                name=name,
                code=code,
                description=description,
                level=level,
                parent_id=parent_id,
                config=config
            )
            
            self.db.add(role)
            self.db.commit()
            self.db.refresh(role)
            
            # 记录审计日志
            await self._log_audit(
                action="create_role",
                resource="role",
                resource_id=str(role.id),
                user_id=created_by,
                description=f"创建角色 {name}",
                new_values={"name": name, "code": code, "level": level},
                success=True
            )
            
            return role
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to create role: {e}")
            return None
    
    async def assign_permissions_to_role(
        self,
        role_id: int,
        permission_ids: List[int],
        assigned_by: Optional[int] = None
    ) -> bool:
        """为角色分配权限"""
        try:
            # 检查角色是否存在
            role = self.db.query(Role).filter(Role.id == role_id).first()
            if not role:
                logger.error(f"Role {role_id} not found")
                return False
            
            # 检查权限是否存在
            permissions = self.db.query(Permission).filter(
                Permission.id.in_(permission_ids)
            ).all()
            
            if len(permissions) != len(permission_ids):
                logger.error("Some permissions not found")
                return False
            
            # 删除现有权限分配
            self.db.query(RolePermission).filter(
                RolePermission.role_id == role_id
            ).delete()
            
            # 添加新的权限分配
            for permission_id in permission_ids:
                role_permission = RolePermission(
                    role_id=role_id,
                    permission_id=permission_id,
                    assigned_by=assigned_by
                )
                self.db.add(role_permission)
            
            self.db.commit()
            
            # 记录审计日志
            await self._log_audit(
                action="assign_permissions",
                resource="role_permission",
                resource_id=str(role_id),
                user_id=assigned_by,
                description=f"为角色 {role.name} 分配 {len(permission_ids)} 个权限",
                new_values={"permission_ids": permission_ids},
                success=True
            )
            
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to assign permissions to role: {e}")
            return False
    
    async def is_admin(self, user_id: int) -> bool:
        """检查用户是否是管理员"""
        try:
            user_permissions = await self.get_user_permissions(user_id)
            return "admin_access" in user_permissions
        except Exception as e:
            logger.error(f"Failed to check admin status: {e}")
            return False
    
    async def is_super_admin(self, user_id: int) -> bool:
        """检查用户是否是超级管理员"""
        try:
            user_roles = await self.get_user_roles(user_id)
            return any(role.code == "super_admin" for role in user_roles)
        except Exception as e:
            logger.error(f"Failed to check super admin status: {e}")
            return False
    
    async def _get_permission_source(
        self,
        user_id: int,
        permission_code: str
    ) -> Optional[str]:
        """获取权限来源"""
        try:
            # 获取用户角色
            user_roles = await self.get_user_roles(user_id)
            
            for role in user_roles:
                # 检查该角色是否有此权限
                role_permission = self.db.query(RolePermission).join(Permission).filter(
                    RolePermission.role_id == role.id,
                    Permission.code == permission_code,
                    RolePermission.is_active == True,
                    Permission.is_active == True
                ).first()
                
                if role_permission:
                    return f"{role.name} 角色"
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get permission source: {e}")
            return None
    
    async def _log_audit(
        self,
        action: str,
        resource: str,
        resource_id: Optional[str] = None,
        user_id: Optional[int] = None,
        description: Optional[str] = None,
        old_values: Optional[Dict[str, Any]] = None,
        new_values: Optional[Dict[str, Any]] = None,
        success: bool = True,
        error_message: Optional[str] = None
    ):
        """记录审计日志"""
        try:
            # 获取用户信息
            username = None
            if user_id:
                user = self.db.query(User).filter(User.id == user_id).first()
                if user:
                    username = user.username
            
            audit_log = AuditLog(
                action=action,
                resource=resource,
                resource_id=resource_id,
                user_id=user_id,
                username=username,
                description=description,
                old_values=old_values,
                new_values=new_values,
                success=success,
                error_message=error_message
            )
            
            self.db.add(audit_log)
            self.db.commit()
            
        except Exception as e:
            logger.error(f"Failed to log audit: {e}")
            # 审计日志失败不应该影响主要操作
