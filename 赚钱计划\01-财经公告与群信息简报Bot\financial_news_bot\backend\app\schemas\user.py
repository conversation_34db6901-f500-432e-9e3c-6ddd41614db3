from pydantic import BaseModel, EmailStr, Field
from typing import Optional
from datetime import datetime
from enum import Enum

class UserRole(str, Enum):
    FREE = "FREE"
    PRO = "PRO"
    ENTERPRISE = "ENTERPRISE"
    ADMIN = "ADMIN"

class UserBase(BaseModel):
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    email: EmailStr = Field(..., description="邮箱地址")

class UserCreate(UserBase):
    password: str = Field(..., min_length=8, max_length=100, description="密码")
    
    class Config:
        json_schema_extra = {
            "example": {
                "username": "testuser",
                "email": "<EMAIL>",
                "password": "password123"
            }
        }

class UserUpdate(BaseModel):
    username: Optional[str] = Field(None, min_length=3, max_length=50, description="用户名")
    email: Optional[EmailStr] = Field(None, description="邮箱地址")

    class Config:
        json_schema_extra = {
            "example": {
                "username": "newusername",
                "email": "<EMAIL>"
            }
        }

class UserRoleUpdate(BaseModel):
    role: str = Field(..., description="用户角色")

    class Config:
        json_schema_extra = {
            "example": {
                "role": "PRO"
            }
        }

class UserLogin(BaseModel):
    email: EmailStr = Field(..., description="邮箱地址")
    password: str = Field(..., description="密码")
    
    class Config:
        json_schema_extra = {
            "example": {
                "email": "<EMAIL>",
                "password": "password123"
            }
        }

class UserResponse(UserBase):
    id: int
    role: UserRole
    created_at: datetime
    updated_at: datetime
    last_login_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": 1,
                "username": "testuser",
                "email": "<EMAIL>",
                "role": "FREE",
                "created_at": "2024-01-01T00:00:00",
                "updated_at": "2024-01-01T00:00:00",
                "last_login_at": "2024-01-01T00:00:00"
            }
        }

class Token(BaseModel):
    access_token: str
    token_type: str = "bearer"
    expires_in: int
    
    class Config:
        json_schema_extra = {
            "example": {
                "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "token_type": "bearer",
                "expires_in": 3600
            }
        }

class TokenData(BaseModel):
    user_id: Optional[int] = None
    email: Optional[str] = None
