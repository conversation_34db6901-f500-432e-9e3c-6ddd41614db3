# 备份和灾难恢复方案

## 数据库备份策略

### 1. 自动备份策略
```bash
#!/bin/bash
# backup_database.sh

# 配置参数
DB_HOST="***********"
DB_PORT="3306"
DB_USER="velen"
DB_PASS="Lovejq4ever."
DB_NAME="financial_news_bot"
BACKUP_DIR="/backup/mysql"
RETENTION_DAYS=30

# 创建备份目录
mkdir -p $BACKUP_DIR

# 生成备份文件名
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="$BACKUP_DIR/financial_news_bot_$TIMESTAMP.sql"

# 执行全量备份
mysqldump -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASS \
  --single-transaction \
  --routines \
  --triggers \
  --events \
  --hex-blob \
  --default-character-set=utf8mb4 \
  $DB_NAME > $BACKUP_FILE

# 压缩备份文件
gzip $BACKUP_FILE

# 删除过期备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +$RETENTION_DAYS -delete

echo "数据库备份完成: ${BACKUP_FILE}.gz"
```

### 2. 增量备份策略
```bash
#!/bin/bash
# incremental_backup.sh

# 配置参数
BINLOG_DIR="/var/log/mysql"
BACKUP_DIR="/backup/mysql/incremental"
LAST_BACKUP_FILE="$BACKUP_DIR/last_backup_position"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 获取当前binlog位置
CURRENT_BINLOG=$(mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASS -e "SHOW MASTER STATUS\G" | grep File | awk '{print $2}')
CURRENT_POSITION=$(mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASS -e "SHOW MASTER STATUS\G" | grep Position | awk '{print $2}')

# 读取上次备份位置
if [ -f $LAST_BACKUP_FILE ]; then
    LAST_BINLOG=$(cat $LAST_BACKUP_FILE | grep File | awk '{print $2}')
    LAST_POSITION=$(cat $LAST_BACKUP_FILE | grep Position | awk '{print $2}')
else
    LAST_BINLOG=$CURRENT_BINLOG
    LAST_POSITION=4
fi

# 生成增量备份
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
INCREMENTAL_FILE="$BACKUP_DIR/incremental_$TIMESTAMP.sql"

mysqlbinlog --start-position=$LAST_POSITION \
  --stop-position=$CURRENT_POSITION \
  $BINLOG_DIR/$LAST_BINLOG > $INCREMENTAL_FILE

# 压缩增量备份
gzip $INCREMENTAL_FILE

# 更新备份位置
echo "File: $CURRENT_BINLOG" > $LAST_BACKUP_FILE
echo "Position: $CURRENT_POSITION" >> $LAST_BACKUP_FILE

echo "增量备份完成: ${INCREMENTAL_FILE}.gz"
```

### 3. 定时备份任务
```bash
# crontab -e
# 每天凌晨2点执行全量备份
0 2 * * * /backup/scripts/backup_database.sh

# 每小时执行增量备份
0 * * * * /backup/scripts/incremental_backup.sh

# 每周日凌晨3点执行备份验证
0 3 * * 0 /backup/scripts/verify_backup.sh
```

## 跨地域数据同步

### 1. MySQL主从复制配置
```sql
-- 主库配置 (my.cnf)
[mysqld]
server-id = 1
log-bin = mysql-bin
binlog-format = ROW
gtid-mode = ON
enforce-gtid-consistency = ON

-- 从库配置 (my.cnf)
[mysqld]
server-id = 2
relay-log = relay-bin
read-only = ON
gtid-mode = ON
enforce-gtid-consistency = ON
```

### 2. 主从同步脚本
```bash
#!/bin/bash
# setup_replication.sh

# 主库操作
mysql -h$MASTER_HOST -u$MASTER_USER -p$MASTER_PASS << EOF
CREATE USER 'replication'@'%' IDENTIFIED BY 'repl_password';
GRANT REPLICATION SLAVE ON *.* TO 'replication'@'%';
FLUSH PRIVILEGES;
SHOW MASTER STATUS;
EOF

# 从库操作
mysql -h$SLAVE_HOST -u$SLAVE_USER -p$SLAVE_PASS << EOF
CHANGE MASTER TO
  MASTER_HOST='$MASTER_HOST',
  MASTER_USER='replication',
  MASTER_PASSWORD='repl_password',
  MASTER_AUTO_POSITION=1;

START SLAVE;
SHOW SLAVE STATUS\G;
EOF
```

### 3. 同步监控脚本
```bash
#!/bin/bash
# monitor_replication.sh

# 检查从库同步状态
SLAVE_STATUS=$(mysql -h$SLAVE_HOST -u$SLAVE_USER -p$SLAVE_PASS -e "SHOW SLAVE STATUS\G")

# 检查IO线程状态
IO_RUNNING=$(echo "$SLAVE_STATUS" | grep "Slave_IO_Running" | awk '{print $2}')
SQL_RUNNING=$(echo "$SLAVE_STATUS" | grep "Slave_SQL_Running" | awk '{print $2}')

# 检查延迟
SECONDS_BEHIND=$(echo "$SLAVE_STATUS" | grep "Seconds_Behind_Master" | awk '{print $2}')

if [ "$IO_RUNNING" != "Yes" ] || [ "$SQL_RUNNING" != "Yes" ]; then
    echo "CRITICAL: MySQL replication is broken"
    # 发送告警
    curl -X POST http://localhost:8000/api/v1/alerts/webhook \
      -H "Content-Type: application/json" \
      -d '{"alert":"MySQL replication failure","severity":"critical"}'
elif [ "$SECONDS_BEHIND" -gt 300 ]; then
    echo "WARNING: MySQL replication lag is ${SECONDS_BEHIND}s"
    # 发送警告
    curl -X POST http://localhost:8000/api/v1/alerts/webhook \
      -H "Content-Type: application/json" \
      -d '{"alert":"MySQL replication lag","severity":"warning","lag":"'$SECONDS_BEHIND'"}'
else
    echo "OK: MySQL replication is healthy"
fi
```

## 灾难恢复流程

### 1. RTO/RPO目标
```yaml
# 恢复目标
recovery_objectives:
  RTO: # 恢复时间目标
    critical_services: "< 15 minutes"
    database: "< 30 minutes"
    full_system: "< 2 hours"
  
  RPO: # 恢复点目标
    database: "< 1 hour"
    application_data: "< 5 minutes"
    configuration: "< 24 hours"
```

### 2. 灾难恢复步骤
```bash
#!/bin/bash
# disaster_recovery.sh

echo "开始灾难恢复流程..."

# 1. 评估损坏程度
echo "1. 评估系统状态..."
./scripts/assess_damage.sh

# 2. 启动备用系统
echo "2. 启动备用系统..."
docker-compose -f docker-compose.disaster.yml up -d

# 3. 恢复数据库
echo "3. 恢复数据库..."
./scripts/restore_database.sh

# 4. 恢复应用数据
echo "4. 恢复应用数据..."
./scripts/restore_application_data.sh

# 5. 验证系统功能
echo "5. 验证系统功能..."
./scripts/verify_system.sh

# 6. 切换DNS
echo "6. 切换DNS到备用系统..."
./scripts/switch_dns.sh

echo "灾难恢复完成！"
```

### 3. 数据库恢复脚本
```bash
#!/bin/bash
# restore_database.sh

BACKUP_DIR="/backup/mysql"
RESTORE_DB="financial_news_bot_recovery"

# 查找最新的全量备份
LATEST_FULL_BACKUP=$(ls -t $BACKUP_DIR/financial_news_bot_*.sql.gz | head -1)

if [ -z "$LATEST_FULL_BACKUP" ]; then
    echo "ERROR: No full backup found"
    exit 1
fi

echo "恢复全量备份: $LATEST_FULL_BACKUP"

# 创建恢复数据库
mysql -h$DB_HOST -u$DB_USER -p$DB_PASS -e "CREATE DATABASE IF NOT EXISTS $RESTORE_DB"

# 恢复全量备份
gunzip -c $LATEST_FULL_BACKUP | mysql -h$DB_HOST -u$DB_USER -p$DB_PASS $RESTORE_DB

# 获取备份时间点
BACKUP_TIME=$(basename $LATEST_FULL_BACKUP | sed 's/financial_news_bot_\(.*\)\.sql\.gz/\1/')

# 应用增量备份
INCREMENTAL_DIR="$BACKUP_DIR/incremental"
for INCREMENTAL_FILE in $(ls -t $INCREMENTAL_DIR/incremental_*.sql.gz); do
    INCREMENTAL_TIME=$(basename $INCREMENTAL_FILE | sed 's/incremental_\(.*\)\.sql\.gz/\1/')
    
    if [ "$INCREMENTAL_TIME" -gt "$BACKUP_TIME" ]; then
        echo "应用增量备份: $INCREMENTAL_FILE"
        gunzip -c $INCREMENTAL_FILE | mysql -h$DB_HOST -u$DB_USER -p$DB_PASS $RESTORE_DB
    fi
done

echo "数据库恢复完成"
```

### 4. 系统验证脚本
```bash
#!/bin/bash
# verify_system.sh

echo "验证系统功能..."

# 检查数据库连接
if mysql -h$DB_HOST -u$DB_USER -p$DB_PASS -e "SELECT 1" > /dev/null 2>&1; then
    echo "✓ 数据库连接正常"
else
    echo "✗ 数据库连接失败"
    exit 1
fi

# 检查应用服务
if curl -f http://localhost:8000/api/v1/infrastructure/health > /dev/null 2>&1; then
    echo "✓ 应用服务正常"
else
    echo "✗ 应用服务异常"
    exit 1
fi

# 检查关键功能
if curl -f http://localhost:8000/api/v1/news/ > /dev/null 2>&1; then
    echo "✓ 新闻API正常"
else
    echo "✗ 新闻API异常"
    exit 1
fi

# 检查数据完整性
NEWS_COUNT=$(mysql -h$DB_HOST -u$DB_USER -p$DB_PASS -e "SELECT COUNT(*) FROM financial_news_bot.news" | tail -1)
if [ "$NEWS_COUNT" -gt 0 ]; then
    echo "✓ 数据完整性验证通过 ($NEWS_COUNT 条新闻)"
else
    echo "✗ 数据完整性验证失败"
    exit 1
fi

echo "系统验证完成，所有功能正常"
```

## 备份验证和测试

### 1. 备份验证脚本
```bash
#!/bin/bash
# verify_backup.sh

BACKUP_DIR="/backup/mysql"
TEST_DB="backup_test_$(date +%s)"

# 获取最新备份
LATEST_BACKUP=$(ls -t $BACKUP_DIR/financial_news_bot_*.sql.gz | head -1)

echo "验证备份文件: $LATEST_BACKUP"

# 创建测试数据库
mysql -h$DB_HOST -u$DB_USER -p$DB_PASS -e "CREATE DATABASE $TEST_DB"

# 恢复备份到测试数据库
gunzip -c $LATEST_BACKUP | mysql -h$DB_HOST -u$DB_USER -p$DB_PASS $TEST_DB

# 验证数据完整性
ORIGINAL_COUNT=$(mysql -h$DB_HOST -u$DB_USER -p$DB_PASS -e "SELECT COUNT(*) FROM financial_news_bot.news" | tail -1)
BACKUP_COUNT=$(mysql -h$DB_HOST -u$DB_USER -p$DB_PASS -e "SELECT COUNT(*) FROM $TEST_DB.news" | tail -1)

if [ "$ORIGINAL_COUNT" -eq "$BACKUP_COUNT" ]; then
    echo "✓ 备份验证成功 (原始: $ORIGINAL_COUNT, 备份: $BACKUP_COUNT)"
else
    echo "✗ 备份验证失败 (原始: $ORIGINAL_COUNT, 备份: $BACKUP_COUNT)"
fi

# 清理测试数据库
mysql -h$DB_HOST -u$DB_USER -p$DB_PASS -e "DROP DATABASE $TEST_DB"
```

### 2. 灾难恢复演练
```bash
#!/bin/bash
# disaster_recovery_drill.sh

echo "开始灾难恢复演练..."

# 1. 模拟灾难场景
echo "1. 模拟系统故障..."
docker-compose down

# 2. 执行恢复流程
echo "2. 执行恢复流程..."
./scripts/disaster_recovery.sh

# 3. 验证恢复结果
echo "3. 验证恢复结果..."
./scripts/verify_system.sh

# 4. 记录演练结果
echo "4. 记录演练结果..."
cat > disaster_recovery_drill_$(date +%Y%m%d).log << EOF
灾难恢复演练报告
演练时间: $(date)
恢复时间: $RECOVERY_TIME
验证结果: $VERIFICATION_RESULT
问题记录: $ISSUES_FOUND
改进建议: $IMPROVEMENT_SUGGESTIONS
EOF

echo "灾难恢复演练完成"
```
