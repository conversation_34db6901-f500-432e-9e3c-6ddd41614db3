#!/usr/bin/env python3
"""
数据真实性验证脚本
检查系统中是否还有模拟数据、硬编码值、TODO标记
"""
import os
import re
import json
from typing import List, Dict, Any
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataAuthenticityChecker:
    """数据真实性检查器"""
    
    def __init__(self, project_root: str):
        self.project_root = project_root
        self.issues = []
        
        # 需要检查的模式
        self.patterns = {
            "mock_data": [
                r"mock|fake|dummy|placeholder",
                r"test.*data|demo.*data|example.*data",
                r"模拟|仿真|虚拟|假数据|示例数据|样本数据",
                r"占位符|默认值|硬编码|固定值|临时值|测试值"
            ],
            "todo_markers": [
                r"TODO|FIXME|HACK|XXX|BUG",
                r"待实现|待完成|临时|暂时"
            ],
            "hardcoded_values": [
                r"unknown|test|demo|example",
                r"localhost:\d+",
                r"127\.0\.0\.1:\d+",
                r"192\.168\.\d+\.\d+:\d+"
            ],
            "fixed_returns": [
                r"return\s+\[\]",
                r"return\s+\{\}",
                r"return\s+None",
                r"return\s+\".*test.*\"",
                r"return\s+\".*demo.*\""
            ]
        }
        
        # 需要检查的文件扩展名
        self.file_extensions = ['.py', '.js', '.ts', '.json', '.yml', '.yaml', '.env']
        
        # 排除的目录
        self.exclude_dirs = {
            '__pycache__', '.git', 'node_modules', '.pytest_cache',
            'logs', 'tests', 'test', 'docs', '.vscode', '.idea'
        }
    
    def scan_files(self) -> List[str]:
        """扫描需要检查的文件"""
        files_to_check = []
        
        for root, dirs, files in os.walk(self.project_root):
            # 排除不需要检查的目录
            dirs[:] = [d for d in dirs if d not in self.exclude_dirs]
            
            for file in files:
                if any(file.endswith(ext) for ext in self.file_extensions):
                    file_path = os.path.join(root, file)
                    files_to_check.append(file_path)
        
        return files_to_check
    
    def check_file(self, file_path: str) -> List[Dict[str, Any]]:
        """检查单个文件"""
        file_issues = []
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                lines = content.split('\n')
            
            # 检查每种模式
            for category, patterns in self.patterns.items():
                for pattern in patterns:
                    matches = re.finditer(pattern, content, re.IGNORECASE)
                    for match in matches:
                        # 找到匹配的行号
                        line_num = content[:match.start()].count('\n') + 1
                        line_content = lines[line_num - 1].strip()
                        
                        file_issues.append({
                            "file": os.path.relpath(file_path, self.project_root),
                            "line": line_num,
                            "category": category,
                            "pattern": pattern,
                            "matched_text": match.group(),
                            "line_content": line_content
                        })
        
        except Exception as e:
            logger.warning(f"无法读取文件 {file_path}: {e}")
        
        return file_issues
    
    def check_database_connections(self) -> List[Dict[str, Any]]:
        """检查数据库连接配置"""
        db_issues = []
        
        # 检查.env文件
        env_file = os.path.join(self.project_root, '.env')
        if os.path.exists(env_file):
            with open(env_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # 检查数据库URL
                if 'DATABASE_URL' in content:
                    if '1.95.67.162:3306' in content:
                        logger.info("✅ 数据库连接使用真实远程MySQL")
                    else:
                        db_issues.append({
                            "file": ".env",
                            "issue": "数据库连接可能不是真实配置",
                            "category": "database_config"
                        })
                
                # 检查Redis配置
                if 'REDIS_URL' in content:
                    if 'redis://redis:6379' in content:
                        logger.info("✅ Redis连接使用Docker服务名")
                    else:
                        db_issues.append({
                            "file": ".env",
                            "issue": "Redis连接配置需要验证",
                            "category": "redis_config"
                        })
        
        return db_issues
    
    def check_microservice_urls(self) -> List[Dict[str, Any]]:
        """检查微服务URL配置"""
        url_issues = []
        
        # 检查微服务适配器
        adapter_file = os.path.join(self.project_root, 'backend/app/adapters/microservice_adapter.py')
        if os.path.exists(adapter_file):
            with open(adapter_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # 检查安全监控服务URL
                if 'http://security-monitor:8001' in content:
                    logger.info("✅ 安全监控微服务URL配置正确")
                else:
                    url_issues.append({
                        "file": "backend/app/adapters/microservice_adapter.py",
                        "issue": "安全监控微服务URL配置可能有问题",
                        "category": "microservice_url"
                    })
                
                # 检查合规监控服务URL
                if 'http://compliance-monitor:8002' in content:
                    logger.info("✅ 合规监控微服务URL配置正确")
                else:
                    url_issues.append({
                        "file": "backend/app/adapters/microservice_adapter.py",
                        "issue": "合规监控微服务URL配置可能有问题",
                        "category": "microservice_url"
                    })
        
        return url_issues
    
    def run_full_check(self) -> Dict[str, Any]:
        """运行完整检查"""
        logger.info("🔍 开始数据真实性检查...")
        
        # 1. 扫描文件中的问题
        logger.info("📁 扫描文件中的模拟数据和硬编码值...")
        files_to_check = self.scan_files()
        logger.info(f"需要检查 {len(files_to_check)} 个文件")
        
        all_file_issues = []
        for file_path in files_to_check:
            file_issues = self.check_file(file_path)
            all_file_issues.extend(file_issues)
        
        # 2. 检查数据库连接
        logger.info("🗄️ 检查数据库连接配置...")
        db_issues = self.check_database_connections()
        
        # 3. 检查微服务URL
        logger.info("🔗 检查微服务URL配置...")
        url_issues = self.check_microservice_urls()
        
        # 汇总结果
        results = {
            "file_issues": all_file_issues,
            "database_issues": db_issues,
            "microservice_issues": url_issues,
            "summary": {
                "total_file_issues": len(all_file_issues),
                "total_database_issues": len(db_issues),
                "total_microservice_issues": len(url_issues),
                "files_checked": len(files_to_check)
            }
        }
        
        return results
    
    def print_report(self, results: Dict[str, Any]):
        """打印检查报告"""
        logger.info("📊 数据真实性检查报告:")
        logger.info("=" * 50)
        
        # 文件问题
        if results["file_issues"]:
            logger.warning(f"⚠️ 发现 {len(results['file_issues'])} 个文件问题:")
            for issue in results["file_issues"][:10]:  # 只显示前10个
                logger.warning(f"  📄 {issue['file']}:{issue['line']} - {issue['category']}")
                logger.warning(f"     匹配: {issue['matched_text']}")
        else:
            logger.info("✅ 未发现文件中的模拟数据问题")
        
        # 数据库问题
        if results["database_issues"]:
            logger.warning(f"⚠️ 发现 {len(results['database_issues'])} 个数据库配置问题:")
            for issue in results["database_issues"]:
                logger.warning(f"  🗄️ {issue['file']} - {issue['issue']}")
        else:
            logger.info("✅ 数据库配置检查通过")
        
        # 微服务问题
        if results["microservice_issues"]:
            logger.warning(f"⚠️ 发现 {len(results['microservice_issues'])} 个微服务配置问题:")
            for issue in results["microservice_issues"]:
                logger.warning(f"  🔗 {issue['file']} - {issue['issue']}")
        else:
            logger.info("✅ 微服务配置检查通过")
        
        # 总结
        total_issues = (len(results["file_issues"]) + 
                       len(results["database_issues"]) + 
                       len(results["microservice_issues"]))
        
        if total_issues == 0:
            logger.info("🎉 所有检查通过！系统使用真实数据")
        else:
            logger.warning(f"⚠️ 发现 {total_issues} 个问题需要修复")

def main():
    """主函数"""
    project_root = os.path.dirname(os.path.abspath(__file__))
    checker = DataAuthenticityChecker(project_root)
    
    results = checker.run_full_check()
    checker.print_report(results)
    
    # 保存详细结果
    with open("data_authenticity_report.json", "w", encoding="utf-8") as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    logger.info("📄 详细报告已保存到 data_authenticity_report.json")
    
    # 返回退出码
    total_issues = (len(results["file_issues"]) + 
                   len(results["database_issues"]) + 
                   len(results["microservice_issues"]))
    
    return 0 if total_issues == 0 else 1

if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
