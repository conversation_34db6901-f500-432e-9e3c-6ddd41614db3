"""
飞书推送提供商
支持飞书群机器人消息推送
"""
import logging
import json
import re
from typing import Dict, Any, List
import httpx

from ..unified_push_service import BasePushProvider, PushMessage, PushResult, PushChannel, MessageType

logger = logging.getLogger(__name__)

class FeishuProvider(BasePushProvider):
    """飞书推送提供商"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.channel = PushChannel.FEISHU
        self.timeout = config.get('timeout', 30)
        self.max_content_length = config.get('max_content_length', 8192)
    
    def validate_target(self, target: str) -> bool:
        """
        验证飞书Webhook地址是否有效
        
        Args:
            target: Webhook URL
        
        Returns:
            是否有效
        """
        if not target:
            return False
        
        # 飞书机器人webhook格式验证
        pattern = r'https://open\.feishu\.cn/open-apis/bot/v2/hook/[a-zA-Z0-9\-_]+'
        return bool(re.match(pattern, target))
    
    async def send_message(self, target: str, message: PushMessage) -> PushResult:
        """
        发送飞书消息
        
        Args:
            target: 飞书Webhook URL
            message: 推送消息
        
        Returns:
            推送结果
        """
        try:
            # 根据消息类型格式化消息
            payload = self._format_message_payload(message)
            
            # 发送HTTP请求
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    target,
                    json=payload,
                    headers={'Content-Type': 'application/json'}
                )
                
                response_data = response.json()
                
                if response.status_code == 200 and response_data.get('code') == 0:
                    return PushResult(
                        success=True,
                        message="飞书消息发送成功",
                        response_data=response_data
                    )
                else:
                    error_msg = response_data.get('msg', '未知错误')
                    return PushResult(
                        success=False,
                        message=f"飞书消息发送失败: {error_msg}",
                        error_code=str(response_data.get('code', 'UNKNOWN')),
                        response_data=response_data
                    )
                    
        except httpx.TimeoutException:
            return PushResult(
                success=False,
                message="飞书消息发送超时",
                error_code="TIMEOUT"
            )
        except Exception as e:
            return PushResult(
                success=False,
                message=f"飞书消息发送异常: {str(e)}",
                error_code="EXCEPTION"
            )
    
    def _format_message_payload(self, message: PushMessage) -> Dict[str, Any]:
        """
        格式化飞书消息载荷
        
        Args:
            message: 推送消息
        
        Returns:
            飞书API格式的消息载荷
        """
        # 截断过长的内容
        content = self._truncate_content(message.content)
        
        if message.message_type == MessageType.MARKDOWN:
            return self._format_markdown_message(message.title, content)
        elif message.message_type == MessageType.CARD:
            return self._format_card_message(message.title, content, message.extra_data)
        else:
            return self._format_text_message(message.title, content)
    
    def _format_text_message(self, title: str, content: str) -> Dict[str, Any]:
        """格式化文本消息"""
        full_content = f"{title}\n\n{content}" if title else content
        
        return {
            "msg_type": "text",
            "content": {
                "text": full_content
            }
        }
    
    def _format_markdown_message(self, title: str, content: str) -> Dict[str, Any]:
        """格式化Markdown消息"""
        # 飞书支持的Markdown格式
        markdown_content = content
        
        # 如果有标题，添加到Markdown内容中
        if title:
            markdown_content = f"# {title}\n\n{content}"
        
        return {
            "msg_type": "post",
            "content": {
                "post": {
                    "zh_cn": {
                        "title": title or "财经新闻推送",
                        "content": self._parse_markdown_to_feishu_format(markdown_content)
                    }
                }
            }
        }
    
    def _format_card_message(self, title: str, content: str, extra_data: Dict[str, Any]) -> Dict[str, Any]:
        """格式化卡片消息"""
        # 飞书的交互式卡片格式
        card_elements = []
        
        # 标题
        if title:
            card_elements.append({
                "tag": "div",
                "text": {
                    "tag": "lark_md",
                    "content": f"**{title}**"
                }
            })
        
        # 内容
        if content:
            card_elements.append({
                "tag": "div",
                "text": {
                    "tag": "lark_md",
                    "content": content
                }
            })
        
        # 额外的新闻条目
        news_items = extra_data.get('news_items', [])
        if news_items:
            for item in news_items[:5]:  # 最多显示5条
                card_elements.append({
                    "tag": "hr"
                })
                card_elements.append({
                    "tag": "div",
                    "text": {
                        "tag": "lark_md",
                        "content": f"**{item.get('title', '')}**\n{item.get('summary', '')}"
                    }
                })
                
                # 如果有链接，添加按钮
                if item.get('url'):
                    card_elements.append({
                        "tag": "action",
                        "actions": [{
                            "tag": "button",
                            "text": {
                                "tag": "plain_text",
                                "content": "查看详情"
                            },
                            "type": "primary",
                            "url": item['url']
                        }]
                    })
        
        return {
            "msg_type": "interactive",
            "card": {
                "config": {
                    "wide_screen_mode": True
                },
                "elements": card_elements
            }
        }
    
    def _parse_markdown_to_feishu_format(self, markdown_content: str) -> List[List[Dict[str, Any]]]:
        """
        将Markdown内容转换为飞书富文本格式
        
        Args:
            markdown_content: Markdown内容
        
        Returns:
            飞书富文本格式的内容
        """
        lines = markdown_content.split('\n')
        content = []
        
        for line in lines:
            if not line.strip():
                continue
            
            # 解析不同的Markdown元素
            if line.startswith('# '):
                # 一级标题
                content.append([{
                    "tag": "text",
                    "text": line[2:],
                    "style": ["bold"]
                }])
            elif line.startswith('## '):
                # 二级标题
                content.append([{
                    "tag": "text",
                    "text": line[3:],
                    "style": ["bold"]
                }])
            elif line.startswith('- ') or line.startswith('• '):
                # 列表项
                content.append([{
                    "tag": "text",
                    "text": f"• {line[2:]}"
                }])
            elif '**' in line:
                # 粗体文本
                content.append(self._parse_bold_text(line))
            else:
                # 普通文本
                content.append([{
                    "tag": "text",
                    "text": line
                }])
        
        return content
    
    def _parse_bold_text(self, line: str) -> List[Dict[str, Any]]:
        """解析包含粗体的文本行"""
        parts = []
        current_pos = 0
        
        while True:
            start = line.find('**', current_pos)
            if start == -1:
                # 没有更多粗体标记，添加剩余文本
                if current_pos < len(line):
                    parts.append({
                        "tag": "text",
                        "text": line[current_pos:]
                    })
                break
            
            # 添加粗体前的普通文本
            if start > current_pos:
                parts.append({
                    "tag": "text",
                    "text": line[current_pos:start]
                })
            
            # 查找粗体结束标记
            end = line.find('**', start + 2)
            if end == -1:
                # 没有结束标记，将剩余部分作为普通文本
                parts.append({
                    "tag": "text",
                    "text": line[start:]
                })
                break
            
            # 添加粗体文本
            parts.append({
                "tag": "text",
                "text": line[start + 2:end],
                "style": ["bold"]
            })
            
            current_pos = end + 2
        
        return parts
    
    def _truncate_content(self, content: str) -> str:
        """
        截断过长的内容
        
        Args:
            content: 原始内容
        
        Returns:
            截断后的内容
        """
        if len(content) <= self.max_content_length:
            return content
        
        # 截断并添加省略号
        truncated = content[:self.max_content_length - 10]
        return truncated + "\n\n... (内容过长已截断)"
    
    def format_news_list_message(self, news_list, subscription_name: str) -> PushMessage:
        """
        格式化新闻列表为飞书消息
        
        Args:
            news_list: 新闻列表
            subscription_name: 订阅名称
        
        Returns:
            格式化后的推送消息
        """
        if not news_list:
            return PushMessage(
                title="📰 财经新闻推送",
                content="暂无新闻更新",
                message_type=MessageType.TEXT
            )
        
        # 构建富文本格式的新闻推送
        title = f"📰 {subscription_name} - 财经新闻推送"
        
        content_lines = [
            f"## 📊 本次推送概览",
            f"- **新闻数量**: {len(news_list)} 条",
            f"- **推送时间**: {news_list[0].created_at.strftime('%Y-%m-%d %H:%M')}",
            "",
            "## 📰 重要新闻"
        ]
        
        # 添加重要新闻
        important_news = [n for n in news_list if n.importance_score >= 80][:3]
        for i, news in enumerate(important_news, 1):
            content_lines.extend([
                f"{i}. **{news.title}**",
                f"   📅 {news.published_at.strftime('%m-%d %H:%M')} | 🏢 {news.source} | 📊 {news.importance_score}分",
                ""
            ])
        
        # 添加其他新闻
        if len(news_list) > len(important_news):
            content_lines.append("## 📋 其他新闻")
            other_news = [n for n in news_list if n.importance_score < 80][:5]
            for news in other_news:
                content_lines.append(f"• {news.title}")
        
        content_lines.extend([
            "",
            "---",
            "💼 **财经新闻Bot** | 智能推送服务"
        ])
        
        return PushMessage(
            title=title,
            content="\n".join(content_lines),
            message_type=MessageType.MARKDOWN
        )
    
    def create_news_card_message(self, news_list, subscription_name: str) -> PushMessage:
        """
        创建新闻卡片消息
        
        Args:
            news_list: 新闻列表
            subscription_name: 订阅名称
        
        Returns:
            卡片格式的推送消息
        """
        if not news_list:
            return self.format_news_list_message(news_list, subscription_name)
        
        title = f"📰 {subscription_name} - 财经新闻推送"
        content = f"本次推送包含 **{len(news_list)}** 条财经新闻"
        
        # 构建新闻条目
        news_items = []
        for news in news_list[:5]:  # 最多5条新闻
            news_items.append({
                "title": news.title,
                "summary": news.summary[:100] + "..." if news.summary and len(news.summary) > 100 else (news.summary or ""),
                "url": news.source_url or ""
            })
        
        extra_data = {
            "news_items": news_items
        }
        
        return PushMessage(
            title=title,
            content=content,
            message_type=MessageType.CARD,
            extra_data=extra_data
        )
