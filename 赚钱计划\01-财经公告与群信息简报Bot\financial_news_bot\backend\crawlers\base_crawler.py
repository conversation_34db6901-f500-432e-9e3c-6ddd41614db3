from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
import httpx
import asyncio
import logging
from datetime import datetime, timedelta
import hashlib
import time
import random
import os

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AntiCrawlerManager:
    """反爬虫机制管理器"""

    def __init__(self):
        # User-Agent池 - 60+个真实浏览器UA，覆盖主流浏览器和操作系统
        self.user_agents = [
            # Chrome - Windows 10/11 (最新版本)
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (<PERSON>HT<PERSON>, like Gecko) Chrome/118.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 11.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 11.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 11.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36',

            # Chrome - macOS (不同版本)
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 13_6_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 14_1_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 14_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',

            # Chrome - Linux (不同发行版)
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Ubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Fedora; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',

            # Firefox - Windows
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:118.0) Gecko/20100101 Firefox/118.0',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:117.0) Gecko/20100101 Firefox/117.0',
            'Mozilla/5.0 (Windows NT 11.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
            'Mozilla/5.0 (Windows NT 11.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0',

            # Firefox - macOS
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:120.0) Gecko/20100101 Firefox/120.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:119.0) Gecko/20100101 Firefox/119.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:118.0) Gecko/20100101 Firefox/118.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 13.6; rv:120.0) Gecko/20100101 Firefox/120.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 14.1; rv:120.0) Gecko/20100101 Firefox/120.0',

            # Firefox - Linux
            'Mozilla/5.0 (X11; Linux x86_64; rv:120.0) Gecko/20100101 Firefox/120.0',
            'Mozilla/5.0 (X11; Linux x86_64; rv:119.0) Gecko/20100101 Firefox/119.0',
            'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:120.0) Gecko/20100101 Firefox/120.0',
            'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:119.0) Gecko/20100101 Firefox/119.0',

            # Safari - macOS (不同版本)
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Safari/605.1.15',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 13_6_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 14_1_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 14_2) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15',

            # Edge - Windows
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36 Edg/118.0.0.0',
            'Mozilla/5.0 (Windows NT 11.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
            'Mozilla/5.0 (Windows NT 11.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0',

            # Edge - macOS
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0',

            # Edge - Linux
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0',

            # Opera - 多平台
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 OPR/106.0.0.0',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 OPR/105.0.0.0',
            'Mozilla/5.0 (Windows NT 11.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 OPR/106.0.0.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 OPR/106.0.0.0',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 OPR/106.0.0.0',

            # Brave - 隐私浏览器
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Brave/120.0.0.0',
            'Mozilla/5.0 (Windows NT 11.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Brave/120.0.0.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Brave/120.0.0.0',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Brave/120.0.0.0',

            # Vivaldi - 定制化浏览器
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Vivaldi/6.4.3160.47',
            'Mozilla/5.0 (Windows NT 11.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Vivaldi/6.4.3160.47',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Vivaldi/6.4.3160.47',

            # 移动端 User-Agent (模拟移动设备访问)
            'Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1',
            'Mozilla/5.0 (iPhone; CPU iPhone OS 16_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
            'Mozilla/5.0 (iPad; CPU OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1',
            'Mozilla/5.0 (iPad; CPU OS 16_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
            'Mozilla/5.0 (Linux; Android 14; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
            'Mozilla/5.0 (Linux; Android 13; Pixel 7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
            'Mozilla/5.0 (Linux; Android 13; SM-A546B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
            'Mozilla/5.0 (Linux; Android 12; SM-G973F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',

            # 一些特殊的企业版和定制版浏览器
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Enterprise',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Corporate',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Chromium/120.0.0.0',

            # 一些较老但仍在使用的浏览器版本
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:115.0) Gecko/20100101 Firefox/115.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5 Safari/605.1.15'
        ]

        # 代理配置
        self.proxies = self._load_proxies()

        # 请求失败计数器
        self.failure_counts = {}

        # 域名请求间隔配置
        self.domain_intervals = {
            'sse.com.cn': (2, 4),      # 上交所：2-4秒
            'szse.cn': (2, 4),         # 深交所：2-4秒
            'csrc.gov.cn': (3, 6),     # 证监会：3-6秒
            'default': (1, 3)          # 默认：1-3秒
        }

        # 浏览器指纹特征池
        self.browser_fingerprints = self._init_browser_fingerprints()

    def _load_proxies(self) -> List[str]:
        """从环境变量加载代理配置"""
        proxy_list = os.getenv('HTTP_PROXIES', '').split(',')
        return [proxy.strip() for proxy in proxy_list if proxy.strip()]

    def _init_browser_fingerprints(self) -> Dict[str, Any]:
        """初始化浏览器指纹特征池"""
        return {
            # Accept头变体
            'accept_headers': [
                'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
                'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8'
            ],

            # Accept-Language头变体
            'accept_language_headers': [
                'zh-CN,zh;q=0.9,en;q=0.8',
                'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
                'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
                'zh-CN,zh;q=0.9',
                'zh-CN,zh;q=0.9,en;q=0.8,ja;q=0.7',
                'zh-CN,zh-TW;q=0.9,zh;q=0.8,en;q=0.7'
            ],

            # Accept-Encoding头变体
            'accept_encoding_headers': [
                'gzip, deflate, br',
                'gzip, deflate, br, zstd',
                'gzip, deflate',
                'gzip, deflate, br, zstd, compress'
            ],

            # Cache-Control头变体
            'cache_control_headers': [
                'max-age=0',
                'no-cache',
                'max-age=0, no-cache',
                'no-cache, no-store, must-revalidate',
                'max-age=300'
            ],

            # DNT (Do Not Track) 头变体
            'dnt_headers': ['1', '0', None],

            # Sec-Fetch-* 头变体 (Chrome特有)
            'sec_fetch_headers': {
                'Sec-Fetch-Dest': ['document', 'empty', 'iframe'],
                'Sec-Fetch-Mode': ['navigate', 'cors', 'no-cors'],
                'Sec-Fetch-Site': ['none', 'same-origin', 'cross-site'],
                'Sec-Fetch-User': ['?1', None]
            },

            # 连接类型变体
            'connection_headers': ['keep-alive', 'close'],

            # 升级不安全请求
            'upgrade_insecure_requests': ['1', None],

            # 浏览器特定的头部组合
            'browser_specific_headers': {
                'chrome': {
                    'sec-ch-ua': [
                        '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                        '"Not_A Brand";v="8", "Chromium";v="119", "Google Chrome";v="119"',
                        '"Chromium";v="120", "Not(A:Brand";v="24", "Google Chrome";v="120"'
                    ],
                    'sec-ch-ua-mobile': ['?0'],
                    'sec-ch-ua-platform': ['"Windows"', '"macOS"', '"Linux"'],
                    'sec-ch-ua-platform-version': ['"10.0.0"', '"13.6.1"', '"6.2.0"']
                },
                'firefox': {
                    'Accept': ['text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8'],
                    'Accept-Encoding': ['gzip, deflate, br'],
                    'Accept-Language': ['zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2']
                },
                'safari': {
                    'Accept': ['text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'],
                    'Accept-Language': ['zh-CN,zh-Hans;q=0.9'],
                    'Accept-Encoding': ['gzip, deflate, br']
                }
            },

            # 屏幕分辨率和设备特征
            'screen_resolutions': [
                '1920x1080', '1366x768', '1536x864', '1440x900', '1280x720',
                '2560x1440', '3840x2160', '1680x1050', '1600x900', '1024x768'
            ],

            # 时区变体
            'timezones': [
                'Asia/Shanghai', 'Asia/Beijing', 'Asia/Hong_Kong',
                'Asia/Taipei', 'UTC+8', 'GMT+8'
            ]
        }

    def get_random_user_agent(self) -> str:
        """获取随机User-Agent"""
        return random.choice(self.user_agents)

    def get_random_browser_fingerprint(self) -> Dict[str, str]:
        """生成随机的浏览器指纹头部"""
        fingerprint = {}

        # 基础头部
        fingerprint['Accept'] = random.choice(self.browser_fingerprints['accept_headers'])
        fingerprint['Accept-Language'] = random.choice(self.browser_fingerprints['accept_language_headers'])
        fingerprint['Accept-Encoding'] = random.choice(self.browser_fingerprints['accept_encoding_headers'])
        fingerprint['Connection'] = random.choice(self.browser_fingerprints['connection_headers'])

        # 可选头部
        if random.random() > 0.3:  # 70%概率添加Cache-Control
            fingerprint['Cache-Control'] = random.choice(self.browser_fingerprints['cache_control_headers'])

        if random.random() > 0.5:  # 50%概率添加DNT
            dnt = random.choice(self.browser_fingerprints['dnt_headers'])
            if dnt:
                fingerprint['DNT'] = dnt

        if random.random() > 0.4:  # 60%概率添加Upgrade-Insecure-Requests
            upgrade = random.choice(self.browser_fingerprints['upgrade_insecure_requests'])
            if upgrade:
                fingerprint['Upgrade-Insecure-Requests'] = upgrade

        # 根据User-Agent添加浏览器特定头部
        user_agent = fingerprint.get('User-Agent', '')
        if 'Chrome' in user_agent and random.random() > 0.2:  # 80%概率为Chrome添加sec-ch-ua头
            sec_fetch = self.browser_fingerprints['sec_fetch_headers']
            fingerprint['Sec-Fetch-Dest'] = random.choice(sec_fetch['Sec-Fetch-Dest'])
            fingerprint['Sec-Fetch-Mode'] = random.choice(sec_fetch['Sec-Fetch-Mode'])
            fingerprint['Sec-Fetch-Site'] = random.choice(sec_fetch['Sec-Fetch-Site'])

            user_header = random.choice(sec_fetch['Sec-Fetch-User'])
            if user_header:
                fingerprint['Sec-Fetch-User'] = user_header

            # Chrome特有的sec-ch-ua头
            chrome_headers = self.browser_fingerprints['browser_specific_headers']['chrome']
            fingerprint['sec-ch-ua'] = random.choice(chrome_headers['sec-ch-ua'])
            fingerprint['sec-ch-ua-mobile'] = random.choice(chrome_headers['sec-ch-ua-mobile'])
            fingerprint['sec-ch-ua-platform'] = random.choice(chrome_headers['sec-ch-ua-platform'])

        return fingerprint

    def get_random_proxy(self) -> Optional[str]:
        """获取随机代理"""
        if not self.proxies:
            return None
        return random.choice(self.proxies)

    def get_domain_interval(self, url: str) -> tuple:
        """获取域名对应的请求间隔"""
        for domain, interval in self.domain_intervals.items():
            if domain in url:
                return interval
        return self.domain_intervals['default']

    def calculate_backoff_delay(self, domain: str, attempt: int) -> float:
        """计算指数退避延迟时间"""
        base_delay = 2.0
        max_delay = 60.0

        # 指数退避：2^attempt * base_delay，加上随机抖动
        delay = min(max_delay, (2 ** attempt) * base_delay)
        jitter = random.uniform(0, delay * 0.1)  # 10%的随机抖动

        return delay + jitter

    def record_failure(self, domain: str):
        """记录请求失败"""
        if domain not in self.failure_counts:
            self.failure_counts[domain] = 0
        self.failure_counts[domain] += 1

    def reset_failure_count(self, domain: str):
        """重置失败计数"""
        if domain in self.failure_counts:
            self.failure_counts[domain] = 0

    def get_failure_count(self, domain: str) -> int:
        """获取失败计数"""
        return self.failure_counts.get(domain, 0)

    def should_alert(self, domain: str) -> bool:
        """判断是否应该发送告警"""
        return self.get_failure_count(domain) >= 5


class BaseCrawler(ABC):
    """基础爬虫抽象类"""

    def __init__(self, name: str, base_url: str, rate_limit: float = 1.0):
        """
        初始化爬虫

        Args:
            name: 爬虫名称
            base_url: 基础URL
            rate_limit: 请求间隔（秒）
        """
        self.name = name
        self.base_url = base_url
        self.rate_limit = rate_limit
        self.session = None
        self.last_request_time = 0

        # 反爬虫管理器（包含User-Agent池和代理管理）
        self.anti_crawler = AntiCrawlerManager()

        # 域名提取
        from urllib.parse import urlparse
        self.domain = urlparse(base_url).netloc

        # 统计信息
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_items': 0,
            'duplicate_items': 0,
            'start_time': None,
            'end_time': None,
            'consecutive_failures': 0,
            'alerts_sent': 0
        }
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close_session()
    
    async def start_session(self):
        """启动HTTP会话"""
        # 使用反爬虫管理器生成随机浏览器指纹
        headers = self.anti_crawler.get_random_browser_fingerprint()
        headers['User-Agent'] = self.anti_crawler.get_random_user_agent()

        # 添加Referer（如果不是首次访问）
        if hasattr(self, '_last_url'):
            headers['Referer'] = self._last_url

        timeout = httpx.Timeout(30.0, connect=10.0)

        # 配置代理
        proxy = self.anti_crawler.get_random_proxy()
        proxies = {"http://": proxy, "https://": proxy} if proxy else None

        self.session = httpx.AsyncClient(
            headers=headers,
            timeout=timeout,
            follow_redirects=True,
            proxies=proxies
        )
        logger.info(f"[{self.name}] HTTP会话已启动，代理: {proxy or '无'}")
    
    async def close_session(self):
        """关闭HTTP会话"""
        if self.session:
            await self.session.aclose()
            logger.info(f"[{self.name}] HTTP会话已关闭")
    
    async def rate_limit_wait(self, url: str = None):
        """智能速率限制等待"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time

        # 根据域名获取动态间隔
        if url:
            min_interval, max_interval = self.anti_crawler.get_domain_interval(url)
            dynamic_interval = random.uniform(min_interval, max_interval)
        else:
            dynamic_interval = self.rate_limit

        if time_since_last < dynamic_interval:
            wait_time = dynamic_interval - time_since_last
            # 添加随机抖动，避免被检测
            wait_time += random.uniform(0, 0.5)
            logger.debug(f"[{self.name}] 速率限制等待 {wait_time:.2f}秒")
            await asyncio.sleep(wait_time)

        self.last_request_time = time.time()
    
    async def make_request(self, url: str, method: str = 'GET', max_retries: int = 3, **kwargs) -> Optional[httpx.Response]:
        """
        发起HTTP请求（带重试和反爬虫机制）

        Args:
            url: 请求URL
            method: 请求方法
            max_retries: 最大重试次数
            **kwargs: 其他请求参数

        Returns:
            响应对象或None
        """
        for attempt in range(max_retries + 1):
            await self.rate_limit_wait(url)

            try:
                self.stats['total_requests'] += 1

                # 添加防缓存参数
                if method.upper() == 'GET' and 'params' not in kwargs:
                    kwargs['params'] = {'_t': int(time.time() * 1000)}
                elif method.upper() == 'GET' and 'params' in kwargs:
                    kwargs['params']['_t'] = int(time.time() * 1000)

                # 随机更换User-Agent
                if self.session and random.random() < 0.1:  # 10%概率更换
                    self.session.headers['User-Agent'] = self.anti_crawler.get_random_user_agent()

                # 执行请求
                if method.upper() == 'GET':
                    response = await self.session.get(url, **kwargs)
                elif method.upper() == 'POST':
                    response = await self.session.post(url, **kwargs)
                else:
                    raise ValueError(f"不支持的HTTP方法: {method}")

                # 检查响应状态
                if response.status_code == 429:  # Too Many Requests
                    if attempt < max_retries:
                        backoff_delay = self.anti_crawler.calculate_backoff_delay(self.domain, attempt)
                        logger.warning(f"[{self.name}] 触发速率限制，等待 {backoff_delay:.2f}秒后重试")
                        await asyncio.sleep(backoff_delay)
                        continue

                response.raise_for_status()

                # 请求成功，重置失败计数
                self.anti_crawler.reset_failure_count(self.domain)
                self.stats['successful_requests'] += 1
                self.stats['consecutive_failures'] = 0

                # 更新最后访问的URL（用于Referer）
                self._last_url = url

                logger.debug(f"[{self.name}] 请求成功: {url}")
                return response

            except httpx.HTTPStatusError as e:
                self.stats['failed_requests'] += 1
                self.stats['consecutive_failures'] += 1
                self.anti_crawler.record_failure(self.domain)

                if e.response.status_code in [403, 429, 503]:  # 可能的反爬虫响应
                    if attempt < max_retries:
                        backoff_delay = self.anti_crawler.calculate_backoff_delay(self.domain, attempt)
                        logger.warning(f"[{self.name}] HTTP错误 {e.response.status_code}，等待 {backoff_delay:.2f}秒后重试")
                        await asyncio.sleep(backoff_delay)
                        continue

                logger.error(f"[{self.name}] HTTP错误 {e.response.status_code}: {url}")
                break

            except httpx.RequestError as e:
                self.stats['failed_requests'] += 1
                self.stats['consecutive_failures'] += 1
                self.anti_crawler.record_failure(self.domain)

                if attempt < max_retries:
                    backoff_delay = self.anti_crawler.calculate_backoff_delay(self.domain, attempt)
                    logger.warning(f"[{self.name}] 请求错误，等待 {backoff_delay:.2f}秒后重试: {str(e)}")
                    await asyncio.sleep(backoff_delay)
                    continue

                logger.error(f"[{self.name}] 请求错误: {url} - {str(e)}")
                break

            except Exception as e:
                self.stats['failed_requests'] += 1
                self.stats['consecutive_failures'] += 1
                self.anti_crawler.record_failure(self.domain)

                logger.error(f"[{self.name}] 未知错误: {url} - {str(e)}")
                break

        # 检查是否需要发送告警
        if self.anti_crawler.should_alert(self.domain):
            await self._send_failure_alert()

        return None

    async def _send_failure_alert(self):
        """发送失败告警"""
        try:
            failure_count = self.anti_crawler.get_failure_count(self.domain)
            alert_message = f"爬虫 {self.name} 连续失败 {failure_count} 次，域名: {self.domain}"

            # 这里可以集成邮件、钉钉、企业微信等告警渠道
            logger.error(f"[告警] {alert_message}")

            # 记录告警次数
            self.stats['alerts_sent'] += 1

            # 集成监控系统告警
            try:
                from app.services.monitoring_service import MonitoringService
                monitoring = MonitoringService()
                await monitoring.send_alert(
                    alert_type="crawler_alert",
                    message=alert_message,
                    severity="warning",
                    source=self.name
                )
            except ImportError:
                logger.warning("监控服务未配置，告警仅记录到日志")

        except Exception as e:
            logger.error(f"[{self.name}] 发送告警失败: {str(e)}")

    def test_connection(self) -> bool:
        """测试连接可用性"""
        try:
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            async def _test():
                async with self:
                    response = await self.make_request(self.base_url, max_retries=1)
                    return response is not None

            result = loop.run_until_complete(_test())
            loop.close()
            return result

        except Exception as e:
            logger.error(f"[{self.name}] 连接测试失败: {str(e)}")
            return False

    def generate_content_hash(self, content: str) -> str:
        """
        生成内容哈希值，用于去重
        
        Args:
            content: 内容文本
            
        Returns:
            SHA256哈希值
        """
        return hashlib.sha256(content.encode('utf-8')).hexdigest()
    
    def clean_text(self, text: str) -> str:
        """
        清理文本内容
        
        Args:
            text: 原始文本
            
        Returns:
            清理后的文本
        """
        if not text:
            return ""
        
        # 移除多余的空白字符
        text = ' '.join(text.split())
        
        # 移除特殊字符（保留中文、英文、数字、常用标点）
        import re
        text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s\.,;:!?()（）【】""''、。，；：！？]', '', text)
        
        return text.strip()
    
    def extract_companies_and_codes(self, text: str) -> tuple[List[str], List[str]]:
        """
        从文本中提取公司名称和股票代码
        
        Args:
            text: 文本内容
            
        Returns:
            (公司列表, 股票代码列表)
        """
        companies = []
        stock_codes = []
        
        # 简单的股票代码匹配（6位数字）
        import re
        code_pattern = r'\b\d{6}\b'
        codes = re.findall(code_pattern, text)
        stock_codes.extend(codes)
        
        # 实现公司名称提取逻辑
        # 使用预定义的公司名称词典和NLP技术
        try:
            from app.services.entity_recognition_service import EntityRecognitionService
            entity_service = EntityRecognitionService()
            extracted_companies = entity_service.extract_companies(text)
            companies.extend(extracted_companies)
        except ImportError:
            # 如果NLP服务不可用，使用基础的关键词匹配
            import re
            # 匹配常见的公司名称模式
            company_patterns = [
                r'[\u4e00-\u9fa5]+(?:股份)?(?:有限)?公司',
                r'[\u4e00-\u9fa5]+集团',
                r'[\u4e00-\u9fa5]+银行',
                r'[\u4e00-\u9fa5]+证券'
            ]
            for pattern in company_patterns:
                matches = re.findall(pattern, text)
                companies.extend(matches)
        
        return list(set(companies)), list(set(stock_codes))
    
    def calculate_importance_score(self, item: Dict[str, Any]) -> int:
        """
        计算新闻重要性评分
        
        Args:
            item: 新闻项目数据
            
        Returns:
            重要性评分 (0-100)
        """
        score = 50  # 基础分数
        
        title = item.get('title', '').lower()
        content = item.get('content', '').lower()
        
        # 关键词权重
        important_keywords = {
            '重大': 20, '重要': 15, '紧急': 15, '突发': 15,
            '财报': 10, '业绩': 10, '分红': 10, '重组': 15,
            '停牌': 10, '复牌': 8, '退市': 20, '上市': 15,
            '监管': 12, '处罚': 15, '违规': 12, '调查': 10
        }
        
        for keyword, weight in important_keywords.items():
            if keyword in title:
                score += weight
            elif keyword in content:
                score += weight // 2
        
        # 限制在0-100范围内
        return max(0, min(100, score))
    
    @abstractmethod
    async def fetch_news_list(self, start_date: Optional[datetime] = None, 
                             end_date: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """
        获取新闻列表
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            新闻项目列表
        """
        pass
    
    @abstractmethod
    async def fetch_news_detail(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取新闻详情
        
        Args:
            item: 新闻项目基本信息
            
        Returns:
            完整的新闻数据
        """
        pass
    
    async def crawl(self, start_date: Optional[datetime] = None, 
                   end_date: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """
        执行爬取任务
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            爬取的新闻数据列表
        """
        self.stats['start_time'] = datetime.now()
        logger.info(f"[{self.name}] 开始爬取任务")
        
        try:
            # 获取新闻列表
            news_list = await self.fetch_news_list(start_date, end_date)
            logger.info(f"[{self.name}] 获取到 {len(news_list)} 条新闻")
            
            # 获取详情
            detailed_news = []
            for item in news_list:
                try:
                    detail = await self.fetch_news_detail(item)
                    if detail:
                        detailed_news.append(detail)
                        self.stats['total_items'] += 1
                except Exception as e:
                    logger.error(f"[{self.name}] 获取详情失败: {str(e)}")
                    continue
            
            self.stats['end_time'] = datetime.now()
            duration = (self.stats['end_time'] - self.stats['start_time']).total_seconds()
            
            logger.info(f"[{self.name}] 爬取完成，耗时 {duration:.2f}秒，"
                       f"成功 {len(detailed_news)} 条")
            
            return detailed_news
            
        except Exception as e:
            logger.error(f"[{self.name}] 爬取任务失败: {str(e)}")
            return []
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()
        if stats['start_time'] and stats['end_time']:
            stats['duration'] = (stats['end_time'] - stats['start_time']).total_seconds()
        return stats
