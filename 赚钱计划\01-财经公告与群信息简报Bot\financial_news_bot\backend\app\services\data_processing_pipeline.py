"""
数据处理管道
基于管道模式重构数据处理逻辑，提高可维护性和扩展性
"""
import re
import jieba
import hashlib
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class ProcessingStage(Enum):
    """处理阶段枚举"""
    TEXT_CLEANING = "text_cleaning"
    ENTITY_EXTRACTION = "entity_extraction"
    SENTIMENT_ANALYSIS = "sentiment_analysis"
    IMPORTANCE_SCORING = "importance_scoring"
    CONTENT_CLASSIFICATION = "content_classification"
    DUPLICATE_DETECTION = "duplicate_detection"

@dataclass
class ProcessingResult:
    """处理结果数据结构"""
    success: bool = True
    data: Any = None
    metadata: Dict[str, Any] = None
    errors: List[str] = None
    stage: ProcessingStage = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
        if self.errors is None:
            self.errors = []

class DataProcessor(ABC):
    """数据处理器抽象基类"""
    
    @abstractmethod
    def process(self, data: Any, context: Dict[str, Any] = None) -> ProcessingResult:
        """处理数据"""
        pass
    
    @abstractmethod
    def get_stage(self) -> ProcessingStage:
        """获取处理阶段"""
        pass

class TextCleaner(DataProcessor):
    """文本清理处理器"""
    
    def __init__(self):
        # 清理规则
        self.cleaning_patterns = [
            (r'\s+', ' '),  # 多个空白字符替换为单个空格
            (r'[^\u4e00-\u9fa5a-zA-Z0-9\s\.,;:!?()（）【】""''、。，；：！？]', ''),  # 移除特殊字符
            (r'^\s+|\s+$', ''),  # 去除首尾空白
        ]
    
    def process(self, data: str, context: Dict[str, Any] = None) -> ProcessingResult:
        """清理文本内容"""
        try:
            if not isinstance(data, str):
                return ProcessingResult(
                    success=False,
                    errors=["输入数据必须是字符串类型"],
                    stage=self.get_stage()
                )
            
            cleaned_text = data
            
            # 应用清理规则
            for pattern, replacement in self.cleaning_patterns:
                cleaned_text = re.sub(pattern, replacement, cleaned_text)
            
            # 记录清理统计
            original_length = len(data)
            cleaned_length = len(cleaned_text)
            
            return ProcessingResult(
                success=True,
                data=cleaned_text,
                metadata={
                    "original_length": original_length,
                    "cleaned_length": cleaned_length,
                    "reduction_ratio": (original_length - cleaned_length) / original_length if original_length > 0 else 0
                },
                stage=self.get_stage()
            )
            
        except Exception as e:
            logger.error(f"文本清理失败: {str(e)}")
            return ProcessingResult(
                success=False,
                data=data,
                errors=[f"文本清理异常: {str(e)}"],
                stage=self.get_stage()
            )
    
    def get_stage(self) -> ProcessingStage:
        return ProcessingStage.TEXT_CLEANING

class EntityExtractor(DataProcessor):
    """实体提取处理器"""
    
    def __init__(self):
        # 初始化jieba分词
        jieba.initialize()
        
        # 公司名称后缀
        self.company_suffixes = [
            '股份有限公司', '有限公司', '集团', '控股', '科技', '实业', 
            '投资', '发展', '建设', '工业', '贸易', '电子', '信息',
            '股份', '公司'
        ]
        
        # 股票代码正则表达式
        self.stock_code_patterns = [
            r'\b[0-9]{6}\b',  # A股代码
            r'\b[0-9]{5}\b',  # 港股代码
            r'\b[A-Z]{1,4}\b'  # 美股代码
        ]
    
    def process(self, data: str, context: Dict[str, Any] = None) -> ProcessingResult:
        """提取实体信息"""
        try:
            if not isinstance(data, str):
                return ProcessingResult(
                    success=False,
                    errors=["输入数据必须是字符串类型"],
                    stage=self.get_stage()
                )
            
            entities = {
                'companies': self._extract_companies(data),
                'stock_codes': self._extract_stock_codes(data),
                'organizations': self._extract_organizations(data),
                'keywords': self._extract_keywords(data)
            }
            
            # 统计信息
            total_entities = sum(len(entity_list) for entity_list in entities.values())
            
            return ProcessingResult(
                success=True,
                data=entities,
                metadata={
                    "total_entities": total_entities,
                    "entity_types": list(entities.keys()),
                    "extraction_method": "rule_based"
                },
                stage=self.get_stage()
            )
            
        except Exception as e:
            logger.error(f"实体提取失败: {str(e)}")
            return ProcessingResult(
                success=False,
                data={},
                errors=[f"实体提取异常: {str(e)}"],
                stage=self.get_stage()
            )
    
    def _extract_companies(self, text: str) -> List[str]:
        """提取公司名称"""
        companies = []
        for suffix in self.company_suffixes:
            pattern = r'[\u4e00-\u9fa5A-Za-z0-9]+' + re.escape(suffix)
            matches = re.findall(pattern, text)
            companies.extend(matches)
        return list(set(companies))
    
    def _extract_stock_codes(self, text: str) -> List[str]:
        """提取股票代码"""
        codes = []
        for pattern in self.stock_code_patterns:
            matches = re.findall(pattern, text)
            codes.extend(matches)
        return list(set(codes))
    
    def _extract_organizations(self, text: str) -> List[str]:
        """提取机构名称"""
        org_patterns = [
            r'[\u4e00-\u9fa5]+银行',
            r'[\u4e00-\u9fa5]+证券',
            r'[\u4e00-\u9fa5]+基金',
            r'[\u4e00-\u9fa5]+保险',
            r'[\u4e00-\u9fa5]+信托'
        ]
        
        organizations = []
        for pattern in org_patterns:
            matches = re.findall(pattern, text)
            organizations.extend(matches)
        
        return list(set(organizations))
    
    def _extract_keywords(self, text: str) -> List[str]:
        """提取关键词"""
        # 使用jieba分词
        words = jieba.lcut(text)
        
        # 过滤停用词和短词
        stopwords = {'的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这'}
        keywords = [word for word in words if len(word) > 1 and word not in stopwords]
        
        # 返回前20个关键词
        return keywords[:20]
    
    def get_stage(self) -> ProcessingStage:
        return ProcessingStage.ENTITY_EXTRACTION

class SentimentAnalyzer(DataProcessor):
    """情感分析处理器"""
    
    def __init__(self):
        # 情感词典
        self.positive_words = {
            '上涨', '增长', '盈利', '利好', '收益', '成功', '优秀', '强劲', '积极', '乐观',
            '突破', '创新', '领先', '稳定', '健康', '良好', '提升', '改善', '增加', '扩大'
        }
        
        self.negative_words = {
            '下跌', '亏损', '利空', '风险', '危机', '困难', '问题', '下降', '减少', '恶化',
            '违规', '处罚', '调查', '暂停', '停牌', '退市', '破产', '债务', '损失', '警告'
        }
    
    def process(self, data: Union[str, Dict[str, str]], context: Dict[str, Any] = None) -> ProcessingResult:
        """分析情感倾向"""
        try:
            # 处理输入数据
            if isinstance(data, str):
                text = data
            elif isinstance(data, dict) and 'content' in data:
                text = data.get('title', '') + ' ' + data.get('content', '')
            else:
                return ProcessingResult(
                    success=False,
                    errors=["输入数据格式不正确"],
                    stage=self.get_stage()
                )
            
            # 分词
            words = jieba.lcut(text)
            
            # 计算情感分数
            positive_score = sum(1 for word in words if word in self.positive_words)
            negative_score = sum(1 for word in words if word in self.negative_words)
            
            # 确定情感倾向
            if positive_score > negative_score:
                sentiment = "positive"
                confidence = positive_score / (positive_score + negative_score)
            elif negative_score > positive_score:
                sentiment = "negative"
                confidence = negative_score / (positive_score + negative_score)
            else:
                sentiment = "neutral"
                confidence = 0.5
            
            # 计算情感分数 (-1到1之间)
            total_sentiment_words = positive_score + negative_score
            if total_sentiment_words > 0:
                sentiment_score = (positive_score - negative_score) / total_sentiment_words
            else:
                sentiment_score = 0.0
            
            return ProcessingResult(
                success=True,
                data={
                    "sentiment": sentiment,
                    "confidence": confidence,
                    "score": sentiment_score,
                    "positive_words_count": positive_score,
                    "negative_words_count": negative_score
                },
                metadata={
                    "analysis_method": "lexicon_based",
                    "total_words": len(words),
                    "sentiment_words": total_sentiment_words
                },
                stage=self.get_stage()
            )
            
        except Exception as e:
            logger.error(f"情感分析失败: {str(e)}")
            return ProcessingResult(
                success=False,
                data={"sentiment": "unknown", "confidence": 0.0, "score": 0.0},
                errors=[f"情感分析异常: {str(e)}"],
                stage=self.get_stage()
            )
    
    def get_stage(self) -> ProcessingStage:
        return ProcessingStage.SENTIMENT_ANALYSIS

class ImportanceScorer(DataProcessor):
    """重要性评分处理器"""
    
    def __init__(self):
        # 重要关键词及权重
        self.importance_keywords = {
            # 高重要性
            '重大资产重组': 25, '并购': 20, '收购': 18, '重大合同': 15,
            '业绩预告': 15, '业绩快报': 15, '年报': 12, '半年报': 10,
            '停牌': 15, '复牌': 12, '退市': 25, '上市': 20,
            '违规': 18, '处罚': 20, '调查': 15, '立案': 18,
            
            # 中等重要性
            '分红': 10, '派息': 8, '股权激励': 8, '增持': 8,
            '减持': 10, '回购': 8, '配股': 10, '增发': 8,
            '董事会': 5, '股东大会': 5, '高管变动': 8,
            
            # 低重要性
            '公告': 3, '提示': 2, '澄清': 5, '说明': 3
        }
    
    def process(self, data: Union[str, Dict[str, str]], context: Dict[str, Any] = None) -> ProcessingResult:
        """计算重要性评分"""
        try:
            # 处理输入数据
            if isinstance(data, str):
                title = data
                content = ""
            elif isinstance(data, dict):
                title = data.get('title', '')
                content = data.get('content', '')
            else:
                return ProcessingResult(
                    success=False,
                    errors=["输入数据格式不正确"],
                    stage=self.get_stage()
                )
            
            score = 0
            matched_keywords = []
            
            # 标题权重更高
            for keyword, weight in self.importance_keywords.items():
                if keyword in title:
                    score += weight * 1.5  # 标题中的关键词权重加倍
                    matched_keywords.append(f"{keyword}(标题)")
                elif keyword in content:
                    score += weight
                    matched_keywords.append(f"{keyword}(内容)")
            
            # 根据标题长度调整
            if len(title) > 50:
                score += 5  # 长标题通常更重要
            elif len(title) < 10:
                score -= 5  # 短标题可能不够重要
            
            # 限制在0-100范围内
            final_score = max(0, min(100, score))
            
            # 确定重要性级别
            if final_score >= 80:
                importance_level = "critical"
            elif final_score >= 60:
                importance_level = "high"
            elif final_score >= 40:
                importance_level = "medium"
            elif final_score >= 20:
                importance_level = "low"
            else:
                importance_level = "minimal"
            
            return ProcessingResult(
                success=True,
                data={
                    "score": final_score,
                    "level": importance_level,
                    "matched_keywords": matched_keywords
                },
                metadata={
                    "title_length": len(title),
                    "content_length": len(content),
                    "keyword_matches": len(matched_keywords)
                },
                stage=self.get_stage()
            )
            
        except Exception as e:
            logger.error(f"重要性评分失败: {str(e)}")
            return ProcessingResult(
                success=False,
                data={"score": 0, "level": "unknown"},
                errors=[f"重要性评分异常: {str(e)}"],
                stage=self.get_stage()
            )
    
    def get_stage(self) -> ProcessingStage:
        return ProcessingStage.IMPORTANCE_SCORING

class ContentClassifier(DataProcessor):
    """内容分类处理器"""
    
    def __init__(self):
        # 分类关键词
        self.classification_keywords = {
            "政策监管": ["政策", "监管", "规定", "法规", "通知", "意见", "办法", "条例"],
            "公司公告": ["公告", "披露", "说明", "澄清", "提示", "风险", "业绩", "财报"],
            "市场动态": ["市场", "行情", "涨跌", "交易", "成交", "指数", "板块", "热点"],
            "行业资讯": ["行业", "产业", "技术", "创新", "发展", "趋势", "前景", "分析"],
            "风险提示": ["风险", "警告", "提示", "注意", "暂停", "停牌", "调查", "处罚"]
        }
    
    def process(self, data: Union[str, Dict[str, str]], context: Dict[str, Any] = None) -> ProcessingResult:
        """内容分类"""
        try:
            # 处理输入数据
            if isinstance(data, str):
                text = data
            elif isinstance(data, dict):
                text = data.get('title', '') + ' ' + data.get('content', '')
            else:
                return ProcessingResult(
                    success=False,
                    errors=["输入数据格式不正确"],
                    stage=self.get_stage()
                )
            
            category_scores = {}
            
            # 计算每个分类的匹配分数
            for category, keywords in self.classification_keywords.items():
                score = 0
                matched_keywords = []
                
                for keyword in keywords:
                    count = text.count(keyword)
                    if count > 0:
                        score += count
                        matched_keywords.append(keyword)
                
                category_scores[category] = {
                    "score": score,
                    "matched_keywords": matched_keywords
                }
            
            # 找出最高分的分类
            best_category = max(category_scores.keys(), key=lambda k: category_scores[k]["score"])
            best_score = category_scores[best_category]["score"]
            
            # 计算置信度
            total_score = sum(cat["score"] for cat in category_scores.values())
            confidence = best_score / total_score if total_score > 0 else 0
            
            # 如果没有匹配的关键词，归为"其他"
            if best_score == 0:
                best_category = "其他"
                confidence = 0.5
            
            return ProcessingResult(
                success=True,
                data={
                    "category": best_category,
                    "confidence": confidence,
                    "all_scores": category_scores
                },
                metadata={
                    "classification_method": "keyword_matching",
                    "total_keywords_matched": sum(len(cat["matched_keywords"]) for cat in category_scores.values())
                },
                stage=self.get_stage()
            )
            
        except Exception as e:
            logger.error(f"内容分类失败: {str(e)}")
            return ProcessingResult(
                success=False,
                data={"category": "unknown", "confidence": 0.0},
                errors=[f"内容分类异常: {str(e)}"],
                stage=self.get_stage()
            )
    
    def get_stage(self) -> ProcessingStage:
        return ProcessingStage.CONTENT_CLASSIFICATION

class DuplicateDetector(DataProcessor):
    """重复检测处理器"""
    
    def __init__(self):
        self.similarity_threshold = 0.8
    
    def process(self, data: Union[str, Dict[str, str]], context: Dict[str, Any] = None) -> ProcessingResult:
        """检测重复内容"""
        try:
            # 处理输入数据
            if isinstance(data, str):
                content = data
            elif isinstance(data, dict):
                content = data.get('content', data.get('title', ''))
            else:
                return ProcessingResult(
                    success=False,
                    errors=["输入数据格式不正确"],
                    stage=self.get_stage()
                )
            
            # 生成内容哈希
            content_hash = hashlib.md5(content.encode('utf-8')).hexdigest()
            
            # 生成简化哈希（用于相似度检测）
            simplified_content = re.sub(r'\s+', '', content)  # 移除空白字符
            simplified_hash = hashlib.md5(simplified_content.encode('utf-8')).hexdigest()
            
            return ProcessingResult(
                success=True,
                data={
                    "content_hash": content_hash,
                    "simplified_hash": simplified_hash,
                    "content_length": len(content),
                    "is_duplicate": False  # 需要与历史数据比较才能确定
                },
                metadata={
                    "detection_method": "hash_based",
                    "similarity_threshold": self.similarity_threshold
                },
                stage=self.get_stage()
            )
            
        except Exception as e:
            logger.error(f"重复检测失败: {str(e)}")
            return ProcessingResult(
                success=False,
                data={"content_hash": "", "is_duplicate": False},
                errors=[f"重复检测异常: {str(e)}"],
                stage=self.get_stage()
            )
    
    def get_stage(self) -> ProcessingStage:
        return ProcessingStage.DUPLICATE_DETECTION

class DataProcessingPipeline:
    """数据处理管道 - 协调各个处理器的执行"""

    def __init__(self):
        # 初始化所有处理器
        self.processors = {
            ProcessingStage.TEXT_CLEANING: TextCleaner(),
            ProcessingStage.ENTITY_EXTRACTION: EntityExtractor(),
            ProcessingStage.SENTIMENT_ANALYSIS: SentimentAnalyzer(),
            ProcessingStage.IMPORTANCE_SCORING: ImportanceScorer(),
            ProcessingStage.CONTENT_CLASSIFICATION: ContentClassifier(),
            ProcessingStage.DUPLICATE_DETECTION: DuplicateDetector()
        }

        # 定义默认处理流程
        self.default_pipeline = [
            ProcessingStage.TEXT_CLEANING,
            ProcessingStage.ENTITY_EXTRACTION,
            ProcessingStage.SENTIMENT_ANALYSIS,
            ProcessingStage.IMPORTANCE_SCORING,
            ProcessingStage.CONTENT_CLASSIFICATION,
            ProcessingStage.DUPLICATE_DETECTION
        ]

        logger.info("数据处理管道初始化完成")

    def process_text(self, text: str, stages: List[ProcessingStage] = None) -> Dict[str, Any]:
        """
        处理单个文本

        Args:
            text: 要处理的文本
            stages: 要执行的处理阶段，如果为None则使用默认流程

        Returns:
            处理结果字典
        """
        if stages is None:
            stages = self.default_pipeline

        results = {
            "input": text,
            "stages": {},
            "success": True,
            "errors": [],
            "metadata": {
                "pipeline_start": datetime.now().isoformat(),
                "stages_executed": []
            }
        }

        current_data = text

        for stage in stages:
            try:
                processor = self.processors[stage]
                result = processor.process(current_data)

                results["stages"][stage.value] = {
                    "success": result.success,
                    "data": result.data,
                    "metadata": result.metadata,
                    "errors": result.errors
                }

                results["metadata"]["stages_executed"].append(stage.value)

                if not result.success:
                    results["success"] = False
                    results["errors"].extend(result.errors)
                    logger.warning(f"处理阶段 {stage.value} 失败: {result.errors}")

                # 某些阶段的输出可以作为下一阶段的输入
                if stage == ProcessingStage.TEXT_CLEANING and result.success:
                    current_data = result.data

            except Exception as e:
                error_msg = f"处理阶段 {stage.value} 异常: {str(e)}"
                results["errors"].append(error_msg)
                results["success"] = False
                logger.error(error_msg)

        results["metadata"]["pipeline_end"] = datetime.now().isoformat()
        return results

    def process_news_article(self, title: str, content: str, stages: List[ProcessingStage] = None) -> Dict[str, Any]:
        """
        处理新闻文章

        Args:
            title: 文章标题
            content: 文章内容
            stages: 要执行的处理阶段

        Returns:
            处理结果字典
        """
        if stages is None:
            stages = self.default_pipeline

        results = {
            "input": {"title": title, "content": content},
            "stages": {},
            "success": True,
            "errors": [],
            "metadata": {
                "pipeline_start": datetime.now().isoformat(),
                "stages_executed": []
            }
        }

        # 合并标题和内容用于某些处理
        full_text = f"{title} {content}"
        article_data = {"title": title, "content": content}

        for stage in stages:
            try:
                processor = self.processors[stage]

                # 根据处理器类型选择输入数据
                if stage in [ProcessingStage.TEXT_CLEANING, ProcessingStage.ENTITY_EXTRACTION]:
                    input_data = full_text
                else:
                    input_data = article_data

                result = processor.process(input_data)

                results["stages"][stage.value] = {
                    "success": result.success,
                    "data": result.data,
                    "metadata": result.metadata,
                    "errors": result.errors
                }

                results["metadata"]["stages_executed"].append(stage.value)

                if not result.success:
                    results["success"] = False
                    results["errors"].extend(result.errors)
                    logger.warning(f"处理阶段 {stage.value} 失败: {result.errors}")

            except Exception as e:
                error_msg = f"处理阶段 {stage.value} 异常: {str(e)}"
                results["errors"].append(error_msg)
                results["success"] = False
                logger.error(error_msg)

        results["metadata"]["pipeline_end"] = datetime.now().isoformat()
        return results

    def process_batch(self, items: List[Union[str, Dict[str, str]]], stages: List[ProcessingStage] = None) -> List[Dict[str, Any]]:
        """
        批量处理

        Args:
            items: 要处理的项目列表
            stages: 要执行的处理阶段

        Returns:
            处理结果列表
        """
        results = []

        for i, item in enumerate(items):
            try:
                if isinstance(item, str):
                    result = self.process_text(item, stages)
                elif isinstance(item, dict) and 'title' in item and 'content' in item:
                    result = self.process_news_article(item['title'], item['content'], stages)
                else:
                    result = {
                        "input": item,
                        "success": False,
                        "errors": ["不支持的输入格式"],
                        "stages": {}
                    }

                result["batch_index"] = i
                results.append(result)

            except Exception as e:
                logger.error(f"批量处理第 {i} 项失败: {str(e)}")
                results.append({
                    "input": item,
                    "batch_index": i,
                    "success": False,
                    "errors": [f"批量处理异常: {str(e)}"],
                    "stages": {}
                })

        return results

    def get_available_stages(self) -> List[str]:
        """获取可用的处理阶段列表"""
        return [stage.value for stage in ProcessingStage]

    def get_processor_info(self, stage: ProcessingStage) -> Dict[str, Any]:
        """获取处理器信息"""
        if stage not in self.processors:
            return {"error": "处理器不存在"}

        processor = self.processors[stage]
        return {
            "stage": stage.value,
            "class_name": processor.__class__.__name__,
            "description": processor.__class__.__doc__ or "无描述"
        }

# 全局数据处理管道实例
data_pipeline = DataProcessingPipeline()
