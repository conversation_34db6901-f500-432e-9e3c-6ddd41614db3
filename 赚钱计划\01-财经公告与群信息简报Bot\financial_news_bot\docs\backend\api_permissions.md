# API权限控制说明文档

## 概述

本文档详细说明了财经新闻Bot系统中所有API端点的权限要求、访问控制和使用限制。

## 权限验证流程

```mermaid
graph TD
    A[API请求] --> B{是否公开端点?}
    B -->|是| C[直接访问]
    B -->|否| D{Token验证}
    D -->|失败| E[401 未认证]
    D -->|成功| F{权限检查}
    F -->|失败| G[403 权限不足]
    F -->|成功| H[执行请求]
    H --> I[记录审计日志]
```

## API端点权限矩阵

### 用户管理API (`/users`)

| 端点 | 方法 | 权限要求 | 角色限制 | 描述 |
|------|------|----------|----------|------|
| `/users/register` | POST | 无 | 公开 | 用户注册 |
| `/users/login` | POST | 无 | 公开 | 用户登录 |
| `/users/me` | GET | 认证用户 | 所有 | 获取当前用户信息 |
| `/users/me` | PUT | 认证用户 | 所有 | 更新当前用户信息 |
| `/users/permissions` | GET | 认证用户 | 所有 | 获取当前用户权限 |
| `/users/` | GET | `user_management` | ADMIN | 获取用户列表 |
| `/users/{id}` | GET | 认证用户 | 所有 | 获取用户信息 |
| `/users/{id}/role` | PUT | `user_management` | ADMIN | 更新用户角色 |
| `/users/{id}` | DELETE | ADMIN权限 | ADMIN | 删除用户 |

### 新闻管理API (`/news`)

| 端点 | 方法 | 权限要求 | 角色限制 | 描述 |
|------|------|----------|----------|------|
| `/news/public/latest` | GET | 无 | 公开 | 获取最新公开新闻 |
| `/news/` | GET | `read_news` | 所有 | 获取新闻列表 |
| `/news/{id}` | GET | `read_news` | 所有 | 获取新闻详情 |
| `/news/search/` | GET | `read_news` | 所有 | 搜索新闻 |
| `/news/export/csv` | GET | `export_data` | PRO+ | 导出新闻数据 |
| `/news/analytics/stats` | GET | `view_analytics` | PRO+ | 获取新闻统计 |
| `/news/` | POST | ADMIN权限 | ADMIN | 创建新闻 |
| `/news/{id}` | PUT | ADMIN权限 | ADMIN | 更新新闻 |
| `/news/{id}` | DELETE | ADMIN权限 | ADMIN | 删除新闻 |

### 订阅管理API (`/subscriptions`)

| 端点 | 方法 | 权限要求 | 角色限制 | 描述 |
|------|------|----------|----------|------|
| `/subscriptions/` | GET | `basic_subscription` | 所有 | 获取用户订阅列表 |
| `/subscriptions/{id}` | GET | `basic_subscription` | 所有 | 获取订阅详情 |
| `/subscriptions/basic` | POST | `basic_subscription` | 所有 | 创建基础订阅 |
| `/subscriptions/advanced` | POST | `advanced_subscription` | PRO+ | 创建高级订阅 |
| `/subscriptions/{id}` | PUT | `basic_subscription` | 所有 | 更新订阅 |
| `/subscriptions/{id}` | DELETE | `basic_subscription` | 所有 | 删除订阅 |
| `/subscriptions/bulk-create` | POST | `bulk_operations` | ENTERPRISE+ | 批量创建订阅 |
| `/subscriptions/stats/summary` | GET | `basic_subscription` | 所有 | 获取订阅统计 |
| `/subscriptions/admin/all` | GET | ADMIN权限 | ADMIN | 获取所有订阅 |
| `/subscriptions/admin/stats` | GET | ADMIN权限 | ADMIN | 获取管理员统计 |

### 推送管理API (`/push`)

| 端点 | 方法 | 权限要求 | 角色限制 | 描述 |
|------|------|----------|----------|------|
| `/push/send` | POST | `basic_subscription` | 所有 | 发送推送消息 |
| `/push/logs` | GET | `basic_subscription` | 所有 | 获取推送日志 |
| `/push/quota` | GET | `basic_subscription` | 所有 | 获取推送配额 |
| `/push/templates` | GET | `advanced_subscription` | PRO+ | 获取推送模板 |
| `/push/templates` | POST | `advanced_subscription` | PRO+ | 创建推送模板 |
| `/push/templates/{id}` | PUT | `advanced_subscription` | PRO+ | 更新推送模板 |
| `/push/templates/{id}` | DELETE | `advanced_subscription` | PRO+ | 删除推送模板 |
| `/push/stats` | GET | `basic_subscription` | 所有 | 获取推送统计 |
| `/push/admin/logs` | GET | ADMIN权限 | ADMIN | 获取所有推送日志 |
| `/push/admin/stats` | GET | ADMIN权限 | ADMIN | 获取管理员推送统计 |
| `/push/admin/broadcast` | POST | ADMIN权限 | ADMIN | 广播消息 |
| `/push/admin/config` | PUT | `system_config` | ADMIN | 更新推送配置 |

### 管理员API (`/admin`)

| 端点 | 方法 | 权限要求 | 角色限制 | 描述 |
|------|------|----------|----------|------|
| `/admin/stats/system` | GET | ADMIN权限 | ADMIN | 获取系统统计 |
| `/admin/users/management` | GET | `user_management` | ADMIN | 获取用户管理列表 |
| `/admin/users/{id}/role` | PUT | `user_management` | ADMIN | 更新用户角色 |
| `/admin/users/{id}/deactivate` | POST | `user_management` | ADMIN | 停用用户 |
| `/admin/users/{id}/activate` | POST | `user_management` | ADMIN | 激活用户 |
| `/admin/permissions/matrix` | GET | ADMIN权限 | ADMIN | 获取权限矩阵 |
| `/admin/audit/permissions` | GET | `monitoring_access` | ADMIN | 获取权限审计日志 |
| `/admin/config/system` | GET | `system_config` | ADMIN | 获取系统配置 |
| `/admin/config/system` | PUT | `system_config` | ADMIN | 更新系统配置 |
| `/admin/health/detailed` | GET | `monitoring_access` | ADMIN | 获取详细健康检查 |
| `/admin/maintenance/mode` | POST | `system_config` | ADMIN | 切换维护模式 |

## 权限级别说明

### 基础权限 (FREE用户)

**可访问的功能：**
- 阅读新闻内容
- 创建基础订阅（限制：最多3个关键词，5个公司）
- 查看个人资料
- 基础推送功能（限制：每日10条）

**限制：**
- 无法导出数据
- 无法查看分析统计
- 无法使用高级订阅功能
- 推送配额有限

### 专业权限 (PRO用户)

**额外功能：**
- 高级订阅（无限关键词和公司）
- 数据导出功能
- 查看分析统计
- 自定义提醒
- 推送模板管理
- 更高的推送配额（每日100条）

### 企业权限 (ENTERPRISE用户)

**额外功能：**
- API访问权限
- 批量操作功能
- 团队管理
- 更高的推送配额（每日1000条）
- 优先技术支持

### 管理员权限 (ADMIN用户)

**完整功能：**
- 所有用户功能
- 用户管理和角色变更
- 系统配置管理
- 监控和审计日志访问
- 维护模式控制
- 系统统计查看
- 无限推送配额

## 使用配额和限制

### 推送配额

| 角色 | 每日配额 | 每小时配额 | 模板数量 |
|------|----------|------------|----------|
| FREE | 10条 | 2条 | 0个 |
| PRO | 100条 | 20条 | 5个 |
| ENTERPRISE | 1000条 | 100条 | 20个 |
| ADMIN | 无限 | 无限 | 无限 |

### 订阅限制

| 角色 | 关键词数量 | 公司数量 | 订阅总数 |
|------|------------|----------|----------|
| FREE | 3个 | 5个 | 10个 |
| PRO | 无限 | 无限 | 50个 |
| ENTERPRISE | 无限 | 无限 | 200个 |
| ADMIN | 无限 | 无限 | 无限 |

### 数据导出限制

| 角色 | 每日导出次数 | 单次记录数 | 支持格式 |
|------|--------------|------------|----------|
| FREE | 0次 | - | - |
| PRO | 5次 | 10,000条 | CSV |
| ENTERPRISE | 20次 | 100,000条 | CSV, JSON, Excel |
| ADMIN | 无限 | 无限 | 所有格式 |

## 错误响应格式

### 认证错误 (401)

```json
{
  "error": {
    "code": "AUTHENTICATION_REQUIRED",
    "message": "Authentication required",
    "type": "AuthenticationRequiredError"
  }
}
```

### 权限不足错误 (403)

```json
{
  "error": {
    "code": "PERMISSION_DENIED",
    "message": "Permission 'export_data' required",
    "type": "InsufficientPermissionError",
    "required_permission": "export_data",
    "user_role": "FREE"
  }
}
```

### 配额超限错误 (429)

```json
{
  "error": {
    "code": "QUOTA_EXCEEDED",
    "message": "Daily push quota exceeded",
    "type": "QuotaExceededError",
    "quota_type": "daily_push",
    "current_usage": 10,
    "quota_limit": 10,
    "reset_time": "2025-08-19T00:00:00Z"
  }
}
```

## 权限升级路径

### 从FREE升级到PRO

**解锁功能：**
- 高级订阅功能
- 数据导出（CSV格式）
- 分析统计查看
- 推送配额提升至100条/日
- 推送模板管理（5个）

### 从PRO升级到ENTERPRISE

**解锁功能：**
- API访问权限
- 批量操作功能
- 团队管理功能
- 推送配额提升至1000条/日
- 推送模板增加至20个
- 多格式数据导出

## 安全注意事项

### Token安全

- 使用HTTPS传输
- Token有效期24小时
- 支持Token刷新机制
- 异常登录检测

### 权限验证

- 每个请求都进行权限验证
- 权限检查结果缓存1小时
- 角色变更立即生效
- 完整的审计日志记录

### 防护措施

- 请求频率限制
- 异常访问检测
- 自动封禁机制
- 实时监控告警

## 开发者指南

### 权限检查示例

```python
# 检查单个权限
from app.dependencies.permissions import require_export_data

@router.get("/export")
async def export_data(
    current_user: User = Depends(require_export_data())
):
    # 自动检查export_data权限
    pass

# 检查多个权限
from app.dependencies.permissions import require_permissions

@router.get("/advanced-feature")
async def advanced_feature(
    current_user: User = Depends(require_permissions(["export_data", "view_analytics"]))
):
    # 需要同时具备两个权限
    pass
```

### 自定义权限检查

```python
from app.utils.permissions import has_permission

def check_resource_access(user: User, resource_id: int) -> bool:
    # 基础权限检查
    if not has_permission(user.role, "read_news"):
        return False
    
    # 资源所有权检查
    if resource.user_id != user.id and user.role != UserRole.ADMIN:
        return False
    
    return True
```

## 测试指南

### 权限测试用例

```python
def test_free_user_permissions():
    """测试免费用户权限"""
    free_user = create_user(role=UserRole.FREE)
    
    # 应该可以访问
    assert has_permission(free_user.role, "read_news")
    assert has_permission(free_user.role, "basic_subscription")
    
    # 应该无法访问
    assert not has_permission(free_user.role, "export_data")
    assert not has_permission(free_user.role, "user_management")

def test_api_permission_enforcement():
    """测试API权限强制执行"""
    client = TestClient(app)
    free_user_token = create_token(role=UserRole.FREE)
    
    # 应该被拒绝
    response = client.get(
        "/news/export/csv",
        headers={"Authorization": f"Bearer {free_user_token}"}
    )
    assert response.status_code == 403
```

## 常见问题

### Q: 如何检查用户是否有特定权限？

A: 使用 `has_permission(user.role, "permission_name")` 函数。

### Q: 如何为API端点添加权限控制？

A: 使用依赖注入：`current_user: User = Depends(require_permission("permission_name"))`

### Q: 权限变更多久生效？

A: 立即生效。权限缓存会在角色变更时自动清除。

### Q: 如何查看权限审计日志？

A: 管理员可以通过 `/admin/audit/permissions` 端点查看完整的审计日志。

### Q: 如何处理权限不足的情况？

A: 系统会返回403错误，包含详细的权限要求信息，前端应引导用户升级账户。
