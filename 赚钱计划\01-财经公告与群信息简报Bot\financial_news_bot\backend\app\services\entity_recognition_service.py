"""
实体识别服务
使用NER模型和金融词典识别各类实体

TODO: 此服务已实现但未在路由中使用，为智能内容分析功能预留
      可考虑在新闻分析、关键词提取、智能标签等功能中使用
      依赖ai_service，需要GLM API支持
"""
import asyncio
import logging
import re
from typing import Dict, List, Optional, Any, Set, Tuple
from dataclasses import dataclass
from datetime import datetime
from enum import Enum

from app.config import settings
from app.services.ai_service import get_glm_service

logger = logging.getLogger(__name__)


class EntityType(Enum):
    """实体类型枚举"""
    COMPANY = "company"           # 公司名称
    STOCK_CODE = "stock_code"     # 股票代码
    PERSON = "person"             # 人名
    LOCATION = "location"         # 地名
    ORGANIZATION = "organization" # 机构名
    AMOUNT = "amount"             # 金额
    PERCENTAGE = "percentage"     # 百分比
    DATE = "date"                 # 日期
    FINANCIAL_TERM = "financial_term"  # 金融术语
    INDUSTRY = "industry"         # 行业


@dataclass
class Entity:
    """实体数据类"""
    text: str                    # 实体文本
    entity_type: EntityType      # 实体类型
    start_pos: int              # 开始位置
    end_pos: int                # 结束位置
    confidence: float           # 置信度
    normalized_form: Optional[str] = None  # 标准化形式
    metadata: Optional[Dict[str, Any]] = None  # 元数据


@dataclass
class EntityRecognitionResult:
    """实体识别结果"""
    entities: List[Entity]
    accuracy_score: float
    processing_time: float
    total_entities: int
    entities_by_type: Dict[EntityType, int]


class EntityRecognitionService:
    """实体识别服务"""
    
    def __init__(self):
        """初始化实体识别服务"""
        self.glm_service = get_glm_service()
        self.entity_dictionaries = self._load_entity_dictionaries()
        self.pattern_rules = self._load_pattern_rules()
        self.normalization_rules = self._load_normalization_rules()
        
        logger.info("实体识别服务初始化完成")
    
    def _load_entity_dictionaries(self) -> Dict[EntityType, Set[str]]:
        """加载实体词典"""
        return {
            EntityType.COMPANY: {
                # 主要上市公司
                "中国平安", "贵州茅台", "腾讯控股", "阿里巴巴", "美团",
                "比亚迪", "宁德时代", "隆基绿能", "中国石油", "中国石化",
                "工商银行", "建设银行", "农业银行", "中国银行", "交通银行",
                "招商银行", "民生银行", "浦发银行", "中信银行", "光大银行",
                "五粮液", "泸州老窖", "剑南春", "水井坊", "舍得酒业",
                "万科A", "保利发展", "中国恒大", "碧桂园", "融创中国",
                "中国移动", "中国联通", "中国电信", "华为技术", "小米集团"
            },
            
            EntityType.STOCK_CODE: {
                # 主要股票代码
                "000001", "000002", "000858", "600000", "600036", "600519",
                "600887", "002415", "300750", "601318", "600276", "000568",
                "002304", "600809", "000596", "002142", "600031", "601166",
                "000725", "002594", "600104", "000063", "002230", "600585"
            },
            
            EntityType.FINANCIAL_TERM: {
                # 金融术语
                "营业收入", "净利润", "毛利率", "净资产收益率", "市盈率",
                "市净率", "资产负债率", "现金流", "分红", "配股", "增发",
                "重组", "并购", "IPO", "定增", "回购", "减持", "质押",
                "业绩预告", "年报", "中报", "季报", "财报", "审计报告"
            },
            
            EntityType.INDUSTRY: {
                # 行业分类
                "银行", "保险", "证券", "基金", "信托", "租赁",
                "房地产", "建筑", "钢铁", "煤炭", "石油", "化工",
                "电力", "公用事业", "交通运输", "仓储物流",
                "食品饮料", "纺织服装", "医药生物", "电子",
                "计算机", "通信", "传媒", "汽车", "家电", "轻工制造"
            },
            
            EntityType.ORGANIZATION: {
                # 监管机构和重要组织
                "中国证监会", "证监会", "银保监会", "央行", "人民银行",
                "上交所", "深交所", "北交所", "中证协", "基金业协会",
                "国务院", "财政部", "发改委", "工信部", "商务部",
                "统计局", "审计署", "国资委", "银行间市场"
            }
        }
    
    def _load_pattern_rules(self) -> Dict[EntityType, List[str]]:
        """加载正则表达式规则"""
        return {
            EntityType.STOCK_CODE: [
                r'\b\d{6}\b',  # 6位数字股票代码
                r'[A-Z]{2,4}\d{4}',  # 港股代码格式
                r'[A-Z]+\.\w{2,3}'   # 美股代码格式
            ],
            
            EntityType.AMOUNT: [
                r'(\d+(?:\.\d+)?)\s*([万亿千百十]?元|万元|亿元|千万元)',
                r'(\d+(?:\.\d+)?)\s*(万|亿|千万|百万)',
                r'(\d{1,3}(?:,\d{3})*(?:\.\d+)?)\s*元',
                r'(\d+(?:\.\d+)?)\s*(美元|港元|欧元|英镑)'
            ],
            
            EntityType.PERCENTAGE: [
                r'(\d+(?:\.\d+)?)\s*%',
                r'(\d+(?:\.\d+)?)\s*个百分点',
                r'增长\s*(\d+(?:\.\d+)?)\s*%',
                r'下降\s*(\d+(?:\.\d+)?)\s*%'
            ],
            
            EntityType.DATE: [
                r'\d{4}年\d{1,2}月\d{1,2}日',
                r'\d{4}-\d{1,2}-\d{1,2}',
                r'\d{1,2}月\d{1,2}日',
                r'\d{4}年第[一二三四]季度',
                r'\d{4}年[上下]半年'
            ],
            
            EntityType.PERSON: [
                r'董事长\s*([^\s，。；！？]{2,4})',
                r'总经理\s*([^\s，。；！？]{2,4})',
                r'CEO\s*([^\s，。；！？]{2,4})',
                r'CFO\s*([^\s，。；！？]{2,4})',
                r'([^\s，。；！？]{2,4})\s*表示',
                r'([^\s，。；！？]{2,4})\s*认为'
            ]
        }
    
    def _load_normalization_rules(self) -> Dict[str, str]:
        """加载标准化规则"""
        return {
            # 公司名称标准化
            "中国平安保险": "中国平安",
            "平安保险": "中国平安",
            "茅台": "贵州茅台",
            "五粮液集团": "五粮液",
            
            # 机构名称标准化
            "证监会": "中国证监会",
            "银保监会": "中国银保监会",
            "央行": "中国人民银行",
            "人行": "中国人民银行",
            
            # 金融术语标准化
            "ROE": "净资产收益率",
            "PE": "市盈率",
            "PB": "市净率",
            "IPO": "首次公开发行"
        }
    
    async def recognize_entities(self, text: str) -> EntityRecognitionResult:
        """
        识别文本中的实体
        
        Args:
            text: 输入文本
            
        Returns:
            实体识别结果
        """
        start_time = datetime.now()
        
        try:
            # 并行执行多种识别方法
            tasks = [
                self._recognize_by_dictionary(text),
                self._recognize_by_patterns(text),
                self._recognize_by_ai(text)
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            dict_entities = results[0] if not isinstance(results[0], Exception) else []
            pattern_entities = results[1] if not isinstance(results[1], Exception) else []
            ai_entities = results[2] if not isinstance(results[2], Exception) else []
            
            # 合并和去重实体
            all_entities = dict_entities + pattern_entities + ai_entities
            merged_entities = self._merge_entities(all_entities)
            
            # 标准化实体
            normalized_entities = self._normalize_entities(merged_entities)
            
            # 计算统计信息
            processing_time = (datetime.now() - start_time).total_seconds()
            entities_by_type = self._count_entities_by_type(normalized_entities)
            accuracy_score = self._calculate_accuracy_score(normalized_entities)
            
            return EntityRecognitionResult(
                entities=normalized_entities,
                accuracy_score=accuracy_score,
                processing_time=processing_time,
                total_entities=len(normalized_entities),
                entities_by_type=entities_by_type
            )
            
        except Exception as e:
            logger.error(f"实体识别失败: {str(e)}")
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return EntityRecognitionResult(
                entities=[],
                accuracy_score=0.0,
                processing_time=processing_time,
                total_entities=0,
                entities_by_type={}
            )
    
    async def _recognize_by_dictionary(self, text: str) -> List[Entity]:
        """基于词典的实体识别"""
        entities = []
        
        for entity_type, dictionary in self.entity_dictionaries.items():
            for term in dictionary:
                start = 0
                while True:
                    pos = text.find(term, start)
                    if pos == -1:
                        break
                    
                    entities.append(Entity(
                        text=term,
                        entity_type=entity_type,
                        start_pos=pos,
                        end_pos=pos + len(term),
                        confidence=0.95  # 词典匹配置信度较高
                    ))
                    start = pos + 1
        
        return entities
    
    async def _recognize_by_patterns(self, text: str) -> List[Entity]:
        """基于正则表达式的实体识别"""
        entities = []
        
        for entity_type, patterns in self.pattern_rules.items():
            for pattern in patterns:
                matches = re.finditer(pattern, text)
                for match in matches:
                    entities.append(Entity(
                        text=match.group(),
                        entity_type=entity_type,
                        start_pos=match.start(),
                        end_pos=match.end(),
                        confidence=0.85  # 模式匹配置信度中等
                    ))
        
        return entities
    
    async def _recognize_by_ai(self, text: str) -> List[Entity]:
        """基于AI的实体识别"""
        try:
            prompt = f"""
请识别以下财经新闻文本中的实体，并按JSON格式返回：

文本：{text[:800]}

请识别以下类型的实体：
- company: 公司名称
- stock_code: 股票代码
- person: 人名
- location: 地名
- organization: 机构名
- amount: 金额
- percentage: 百分比
- date: 日期
- financial_term: 金融术语
- industry: 行业

返回格式：
[
    {{"text": "实体文本", "type": "实体类型", "confidence": 0.9}},
    ...
]
"""
            
            response = await self.glm_service._call_api([
                {"role": "user", "content": prompt}
            ], temperature=0.1)
            
            if response:
                import json
                import re
                
                # 提取JSON部分
                json_match = re.search(r'\[.*\]', response, re.DOTALL)
                if json_match:
                    entities_data = json.loads(json_match.group(0))
                    
                    entities = []
                    for entity_data in entities_data:
                        entity_text = entity_data.get("text", "")
                        entity_type_str = entity_data.get("type", "")
                        confidence = entity_data.get("confidence", 0.7)
                        
                        # 查找实体在文本中的位置
                        pos = text.find(entity_text)
                        if pos != -1:
                            try:
                                entity_type = EntityType(entity_type_str)
                                entities.append(Entity(
                                    text=entity_text,
                                    entity_type=entity_type,
                                    start_pos=pos,
                                    end_pos=pos + len(entity_text),
                                    confidence=confidence
                                ))
                            except ValueError:
                                # 未知实体类型，跳过
                                continue
                    
                    return entities
            
            return []
            
        except Exception as e:
            logger.error(f"AI实体识别失败: {str(e)}")
            return []

    def _merge_entities(self, entities: List[Entity]) -> List[Entity]:
        """合并重复的实体"""
        if not entities:
            return []

        # 按位置排序
        entities.sort(key=lambda x: (x.start_pos, x.end_pos))

        merged = []
        for entity in entities:
            # 检查是否与已有实体重叠
            overlapped = False
            for existing in merged:
                if (entity.start_pos < existing.end_pos and
                    entity.end_pos > existing.start_pos):
                    # 有重叠，选择置信度更高的
                    if entity.confidence > existing.confidence:
                        merged.remove(existing)
                        merged.append(entity)
                    overlapped = True
                    break

            if not overlapped:
                merged.append(entity)

        return merged

    def _normalize_entities(self, entities: List[Entity]) -> List[Entity]:
        """标准化实体"""
        normalized = []

        for entity in entities:
            normalized_form = self.normalization_rules.get(entity.text, entity.text)

            normalized_entity = Entity(
                text=entity.text,
                entity_type=entity.entity_type,
                start_pos=entity.start_pos,
                end_pos=entity.end_pos,
                confidence=entity.confidence,
                normalized_form=normalized_form,
                metadata=entity.metadata
            )

            normalized.append(normalized_entity)

        return normalized

    def _count_entities_by_type(self, entities: List[Entity]) -> Dict[EntityType, int]:
        """按类型统计实体数量"""
        counts = {}
        for entity in entities:
            counts[entity.entity_type] = counts.get(entity.entity_type, 0) + 1
        return counts

    def _calculate_accuracy_score(self, entities: List[Entity]) -> float:
        """计算准确率评分"""
        if not entities:
            return 0.0

        # 基于置信度计算平均准确率
        total_confidence = sum(entity.confidence for entity in entities)
        return total_confidence / len(entities)

    async def extract_key_entities(self, text: str,
                                  entity_types: List[EntityType] = None) -> Dict[EntityType, List[Entity]]:
        """
        提取关键实体

        Args:
            text: 输入文本
            entity_types: 指定要提取的实体类型

        Returns:
            按类型分组的实体字典
        """
        result = await self.recognize_entities(text)

        if entity_types:
            filtered_entities = [
                entity for entity in result.entities
                if entity.entity_type in entity_types
            ]
        else:
            filtered_entities = result.entities

        # 按类型分组
        grouped_entities = {}
        for entity in filtered_entities:
            if entity.entity_type not in grouped_entities:
                grouped_entities[entity.entity_type] = []
            grouped_entities[entity.entity_type].append(entity)

        return grouped_entities


# 全局实体识别服务实例
entity_recognition_service = None

def get_entity_recognition_service() -> EntityRecognitionService:
    """获取实体识别服务实例"""
    global entity_recognition_service
    if entity_recognition_service is None:
        entity_recognition_service = EntityRecognitionService()
    return entity_recognition_service
