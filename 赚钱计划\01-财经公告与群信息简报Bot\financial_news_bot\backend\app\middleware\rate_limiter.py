"""
速率限制中间件 - 混合模式
实现基于IP、用户、API端点的多维度速率限制
IP管理功能集成微服务，速率限制保留本地处理
"""
import time
import logging
from typing import Dict, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse
import asyncio

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class RateLimitRule:
    """速率限制规则"""
    requests: int  # 允许的请求数
    window: int    # 时间窗口（秒）
    burst: int = 0 # 突发请求数
    description: str = ""


@dataclass
class RequestRecord:
    """请求记录"""
    timestamp: float
    ip_address: str
    user_id: Optional[str] = None
    endpoint: str = ""
    user_agent: str = ""


class RateLimiter:
    """速率限制器"""
    
    def __init__(self):
        # 导入微服务适配器
        from app.adapters.microservice_adapter import microservice_manager
        self.microservice_manager = microservice_manager

        # 请求记录存储
        self.request_records: Dict[str, list] = {}
        
        # 默认速率限制规则
        self.default_rules = {
            # 全局限制
            "global": RateLimitRule(
                requests=1000, 
                window=3600, 
                description="全局每小时1000次请求"
            ),
            
            # IP限制
            "ip": RateLimitRule(
                requests=100, 
                window=3600, 
                burst=10,
                description="单IP每小时100次请求，突发10次"
            ),
            
            # 用户限制
            "user": RateLimitRule(
                requests=500, 
                window=3600, 
                description="单用户每小时500次请求"
            ),
            
            # API端点限制
            "api_login": RateLimitRule(
                requests=5, 
                window=300, 
                description="登录接口每5分钟5次请求"
            ),
            
            "api_register": RateLimitRule(
                requests=3, 
                window=3600, 
                description="注册接口每小时3次请求"
            ),
            
            "api_crawler": RateLimitRule(
                requests=10, 
                window=60, 
                description="爬虫接口每分钟10次请求"
            ),
            
            "api_search": RateLimitRule(
                requests=30, 
                window=60, 
                description="搜索接口每分钟30次请求"
            )
        }
        
        # 端点映射
        self.endpoint_mapping = {
            "/api/v1/auth/login": "api_login",
            "/api/v1/auth/register": "api_register",
            "/api/v1/crawler/": "api_crawler",
            "/api/v1/news/search": "api_search"
        }
        
        # 白名单IP
        self.whitelist_ips = set([
            "127.0.0.1",
            "::1",
            "localhost"
        ])
        
        # 黑名单IP
        self.blacklist_ips = set()
        
        # 清理任务
        self._last_cleanup = time.time()
        self._cleanup_interval = 300  # 5分钟清理一次
    
    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        # 检查代理头
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        return request.client.host if hasattr(request, 'client') else "unknown"
    
    def _get_user_id(self, request: Request) -> Optional[str]:
        """从请求中获取用户ID"""
        # 从JWT令牌中提取用户ID
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            try:
                from app.utils.security import verify_token
                token = auth_header.split(" ")[1]
                payload = verify_token(token)
                return payload.get("user_id") if payload else None
            except Exception:
                return None
        return None
    
    def _get_endpoint_key(self, path: str) -> str:
        """获取端点对应的限制规则键"""
        for endpoint, key in self.endpoint_mapping.items():
            if path.startswith(endpoint):
                return key
        return "default"
    
    def _cleanup_old_records(self):
        """清理过期的请求记录"""
        current_time = time.time()
        
        # 检查是否需要清理
        if current_time - self._last_cleanup < self._cleanup_interval:
            return
        
        cutoff_time = current_time - 3600  # 保留1小时内的记录
        
        for key in list(self.request_records.keys()):
            self.request_records[key] = [
                record for record in self.request_records[key]
                if record.timestamp > cutoff_time
            ]
            
            # 删除空的记录
            if not self.request_records[key]:
                del self.request_records[key]
        
        self._last_cleanup = current_time
        logger.debug("Cleaned up old rate limit records")
    
    def _check_rate_limit(self, key: str, rule: RateLimitRule, current_time: float) -> tuple[bool, dict]:
        """检查速率限制"""
        if key not in self.request_records:
            return True, {"remaining": rule.requests - 1, "reset_time": current_time + rule.window}
        
        # 过滤时间窗口内的请求
        window_start = current_time - rule.window
        recent_requests = [
            record for record in self.request_records[key]
            if record.timestamp > window_start
        ]
        
        # 检查是否超过限制
        if len(recent_requests) >= rule.requests:
            # 计算重置时间
            oldest_request = min(recent_requests, key=lambda x: x.timestamp)
            reset_time = oldest_request.timestamp + rule.window
            
            return False, {
                "remaining": 0,
                "reset_time": reset_time,
                "retry_after": int(reset_time - current_time)
            }
        
        return True, {
            "remaining": rule.requests - len(recent_requests) - 1,
            "reset_time": current_time + rule.window
        }
    
    def _record_request(self, key: str, record: RequestRecord):
        """记录请求"""
        if key not in self.request_records:
            self.request_records[key] = []
        
        self.request_records[key].append(record)
        
        # 限制单个键的记录数量
        if len(self.request_records[key]) > 1000:
            self.request_records[key] = self.request_records[key][-500:]
    
    async def check_rate_limit(self, request: Request) -> Optional[JSONResponse]:
        """检查请求是否超过速率限制"""
        try:
            # 清理过期记录
            self._cleanup_old_records()
            
            current_time = time.time()
            ip_address = self._get_client_ip(request)
            user_id = self._get_user_id(request)
            path = str(request.url.path)
            user_agent = request.headers.get("User-Agent", "")
            
            # 检查黑名单
            if ip_address in self.blacklist_ips:
                logger.warning(f"Blocked request from blacklisted IP: {ip_address}")
                return JSONResponse(
                    status_code=status.HTTP_403_FORBIDDEN,
                    content={"error": "IP地址已被封禁"}
                )
            
            # 白名单跳过限制
            if ip_address in self.whitelist_ips:
                return None
            
            # 创建请求记录
            record = RequestRecord(
                timestamp=current_time,
                ip_address=ip_address,
                user_id=user_id,
                endpoint=path,
                user_agent=user_agent
            )
            
            # 检查各种限制
            checks = []
            
            # 1. IP限制
            ip_key = f"ip:{ip_address}"
            ip_allowed, ip_info = self._check_rate_limit(ip_key, self.default_rules["ip"], current_time)
            checks.append(("IP", ip_allowed, ip_info))
            
            # 2. 用户限制（如果已登录）
            if user_id:
                user_key = f"user:{user_id}"
                user_allowed, user_info = self._check_rate_limit(user_key, self.default_rules["user"], current_time)
                checks.append(("User", user_allowed, user_info))
            
            # 3. 端点限制
            endpoint_rule_key = self._get_endpoint_key(path)
            if endpoint_rule_key in self.default_rules:
                endpoint_key = f"endpoint:{endpoint_rule_key}:{ip_address}"
                endpoint_allowed, endpoint_info = self._check_rate_limit(
                    endpoint_key, self.default_rules[endpoint_rule_key], current_time
                )
                checks.append(("Endpoint", endpoint_allowed, endpoint_info))
            
            # 检查是否有任何限制被触发
            for check_type, allowed, info in checks:
                if not allowed:
                    logger.warning(f"Rate limit exceeded for {check_type}: {ip_address}, path: {path}")
                    
                    headers = {
                        "X-RateLimit-Limit": str(self.default_rules[endpoint_rule_key if endpoint_rule_key in self.default_rules else "ip"].requests),
                        "X-RateLimit-Remaining": "0",
                        "X-RateLimit-Reset": str(int(info["reset_time"])),
                        "Retry-After": str(info.get("retry_after", 60))
                    }
                    
                    return JSONResponse(
                        status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                        content={
                            "error": "请求过于频繁，请稍后再试",
                            "type": check_type.lower(),
                            "retry_after": info.get("retry_after", 60)
                        },
                        headers=headers
                    )
            
            # 记录请求
            self._record_request(ip_key, record)
            if user_id:
                self._record_request(f"user:{user_id}", record)
            if endpoint_rule_key in self.default_rules:
                self._record_request(f"endpoint:{endpoint_rule_key}:{ip_address}", record)
            
            return None
            
        except Exception as e:
            logger.error(f"Rate limiter error: {str(e)}")
            # 出错时不阻止请求
            return None
    
    def add_to_blacklist(self, ip_address: str, reason: str = "Rate limit exceeded"):
        """添加IP到黑名单"""
        self.blacklist_ips.add(ip_address)
        logger.info(f"Added IP to blacklist: {ip_address}")

        # 同步到微服务
        asyncio.create_task(
            self._sync_ip_to_microservice([ip_address], "add", "blacklist", reason)
        )

    def remove_from_blacklist(self, ip_address: str, reason: str = "Manual removal"):
        """从黑名单移除IP"""
        self.blacklist_ips.discard(ip_address)
        logger.info(f"Removed IP from blacklist: {ip_address}")

        # 同步到微服务
        asyncio.create_task(
            self._sync_ip_to_microservice([ip_address], "remove", "blacklist", reason)
        )

    async def _sync_ip_to_microservice(self, ip_addresses: list, action: str, list_type: str, reason: str):
        """同步IP管理到微服务"""
        try:
            await self.microservice_manager.security_adapter.manage_ip_lists(
                ip_addresses=ip_addresses,
                action=action,
                list_type=list_type,
                reason=reason
            )
            logger.info(f"IP管理已同步到微服务: {action} {ip_addresses} to {list_type}")
        except Exception as e:
            logger.error(f"同步IP管理到微服务失败: {e}")

    async def sync_blacklist_from_microservice(self):
        """从微服务同步黑名单"""
        try:
            response = await self.microservice_manager.security_adapter.get_blocked_ips()
            blocked_ips = response.get("blocked_ips", [])

            # 更新本地黑名单
            self.blacklist_ips.update(blocked_ips)
            logger.info(f"从微服务同步了 {len(blocked_ips)} 个被阻止的IP")
        except Exception as e:
            logger.error(f"从微服务同步黑名单失败: {e}")
    
    def get_stats(self) -> dict:
        """获取速率限制统计信息"""
        current_time = time.time()
        stats = {
            "total_keys": len(self.request_records),
            "blacklist_count": len(self.blacklist_ips),
            "whitelist_count": len(self.whitelist_ips),
            "recent_requests": 0,
            "last_cleanup": self._last_cleanup
        }
        
        # 统计最近1小时的请求
        cutoff_time = current_time - 3600
        for records in self.request_records.values():
            stats["recent_requests"] += len([
                r for r in records if r.timestamp > cutoff_time
            ])
        
        return stats


# 全局速率限制器实例
rate_limiter = RateLimiter()
