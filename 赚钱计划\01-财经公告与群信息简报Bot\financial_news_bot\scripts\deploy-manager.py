#!/usr/bin/env python3
"""
统一部署管理工具
整合启动、停止、更新、部署等操作
"""
import os
import sys
import subprocess
import argparse
import time
from pathlib import Path

class DeployManager:
    """部署管理器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.compose_file = self.project_root / "docker-compose.yml"
    
    def check_docker(self):
        """检查Docker环境"""
        try:
            result = subprocess.run(["docker", "--version"], capture_output=True, text=True)
            if result.returncode != 0:
                print("❌ Docker未安装或未启动")
                return False
            
            result = subprocess.run(["docker-compose", "--version"], capture_output=True, text=True)
            if result.returncode != 0:
                print("❌ Docker Compose未安装")
                return False
            
            print("✅ Docker环境检查通过")
            return True
        except FileNotFoundError:
            print("❌ Docker命令未找到")
            return False
    
    def start_services(self, build=False):
        """启动服务"""
        print("🚀 启动服务...")
        
        if not self.check_docker():
            return False
        
        cmd = ["docker-compose", "up", "-d"]
        if build:
            cmd.append("--build")
        
        result = subprocess.run(cmd, cwd=self.project_root)
        
        if result.returncode == 0:
            print("✅ 服务启动成功")
            self.show_status()
            return True
        else:
            print("❌ 服务启动失败")
            return False
    
    def stop_services(self):
        """停止服务"""
        print("🛑 停止服务...")
        
        result = subprocess.run(["docker-compose", "down"], cwd=self.project_root)
        
        if result.returncode == 0:
            print("✅ 服务停止成功")
            return True
        else:
            print("❌ 服务停止失败")
            return False
    
    def restart_services(self):
        """重启服务"""
        print("🔄 重启服务...")
        
        if self.stop_services():
            time.sleep(2)
            return self.start_services()
        return False
    
    def show_status(self):
        """显示服务状态"""
        print("📊 服务状态:")
        subprocess.run(["docker-compose", "ps"], cwd=self.project_root)
    
    def show_logs(self, service=None, follow=False):
        """显示日志"""
        cmd = ["docker-compose", "logs"]
        if follow:
            cmd.append("-f")
        if service:
            cmd.append(service)
        
        subprocess.run(cmd, cwd=self.project_root)
    
    def update_services(self):
        """更新服务"""
        print("🔄 更新服务...")
        
        steps = [
            ("拉取最新镜像", ["docker-compose", "pull"]),
            ("重新构建", ["docker-compose", "build", "--no-cache"]),
            ("重启服务", ["docker-compose", "up", "-d", "--force-recreate"])
        ]
        
        for name, cmd in steps:
            print(f"执行: {name}")
            result = subprocess.run(cmd, cwd=self.project_root)
            if result.returncode != 0:
                print(f"❌ {name} 失败")
                return False
        
        print("✅ 服务更新完成")
        self.show_status()
        return True
    
    def backup_data(self):
        """备份数据"""
        print("💾 备份数据...")
        
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        backup_dir = self.project_root / "backups" / timestamp
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        # 备份数据库
        db_backup_cmd = [
            "docker-compose", "exec", "-T", "mysql",
            "mysqldump", "-u", "root", "-p${MYSQL_ROOT_PASSWORD}",
            "${MYSQL_DATABASE}"
        ]
        
        try:
            with open(backup_dir / "database.sql", "w") as f:
                result = subprocess.run(db_backup_cmd, cwd=self.project_root, stdout=f)
            
            if result.returncode == 0:
                print(f"✅ 数据库备份完成: {backup_dir / 'database.sql'}")
            else:
                print("❌ 数据库备份失败")
                return False
        except Exception as e:
            print(f"❌ 备份异常: {str(e)}")
            return False
        
        # 备份配置文件
        config_files = [".env", "docker-compose.yml"]
        for config_file in config_files:
            src = self.project_root / config_file
            if src.exists():
                dst = backup_dir / config_file
                import shutil
                shutil.copy2(src, dst)
                print(f"✅ 配置文件备份: {config_file}")
        
        print(f"✅ 备份完成: {backup_dir}")
        return True
    
    def health_check(self):
        """健康检查"""
        print("🏥 健康检查...")
        
        # 检查服务状态
        result = subprocess.run(
            ["docker-compose", "ps", "--format", "table"],
            cwd=self.project_root,
            capture_output=True,
            text=True
        )
        
        if result.returncode != 0:
            print("❌ 无法获取服务状态")
            return False
        
        # 检查API健康
        try:
            import requests
            response = requests.get("http://localhost:8000/health", timeout=10)
            if response.status_code == 200:
                print("✅ API健康检查通过")
                api_healthy = True
            else:
                print(f"⚠️ API健康检查失败: HTTP {response.status_code}")
                api_healthy = False
        except Exception as e:
            print(f"⚠️ API健康检查异常: {str(e)}")
            api_healthy = False
        
        # 检查数据库连接
        try:
            db_check = subprocess.run([
                "docker-compose", "exec", "-T", "mysql",
                "mysql", "-u", "root", "-p${MYSQL_ROOT_PASSWORD}",
                "-e", "SELECT 1"
            ], cwd=self.project_root, capture_output=True)
            
            if db_check.returncode == 0:
                print("✅ 数据库连接正常")
                db_healthy = True
            else:
                print("❌ 数据库连接失败")
                db_healthy = False
        except Exception as e:
            print(f"❌ 数据库检查异常: {str(e)}")
            db_healthy = False
        
        overall_healthy = api_healthy and db_healthy
        
        if overall_healthy:
            print("🎉 系统健康状态良好")
        else:
            print("⚠️ 系统存在健康问题")
        
        return overall_healthy
    
    def clean_system(self):
        """清理系统"""
        print("🧹 清理系统...")
        
        # 清理未使用的Docker资源
        cleanup_commands = [
            (["docker", "system", "prune", "-f"], "清理未使用的Docker资源"),
            (["docker", "volume", "prune", "-f"], "清理未使用的数据卷"),
            (["docker", "network", "prune", "-f"], "清理未使用的网络")
        ]
        
        for cmd, desc in cleanup_commands:
            print(f"执行: {desc}")
            result = subprocess.run(cmd)
            if result.returncode == 0:
                print(f"✅ {desc} 完成")
            else:
                print(f"⚠️ {desc} 失败")
        
        print("✅ 系统清理完成")

def main():
    parser = argparse.ArgumentParser(description="财经新闻Bot部署管理工具")
    parser.add_argument("command", choices=[
        "start", "stop", "restart", "status", "logs", "update", 
        "backup", "health", "clean"
    ], help="要执行的命令")
    parser.add_argument("--build", action="store_true", help="启动时重新构建镜像")
    parser.add_argument("--service", help="指定服务名称（用于logs命令）")
    parser.add_argument("--follow", "-f", action="store_true", help="跟踪日志输出")
    
    args = parser.parse_args()
    manager = DeployManager()
    
    if args.command == "start":
        success = manager.start_services(build=args.build)
    elif args.command == "stop":
        success = manager.stop_services()
    elif args.command == "restart":
        success = manager.restart_services()
    elif args.command == "status":
        manager.show_status()
        success = True
    elif args.command == "logs":
        manager.show_logs(service=args.service, follow=args.follow)
        success = True
    elif args.command == "update":
        success = manager.update_services()
    elif args.command == "backup":
        success = manager.backup_data()
    elif args.command == "health":
        success = manager.health_check()
    elif args.command == "clean":
        manager.clean_system()
        success = True
    else:
        parser.print_help()
        success = False
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
