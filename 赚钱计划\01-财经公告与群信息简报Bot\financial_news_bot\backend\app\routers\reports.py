"""
简报API路由
提供简报生成、查看、订阅等功能
"""
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta

from ..database import get_db
from ..dependencies.auth import get_current_active_user
from ..dependencies.permissions import (
    require_permission,
    require_basic_subscription,
    require_advanced_subscription,
    require_admin
)
from ..models.user import User
from ..models.report import Report, ReportType, ReportStatus
from ..schemas.report import (
    ReportGenerateRequest, ReportResponse, ReportListResponse,
    ReportTemplateCreate, ReportTemplateResponse,
    ReportSubscriptionCreate, ReportSubscriptionResponse,
    ReportFeedbackCreate, ReportStatistics
)
from ..services.report_service import ReportService
from ..services.report_scheduler import get_report_scheduler

router = APIRouter(prefix="/reports", tags=["简报管理"])


@router.post("/generate", response_model=ReportResponse)
async def generate_report(
    request: ReportGenerateRequest,
    current_user: User = Depends(require_basic_subscription()),
    db: Session = Depends(get_db)
):
    """
    生成简报（需要basic_subscription权限）
    """
    try:
        report_service = ReportService(db)
        
        # 生成简报
        report = await report_service.generate_report(
            report_type=request.report_type,
            template_id=request.template_id,
            report_date=request.report_date,
            user=current_user,
            personalization_config=request.personalization_config
        )
        
        return ReportResponse.model_validate(report)
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"生成简报失败: {str(e)}"
        )


@router.get("/", response_model=ReportListResponse)
async def get_reports(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    report_type: Optional[ReportType] = Query(None, description="简报类型过滤"),
    status: Optional[ReportStatus] = Query(None, description="状态过滤"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    current_user: User = Depends(require_basic_subscription()),
    db: Session = Depends(get_db)
):
    """
    获取简报列表（需要basic_subscription权限）
    """
    try:
        # 构建查询
        query = db.query(Report)
        
        # 应用过滤条件
        if report_type:
            query = query.filter(Report.type == report_type)
        
        if status:
            query = query.filter(Report.status == status)
        
        if start_date:
            query = query.filter(Report.report_date >= start_date)
        
        if end_date:
            query = query.filter(Report.report_date <= end_date)
        
        # 计算总数
        total = query.count()
        
        # 分页查询
        reports = query.order_by(Report.report_date.desc()).offset(
            (page - 1) * size
        ).limit(size).all()
        
        # 计算总页数
        pages = (total + size - 1) // size
        
        return ReportListResponse(
            reports=[ReportResponse.model_validate(report) for report in reports],
            total=total,
            page=page,
            size=size,
            pages=pages
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取简报列表失败: {str(e)}"
        )


@router.get("/{report_id}", response_model=ReportResponse)
async def get_report(
    report_id: int,
    current_user: User = Depends(require_basic_subscription()),
    db: Session = Depends(get_db)
):
    """
    获取简报详情（需要basic_subscription权限）
    """
    try:
        report = db.query(Report).filter(Report.id == report_id).first()
        
        if not report:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="简报不存在"
            )
        
        return ReportResponse.model_validate(report)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取简报详情失败: {str(e)}"
        )


@router.get("/latest/{report_type}", response_model=ReportResponse)
async def get_latest_report(
    report_type: ReportType,
    current_user: User = Depends(require_basic_subscription()),
    db: Session = Depends(get_db)
):
    """
    获取最新简报（需要basic_subscription权限）
    """
    try:
        report = db.query(Report).filter(
            Report.type == report_type,
            Report.status == ReportStatus.COMPLETED
        ).order_by(Report.report_date.desc()).first()
        
        if not report:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"暂无{report_type.value}简报"
            )
        
        return ReportResponse.model_validate(report)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取最新简报失败: {str(e)}"
        )


@router.post("/schedule", response_model=Dict[str, str])
async def schedule_custom_report(
    request: ReportGenerateRequest,
    scheduled_time: datetime = Query(..., description="计划生成时间"),
    current_user: User = Depends(require_advanced_subscription()),
    db: Session = Depends(get_db)
):
    """
    安排自定义简报生成（需要advanced_subscription权限）
    """
    try:
        # 检查计划时间
        if scheduled_time <= datetime.now():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="计划时间必须是未来时间"
            )
        
        # 安排任务
        scheduler = get_report_scheduler()
        job_id = await scheduler.schedule_custom_report(
            report_type=request.report_type,
            scheduled_time=scheduled_time,
            user_id=current_user.id,
            personalization_config=request.personalization_config
        )
        
        return {
            "message": "自定义简报已安排生成",
            "job_id": job_id,
            "scheduled_time": scheduled_time.isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"安排简报生成失败: {str(e)}"
        )


@router.post("/feedback/{report_id}", response_model=Dict[str, str])
async def submit_report_feedback(
    report_id: int,
    feedback: ReportFeedbackCreate,
    current_user: User = Depends(require_basic_subscription()),
    db: Session = Depends(get_db)
):
    """
    提交简报反馈（需要basic_subscription权限）
    """
    try:
        # 检查简报是否存在
        report = db.query(Report).filter(Report.id == report_id).first()
        if not report:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="简报不存在"
            )
        
        # 创建反馈记录
        from ..models.report import ReportFeedback
        
        feedback_record = ReportFeedback(
            report_id=report_id,
            user_id=current_user.id,
            rating=feedback.rating,
            feedback_type=feedback.feedback_type,
            content=feedback.content,
            tags=feedback.tags
        )
        
        db.add(feedback_record)
        db.commit()
        
        return {"message": "反馈提交成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"提交反馈失败: {str(e)}"
        )


@router.get("/statistics/summary", response_model=ReportStatistics)
async def get_report_statistics(
    days: int = Query(30, ge=1, le=365, description="统计天数"),
    current_user: User = Depends(require_admin()),
    db: Session = Depends(get_db)
):
    """
    获取简报统计信息（管理员权限）
    """
    try:
        # 计算时间范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # 总简报数
        total_reports = db.query(Report).filter(
            Report.created_at >= start_date
        ).count()
        
        # 按类型统计
        reports_by_type = {}
        for report_type in ReportType:
            count = db.query(Report).filter(
                Report.type == report_type,
                Report.created_at >= start_date
            ).count()
            reports_by_type[report_type.value] = count
        
        # 按状态统计
        reports_by_status = {}
        for report_status in ReportStatus:
            count = db.query(Report).filter(
                Report.status == report_status,
                Report.created_at >= start_date
            ).count()
            reports_by_status[report_status.value] = count
        
        # 计算平均生成时间
        completed_reports = db.query(Report).filter(
            Report.status == ReportStatus.COMPLETED,
            Report.generation_duration.isnot(None),
            Report.created_at >= start_date
        ).all()
        
        avg_generation_time = 0.0
        if completed_reports:
            total_time = sum(r.generation_duration for r in completed_reports)
            avg_generation_time = total_time / len(completed_reports)
        
        # 计算成功率
        total_attempts = db.query(Report).filter(
            Report.created_at >= start_date
        ).count()
        
        successful_reports = db.query(Report).filter(
            Report.status == ReportStatus.COMPLETED,
            Report.created_at >= start_date
        ).count()
        
        success_rate = (successful_reports / total_attempts * 100) if total_attempts > 0 else 0
        
        return ReportStatistics(
            total_reports=total_reports,
            reports_by_type=reports_by_type,
            reports_by_status=reports_by_status,
            avg_generation_time=avg_generation_time,
            avg_quality_score=await _calculate_avg_quality_score(db),
            success_rate=success_rate,
            daily_reports=await _get_daily_reports_stats(db),
            weekly_reports=await _get_weekly_reports_stats(db),
            active_subscribers=await _get_active_subscribers_count(db),
            avg_read_time=await _calculate_avg_read_time(db)
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取统计信息失败: {str(e)}"
        )


# 管理员专用API
@router.get("/admin/jobs", response_model=List[Dict[str, Any]])
async def list_scheduled_jobs(
    current_user: User = Depends(require_admin()),
):
    """
    列出所有计划任务（管理员权限）
    """
    try:
        scheduler = get_report_scheduler()
        jobs = scheduler.list_jobs()
        return jobs
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取计划任务失败: {str(e)}"
        )


@router.delete("/admin/jobs/{job_id}", response_model=Dict[str, str])
async def cancel_scheduled_job(
    job_id: str,
    current_user: User = Depends(require_admin()),
):
    """
    取消计划任务（管理员权限）
    """
    try:
        scheduler = get_report_scheduler()
        success = scheduler.cancel_job(job_id)
        
        if success:
            return {"message": f"任务 {job_id} 已取消"}
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在或取消失败"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"取消任务失败: {str(e)}"
        )


# 辅助函数：计算真实统计数据
async def _calculate_avg_quality_score(db: Session) -> float:
    """计算平均质量分数"""
    try:
        from sqlalchemy import func
        avg_score = db.query(func.avg(Report.quality_score)).filter(
            Report.quality_score.isnot(None)
        ).scalar()
        return round(float(avg_score or 0), 1)
    except:
        return 0.0


async def _get_daily_reports_stats(db: Session) -> dict:
    """获取每日简报统计"""
    try:
        from datetime import datetime, timedelta
        from sqlalchemy import func

        # 过去7天的每日统计
        seven_days_ago = datetime.now() - timedelta(days=7)
        daily_stats = db.query(
            func.date(Report.created_at).label('date'),
            func.count(Report.id).label('count')
        ).filter(
            Report.created_at >= seven_days_ago
        ).group_by(func.date(Report.created_at)).all()

        return {str(stat.date): stat.count for stat in daily_stats}
    except:
        return {}


async def _get_weekly_reports_stats(db: Session) -> dict:
    """获取每周简报统计"""
    try:
        from datetime import datetime, timedelta
        from sqlalchemy import func

        # 过去4周的统计
        four_weeks_ago = datetime.now() - timedelta(weeks=4)
        weekly_stats = db.query(
            func.yearweek(Report.created_at).label('week'),
            func.count(Report.id).label('count')
        ).filter(
            Report.created_at >= four_weeks_ago
        ).group_by(func.yearweek(Report.created_at)).all()

        return {f"week_{stat.week}": stat.count for stat in weekly_stats}
    except:
        return {}


async def _get_active_subscribers_count(db: Session) -> int:
    """获取活跃订阅者数量"""
    try:
        from ..models.subscription import Subscription
        count = db.query(func.count(Subscription.id)).filter(
            Subscription.is_active == True
        ).scalar()
        return count or 0
    except:
        return 0


async def _calculate_avg_read_time(db: Session) -> float:
    """计算平均阅读时间"""
    try:
        # 基于简报长度估算阅读时间（每分钟约200字）
        from sqlalchemy import func
        avg_length = db.query(func.avg(func.length(Report.content))).scalar()
        if avg_length:
            # 估算阅读时间（分钟）
            read_time = (avg_length / 200.0) * 60  # 转换为秒
            return round(read_time / 60.0, 1)  # 转换回分钟
        return 0.0
    except:
        return 0.0
