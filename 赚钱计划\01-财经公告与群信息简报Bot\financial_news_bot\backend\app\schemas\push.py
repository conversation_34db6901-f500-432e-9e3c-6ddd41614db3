"""
推送相关的Pydantic模式
定义推送请求、响应和配置的数据结构
"""
from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
from enum import Enum


class PushChannel(str, Enum):
    """推送渠道枚举"""
    WECHAT = "wechat"
    EMAIL = "email"
    SMS = "sms"
    WEBHOOK = "webhook"
    APP_PUSH = "app_push"


class PushPriority(str, Enum):
    """推送优先级枚举"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class PushStatus(str, Enum):
    """推送状态枚举"""
    PENDING = "pending"
    SENDING = "sending"
    SENT = "sent"
    FAILED = "failed"
    CANCELLED = "cancelled"


class PushMessageCreate(BaseModel):
    """创建推送消息请求模式"""
    title: str = Field(..., max_length=200, description="推送标题")
    content: str = Field(..., max_length=2000, description="推送内容")
    channel: PushChannel = Field(..., description="推送渠道")
    recipient: str = Field(..., description="接收者")
    priority: PushPriority = Field(PushPriority.NORMAL, description="推送优先级")
    scheduled_at: Optional[datetime] = Field(None, description="计划推送时间")
    template_id: Optional[int] = Field(None, description="使用的模板ID")
    variables: Optional[Dict[str, Any]] = Field(None, description="模板变量")
    metadata: Optional[Dict[str, Any]] = Field(None, description="附加元数据")


class PushMessageResponse(BaseModel):
    """推送消息响应模式"""
    id: int = Field(..., description="推送消息ID")
    title: str = Field(..., description="推送标题")
    content: str = Field(..., description="推送内容")
    channel: PushChannel = Field(..., description="推送渠道")
    recipient: str = Field(..., description="接收者")
    status: PushStatus = Field(..., description="推送状态")
    priority: PushPriority = Field(..., description="推送优先级")
    created_at: datetime = Field(..., description="创建时间")
    scheduled_at: Optional[datetime] = Field(None, description="计划推送时间")
    sent_at: Optional[datetime] = Field(None, description="实际发送时间")
    delivered_at: Optional[datetime] = Field(None, description="送达时间")
    error_message: Optional[str] = Field(None, description="错误信息")
    metadata: Optional[Dict[str, Any]] = Field(None, description="附加元数据")

    class Config:
        from_attributes = True


class PushRequest(BaseModel):
    """推送请求模式"""
    title: str = Field(..., max_length=200, description="推送标题")
    content: str = Field(..., max_length=2000, description="推送内容")
    channels: List[PushChannel] = Field(..., description="推送渠道列表")
    recipients: List[str] = Field(..., description="接收者列表")
    priority: PushPriority = Field(PushPriority.NORMAL, description="推送优先级")
    scheduled_at: Optional[datetime] = Field(None, description="计划推送时间")
    template_id: Optional[int] = Field(None, description="使用的模板ID")
    variables: Optional[Dict[str, Any]] = Field(None, description="模板变量")
    metadata: Optional[Dict[str, Any]] = Field(None, description="附加元数据")
    
    @validator('recipients')
    def validate_recipients(cls, v):
        if not v or len(v) == 0:
            raise ValueError('接收者列表不能为空')
        return v


class PushResponse(BaseModel):
    """推送响应模式"""
    id: int = Field(..., description="推送任务ID")
    status: PushStatus = Field(..., description="推送状态")
    title: str = Field(..., description="推送标题")
    channels: List[PushChannel] = Field(..., description="推送渠道")
    recipients_count: int = Field(..., description="接收者数量")
    sent_count: int = Field(0, description="已发送数量")
    failed_count: int = Field(0, description="失败数量")
    created_at: datetime = Field(..., description="创建时间")
    scheduled_at: Optional[datetime] = Field(None, description="计划推送时间")
    sent_at: Optional[datetime] = Field(None, description="实际发送时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    error_message: Optional[str] = Field(None, description="错误信息")
    
    class Config:
        from_attributes = True


class PushBatchRequest(BaseModel):
    """批量推送请求模式"""
    requests: List[PushRequest] = Field(..., description="推送请求列表")
    batch_name: Optional[str] = Field(None, max_length=100, description="批次名称")
    priority: PushPriority = Field(PushPriority.NORMAL, description="批次优先级")
    scheduled_at: Optional[datetime] = Field(None, description="计划推送时间")
    
    @validator('requests')
    def validate_requests(cls, v):
        if not v or len(v) == 0:
            raise ValueError('推送请求列表不能为空')
        if len(v) > 100:
            raise ValueError('单次批量推送不能超过100个请求')
        return v


class PushBatchResponse(BaseModel):
    """批量推送响应模式"""
    batch_id: str = Field(..., description="批次ID")
    batch_name: Optional[str] = Field(None, description="批次名称")
    total_requests: int = Field(..., description="总请求数")
    successful_requests: int = Field(0, description="成功请求数")
    failed_requests: int = Field(0, description="失败请求数")
    status: str = Field(..., description="批次状态")
    created_at: datetime = Field(..., description="创建时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    requests: List[PushResponse] = Field(..., description="推送响应列表")


class PushTemplateCreate(BaseModel):
    """创建推送模板请求模式"""
    name: str = Field(..., max_length=100, description="模板名称")
    title_template: str = Field(..., max_length=200, description="标题模板")
    content_template: str = Field(..., max_length=2000, description="内容模板")
    channels: List[PushChannel] = Field(..., description="支持的推送渠道")
    variables: List[str] = Field(..., description="模板变量列表")
    is_active: bool = Field(True, description="是否启用")

    @validator('variables')
    def validate_variables(cls, v):
        if not v:
            return v
        # 检查变量名格式
        for var in v:
            if not var.replace('_', '').isalnum():
                raise ValueError(f'变量名 {var} 格式不正确，只能包含字母、数字和下划线')
        return v


class PushTemplateResponse(BaseModel):
    """推送模板响应模式"""
    id: int = Field(..., description="模板ID")
    name: str = Field(..., description="模板名称")
    title_template: str = Field(..., description="标题模板")
    content_template: str = Field(..., description="内容模板")
    channels: List[PushChannel] = Field(..., description="支持的推送渠道")
    variables: List[str] = Field(..., description="模板变量列表")
    is_active: bool = Field(..., description="是否启用")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    usage_count: int = Field(0, description="使用次数")

    class Config:
        from_attributes = True


class PushTemplate(BaseModel):
    """推送模板模式"""
    id: Optional[int] = Field(None, description="模板ID")
    name: str = Field(..., max_length=100, description="模板名称")
    title_template: str = Field(..., max_length=200, description="标题模板")
    content_template: str = Field(..., max_length=2000, description="内容模板")
    channels: List[PushChannel] = Field(..., description="支持的推送渠道")
    variables: List[str] = Field(..., description="模板变量列表")
    is_active: bool = Field(True, description="是否启用")
    created_at: Optional[datetime] = Field(None, description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    
    class Config:
        from_attributes = True


class PushStatistics(BaseModel):
    """推送统计模式"""
    total_pushes: int = Field(..., description="总推送数")
    successful_pushes: int = Field(..., description="成功推送数")
    failed_pushes: int = Field(..., description="失败推送数")
    pending_pushes: int = Field(..., description="待发送推送数")
    success_rate: float = Field(..., description="成功率")
    avg_delivery_time: float = Field(..., description="平均送达时间(秒)")
    channel_stats: Dict[str, Dict[str, int]] = Field(..., description="各渠道统计")
    daily_stats: List[Dict[str, Any]] = Field(..., description="每日统计")
    
    class Config:
        from_attributes = True


class PushConfig(BaseModel):
    """推送配置模式"""
    channel: PushChannel = Field(..., description="推送渠道")
    config: Dict[str, Any] = Field(..., description="渠道配置")
    is_enabled: bool = Field(True, description="是否启用")
    rate_limit: Optional[int] = Field(None, description="速率限制(每分钟)")
    retry_count: int = Field(3, description="重试次数")
    timeout: int = Field(30, description="超时时间(秒)")
    
    class Config:
        from_attributes = True


class PushLogResponse(BaseModel):
    """推送日志响应模式"""
    id: int = Field(..., description="推送日志ID")
    user_id: int = Field(..., description="用户ID")
    subscription_id: Optional[int] = Field(None, description="订阅ID")
    news_id: Optional[int] = Field(None, description="新闻ID")
    push_type: str = Field(..., description="推送类型")
    channel: PushChannel = Field(..., description="推送渠道")
    recipient: str = Field(..., description="接收者")
    title: str = Field(..., description="推送标题")
    content: str = Field(..., description="推送内容")
    status: PushStatus = Field(..., description="推送状态")
    sent_at: Optional[datetime] = Field(None, description="发送时间")
    delivered_at: Optional[datetime] = Field(None, description="送达时间")
    error_message: Optional[str] = Field(None, description="错误信息")
    metadata: Optional[Dict[str, Any]] = Field(None, description="附加元数据")
    created_at: datetime = Field(..., description="创建时间")

    class Config:
        from_attributes = True


class PushHistory(BaseModel):
    """推送历史模式"""
    id: int = Field(..., description="推送ID")
    title: str = Field(..., description="推送标题")
    content: str = Field(..., description="推送内容")
    channel: PushChannel = Field(..., description="推送渠道")
    recipient: str = Field(..., description="接收者")
    status: PushStatus = Field(..., description="推送状态")
    sent_at: Optional[datetime] = Field(None, description="发送时间")
    delivered_at: Optional[datetime] = Field(None, description="送达时间")
    error_message: Optional[str] = Field(None, description="错误信息")
    metadata: Optional[Dict[str, Any]] = Field(None, description="附加元数据")
    
    class Config:
        from_attributes = True


class PushFilter(BaseModel):
    """推送过滤条件模式"""
    channels: Optional[List[PushChannel]] = Field(None, description="渠道过滤")
    status: Optional[List[PushStatus]] = Field(None, description="状态过滤")
    priority: Optional[List[PushPriority]] = Field(None, description="优先级过滤")
    start_date: Optional[datetime] = Field(None, description="开始时间")
    end_date: Optional[datetime] = Field(None, description="结束时间")
    recipient: Optional[str] = Field(None, description="接收者过滤")
    keyword: Optional[str] = Field(None, description="关键词搜索")
    
    @validator('end_date')
    def validate_date_range(cls, v, values):
        if v and values.get('start_date') and v <= values['start_date']:
            raise ValueError('结束时间必须晚于开始时间')
        return v


class PushRuleCreate(BaseModel):
    """创建推送规则请求模式"""
    name: str = Field(..., max_length=100, description="规则名称")
    description: Optional[str] = Field(None, max_length=500, description="规则描述")
    conditions: Dict[str, Any] = Field(..., description="触发条件")
    actions: Dict[str, Any] = Field(..., description="执行动作")
    priority: int = Field(1, ge=1, le=10, description="优先级(1-10)")
    is_active: bool = Field(True, description="是否启用")
    schedule: Optional[str] = Field(None, description="调度表达式")

    @validator('conditions')
    def validate_conditions(cls, v):
        if not v:
            raise ValueError('触发条件不能为空')
        return v

    @validator('actions')
    def validate_actions(cls, v):
        if not v:
            raise ValueError('执行动作不能为空')
        return v


class PushRuleResponse(BaseModel):
    """推送规则响应模式"""
    id: int = Field(..., description="规则ID")
    name: str = Field(..., description="规则名称")
    description: Optional[str] = Field(None, description="规则描述")
    conditions: Dict[str, Any] = Field(..., description="触发条件")
    actions: Dict[str, Any] = Field(..., description="执行动作")
    priority: int = Field(..., description="优先级")
    is_active: bool = Field(..., description="是否启用")
    schedule: Optional[str] = Field(None, description="调度表达式")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    last_triggered_at: Optional[datetime] = Field(None, description="最后触发时间")
    trigger_count: int = Field(0, description="触发次数")

    class Config:
        from_attributes = True


class PushAnalytics(BaseModel):
    """推送分析模式"""
    period: str = Field(..., description="统计周期")
    total_pushes: int = Field(..., description="总推送数")
    success_rate: float = Field(..., description="成功率")
    avg_delivery_time: float = Field(..., description="平均送达时间")
    peak_hour: int = Field(..., description="推送高峰时段")
    most_used_channel: PushChannel = Field(..., description="最常用渠道")
    channel_performance: Dict[str, Dict[str, float]] = Field(..., description="渠道性能")
    trend_data: List[Dict[str, Any]] = Field(..., description="趋势数据")
    
    class Config:
        from_attributes = True
