#!/usr/bin/env python3
"""
测试运行器
运行所有测试并生成覆盖率报告
"""
import os
import sys
import subprocess
import argparse
from pathlib import Path

def run_command(command, description):
    """运行命令并处理结果"""
    print(f"\n{'='*60}")
    print(f"🔄 {description}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            cwd=Path(__file__).parent
        )
        
        if result.stdout:
            print(result.stdout)
        
        if result.stderr:
            print("错误输出:", result.stderr)
        
        if result.returncode == 0:
            print(f"✅ {description} - 成功")
            return True
        else:
            print(f"❌ {description} - 失败 (退出码: {result.returncode})")
            return False
            
    except Exception as e:
        print(f"❌ {description} - 异常: {str(e)}")
        return False

def install_dependencies():
    """安装测试依赖"""
    dependencies = [
        "pytest>=7.0.0",
        "pytest-asyncio>=0.21.0",
        "pytest-cov>=4.0.0",
        "pytest-mock>=3.10.0",
        "coverage>=7.0.0"
    ]
    
    for dep in dependencies:
        success = run_command(
            f"pip install {dep}",
            f"安装依赖: {dep}"
        )
        if not success:
            print(f"⚠️ 依赖 {dep} 安装失败，继续执行...")

def run_unit_tests(verbose=False, coverage=False):
    """运行单元测试"""
    test_files = [
        "tests/test_unified_push_service.py",
        "tests/test_data_processing_pipeline.py", 
        "tests/test_simplified_crawler_service.py",
        "tests/test_ai_service.py",
        "tests/test_integrated_monitoring_service.py"
    ]
    
    # 检查测试文件是否存在
    existing_files = []
    for test_file in test_files:
        if Path(test_file).exists():
            existing_files.append(test_file)
        else:
            print(f"⚠️ 测试文件不存在: {test_file}")
    
    if not existing_files:
        print("❌ 没有找到测试文件")
        return False
    
    # 构建pytest命令
    cmd_parts = ["python", "-m", "pytest"]
    
    if verbose:
        cmd_parts.append("-v")
    
    if coverage:
        cmd_parts.extend([
            "--cov=app",
            "--cov-report=html:htmlcov",
            "--cov-report=term-missing",
            "--cov-report=xml"
        ])
    
    cmd_parts.extend(existing_files)
    
    command = " ".join(cmd_parts)
    
    return run_command(command, "运行单元测试")

def run_integration_tests(verbose=False):
    """运行集成测试"""
    integration_dir = Path("tests/integration")
    
    if not integration_dir.exists():
        print("⚠️ 集成测试目录不存在")
        return True
    
    cmd_parts = ["python", "-m", "pytest"]
    
    if verbose:
        cmd_parts.append("-v")
    
    cmd_parts.append("tests/integration/")
    
    command = " ".join(cmd_parts)
    
    return run_command(command, "运行集成测试")

def generate_coverage_report():
    """生成覆盖率报告"""
    if not Path("htmlcov").exists():
        print("⚠️ 没有找到覆盖率数据")
        return True
    
    print(f"\n{'='*60}")
    print("📊 覆盖率报告")
    print(f"{'='*60}")
    
    # 显示覆盖率摘要
    success = run_command("coverage report", "显示覆盖率摘要")
    
    if success:
        print(f"\n📁 详细的HTML覆盖率报告已生成: htmlcov/index.html")
        
        # 尝试计算覆盖率百分比
        try:
            result = subprocess.run(
                "coverage report --format=total",
                shell=True,
                capture_output=True,
                text=True
            )
            if result.returncode == 0:
                coverage_percent = float(result.stdout.strip())
                print(f"📈 总体覆盖率: {coverage_percent:.1f}%")
                
                if coverage_percent >= 80:
                    print("🎉 覆盖率达到目标 (≥80%)")
                else:
                    print(f"⚠️ 覆盖率未达到目标 (目标: ≥80%, 当前: {coverage_percent:.1f}%)")
        except:
            pass
    
    return success

def run_linting():
    """运行代码检查"""
    lint_commands = [
        ("python -m flake8 app/ --max-line-length=120 --ignore=E203,W503", "Flake8 代码风格检查"),
        ("python -m mypy app/ --ignore-missing-imports", "MyPy 类型检查")
    ]
    
    results = []
    for command, description in lint_commands:
        try:
            result = run_command(command, description)
            results.append(result)
        except:
            print(f"⚠️ {description} 工具未安装，跳过")
            results.append(True)
    
    return all(results)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="运行财经新闻Bot测试套件")
    parser.add_argument("-v", "--verbose", action="store_true", help="详细输出")
    parser.add_argument("-c", "--coverage", action="store_true", help="生成覆盖率报告")
    parser.add_argument("--no-install", action="store_true", help="跳过依赖安装")
    parser.add_argument("--unit-only", action="store_true", help="仅运行单元测试")
    parser.add_argument("--integration-only", action="store_true", help="仅运行集成测试")
    parser.add_argument("--lint-only", action="store_true", help="仅运行代码检查")
    
    args = parser.parse_args()
    
    print("🚀 财经新闻Bot - 测试套件")
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {Path.cwd()}")
    
    # 安装依赖
    if not args.no_install:
        install_dependencies()
    
    results = []
    
    # 运行代码检查
    if args.lint_only:
        results.append(run_linting())
    elif not args.unit_only and not args.integration_only:
        results.append(run_linting())
    
    # 运行单元测试
    if args.unit_only or not (args.integration_only or args.lint_only):
        results.append(run_unit_tests(args.verbose, args.coverage))
    
    # 运行集成测试
    if args.integration_only or not (args.unit_only or args.lint_only):
        results.append(run_integration_tests(args.verbose))
    
    # 生成覆盖率报告
    if args.coverage and not args.lint_only:
        results.append(generate_coverage_report())
    
    # 总结结果
    print(f"\n{'='*60}")
    print("📋 测试结果总结")
    print(f"{'='*60}")
    
    if all(results):
        print("🎉 所有测试通过！")
        
        # 显示新增的测试文件
        new_test_files = [
            "test_unified_push_service.py",
            "test_data_processing_pipeline.py",
            "test_simplified_crawler_service.py", 
            "test_ai_service.py",
            "test_integrated_monitoring_service.py"
        ]
        
        print(f"\n📝 新增测试文件 ({len(new_test_files)}个):")
        for test_file in new_test_files:
            if Path(f"tests/{test_file}").exists():
                print(f"  ✅ {test_file}")
            else:
                print(f"  ❌ {test_file} (未找到)")
        
        print(f"\n🎯 测试覆盖的核心服务:")
        print("  • 统一推送服务 (UnifiedPushService)")
        print("  • 数据处理管道 (DataProcessingPipeline)")
        print("  • 简化爬虫服务 (SimplifiedCrawlerService)")
        print("  • AI服务 (AIService)")
        print("  • 集成监控服务 (IntegratedMonitoringService)")
        
        return 0
    else:
        failed_count = sum(1 for result in results if not result)
        print(f"❌ {failed_count} 个测试阶段失败")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
