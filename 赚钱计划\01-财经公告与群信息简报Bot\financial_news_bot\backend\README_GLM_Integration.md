# GLM-4.5 Flash API 集成指南

## 📋 概述

本文档介绍了财经新闻Bot中GLM-4.5 Flash API的集成和使用方法。GLM-4.5 Flash是智谱AI提供的免费高性能语言模型，支持文本摘要、内容分类、关键词提取、情感分析等功能。

## 🚀 快速开始

### 1. 安装依赖

```bash
# 方法1: 使用安装脚本
cd financial_news_bot/backend
python install_dependencies.py

# 方法2: 手动安装
pip install zai-sdk==0.0.3 scikit-learn==1.3.2 numpy==1.24.4 pandas==2.1.4
```

### 2. 配置API密钥

在`.env`文件中添加GLM API配置：

```bash
# GLM-4.5 Flash API配置
GLM_API_KEY=your-glm-api-key-from-zhipu-ai-platform
GLM_BASE_URL=https://open.bigmodel.cn/api/paas/v4
GLM_MODEL=glm-4.5-flash
GLM_MAX_TOKENS=4096
GLM_TEMPERATURE=0.7
GLM_TIMEOUT=60
GLM_MAX_RETRIES=3
```

### 3. 运行测试

```bash
# 确保已在 .env 文件中配置了 GLM_API_KEY
# 或者临时设置环境变量（仅用于测试）
export GLM_API_KEY=your-glm-api-key-here

# 运行集成测试
python test_glm_integration.py
```

## 🔧 功能模块

### 1. AI服务模块 (`app/services/ai_service.py`)

提供GLM-4.5 Flash API的封装，包括：

- **文本摘要**: `generate_summary(content, max_length)`
- **内容分类**: `classify_content(title, content)`
- **关键词提取**: `extract_keywords(title, content)`
- **情感分析**: `analyze_sentiment(title, content)`

#### 使用示例：

```python
from app.services.ai_service import get_glm_service

# 获取服务实例
glm_service = get_glm_service()

# 生成摘要
summary = await glm_service.generate_summary(content)

# 内容分类
classification = await glm_service.classify_content(title, content)

# 提取关键词
keywords = await glm_service.extract_keywords(title, content)

# 情感分析
sentiment = await glm_service.analyze_sentiment(title, content)
```

### 2. 内容处理器 (`app/services/content_processor.py`)

综合处理内容的主要模块，包括：

- **内容质量检查**: 验证数据完整性、格式规范性
- **实体识别**: 提取公司名称、股票代码、金额、时间等
- **TF-IDF关键词提取**: 基于传统算法的关键词提取
- **综合处理**: 整合所有AI功能的一站式处理

#### 使用示例：

```python
from app.services.content_processor import get_content_processor

# 获取处理器实例
processor = get_content_processor()

# 综合处理内容
result = await processor.process_content(title, content, source)

# 访问处理结果
print(f"摘要: {result.summary}")
print(f"分类: {result.category}")
print(f"关键词: {result.keywords}")
print(f"质量得分: {result.quality_result.score}")
```

### 3. 合规检查服务 (`app/services/compliance_service.py`)

实现内容合规检查，包括：

- **敏感词过滤**: 多级敏感词检测和过滤
- **投资建议识别**: 检测和标注投资建议内容
- **风险等级评估**: 评估内容风险等级
- **免责声明**: 自动添加合规免责声明

#### 使用示例：

```python
from app.services.compliance_service import ComplianceService

# 创建合规服务实例
compliance = ComplianceService()

# 执行合规检查
result = compliance.check_compliance(title, content)

# 检查结果
print(f"是否合规: {result.is_compliant}")
print(f"风险等级: {result.risk_level}")
print(f"敏感词数量: {len(result.sensitive_words)}")
```

## 📊 数据处理流程

### 完整处理流程：

1. **数据质量检查** → 验证必填字段、内容长度、格式规范
2. **AI内容分析** → 并行执行摘要、分类、关键词、情感分析
3. **实体识别** → 提取金融实体（公司、股票代码、金额等）
4. **合规检查** → 敏感词过滤、投资建议检测、风险评估
5. **结果整合** → 生成综合处理结果

### 处理结果数据结构：

```python
@dataclass
class ProcessedContent:
    original_title: str              # 原始标题
    original_content: str            # 原始内容
    summary: Optional[str]           # AI生成摘要
    category: Optional[str]          # 内容分类
    category_confidence: Optional[float]  # 分类置信度
    keywords: List[Dict[str, Any]]   # 关键词列表
    sentiment: Optional[Dict[str, Any]]   # 情感分析结果
    quality_result: ContentQualityResult  # 质量检查结果
    entities: List[Dict[str, Any]]   # 实体识别结果
    processing_time: datetime        # 处理时间
```

## ⚙️ 配置说明

### GLM API 配置参数：

- `GLM_API_KEY`: API密钥（必填）
- `GLM_BASE_URL`: API基础URL
- `GLM_MODEL`: 使用的模型名称
- `GLM_MAX_TOKENS`: 最大输出token数
- `GLM_TEMPERATURE`: 生成温度（0-1）
- `GLM_TIMEOUT`: 请求超时时间（秒）
- `GLM_MAX_RETRIES`: 最大重试次数

### 内容处理配置：

- `CONTENT_CLASSIFICATION_CATEGORIES`: 分类类别列表
- `CONTENT_SUMMARY_MIN_LENGTH`: 摘要最小长度
- `CONTENT_SUMMARY_MAX_LENGTH`: 摘要最大长度
- `CONTENT_KEYWORDS_MAX_COUNT`: 最大关键词数量

### 数据质量配置：

- `DATA_QUALITY_MIN_TITLE_LENGTH`: 标题最小长度
- `DATA_QUALITY_MAX_TITLE_LENGTH`: 标题最大长度
- `DATA_QUALITY_MIN_CONTENT_LENGTH`: 内容最小长度
- `DATA_QUALITY_REQUIRED_FIELDS`: 必填字段列表

## 🧪 测试

### 运行完整测试：

```bash
python test_glm_integration.py
```

### 测试内容包括：

1. **GLM API连接测试** - 验证API密钥和连接
2. **文本摘要测试** - 测试摘要生成功能
3. **内容分类测试** - 测试分类准确性
4. **关键词提取测试** - 测试关键词提取效果
5. **情感分析测试** - 测试情感分析准确性
6. **内容处理器测试** - 测试综合处理功能
7. **合规检查测试** - 测试敏感词过滤和合规检查

## 📈 性能优化

### 1. 并发处理
- 使用`asyncio.gather()`并行执行多个AI任务
- 减少总体处理时间

### 2. 缓存机制
- 对相同内容的处理结果进行缓存
- 避免重复的API调用

### 3. 错误处理
- 完善的异常处理机制
- 自动重试和降级策略

### 4. 成本控制
- GLM-4.5 Flash完全免费
- 无需担心API调用成本

## 🔍 故障排除

### 常见问题：

1. **API密钥错误**
   - 检查GLM_API_KEY是否正确设置
   - 确认API密钥有效性

2. **网络连接问题**
   - 检查网络连接
   - 确认防火墙设置

3. **依赖包问题**
   - 运行`pip install -r requirements.txt`
   - 检查Python版本兼容性

4. **内存不足**
   - 调整批处理大小
   - 优化内存使用

## 📚 相关文档

- [GLM-4.5 Flash官方文档](https://docs.bigmodel.cn/cn/guide/models/free/glm-4.5-flash)
- [智谱AI API文档](https://docs.bigmodel.cn/api-reference/)
- [项目技术架构文档](../技术文档/系统架构设计.md)

## 🎯 下一步计划

1. **第4天**: 敏感词与合规模块完善
2. **第5天**: 实体识别和订阅系统
3. **性能优化**: 批处理和缓存机制
4. **监控告警**: API调用监控和异常告警
