# 财经新闻Bot权限管理系统文档

## 概述

财经新闻Bot权限管理系统是一个基于角色的访问控制（RBAC）系统，提供细粒度的权限控制、完整的审计日志和灵活的权限管理功能。

## 系统架构

### 核心组件

1. **权限矩阵** (`app/utils/permissions.py`)
   - 定义用户角色和权限的映射关系
   - 支持权限继承和层级管理
   - 提供权限检查函数

2. **权限装饰器** (`app/utils/decorators.py`)
   - 基于装饰器的权限控制
   - 支持单权限、多权限、角色层级检查
   - 自动异常处理和日志记录

3. **依赖注入** (`app/dependencies/permissions.py`)
   - FastAPI原生依赖注入支持
   - 预定义权限依赖函数
   - 灵活的自定义权限组合

4. **权限中间件** (`app/middleware/permission_middleware.py`)
   - 全局权限检查和异常处理
   - 性能监控和审计日志
   - 维护模式支持

5. **审计日志** (`app/utils/audit_logger.py`)
   - 完整的权限操作审计
   - 多维度日志查询和统计
   - 自动日志清理

## 用户角色体系

### 角色定义

| 角色 | 描述 | 权限数量 | 主要功能 |
|------|------|----------|----------|
| **FREE** | 免费用户 | 3个 | 基础新闻阅读、简单订阅 |
| **PRO** | 专业版用户 | 7个 | 高级订阅、数据导出、分析功能 |
| **ENTERPRISE** | 企业版用户 | 10个 | 批量操作、团队管理、API访问 |
| **ADMIN** | 管理员 | 全部 | 系统管理、用户管理、配置管理 |

### 权限矩阵

#### FREE用户权限
- `read_news` - 阅读新闻
- `view_profile` - 查看个人资料
- `basic_subscription` - 基础订阅功能

#### PRO用户权限（包含FREE权限）
- `advanced_subscription` - 高级订阅功能
- `custom_alerts` - 自定义提醒
- `export_data` - 数据导出
- `view_analytics` - 查看分析数据

#### ENTERPRISE用户权限（包含PRO权限）
- `api_access` - API访问权限
- `bulk_operations` - 批量操作
- `team_management` - 团队管理

#### ADMIN用户权限（包含所有权限）
- `user_management` - 用户管理
- `system_config` - 系统配置
- `monitoring_access` - 监控访问
- `audit_logs` - 审计日志查看

## 使用指南

### 1. 装饰器方式

```python
from app.utils.decorators import require_permission, require_role

@require_permission("export_data")
async def export_news_data(current_user=None):
    # 需要export_data权限
    pass

@require_role(UserRole.ADMIN)
async def admin_function(current_user=None):
    # 需要管理员角色
    pass
```

### 2. 依赖注入方式

```python
from app.dependencies.permissions import require_export_data, require_admin

@router.get("/export")
async def export_data(
    current_user: User = Depends(require_export_data())
):
    # 自动权限检查
    pass

@router.get("/admin/stats")
async def get_admin_stats(
    current_user: User = Depends(require_admin())
):
    # 管理员权限检查
    pass
```

### 3. 手动权限检查

```python
from app.utils.permissions import has_permission, PermissionChecker

# 简单权限检查
if has_permission(user.role, "export_data"):
    # 执行导出操作
    pass

# 复杂权限检查
checker = PermissionChecker(user)
checker.require_permission_or_raise("user_management")
```

## API权限控制

### 新闻API权限

| 端点 | 方法 | 权限要求 | 角色 |
|------|------|----------|------|
| `/news/` | GET | `read_news` | 所有角色 |
| `/news/{id}` | GET | `read_news` | 所有角色 |
| `/news/export/csv` | GET | `export_data` | PRO+ |
| `/news/analytics/stats` | GET | `view_analytics` | PRO+ |
| `/news/` | POST | 管理员权限 | ADMIN |

### 订阅API权限

| 端点 | 方法 | 权限要求 | 角色 |
|------|------|----------|------|
| `/subscriptions/basic` | POST | `basic_subscription` | 所有角色 |
| `/subscriptions/advanced` | POST | `advanced_subscription` | PRO+ |
| `/subscriptions/bulk-create` | POST | `bulk_operations` | ENTERPRISE+ |

### 用户管理API权限

| 端点 | 方法 | 权限要求 | 角色 |
|------|------|----------|------|
| `/users/me` | GET | 认证用户 | 所有角色 |
| `/users/` | GET | `user_management` | ADMIN |
| `/users/{id}/role` | PUT | `user_management` | ADMIN |

## 审计日志

### 日志类型

1. **权限检查日志**
   - 记录所有权限验证操作
   - 包含成功和失败的检查

2. **角色变更日志**
   - 记录用户角色的变更历史
   - 包含变更原因和操作者

3. **系统配置日志**
   - 记录系统配置的修改
   - 包含配置项和变更值

4. **登录尝试日志**
   - 记录用户登录尝试
   - 包含成功和失败的登录

### 日志查询

```python
from app.utils.audit_logger import get_audit_logger

audit_logger = get_audit_logger(db)

# 获取用户的审计日志
logs = await audit_logger.get_audit_logs(
    user_id=123,
    action="permission_check",
    start_date=datetime(2025, 8, 1),
    end_date=datetime(2025, 8, 18)
)

# 获取审计统计
stats = await audit_logger.get_audit_statistics(
    start_date=datetime(2025, 8, 1),
    end_date=datetime(2025, 8, 18)
)
```

## 异常处理

### 权限异常类型

1. **InsufficientPermissionError** - 权限不足
2. **MultiplePermissionsRequiredError** - 多权限要求
3. **RoleHierarchyError** - 角色层级错误
4. **AuthenticationRequiredError** - 认证要求
5. **UserInactiveError** - 用户未激活

### 异常响应格式

```json
{
  "error": {
    "code": "PERMISSION_DENIED",
    "message": "Permission 'export_data' required",
    "type": "InsufficientPermissionError",
    "required_permission": "export_data",
    "user_role": "FREE"
  }
}
```

## 性能优化

### 权限检查性能

- 单权限检查：< 1ms
- 多权限检查：< 2ms
- 装饰器开销：< 2ms
- 并发处理：支持高并发权限检查

### 缓存策略

- 权限矩阵缓存：内存缓存，启动时加载
- 用户权限缓存：Redis缓存，TTL 1小时
- 审计日志：异步写入，批量处理

## 安全考虑

### 权限提升防护

- 严格的角色层级检查
- 防止用户绕过权限验证
- 管理员操作审计

### 会话管理

- JWT Token验证
- Token过期检查
- 会话状态跟踪

### 审计合规

- 完整的操作审计
- 不可篡改的日志记录
- 定期日志归档

## 配置管理

### 系统配置项

| 配置项 | 默认值 | 描述 |
|--------|--------|------|
| `permission_cache_ttl` | 3600 | 权限缓存TTL（秒） |
| `max_failed_attempts` | 5 | 最大失败尝试次数 |
| `session_timeout` | 86400 | 会话超时时间（秒） |
| `maintenance_mode` | false | 维护模式开关 |

### 配置更新

```python
# 通过管理员API更新配置
PUT /admin/config/system
{
  "permission_cache_ttl": 7200,
  "max_failed_attempts": 3
}
```

## 监控和告警

### 关键指标

- 权限检查成功率
- 权限拒绝频率
- 异常登录尝试
- 系统配置变更

### 告警规则

- 权限拒绝率 > 10%
- 连续登录失败 > 5次
- 未授权访问尝试
- 系统配置异常变更

## 故障排除

### 常见问题

1. **权限检查失败**
   - 检查用户角色是否正确
   - 验证权限矩阵配置
   - 查看审计日志

2. **Token验证失败**
   - 检查Token是否过期
   - 验证Token签名
   - 确认用户状态

3. **性能问题**
   - 检查权限缓存状态
   - 监控数据库连接
   - 分析审计日志量

### 调试工具

```python
# 权限调试
from app.utils.permissions import get_user_permissions
permissions = get_user_permissions(user.role)

# 审计日志调试
from app.utils.audit_logger import get_audit_logger
audit_logger = get_audit_logger(db)
logs = await audit_logger.get_audit_logs(user_id=user.id, limit=10)
```

## 最佳实践

1. **权限设计原则**
   - 最小权限原则
   - 权限分离
   - 定期权限审查

2. **代码实践**
   - 优先使用依赖注入
   - 统一异常处理
   - 完整的审计日志

3. **运维实践**
   - 定期备份审计日志
   - 监控权限使用情况
   - 及时更新安全配置

## 版本历史

- **v1.0** (2025-08-18) - 初始版本，基础RBAC功能
- **v1.1** (计划) - 动态权限配置
- **v1.2** (计划) - 细粒度资源权限
