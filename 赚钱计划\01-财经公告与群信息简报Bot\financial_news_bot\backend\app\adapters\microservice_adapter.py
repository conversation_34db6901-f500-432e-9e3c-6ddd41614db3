"""
微服务适配器
提供后端到微服务的统一调用接口
"""
import asyncio
import aiohttp
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
import json

logger = logging.getLogger(__name__)


class MicroserviceAdapter:
    """微服务适配器基类"""
    
    def __init__(self, base_url: str, timeout: int = 30):
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.session = None
    
    async def _get_session(self):
        """获取HTTP会话"""
        if self.session is None:
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=self.timeout)
            )
        return self.session
    
    async def _make_request(self, method: str, endpoint: str, data: Dict = None, retries: int = 3) -> Dict:
        """发起HTTP请求，带重试和降级机制"""
        last_exception = None

        for attempt in range(retries):
            try:
                session = await self._get_session()
                url = f"{self.base_url}{endpoint}"

                async with session.request(method, url, json=data) as response:
                    if response.status == 200:
                        return await response.json()
                    elif response.status == 503:
                        # 服务不可用，等待后重试
                        if attempt < retries - 1:
                            await asyncio.sleep(2 ** attempt)  # 指数退避
                            continue
                    else:
                        error_text = await response.text()
                        raise Exception(f"HTTP {response.status}: {error_text}")

            except asyncio.TimeoutError as e:
                last_exception = e
                logger.warning(f"微服务请求超时 {method} {endpoint} (尝试 {attempt + 1}/{retries})")
                if attempt < retries - 1:
                    await asyncio.sleep(1)
                    continue
            except Exception as e:
                last_exception = e
                logger.warning(f"微服务请求失败 {method} {endpoint} (尝试 {attempt + 1}/{retries}): {str(e)}")
                if attempt < retries - 1:
                    await asyncio.sleep(1)
                    continue

        # 所有重试都失败了
        logger.error(f"微服务请求最终失败 {method} {endpoint}: {str(last_exception)}")
        raise last_exception
    
    async def close(self):
        """关闭HTTP会话"""
        if self.session:
            await self.session.close()
            self.session = None


class SecurityMicroserviceAdapter(MicroserviceAdapter):
    """安全监控微服务适配器"""
    
    def __init__(self, base_url: str = "http://security-monitor:8001"):
        super().__init__(base_url)
    
    async def log_audit_event(
        self,
        event_type: str,
        user_id: Optional[str],
        ip_address: str,
        action: str,
        result: str = "success",
        severity: str = "LOW",
        resource: Optional[str] = None,
        user_agent: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        session_id: Optional[str] = None,
        request_id: Optional[str] = None
    ) -> Dict:
        """记录审计事件"""
        data = {
            "event_type": event_type,
            "user_id": user_id,
            "ip_address": ip_address,
            "action": action,
            "result": result,
            "severity": severity,
            "resource": resource,
            "user_agent": user_agent,
            "details": details or {},
            "session_id": session_id,
            "request_id": request_id
        }
        
        return await self._make_request("POST", "/api/v1/audit/events", data)
    
    async def get_audit_logs(
        self,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        user_id: Optional[str] = None,
        event_type: Optional[str] = None,
        severity: Optional[str] = None,
        ip_address: Optional[str] = None,
        limit: int = 100,
        offset: int = 0
    ) -> Dict:
        """查询审计日志"""
        data = {
            "start_time": start_time.isoformat() if start_time else None,
            "end_time": end_time.isoformat() if end_time else None,
            "user_id": user_id,
            "event_type": event_type,
            "severity": severity,
            "ip_address": ip_address,
            "limit": limit,
            "offset": offset
        }
        
        return await self._make_request("GET", "/api/v1/audit/logs", data)
    
    async def log_security_event(
        self,
        event_type: str,
        severity: str,
        source_ip: str,
        user_agent: str,
        endpoint: str,
        details: Dict[str, Any] = None,
        user_id: Optional[str] = None
    ) -> Dict:
        """记录安全事件"""
        data = {
            "event_type": event_type,
            "severity": severity,
            "source_ip": source_ip,
            "user_agent": user_agent,
            "endpoint": endpoint,
            "details": details or {},
            "user_id": user_id
        }
        
        return await self._make_request("POST", "/api/v1/security/events", data)
    
    async def get_security_status(self) -> Dict:
        """获取安全监控状态"""
        return await self._make_request("GET", "/api/v1/security/status")
    
    async def block_ip(self, ip_address: str, reason: str, duration_minutes: int = 60) -> Dict:
        """阻止IP地址"""
        data = {
            "ip_address": ip_address,
            "reason": reason,
            "duration_minutes": duration_minutes
        }

        return await self._make_request("POST", "/api/v1/security/block-ip", data)

    async def get_system_metrics(
        self,
        include_cpu: bool = True,
        include_memory: bool = True,
        include_disk: bool = True,
        include_network: bool = True
    ) -> Dict:
        """获取系统监控指标"""
        data = {
            "include_cpu": include_cpu,
            "include_memory": include_memory,
            "include_disk": include_disk,
            "include_network": include_network
        }

        return await self._make_request("POST", "/api/v1/monitoring/system", data)

    async def get_active_alerts(self) -> Dict:
        """获取活跃告警"""
        return await self._make_request("GET", "/api/v1/monitoring/alerts")

    async def create_alert_rule(
        self,
        name: str,
        metric: str,
        operator: str,
        threshold: float,
        severity: str,
        duration: int = 0,
        enabled: bool = True,
        description: str = ""
    ) -> Dict:
        """创建告警规则"""
        data = {
            "name": name,
            "metric": metric,
            "operator": operator,
            "threshold": threshold,
            "duration": duration,
            "severity": severity,
            "enabled": enabled,
            "description": description
        }

        return await self._make_request("POST", "/api/v1/monitoring/alert-rules", data)

    async def manage_ip_lists(
        self,
        ip_addresses: List[str],
        action: str,
        list_type: str,
        reason: Optional[str] = None
    ) -> Dict:
        """管理IP白名单和黑名单"""
        data = {
            "ip_addresses": ip_addresses,
            "action": action,
            "list_type": list_type,
            "reason": reason
        }

        return await self._make_request("POST", "/api/v1/security/ip-management", data)


class ComplianceMicroserviceAdapter(MicroserviceAdapter):
    """合规监控微服务适配器"""
    
    def __init__(self, base_url: str = "http://compliance-monitor:8002"):
        super().__init__(base_url)
    
    async def check_compliance(
        self,
        content: str,
        content_type: str = "text",
        strict_mode: bool = False,
        check_official_api: bool = True
    ) -> Dict:
        """内容合规检查"""
        data = {
            "content": content,
            "content_type": content_type,
            "strict_mode": strict_mode,
            "check_official_api": check_official_api
        }
        
        return await self._make_request("POST", "/api/v1/compliance/check", data)
    
    async def check_enhanced_compliance(
        self,
        content: str,
        content_type: str = "text",
        strict_mode: bool = False,
        check_sensitive_words: bool = True,
        check_investment_advice: bool = True,
        check_financial_promise: bool = True,
        filter_content: bool = True
    ) -> Dict:
        """增强合规检查"""
        data = {
            "content": content,
            "content_type": content_type,
            "strict_mode": strict_mode,
            "check_sensitive_words": check_sensitive_words,
            "check_investment_advice": check_investment_advice,
            "check_financial_promise": check_financial_promise,
            "filter_content": filter_content
        }
        
        return await self._make_request("POST", "/api/v1/compliance/check/enhanced", data)
    
    async def get_compliance_status(self) -> Dict:
        """获取合规监控状态"""
        return await self._make_request("GET", "/api/v1/compliance/status")
    
    async def start_audit(
        self,
        audit_type: str,
        target_system: str,
        audit_scope: List[str]
    ) -> Dict:
        """启动合规审计"""
        data = {
            "audit_type": audit_type,
            "target_system": target_system,
            "audit_scope": audit_scope
        }
        
        return await self._make_request("POST", "/api/v1/compliance/audit", data)
    
    async def get_active_violations(self) -> Dict:
        """获取当前活跃的违规事件"""
        return await self._make_request("GET", "/api/v1/compliance/violations")

    async def check_privacy(
        self,
        content: str,
        data_type: str = "text",
        mask_pii: bool = True,
        sensitivity_level: Optional[str] = None
    ) -> Dict:
        """隐私检查"""
        data = {
            "content": content,
            "data_type": data_type,
            "mask_pii": mask_pii,
            "sensitivity_level": sensitivity_level
        }

        return await self._make_request("POST", "/api/v1/privacy/check", data)

    async def sanitize_data(
        self,
        data: Dict[str, Any],
        sensitivity_level: str = "CONFIDENTIAL",
        preserve_structure: bool = True
    ) -> Dict:
        """数据脱敏"""
        request_data = {
            "data": data,
            "sensitivity_level": sensitivity_level,
            "preserve_structure": preserve_structure
        }

        return await self._make_request("POST", "/api/v1/privacy/sanitize", request_data)

    async def classify_data(
        self,
        data_type: str,
        content: str,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict:
        """数据分类"""
        data = {
            "data_type": data_type,
            "content": content,
            "context": context
        }

        return await self._make_request("POST", "/api/v1/privacy/classify", data)


class MicroserviceManager:
    """微服务管理器"""
    
    def __init__(self):
        self.security_adapter = SecurityMicroserviceAdapter()
        self.compliance_adapter = ComplianceMicroserviceAdapter()
    
    async def close_all(self):
        """关闭所有适配器"""
        await self.security_adapter.close()
        await self.compliance_adapter.close()


# 全局微服务管理器实例
microservice_manager = MicroserviceManager()


# 便捷函数
async def log_audit_event(
    event_type: str,
    user_id: Optional[str],
    ip_address: str,
    action: str,
    result: str = "success",
    severity: str = "LOW",
    resource: Optional[str] = None,
    user_agent: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None,
    session_id: Optional[str] = None,
    request_id: Optional[str] = None
) -> Dict:
    """记录审计事件的便捷函数"""
    return await microservice_manager.security_adapter.log_audit_event(
        event_type=event_type,
        user_id=user_id,
        ip_address=ip_address,
        action=action,
        result=result,
        severity=severity,
        resource=resource,
        user_agent=user_agent,
        details=details,
        session_id=session_id,
        request_id=request_id
    )


async def check_content_compliance(
    content: str,
    content_type: str = "text",
    enhanced: bool = True
) -> Dict:
    """检查内容合规的便捷函数"""
    if enhanced:
        return await microservice_manager.compliance_adapter.check_enhanced_compliance(content, content_type)
    else:
        return await microservice_manager.compliance_adapter.check_compliance(content, content_type)
