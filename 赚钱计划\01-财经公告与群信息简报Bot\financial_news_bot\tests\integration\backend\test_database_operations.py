"""
数据库操作集成测试
测试数据库读写操作、事务处理、数据一致性等
"""
import pytest
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from datetime import datetime, timedelta

from app.models.user import User
from app.models.subscription import Subscription
from app.models.news import News
from app.core.security import get_password_hash


@pytest.mark.integration
@pytest.mark.database
class TestUserDatabaseOperations:
    """用户数据库操作测试"""

    def test_create_user_with_relationships(self, db_session: Session):
        """测试创建用户及其关联数据"""
        # 创建用户
        user = User(
            username="testuser",
            email="<EMAIL>",
            password_hash=get_password_hash("password123"),
            is_active=True
        )
        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)
        
        # 创建订阅
        subscription = Subscription(
            name="测试订阅",
            keywords=["测试", "关键词"],
            categories=["finance"],
            user_id=user.id,
            is_active=True
        )
        db_session.add(subscription)
        db_session.commit()
        
        # 验证关联关系
        assert len(user.subscriptions) == 1
        assert user.subscriptions[0].name == "测试订阅"
        assert subscription.user.username == "testuser"

    def test_user_unique_constraints(self, db_session: Session):
        """测试用户唯一性约束"""
        # 创建第一个用户
        user1 = User(
            username="uniqueuser",
            email="<EMAIL>",
            password_hash=get_password_hash("password123")
        )
        db_session.add(user1)
        db_session.commit()
        
        # 尝试创建重复用户名的用户
        user2 = User(
            username="uniqueuser",  # 重复用户名
            email="<EMAIL>",
            password_hash=get_password_hash("password123")
        )
        db_session.add(user2)
        
        with pytest.raises(IntegrityError):
            db_session.commit()
        
        db_session.rollback()
        
        # 尝试创建重复邮箱的用户
        user3 = User(
            username="differentuser",
            email="<EMAIL>",  # 重复邮箱
            password_hash=get_password_hash("password123")
        )
        db_session.add(user3)
        
        with pytest.raises(IntegrityError):
            db_session.commit()

    def test_user_soft_delete(self, db_session: Session, test_user: User):
        """测试用户软删除"""
        # 标记用户为非活跃（软删除）
        test_user.is_active = False
        db_session.commit()
        
        # 验证用户仍存在但非活跃
        user = db_session.query(User).filter(User.id == test_user.id).first()
        assert user is not None
        assert user.is_active is False

    def test_cascade_delete_user_subscriptions(self, db_session: Session):
        """测试用户删除时订阅的级联删除"""
        # 创建用户和订阅
        user = User(
            username="cascadeuser",
            email="<EMAIL>",
            password_hash=get_password_hash("password123")
        )
        db_session.add(user)
        db_session.commit()
        
        subscription = Subscription(
            name="级联测试订阅",
            user_id=user.id,
            is_active=True
        )
        db_session.add(subscription)
        db_session.commit()
        
        subscription_id = subscription.id
        
        # 删除用户
        db_session.delete(user)
        db_session.commit()
        
        # 验证订阅也被删除
        deleted_subscription = db_session.query(Subscription).filter(
            Subscription.id == subscription_id
        ).first()
        assert deleted_subscription is None


@pytest.mark.integration
@pytest.mark.database
class TestNewsDatabaseOperations:
    """新闻数据库操作测试"""

    def test_create_news_with_tags(self, db_session: Session):
        """测试创建带标签的新闻"""
        news = News(
            title="测试新闻标题",
            content="测试新闻内容",
            summary="测试摘要",
            source="sse",
            source_url="https://example.com/news/1",
            category="finance",
            published_at=datetime.now()
        )
        db_session.add(news)
        db_session.commit()
        db_session.refresh(news)
        
        assert news.id is not None
        assert news.source == "sse"
        assert news.source_url == "https://example.com/news/1"

    def test_news_url_uniqueness(self, db_session: Session):
        """测试新闻URL唯一性"""
        url = "https://example.com/unique-news"
        
        # 创建第一条新闻
        news1 = News(
            title="第一条新闻",
            content="内容1",
            source_url=url,
            source="sse",
            category="finance"
        )
        db_session.add(news1)
        db_session.commit()
        
        # 尝试创建相同URL的新闻
        news2 = News(
            title="第二条新闻",
            content="内容2",
            source_url=url,  # 重复URL
            source="sse",
            category="finance"
        )
        db_session.add(news2)
        
        with pytest.raises(IntegrityError):
            db_session.commit()

    def test_news_search_functionality(self, db_session: Session):
        """测试新闻搜索功能"""
        # 创建测试新闻
        news_items = [
            News(
                title="央行发布货币政策报告",
                content="央行今日发布最新货币政策执行报告",
                source="sse",
                source_url="https://example.com/news/1",
                category="finance"
            ),
            News(
                title="股市大涨创新高",
                content="今日股市表现强劲，上证指数创新高",
                source="sse",
                source_url="https://example.com/news/2",
                category="finance"
            ),
            News(
                title="银行业监管新规出台",
                content="银保监会发布银行业监管新规定",
                source="sse",
                source_url="https://example.com/news/3",
                category="finance"
            )
        ]
        
        for news in news_items:
            db_session.add(news)
        db_session.commit()
        
        # 测试标题搜索
        results = db_session.query(News).filter(
            News.title.contains("央行")
        ).all()
        assert len(results) == 1
        assert "央行" in results[0].title
        
        # 测试内容搜索
        results = db_session.query(News).filter(
            News.content.contains("股市")
        ).all()
        assert len(results) == 1
        assert "股市" in results[0].content
        
        # 测试分类过滤
        results = db_session.query(News).filter(
            News.category == "finance"
        ).all()
        assert len(results) == 3
        assert results[0].category == "finance"

    def test_news_pagination(self, db_session: Session):
        """测试新闻分页查询"""
        # 创建多条新闻
        for i in range(15):
            news = News(
                title=f"新闻标题 {i}",
                content=f"新闻内容 {i}",
                url=f"https://example.com/news/{i}",
                category="finance",
                is_published=True
            )
            db_session.add(news)
        db_session.commit()
        
        # 测试分页
        page1 = db_session.query(News).offset(0).limit(10).all()
        page2 = db_session.query(News).offset(10).limit(10).all()
        
        assert len(page1) == 10
        assert len(page2) == 5  # 剩余5条
        
        # 确保没有重复
        page1_ids = [news.id for news in page1]
        page2_ids = [news.id for news in page2]
        assert len(set(page1_ids) & set(page2_ids)) == 0


# @pytest.mark.integration
# @pytest.mark.database
# class TestBookmarkOperations:
#     """收藏功能数据库操作测试 - 已禁用，Bookmark模型不存在"""
#     pass


@pytest.mark.integration
@pytest.mark.database
class TestTransactionHandling:
    """事务处理测试"""

    def test_transaction_rollback_on_error(self, db_session: Session):
        """测试错误时事务回滚"""
        initial_count = db_session.query(User).count()
        
        try:
            # 开始事务
            user1 = User(
                username="transactionuser1",
                email="<EMAIL>",
                password_hash=get_password_hash("password123")
            )
            db_session.add(user1)
            
            # 这个用户会导致错误（重复邮箱）
            user2 = User(
                username="transactionuser2",
                email="<EMAIL>",  # 重复邮箱
                password_hash=get_password_hash("password123")
            )
            db_session.add(user2)
            
            db_session.commit()
        except IntegrityError:
            db_session.rollback()
        
        # 验证没有用户被创建
        final_count = db_session.query(User).count()
        assert final_count == initial_count

    def test_batch_operations(self, db_session: Session):
        """测试批量操作"""
        # 批量创建新闻
        news_items = []
        for i in range(100):
            news = News(
                title=f"批量新闻 {i}",
                content=f"批量内容 {i}",
                source="sse",
                source_url=f"https://example.com/batch/{i}",
                category="finance"
            )
            news_items.append(news)
        
        # 批量添加
        db_session.add_all(news_items)
        db_session.commit()
        
        # 验证数量
        count = db_session.query(News).filter(
            News.title.like("批量新闻%")
        ).count()
        assert count == 100


# @pytest.mark.integration
# @pytest.mark.database
# class TestCrawlLogOperations:
#     """爬虫日志数据库操作测试 - 已禁用，CrawlLog模型不存在"""
#     pass
