from sqlalchemy import Column, Integer, String, JSON, Enum, TIMESTAMP, func
from sqlalchemy.dialects.mysql import ENUM as MySQLEnum
from ..database import Base
import enum

class EntityType(str, enum.Enum):
    COMPANY = "company"
    PERSON = "person"
    LOCATION = "location"

class Entity(Base):
    __tablename__ = "entities"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    name = Column(String(200), nullable=False, index=True, comment="实体名称")
    type = Column(MySQLEnum(EntityType), nullable=False, index=True, comment="实体类型")
    aliases = Column(JSON, nullable=True, comment="别名列表，JSON格式")
    stock_code = Column(String(20), nullable=True, index=True, comment="股票代码（如果是公司）")
    created_at = Column(TIMESTAMP, server_default=func.now(), nullable=False)
    
    def __repr__(self):
        return f"<Entity(id={self.id}, name='{self.name}', type='{self.type}', stock_code='{self.stock_code}')>"
