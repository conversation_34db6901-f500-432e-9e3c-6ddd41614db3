"""
隐私保护和数据最小化服务
实现个人信息保护、数据脱敏、最小化收集
"""
import re
import hashlib
import logging
from typing import Dict, List, Optional, Any, Set
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass
import json

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DataSensitivity(Enum):
    """数据敏感级别"""
    PUBLIC = "public"           # 公开数据
    INTERNAL = "internal"       # 内部数据
    CONFIDENTIAL = "confidential"  # 机密数据
    RESTRICTED = "restricted"   # 限制数据


class PIIType(Enum):
    """个人信息类型"""
    NAME = "name"               # 姓名
    PHONE = "phone"             # 电话
    EMAIL = "email"             # 邮箱
    ID_CARD = "id_card"         # 身份证
    BANK_CARD = "bank_card"     # 银行卡
    ADDRESS = "address"         # 地址
    IP_ADDRESS = "ip_address"   # IP地址
    DEVICE_ID = "device_id"     # 设备ID


@dataclass
class DataRetentionPolicy:
    """数据保留策略"""
    data_type: str
    retention_days: int
    auto_delete: bool
    anonymize_after_days: Optional[int] = None
    description: str = ""


@dataclass
class PrivacyCheckResult:
    """隐私检查结果"""
    has_pii: bool
    pii_types: List[PIIType]
    masked_content: str
    sensitivity_level: DataSensitivity
    recommendations: List[str]


class PrivacyService:
    """隐私保护服务"""
    
    def __init__(self):
        # PII识别模式
        self.pii_patterns = {
            PIIType.PHONE: [
                r'1[3-9]\d{9}',  # 中国手机号
                r'\d{3}-\d{4}-\d{4}',  # 美国电话格式
                r'\(\d{3}\)\s*\d{3}-\d{4}'  # 美国电话格式2
            ],
            PIIType.EMAIL: [
                r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
            ],
            PIIType.ID_CARD: [
                r'\b\d{17}[\dXx]\b',  # 中国身份证
                r'\b\d{15}\b'  # 旧版身份证
            ],
            PIIType.BANK_CARD: [
                r'\b\d{16,19}\b'  # 银行卡号
            ],
            PIIType.IP_ADDRESS: [
                r'\b(?:\d{1,3}\.){3}\d{1,3}\b',  # IPv4
                r'\b(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}\b'  # IPv6
            ]
        }
        
        # 数据保留策略
        self.retention_policies = {
            "user_logs": DataRetentionPolicy(
                data_type="user_logs",
                retention_days=90,
                auto_delete=True,
                anonymize_after_days=30,
                description="用户操作日志保留90天，30天后匿名化"
            ),
            "audit_logs": DataRetentionPolicy(
                data_type="audit_logs",
                retention_days=365,
                auto_delete=False,
                anonymize_after_days=180,
                description="审计日志保留1年，180天后匿名化"
            ),
            "user_data": DataRetentionPolicy(
                data_type="user_data",
                retention_days=1095,  # 3年
                auto_delete=False,
                description="用户数据保留3年"
            ),
            "session_data": DataRetentionPolicy(
                data_type="session_data",
                retention_days=7,
                auto_delete=True,
                description="会话数据保留7天"
            )
        }
        
        # 敏感字段列表
        self.sensitive_fields = {
            "password", "token", "secret", "key", "credential",
            "auth", "session", "cookie", "private", "confidential",
            "phone", "email", "address", "id_card", "bank_card",
            "real_name", "identity", "personal"
        }
        
        # 数据分类规则
        self.classification_rules = {
            DataSensitivity.PUBLIC: ["news", "announcement", "market_data"],
            DataSensitivity.INTERNAL: ["user_preferences", "subscription", "analytics"],
            DataSensitivity.CONFIDENTIAL: ["user_profile", "payment_info", "contact_info"],
            DataSensitivity.RESTRICTED: ["password", "token", "private_key", "audit_log"]
        }
    
    def check_pii(self, content: str) -> PrivacyCheckResult:
        """检查内容中的个人信息"""
        try:
            pii_types = []
            masked_content = content
            recommendations = []
            
            # 检查各种PII类型
            for pii_type, patterns in self.pii_patterns.items():
                for pattern in patterns:
                    matches = re.finditer(pattern, content)
                    for match in matches:
                        if pii_type not in pii_types:
                            pii_types.append(pii_type)
                        
                        # 脱敏处理
                        original = match.group()
                        masked = self._mask_pii(original, pii_type)
                        masked_content = masked_content.replace(original, masked)
                        
                        recommendations.append(f"检测到{pii_type.value}，已进行脱敏处理")
            
            # 确定敏感级别
            sensitivity_level = self._determine_sensitivity(pii_types)
            
            return PrivacyCheckResult(
                has_pii=len(pii_types) > 0,
                pii_types=pii_types,
                masked_content=masked_content,
                sensitivity_level=sensitivity_level,
                recommendations=recommendations
            )
            
        except Exception as e:
            logger.error(f"PII check failed: {str(e)}")
            return PrivacyCheckResult(
                has_pii=False,
                pii_types=[],
                masked_content=content,
                sensitivity_level=DataSensitivity.PUBLIC,
                recommendations=["隐私检查出现异常"]
            )
    
    def _mask_pii(self, text: str, pii_type: PIIType) -> str:
        """脱敏处理"""
        if pii_type == PIIType.PHONE:
            if len(text) == 11:
                return text[:3] + "****" + text[7:]
            else:
                return text[:3] + "****" + text[-2:]
        
        elif pii_type == PIIType.EMAIL:
            parts = text.split('@')
            if len(parts) == 2:
                username = parts[0]
                domain = parts[1]
                if len(username) > 2:
                    masked_username = username[:2] + "*" * (len(username) - 2)
                else:
                    masked_username = "*" * len(username)
                return f"{masked_username}@{domain}"
        
        elif pii_type == PIIType.ID_CARD:
            if len(text) >= 6:
                return text[:3] + "*" * (len(text) - 6) + text[-3:]
        
        elif pii_type == PIIType.BANK_CARD:
            if len(text) >= 8:
                return text[:4] + "*" * (len(text) - 8) + text[-4:]
        
        elif pii_type == PIIType.IP_ADDRESS:
            parts = text.split('.')
            if len(parts) == 4:
                return f"{parts[0]}.{parts[1]}.***.***.***"
        
        # 默认脱敏方式
        if len(text) > 4:
            return text[:2] + "*" * (len(text) - 4) + text[-2:]
        else:
            return "*" * len(text)
    
    def _determine_sensitivity(self, pii_types: List[PIIType]) -> DataSensitivity:
        """确定数据敏感级别"""
        if not pii_types:
            return DataSensitivity.PUBLIC
        
        high_sensitivity_types = {PIIType.ID_CARD, PIIType.BANK_CARD}
        medium_sensitivity_types = {PIIType.PHONE, PIIType.EMAIL, PIIType.ADDRESS}
        
        if any(pii_type in high_sensitivity_types for pii_type in pii_types):
            return DataSensitivity.RESTRICTED
        elif any(pii_type in medium_sensitivity_types for pii_type in pii_types):
            return DataSensitivity.CONFIDENTIAL
        else:
            return DataSensitivity.INTERNAL
    
    def sanitize_data(self, data: Dict[str, Any], sensitivity_level: DataSensitivity = None) -> Dict[str, Any]:
        """数据清理和脱敏"""
        if not isinstance(data, dict):
            return data
        
        sanitized = {}
        
        for key, value in data.items():
            key_lower = key.lower()
            
            # 检查是否为敏感字段
            is_sensitive = any(sensitive in key_lower for sensitive in self.sensitive_fields)
            
            if is_sensitive:
                if sensitivity_level == DataSensitivity.PUBLIC:
                    # 公开数据完全移除敏感字段
                    continue
                else:
                    # 其他级别进行脱敏
                    sanitized[key] = self._sanitize_value(value)
            elif isinstance(value, dict):
                sanitized[key] = self.sanitize_data(value, sensitivity_level)
            elif isinstance(value, list):
                sanitized[key] = [
                    self.sanitize_data(item, sensitivity_level) if isinstance(item, dict) else item
                    for item in value
                ]
            else:
                sanitized[key] = value
        
        return sanitized
    
    def _sanitize_value(self, value: Any) -> str:
        """脱敏单个值"""
        if isinstance(value, str):
            if len(value) > 4:
                return value[:2] + "*" * (len(value) - 4) + value[-2:]
            else:
                return "*" * len(value)
        else:
            return "[REDACTED]"
    
    def classify_data(self, data_type: str, content: Dict[str, Any]) -> DataSensitivity:
        """数据分类"""
        # 根据数据类型分类
        for sensitivity, types in self.classification_rules.items():
            if data_type in types:
                return sensitivity
        
        # 根据内容分析
        if isinstance(content, dict):
            for key in content.keys():
                key_lower = key.lower()
                if any(sensitive in key_lower for sensitive in self.sensitive_fields):
                    return DataSensitivity.CONFIDENTIAL
        
        return DataSensitivity.INTERNAL
    
    def apply_retention_policy(self, data_type: str) -> Optional[DataRetentionPolicy]:
        """应用数据保留策略"""
        return self.retention_policies.get(data_type)
    
    def should_delete_data(self, data_type: str, created_at: datetime) -> bool:
        """判断是否应该删除数据"""
        policy = self.retention_policies.get(data_type)
        if not policy or not policy.auto_delete:
            return False
        
        retention_deadline = created_at + timedelta(days=policy.retention_days)
        return datetime.utcnow() > retention_deadline
    
    def should_anonymize_data(self, data_type: str, created_at: datetime) -> bool:
        """判断是否应该匿名化数据"""
        policy = self.retention_policies.get(data_type)
        if not policy or not policy.anonymize_after_days:
            return False
        
        anonymize_deadline = created_at + timedelta(days=policy.anonymize_after_days)
        return datetime.utcnow() > anonymize_deadline
    
    def anonymize_user_data(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """匿名化用户数据"""
        anonymized = user_data.copy()
        
        # 生成匿名ID
        if 'user_id' in anonymized:
            original_id = str(anonymized['user_id'])
            anonymized['user_id'] = hashlib.sha256(original_id.encode()).hexdigest()[:16]
        
        # 移除直接标识符
        direct_identifiers = ['name', 'email', 'phone', 'address', 'id_card']
        for identifier in direct_identifiers:
            if identifier in anonymized:
                del anonymized[identifier]
        
        # 泛化准标识符
        if 'age' in anonymized:
            age = anonymized['age']
            if isinstance(age, int):
                # 年龄分组
                if age < 18:
                    anonymized['age_group'] = '未成年'
                elif age < 30:
                    anonymized['age_group'] = '18-29'
                elif age < 50:
                    anonymized['age_group'] = '30-49'
                else:
                    anonymized['age_group'] = '50+'
                del anonymized['age']
        
        if 'location' in anonymized:
            # 位置泛化到城市级别
            location = anonymized['location']
            if isinstance(location, str) and len(location) > 2:
                anonymized['location'] = location[:2] + "市"
        
        # 添加匿名化标记
        anonymized['_anonymized'] = True
        anonymized['_anonymized_at'] = datetime.utcnow().isoformat()
        
        return anonymized
    
    def generate_privacy_report(self, data_sample: Dict[str, Any]) -> Dict[str, Any]:
        """生成隐私评估报告"""
        try:
            # 检查PII
            content_str = json.dumps(data_sample, ensure_ascii=False)
            pii_result = self.check_pii(content_str)
            
            # 数据分类
            sensitivity = self.classify_data("unknown", data_sample)
            
            # 敏感字段统计
            sensitive_field_count = 0
            for key in data_sample.keys():
                if any(sensitive in key.lower() for sensitive in self.sensitive_fields):
                    sensitive_field_count += 1
            
            return {
                "timestamp": datetime.utcnow().isoformat(),
                "has_pii": pii_result.has_pii,
                "pii_types": [pii_type.value for pii_type in pii_result.pii_types],
                "sensitivity_level": sensitivity.value,
                "sensitive_field_count": sensitive_field_count,
                "total_fields": len(data_sample),
                "recommendations": pii_result.recommendations,
                "compliance_score": self._calculate_privacy_score(pii_result, sensitive_field_count, len(data_sample))
            }
            
        except Exception as e:
            logger.error(f"Privacy report generation failed: {str(e)}")
            return {
                "timestamp": datetime.utcnow().isoformat(),
                "error": str(e),
                "compliance_score": 0
            }
    
    def _calculate_privacy_score(self, pii_result: PrivacyCheckResult, sensitive_fields: int, total_fields: int) -> float:
        """计算隐私合规评分"""
        score = 100.0
        
        # PII扣分
        if pii_result.has_pii:
            score -= len(pii_result.pii_types) * 15
        
        # 敏感字段比例扣分
        if total_fields > 0:
            sensitive_ratio = sensitive_fields / total_fields
            score -= sensitive_ratio * 20
        
        # 敏感级别扣分
        if pii_result.sensitivity_level == DataSensitivity.RESTRICTED:
            score -= 30
        elif pii_result.sensitivity_level == DataSensitivity.CONFIDENTIAL:
            score -= 20
        elif pii_result.sensitivity_level == DataSensitivity.INTERNAL:
            score -= 10
        
        return max(0.0, score)
    
    def get_retention_policies(self) -> Dict[str, DataRetentionPolicy]:
        """获取数据保留策略"""
        return self.retention_policies.copy()
    
    def update_retention_policy(self, data_type: str, policy: DataRetentionPolicy):
        """更新数据保留策略"""
        self.retention_policies[data_type] = policy
        logger.info(f"Updated retention policy for {data_type}")


# 全局隐私服务实例
privacy_service = PrivacyService()
