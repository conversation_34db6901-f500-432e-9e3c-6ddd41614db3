/**
 * A/B测试配置和管理工具
 * 支持多变量测试、用户分组、效果统计等功能
 */

import { userAnalytics } from './userAnalytics';

// A/B测试实验配置
export interface ABTestExperiment {
  id: string;
  name: string;
  description: string;
  status: 'draft' | 'running' | 'paused' | 'completed';
  startDate: string;
  endDate?: string;
  trafficAllocation: number; // 参与测试的用户比例 0-1
  variants: ABTestVariant[];
  targetMetrics: string[];
  segmentationRules?: SegmentationRule[];
  hypothesis: string;
  successCriteria: string[];
}

// A/B测试变体
export interface ABTestVariant {
  id: string;
  name: string;
  description: string;
  trafficWeight: number; // 该变体的流量权重
  config: Record<string, any>; // 变体配置参数
  isControl: boolean; // 是否为对照组
}

// 用户分组规则
export interface SegmentationRule {
  type: 'user_property' | 'device' | 'location' | 'behavior';
  property: string;
  operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than';
  value: any;
}

// A/B测试结果
export interface ABTestResult {
  experimentId: string;
  variantId: string;
  metric: string;
  value: number;
  sampleSize: number;
  conversionRate?: number;
  confidenceInterval?: [number, number];
  pValue?: number;
  isSignificant?: boolean;
}

// 用户分组信息
export interface UserAssignment {
  userId: string;
  experimentId: string;
  variantId: string;
  assignedAt: number;
  sessionId: string;
}

class ABTestManager {
  private experiments: Map<string, ABTestExperiment> = new Map();
  private userAssignments: Map<string, Map<string, UserAssignment>> = new Map();
  private results: Map<string, ABTestResult[]> = new Map();
  private userId?: string;
  private sessionId: string;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.loadExperiments();
    this.loadUserAssignments();
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private loadExperiments() {
    // 从本地存储或API加载实验配置
    const stored = localStorage.getItem('ab_experiments');
    if (stored) {
      try {
        const experiments = JSON.parse(stored);
        experiments.forEach((exp: ABTestExperiment) => {
          this.experiments.set(exp.id, exp);
        });
      } catch (error) {
        console.error('Failed to load AB experiments:', error);
      }
    }

    // 添加默认实验配置
    this.addDefaultExperiments();
  }

  private addDefaultExperiments() {
    const defaultExperiments: ABTestExperiment[] = [
      {
        id: 'registration_flow_v2',
        name: '注册流程优化',
        description: '测试简化注册流程对转化率的影响',
        status: 'running',
        startDate: '2024-01-15',
        trafficAllocation: 0.5,
        variants: [
          {
            id: 'control',
            name: '当前注册流程',
            description: '现有的5步注册流程',
            trafficWeight: 0.5,
            config: { steps: 5, thirdPartyLogin: false },
            isControl: true,
          },
          {
            id: 'simplified',
            name: '简化注册流程',
            description: '3步注册流程，支持第三方登录',
            trafficWeight: 0.5,
            config: { steps: 3, thirdPartyLogin: true },
            isControl: false,
          },
        ],
        targetMetrics: ['registration_completion_rate', 'registration_time', 'user_satisfaction'],
        hypothesis: '简化注册流程将提升注册完成率和用户满意度',
        successCriteria: [
          '注册完成率提升10%以上',
          '注册时间减少30%以上',
          '用户满意度提升0.5分以上',
        ],
      },
      {
        id: 'subscription_wizard_v2',
        name: '订阅配置向导',
        description: '测试可视化订阅配置向导的效果',
        status: 'running',
        startDate: '2024-01-20',
        trafficAllocation: 0.3,
        variants: [
          {
            id: 'control',
            name: '当前配置界面',
            description: '现有的表单式配置界面',
            trafficWeight: 0.5,
            config: { wizard: false, templates: false },
            isControl: true,
          },
          {
            id: 'wizard',
            name: '配置向导',
            description: '分步骤的可视化配置向导',
            trafficWeight: 0.5,
            config: { wizard: true, templates: true },
            isControl: false,
          },
        ],
        targetMetrics: ['subscription_completion_rate', 'configuration_time', 'configuration_accuracy'],
        hypothesis: '可视化配置向导将提升订阅创建成功率',
        successCriteria: [
          '订阅完成率提升15%以上',
          '配置时间减少25%以上',
          '配置准确性提升20%以上',
        ],
      },
      {
        id: 'mobile_navigation_v2',
        name: '移动端导航优化',
        description: '测试底部导航vs侧边导航的效果',
        status: 'draft',
        startDate: '2024-02-01',
        trafficAllocation: 0.4,
        variants: [
          {
            id: 'sidebar',
            name: '侧边导航',
            description: '当前的侧边抽屉导航',
            trafficWeight: 0.5,
            config: { navigationType: 'sidebar' },
            isControl: true,
          },
          {
            id: 'bottom_nav',
            name: '底部导航',
            description: '底部标签导航',
            trafficWeight: 0.5,
            config: { navigationType: 'bottom' },
            isControl: false,
          },
        ],
        targetMetrics: ['page_depth', 'task_completion_rate', 'navigation_efficiency'],
        segmentationRules: [
          {
            type: 'device',
            property: 'type',
            operator: 'equals',
            value: 'mobile',
          },
        ],
        hypothesis: '底部导航将提升移动端用户的导航效率',
        successCriteria: [
          '页面访问深度提升20%以上',
          '任务完成率提升15%以上',
          '导航效率提升30%以上',
        ],
      },
    ];

    defaultExperiments.forEach(exp => {
      if (!this.experiments.has(exp.id)) {
        this.experiments.set(exp.id, exp);
      }
    });
  }

  private loadUserAssignments() {
    const stored = localStorage.getItem('ab_user_assignments');
    if (stored) {
      try {
        const assignments = JSON.parse(stored);
        Object.entries(assignments).forEach(([userId, userExps]: [string, any]) => {
          const userMap = new Map<string, UserAssignment>();
          Object.entries(userExps).forEach(([expId, assignment]: [string, any]) => {
            userMap.set(expId, assignment as UserAssignment);
          });
          this.userAssignments.set(userId, userMap);
        });
      } catch (error) {
        console.error('Failed to load user assignments:', error);
      }
    }
  }

  private saveUserAssignments() {
    const assignments: Record<string, Record<string, UserAssignment>> = {};
    this.userAssignments.forEach((userMap, userId) => {
      assignments[userId] = {};
      userMap.forEach((assignment, expId) => {
        assignments[userId][expId] = assignment;
      });
    });
    localStorage.setItem('ab_user_assignments', JSON.stringify(assignments));
  }

  // 设置用户ID
  public setUserId(userId: string) {
    this.userId = userId;
  }

  // 获取用户的实验变体
  public getVariant(experimentId: string): string | null {
    if (!this.userId) {
      return null;
    }

    const experiment = this.experiments.get(experimentId);
    if (!experiment || experiment.status !== 'running') {
      return null;
    }

    // 检查用户是否已经分组
    const userExperiments = this.userAssignments.get(this.userId);
    if (userExperiments?.has(experimentId)) {
      return userExperiments.get(experimentId)!.variantId;
    }

    // 检查用户是否符合分组条件
    if (!this.isUserEligible(experiment)) {
      return null;
    }

    // 检查是否在流量分配范围内
    if (!this.isInTrafficAllocation(experiment)) {
      return null;
    }

    // 分配变体
    const variantId = this.assignVariant(experiment);
    
    // 保存分组信息
    this.saveUserAssignment(experimentId, variantId);
    
    return variantId;
  }

  private isUserEligible(experiment: ABTestExperiment): boolean {
    if (!experiment.segmentationRules || experiment.segmentationRules.length === 0) {
      return true;
    }

    // 检查分组规则
    return experiment.segmentationRules.every(rule => {
      return this.evaluateSegmentationRule(rule);
    });
  }

  private evaluateSegmentationRule(rule: SegmentationRule): boolean {
    let actualValue: any;

    switch (rule.type) {
      case 'device':
        actualValue = this.getDeviceProperty(rule.property);
        break;
      case 'user_property':
        actualValue = this.getUserProperty(rule.property);
        break;
      case 'location':
        actualValue = this.getLocationProperty(rule.property);
        break;
      case 'behavior':
        actualValue = this.getBehaviorProperty(rule.property);
        break;
      default:
        return false;
    }

    switch (rule.operator) {
      case 'equals':
        return actualValue === rule.value;
      case 'not_equals':
        return actualValue !== rule.value;
      case 'contains':
        return String(actualValue).includes(String(rule.value));
      case 'greater_than':
        return Number(actualValue) > Number(rule.value);
      case 'less_than':
        return Number(actualValue) < Number(rule.value);
      default:
        return false;
    }
  }

  private getDeviceProperty(property: string): any {
    const userAgent = navigator.userAgent;
    switch (property) {
      case 'type':
        if (/Mobile|Android|iPhone/.test(userAgent)) {
          return /iPad/.test(userAgent) ? 'tablet' : 'mobile';
        }
        return 'desktop';
      case 'os':
        if (userAgent.includes('Windows')) return 'Windows';
        if (userAgent.includes('Mac')) return 'macOS';
        if (userAgent.includes('Linux')) return 'Linux';
        if (userAgent.includes('Android')) return 'Android';
        if (userAgent.includes('iOS')) return 'iOS';
        return 'Unknown';
      default:
        return null;
    }
  }

  private getUserProperty(property: string): any {
    // 这里可以从用户数据中获取属性
    return null;
  }

  private getLocationProperty(property: string): any {
    // 这里可以从地理位置API获取信息
    return null;
  }

  private getBehaviorProperty(property: string): any {
    // 这里可以从用户行为数据中获取信息
    return null;
  }

  private isInTrafficAllocation(experiment: ABTestExperiment): boolean {
    if (!this.userId) return false;
    
    // 使用用户ID和实验ID生成一致的随机数
    const hash = this.hashString(`${this.userId}_${experiment.id}`);
    const random = (hash % 10000) / 10000; // 0-1之间的随机数
    
    return random < experiment.trafficAllocation;
  }

  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash);
  }

  private assignVariant(experiment: ABTestExperiment): string {
    if (!this.userId) return experiment.variants[0].id;
    
    // 使用用户ID生成一致的随机数
    const hash = this.hashString(`${this.userId}_${experiment.id}_variant`);
    const random = (hash % 10000) / 10000;
    
    // 根据权重分配变体
    let cumulativeWeight = 0;
    for (const variant of experiment.variants) {
      cumulativeWeight += variant.trafficWeight;
      if (random < cumulativeWeight) {
        return variant.id;
      }
    }
    
    // 默认返回第一个变体
    return experiment.variants[0].id;
  }

  private saveUserAssignment(experimentId: string, variantId: string) {
    if (!this.userId) return;

    if (!this.userAssignments.has(this.userId)) {
      this.userAssignments.set(this.userId, new Map());
    }

    const assignment: UserAssignment = {
      userId: this.userId,
      experimentId,
      variantId,
      assignedAt: Date.now(),
      sessionId: this.sessionId,
    };

    this.userAssignments.get(this.userId)!.set(experimentId, assignment);
    this.saveUserAssignments();
  }

  // 记录实验事件
  public trackExperimentEvent(experimentId: string, eventType: string, value?: number) {
    const variantId = this.getVariant(experimentId);
    if (!variantId) return;

    userAnalytics.trackCustomEvent(
      'ab_test',
      eventType,
      `${experimentId}_${variantId}`,
      value,
      {
        experimentId,
        variantId,
        userId: this.userId,
        sessionId: this.sessionId,
      }
    );
  }

  // 获取实验配置
  public getExperimentConfig(experimentId: string): Record<string, any> | null {
    const variantId = this.getVariant(experimentId);
    if (!variantId) return null;

    const experiment = this.experiments.get(experimentId);
    const variant = experiment?.variants.find(v => v.id === variantId);
    
    return variant?.config || null;
  }

  // 检查功能是否启用
  public isFeatureEnabled(experimentId: string, featureName: string): boolean {
    const config = this.getExperimentConfig(experimentId);
    return config?.[featureName] === true;
  }

  // 获取配置值
  public getConfigValue(experimentId: string, configKey: string, defaultValue?: any): any {
    const config = this.getExperimentConfig(experimentId);
    return config?.[configKey] ?? defaultValue;
  }

  // 获取所有运行中的实验
  public getRunningExperiments(): ABTestExperiment[] {
    return Array.from(this.experiments.values()).filter(exp => exp.status === 'running');
  }

  // 获取用户参与的实验
  public getUserExperiments(): UserAssignment[] {
    if (!this.userId || !this.userAssignments.has(this.userId)) {
      return [];
    }

    return Array.from(this.userAssignments.get(this.userId)!.values());
  }
}

// 全局A/B测试管理器实例
export const abTestManager = new ABTestManager();

// 便捷方法
export const getVariant = (experimentId: string): string | null => {
  return abTestManager.getVariant(experimentId);
};

export const isFeatureEnabled = (experimentId: string, featureName: string): boolean => {
  return abTestManager.isFeatureEnabled(experimentId, featureName);
};

export const getConfigValue = (experimentId: string, configKey: string, defaultValue?: any): any => {
  return abTestManager.getConfigValue(experimentId, configKey, defaultValue);
};

export const trackExperimentEvent = (experimentId: string, eventType: string, value?: number) => {
  abTestManager.trackExperimentEvent(experimentId, eventType, value);
};

export const setABTestUserId = (userId: string) => {
  abTestManager.setUserId(userId);
};
