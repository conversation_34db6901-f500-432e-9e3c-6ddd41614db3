"""
权限管理模块
定义用户角色权限矩阵和权限检查函数
"""
from typing import List, Dict, Set
from enum import Enum
from ..models.user import UserRole


class Permission(str, Enum):
    """权限枚举定义"""
    # 基础权限
    READ_NEWS = "read_news"
    VIEW_PROFILE = "view_profile"
    
    # 订阅权限
    BASIC_SUBSCRIPTION = "basic_subscription"
    ADVANCED_SUBSCRIPTION = "advanced_subscription"
    CUSTOM_ALERTS = "custom_alerts"
    
    # 数据权限
    EXPORT_DATA = "export_data"
    VIEW_ANALYTICS = "view_analytics"
    API_ACCESS = "api_access"
    
    # 企业权限
    BULK_OPERATIONS = "bulk_operations"
    TEAM_MANAGEMENT = "team_management"
    
    # 管理员权限
    USER_MANAGEMENT = "user_management"
    SYSTEM_CONFIG = "system_config"
    MONITORING_ACCESS = "monitoring_access"
    AUDIT_LOGS = "audit_logs"
    ALL_PERMISSIONS = "all_permissions"


# 角色权限矩阵
ROLE_PERMISSIONS: Dict[UserRole, List[Permission]] = {
    UserRole.FREE: [
        Permission.READ_NEWS,
        Permission.VIEW_PROFILE,
        Permission.BASIC_SUBSCRIPTION,
    ],
    
    UserRole.PRO: [
        Permission.READ_NEWS,
        Permission.VIEW_PROFILE,
        Permission.BASIC_SUBSCRIPTION,
        Permission.ADVANCED_SUBSCRIPTION,
        Permission.CUSTOM_ALERTS,
        Permission.EXPORT_DATA,
        Permission.VIEW_ANALYTICS,
    ],
    
    UserRole.ENTERPRISE: [
        Permission.READ_NEWS,
        Permission.VIEW_PROFILE,
        Permission.BASIC_SUBSCRIPTION,
        Permission.ADVANCED_SUBSCRIPTION,
        Permission.CUSTOM_ALERTS,
        Permission.EXPORT_DATA,
        Permission.VIEW_ANALYTICS,
        Permission.API_ACCESS,
        Permission.BULK_OPERATIONS,
        Permission.TEAM_MANAGEMENT,
    ],
    
    UserRole.ADMIN: [
        Permission.ALL_PERMISSIONS,
        Permission.USER_MANAGEMENT,
        Permission.SYSTEM_CONFIG,
        Permission.MONITORING_ACCESS,
        Permission.AUDIT_LOGS,
    ]
}


def has_permission(user_role: UserRole, permission: str) -> bool:
    """
    检查用户角色是否具有指定权限
    
    Args:
        user_role: 用户角色
        permission: 权限名称
        
    Returns:
        bool: 是否具有权限
    """
    if user_role not in ROLE_PERMISSIONS:
        return False
    
    role_permissions = ROLE_PERMISSIONS[user_role]
    
    # 管理员拥有所有权限
    if Permission.ALL_PERMISSIONS in role_permissions:
        return True
    
    # 检查具体权限
    return Permission(permission) in role_permissions


def get_user_permissions(user_role: UserRole) -> List[str]:
    """
    获取用户角色的所有权限列表
    
    Args:
        user_role: 用户角色
        
    Returns:
        List[str]: 权限列表
    """
    if user_role not in ROLE_PERMISSIONS:
        return []
    
    role_permissions = ROLE_PERMISSIONS[user_role]
    
    # 如果是管理员，返回所有权限
    if Permission.ALL_PERMISSIONS in role_permissions:
        return [perm.value for perm in Permission if perm != Permission.ALL_PERMISSIONS]
    
    return [perm.value for perm in role_permissions]


def has_any_permission(user_role: UserRole, permissions: List[str]) -> bool:
    """
    检查用户是否具有任意一个指定权限
    
    Args:
        user_role: 用户角色
        permissions: 权限列表
        
    Returns:
        bool: 是否具有任意权限
    """
    return any(has_permission(user_role, perm) for perm in permissions)


def has_all_permissions(user_role: UserRole, permissions: List[str]) -> bool:
    """
    检查用户是否具有所有指定权限
    
    Args:
        user_role: 用户角色
        permissions: 权限列表
        
    Returns:
        bool: 是否具有所有权限
    """
    return all(has_permission(user_role, perm) for perm in permissions)


def get_role_hierarchy() -> Dict[UserRole, int]:
    """
    获取角色层级关系
    
    Returns:
        Dict[UserRole, int]: 角色层级映射
    """
    return {
        UserRole.FREE: 1,
        UserRole.PRO: 2,
        UserRole.ENTERPRISE: 3,
        UserRole.ADMIN: 4
    }


def is_role_higher_or_equal(user_role: UserRole, target_role: UserRole) -> bool:
    """
    检查用户角色是否高于或等于目标角色
    
    Args:
        user_role: 用户角色
        target_role: 目标角色
        
    Returns:
        bool: 是否高于或等于目标角色
    """
    hierarchy = get_role_hierarchy()
    return hierarchy.get(user_role, 0) >= hierarchy.get(target_role, 0)


# 权限分组定义
PERMISSION_GROUPS = {
    "基础功能": [
        Permission.READ_NEWS,
        Permission.VIEW_PROFILE,
        Permission.BASIC_SUBSCRIPTION,
    ],
    "高级功能": [
        Permission.ADVANCED_SUBSCRIPTION,
        Permission.CUSTOM_ALERTS,
        Permission.EXPORT_DATA,
        Permission.VIEW_ANALYTICS,
    ],
    "企业功能": [
        Permission.API_ACCESS,
        Permission.BULK_OPERATIONS,
        Permission.TEAM_MANAGEMENT,
    ],
    "管理功能": [
        Permission.USER_MANAGEMENT,
        Permission.SYSTEM_CONFIG,
        Permission.MONITORING_ACCESS,
        Permission.AUDIT_LOGS,
    ]
}


def get_permission_groups() -> Dict[str, List[str]]:
    """
    获取权限分组信息
    
    Returns:
        Dict[str, List[str]]: 权限分组
    """
    return {
        group_name: [perm.value for perm in permissions]
        for group_name, permissions in PERMISSION_GROUPS.items()
    }
